<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 Enhanced PDF Viewer - Lector + react-pdf-highlighter-extended</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Header */
        .header {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 16px 24px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
        }

        .header h1 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 700;
            color: #2c3e50;
        }

        .header p {
            margin: 0 0 16px 0;
            color: #6c757d;
            font-size: 14px;
        }

        .controls {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 16px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            color: #495057;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover:not(:disabled) {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn.primary {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn.primary:hover {
            background: #0056b3;
        }

        .btn.success {
            background: #28a745;
            color: white;
            border-color: #28a745;
        }

        .btn.success:hover {
            background: #1e7e34;
        }

        .btn.danger {
            background: #dc3545;
            color: white;
            border-color: #dc3545;
        }

        .btn.danger:hover {
            background: #c82333;
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .file-input {
            display: none;
        }

        .zoom-display {
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            font-weight: 600;
            color: #495057;
            background: #f8f9fa;
            padding: 6px 12px;
            border-radius: 4px;
            border: 1px solid #dee2e6;
            min-width: 60px;
            text-align: center;
        }

        /* Main Container */
        .main-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        /* PDF Container */
        .pdf-container {
            flex: 1;
            overflow: auto;
            background: #f1f3f4;
            position: relative;
            cursor: grab;
        }

        .pdf-container.panning {
            cursor: grabbing;
        }

        .pdf-container.area-selection {
            cursor: crosshair;
        }

        .pdf-pages {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            padding: 20px;
            transition: transform 0.1s ease;
        }

        /* Page Container */
        .page-container {
            position: relative;
            background: white;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            overflow: hidden;
            transition: box-shadow 0.2s ease;
        }

        .page-container:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
        }

        .pdf-page {
            position: relative;
            display: block;
        }

        .pdf-canvas {
            display: block;
            max-width: 100%;
            height: auto;
        }

        /* Text Layer */
        .text-layer {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            opacity: 0.2;
            line-height: 1.0;
            pointer-events: none;
        }

        .text-layer.selectable {
            pointer-events: auto;
            opacity: 1;
        }

        .text-layer span {
            position: absolute;
            color: transparent;
            white-space: pre;
            cursor: text;
            transform-origin: 0% 0%;
            user-select: text;
        }

        .text-layer span::selection {
            background: rgba(33, 150, 243, 0.3);
        }

        /* Highlight Layer */
        .highlight-layer {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 5;
        }

        .highlight {
            position: absolute;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 2px;
            background: rgba(255, 235, 59, 0.4);
            border: 2px solid rgba(255, 152, 0, 0.8);
        }

        .highlight:hover {
            background: rgba(255, 235, 59, 0.5);
            border-color: rgba(255, 152, 0, 1);
            transform: scale(1.02);
        }

        .highlight-remove {
            position: absolute;
            top: -8px;
            right: -8px;
            width: 16px;
            height: 16px;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
            opacity: 0;
            transition: opacity 0.2s ease;
            cursor: pointer;
        }

        .highlight:hover .highlight-remove {
            opacity: 1;
        }

        /* Area Selector */
        .area-selector {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            pointer-events: auto;
            z-index: 10;
        }

        .selection-preview {
            position: absolute;
            border: 2px dashed rgba(255, 152, 0, 0.8);
            background: rgba(255, 235, 59, 0.2);
            border-radius: 2px;
            pointer-events: none;
            animation: selectionPulse 1s ease-in-out infinite alternate;
        }

        @keyframes selectionPulse {
            from {
                opacity: 0.3;
                transform: scale(1);
            }
            to {
                opacity: 0.6;
                transform: scale(1.01);
            }
        }

        /* Sidebar */
        .sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #e1e5e9;
            padding: 20px;
            overflow-y: auto;
            box-shadow: -1px 0 3px rgba(0, 0, 0, 0.1);
        }

        .sidebar h3 {
            margin: 0 0 16px 0;
            font-size: 16px;
            font-weight: 600;
            color: #2c3e50;
        }

        .stats {
            background: #f8f9fa;
            padding: 16px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #e1e5e9;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .stat-item:last-child {
            margin-bottom: 0;
        }

        .stat-label {
            color: #6c757d;
        }

        .stat-value {
            font-weight: 600;
            color: #2c3e50;
        }

        .highlight-list {
            list-style: none;
        }

        .highlight-item {
            padding: 12px;
            margin-bottom: 8px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
            font-size: 14px;
        }

        .highlight-meta {
            color: #6c757d;
            font-size: 12px;
            margin-top: 4px;
        }

        /* No PDF State */
        .no-pdf {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6c757d;
            text-align: center;
        }

        .no-pdf-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.6;
        }

        .no-pdf h2 {
            margin-bottom: 12px;
            color: #2c3e50;
        }

        /* Loading State */
        .loading {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #6c757d;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e1e5e9;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                max-height: 200px;
                border-left: none;
                border-top: 1px solid #e1e5e9;
            }
            
            .controls {
                gap: 8px;
            }
            
            .btn {
                padding: 6px 12px;
                font-size: 13px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Enhanced PDF Viewer</h1>
        <p>Combining Lector's panning/rotation with react-pdf-highlighter-extended's rectangle highlighting</p>
        
        <div class="controls">
            <input type="file" id="fileInput" class="file-input" accept=".pdf">
            <label for="fileInput" class="btn primary">📁 Upload PDF</label>
            
            <button class="btn success" onclick="loadSamplePDF()">📄 Load Sample</button>
            
            <div style="display: flex; align-items: center; gap: 8px;">
                <button class="btn" onclick="zoomOut()" id="zoomOutBtn">🔍−</button>
                <span class="zoom-display" id="zoomDisplay">100%</span>
                <button class="btn" onclick="zoomIn()" id="zoomInBtn">🔍+</button>
                <button class="btn" onclick="resetZoom()">📐 Fit</button>
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
                <button class="btn" onclick="rotateLeft()" id="rotateLeftBtn">↺</button>
                <button class="btn" onclick="rotateRight()" id="rotateRightBtn">↻</button>
            </div>
            
            <div style="display: flex; align-items: center; gap: 8px;">
                <button class="btn" onclick="setMode('text')" id="textModeBtn">📝 Text</button>
                <button class="btn active" onclick="setMode('area')" id="areaModeBtn">⬜ Area</button>
            </div>
            
            <button class="btn danger" onclick="clearHighlights()" id="clearBtn" style="display: none;">🗑️ Clear All</button>
        </div>
    </div>

    <div class="main-container">
        <div class="pdf-container" id="pdfContainer">
            <div class="no-pdf" id="noPdfMessage">
                <div class="no-pdf-icon">📄</div>
                <h2>No PDF loaded</h2>
                <p>Upload a PDF file or load the sample to get started</p>
                <div style="margin-top: 20px;">
                    <h3>✨ Features:</h3>
                    <ul style="text-align: left; margin-top: 10px;">
                        <li>🖱️ <strong>Panning & Zooming</strong> - Mouse wheel zoom, drag to pan</li>
                        <li>🔄 <strong>Rotation</strong> - Rotate pages left/right</li>
                        <li>⬜ <strong>Rectangle Highlighting</strong> - Click and drag to create highlights</li>
                        <li>📝 <strong>Text Selection</strong> - Select and copy text from PDFs</li>
                        <li>🎯 <strong>High Quality Rendering</strong> - Crisp PDF display at any zoom</li>
                    </ul>
                </div>
            </div>
            
            <div class="loading" id="loadingMessage" style="display: none;">
                <div class="spinner"></div>
                <p>Loading PDF...</p>
            </div>
            
            <div class="pdf-pages" id="pdfPages" style="display: none;">
                <!-- PDF pages will be rendered here -->
            </div>
        </div>

        <div class="sidebar">
            <div class="stats">
                <h3>📊 Statistics</h3>
                <div class="stat-item">
                    <span class="stat-label">Total Pages:</span>
                    <span class="stat-value" id="totalPages">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Current Zoom:</span>
                    <span class="stat-value" id="currentZoom">100%</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Rotation:</span>
                    <span class="stat-value" id="currentRotation">0°</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">Highlights:</span>
                    <span class="stat-value" id="highlightCount">0</span>
                </div>
            </div>

            <h3>📝 Highlights</h3>
            <ul class="highlight-list" id="highlightList">
                <li style="color: #999; font-style: italic; padding: 12px;">No highlights yet</li>
            </ul>
        </div>
    </div>

    <script>
        // Configure PDF.js worker
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
        
        // Global state
        let pdfDoc = null;
        let currentScale = 1.0;
        let currentRotation = 0;
        let currentMode = 'area'; // 'text' or 'area'
        let highlights = [];
        let isLoading = false;
        
        // Panning state
        let isPanning = false;
        let panStart = { x: 0, y: 0 };
        let panOffset = { x: 0, y: 0 };
        
        // Selection state
        let isSelecting = false;
        let selectionStart = null;
        let currentSelection = null;

        console.log('Enhanced PDF Viewer initialized');
        console.log('PDF.js version:', pdfjsLib.version);

        // Load sample PDF
        function loadSamplePDF() {
            loadPDF('public/sample.pdf');
        }

        // Load PDF from URL
        async function loadPDF(url) {
            if (isLoading) return;

            setLoading(true);

            try {
                console.log('Loading PDF:', url);
                const loadingTask = pdfjsLib.getDocument(url);
                pdfDoc = await loadingTask.promise;

                console.log('PDF loaded successfully, pages:', pdfDoc.numPages);

                // Update UI
                document.getElementById('totalPages').textContent = pdfDoc.numPages;

                // Render all pages
                await renderAllPages();

                // Show PDF container
                document.getElementById('noPdfMessage').style.display = 'none';
                document.getElementById('pdfPages').style.display = 'flex';

                // Clear highlights
                clearHighlights();

            } catch (error) {
                console.error('Error loading PDF:', error);
                alert('Error loading PDF: ' + error.message);
            } finally {
                setLoading(false);
            }
        }

        // Set loading state
        function setLoading(loading) {
            isLoading = loading;
            document.getElementById('loadingMessage').style.display = loading ? 'flex' : 'none';
            document.getElementById('noPdfMessage').style.display = loading ? 'none' : (pdfDoc ? 'none' : 'flex');
        }

        // Render all pages
        async function renderAllPages() {
            const container = document.getElementById('pdfPages');
            container.innerHTML = '';

            for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
                await renderPage(pageNum, container);
            }
        }

        // Render a single page
        async function renderPage(pageNum, container) {
            try {
                const page = await pdfDoc.getPage(pageNum);
                const viewport = page.getViewport({ scale: currentScale, rotation: currentRotation });

                // Create page container
                const pageContainer = document.createElement('div');
                pageContainer.className = 'page-container';
                pageContainer.dataset.pageNumber = pageNum;

                // Create canvas
                const canvas = document.createElement('canvas');
                canvas.className = 'pdf-canvas';
                const context = canvas.getContext('2d');

                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render PDF page
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                await page.render(renderContext).promise;

                // Create text layer
                const textLayerDiv = document.createElement('div');
                textLayerDiv.className = 'text-layer';
                if (currentMode === 'text') {
                    textLayerDiv.classList.add('selectable');
                }

                // Get text content and render
                try {
                    const textContent = await page.getTextContent();
                    textContent.items.forEach((textItem) => {
                        const textSpan = document.createElement('span');
                        textSpan.textContent = textItem.str;
                        textSpan.style.left = textItem.transform[4] + 'px';
                        textSpan.style.top = (viewport.height - textItem.transform[5]) + 'px';
                        textSpan.style.fontSize = Math.sqrt(textItem.transform[0] * textItem.transform[0] + textItem.transform[1] * textItem.transform[1]) + 'px';
                        textSpan.style.fontFamily = textItem.fontName || 'sans-serif';
                        textLayerDiv.appendChild(textSpan);
                    });
                } catch (textError) {
                    console.warn('Could not load text content for page', pageNum, textError);
                }

                // Create highlight layer
                const highlightLayer = document.createElement('div');
                highlightLayer.className = 'highlight-layer';

                // Create area selector layer
                const areaSelectorLayer = document.createElement('div');
                areaSelectorLayer.className = 'area-selector';
                if (currentMode === 'area') {
                    setupAreaSelector(areaSelectorLayer, pageNum);
                }

                // Assemble page
                pageContainer.appendChild(canvas);
                pageContainer.appendChild(textLayerDiv);
                pageContainer.appendChild(highlightLayer);
                pageContainer.appendChild(areaSelectorLayer);

                container.appendChild(pageContainer);

                // Render existing highlights for this page
                renderHighlightsForPage(pageNum);

                console.log(`Page ${pageNum} rendered successfully`);

            } catch (error) {
                console.error(`Error rendering page ${pageNum}:`, error);
            }
        }

        // Setup area selector for a page
        function setupAreaSelector(layer, pageNum) {
            layer.addEventListener('mousedown', (e) => handleAreaMouseDown(e, pageNum));
            layer.addEventListener('mousemove', (e) => handleAreaMouseMove(e, pageNum));
            layer.addEventListener('mouseup', (e) => handleAreaMouseUp(e, pageNum));
            layer.addEventListener('mouseleave', (e) => handleAreaMouseUp(e, pageNum));
        }

        // Area selection handlers
        function handleAreaMouseDown(e, pageNum) {
            if (currentMode !== 'area') return;

            e.preventDefault();
            e.stopPropagation();

            const rect = e.currentTarget.getBoundingClientRect();
            selectionStart = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top,
                pageNum
            };

            isSelecting = true;
            currentSelection = null;
        }

        function handleAreaMouseMove(e, pageNum) {
            if (!isSelecting || !selectionStart || currentMode !== 'area') return;

            const rect = e.currentTarget.getBoundingClientRect();
            const currentPos = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };

            // Update selection preview
            updateSelectionPreview(selectionStart, currentPos, pageNum);
        }

        function handleAreaMouseUp(e, pageNum) {
            if (!isSelecting || !selectionStart || currentMode !== 'area') return;

            const rect = e.currentTarget.getBoundingClientRect();
            const endPos = {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };

            // Create highlight if selection is large enough
            const width = Math.abs(endPos.x - selectionStart.x);
            const height = Math.abs(endPos.y - selectionStart.y);

            if (width > 10 && height > 10) {
                createHighlight(selectionStart, endPos, pageNum);
            }

            // Clear selection state
            clearSelectionPreview();
            isSelecting = false;
            selectionStart = null;
            currentSelection = null;
        }

        // Update selection preview
        function updateSelectionPreview(start, end, pageNum) {
            // Remove existing preview
            clearSelectionPreview();

            const pageContainer = document.querySelector(`[data-page-number="${pageNum}"]`);
            if (!pageContainer) return;

            const areaSelector = pageContainer.querySelector('.area-selector');
            if (!areaSelector) return;

            const preview = document.createElement('div');
            preview.className = 'selection-preview';
            preview.id = 'selectionPreview';

            const x = Math.min(start.x, end.x);
            const y = Math.min(start.y, end.y);
            const width = Math.abs(end.x - start.x);
            const height = Math.abs(end.y - start.y);

            preview.style.left = x + 'px';
            preview.style.top = y + 'px';
            preview.style.width = width + 'px';
            preview.style.height = height + 'px';

            areaSelector.appendChild(preview);
        }

        // Clear selection preview
        function clearSelectionPreview() {
            const preview = document.getElementById('selectionPreview');
            if (preview) {
                preview.remove();
            }
        }

        // Create highlight
        function createHighlight(start, end, pageNum) {
            const highlight = {
                id: 'highlight-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9),
                pageNum,
                x: Math.min(start.x, end.x),
                y: Math.min(start.y, end.y),
                width: Math.abs(end.x - start.x),
                height: Math.abs(end.y - start.y),
                timestamp: new Date().toLocaleString()
            };

            highlights.push(highlight);
            renderHighlight(highlight);
            updateHighlightStats();

            console.log('Created highlight:', highlight);
        }

        // Render a single highlight
        function renderHighlight(highlight) {
            const pageContainer = document.querySelector(`[data-page-number="${highlight.pageNum}"]`);
            if (!pageContainer) return;

            const highlightLayer = pageContainer.querySelector('.highlight-layer');
            if (!highlightLayer) return;

            const highlightEl = document.createElement('div');
            highlightEl.className = 'highlight';
            highlightEl.dataset.highlightId = highlight.id;
            highlightEl.style.left = highlight.x + 'px';
            highlightEl.style.top = highlight.y + 'px';
            highlightEl.style.width = highlight.width + 'px';
            highlightEl.style.height = highlight.height + 'px';
            highlightEl.title = `Highlight on page ${highlight.pageNum} - Click to remove`;

            // Add remove button
            const removeBtn = document.createElement('div');
            removeBtn.className = 'highlight-remove';
            removeBtn.innerHTML = '×';
            removeBtn.onclick = (e) => {
                e.stopPropagation();
                removeHighlight(highlight.id);
            };

            highlightEl.appendChild(removeBtn);
            highlightEl.onclick = () => removeHighlight(highlight.id);

            highlightLayer.appendChild(highlightEl);
        }

        // Render highlights for a specific page
        function renderHighlightsForPage(pageNum) {
            const pageHighlights = highlights.filter(h => h.pageNum === pageNum);
            pageHighlights.forEach(highlight => renderHighlight(highlight));
        }

        // Remove highlight
        function removeHighlight(highlightId) {
            highlights = highlights.filter(h => h.id !== highlightId);

            const highlightEl = document.querySelector(`[data-highlight-id="${highlightId}"]`);
            if (highlightEl) {
                highlightEl.remove();
            }

            updateHighlightStats();
            console.log('Removed highlight:', highlightId);
        }

        // Clear all highlights
        function clearHighlights() {
            highlights = [];
            document.querySelectorAll('.highlight').forEach(el => el.remove());
            updateHighlightStats();
            console.log('Cleared all highlights');
        }

        // Update highlight statistics
        function updateHighlightStats() {
            const count = highlights.length;
            document.getElementById('highlightCount').textContent = count;
            document.getElementById('clearBtn').style.display = count > 0 ? 'inline-flex' : 'none';

            // Update highlight list
            const list = document.getElementById('highlightList');
            if (count === 0) {
                list.innerHTML = '<li style="color: #999; font-style: italic; padding: 12px;">No highlights yet</li>';
            } else {
                list.innerHTML = highlights.map(h => `
                    <li class="highlight-item">
                        <strong>Page ${h.pageNum} Highlight</strong>
                        <div>Position: ${Math.round(h.x)}, ${Math.round(h.y)}</div>
                        <div>Size: ${Math.round(h.width)} × ${Math.round(h.height)}</div>
                        <div class="highlight-meta">Created: ${h.timestamp}</div>
                    </li>
                `).join('');
            }
        }

        // Zoom functions
        function zoomIn() {
            currentScale = Math.min(5.0, currentScale * 1.25);
            updateZoom();
        }

        function zoomOut() {
            currentScale = Math.max(0.1, currentScale * 0.8);
            updateZoom();
        }

        function resetZoom() {
            currentScale = 1.0;
            updateZoom();
        }

        function updateZoom() {
            if (pdfDoc) {
                renderAllPages();
            }

            const percentage = Math.round(currentScale * 100);
            document.getElementById('zoomDisplay').textContent = percentage + '%';
            document.getElementById('currentZoom').textContent = percentage + '%';

            // Update button states
            document.getElementById('zoomOutBtn').disabled = currentScale <= 0.1;
            document.getElementById('zoomInBtn').disabled = currentScale >= 5.0;
        }

        // Rotation functions
        function rotateLeft() {
            currentRotation = (currentRotation - 90 + 360) % 360;
            updateRotation();
        }

        function rotateRight() {
            currentRotation = (currentRotation + 90) % 360;
            updateRotation();
        }

        function updateRotation() {
            if (pdfDoc) {
                renderAllPages();
            }

            document.getElementById('currentRotation').textContent = currentRotation + '°';
        }

        // Mode switching
        function setMode(mode) {
            currentMode = mode;

            // Update button states
            document.getElementById('textModeBtn').classList.toggle('active', mode === 'text');
            document.getElementById('areaModeBtn').classList.toggle('active', mode === 'area');

            // Update container cursor
            const container = document.getElementById('pdfContainer');
            container.classList.toggle('area-selection', mode === 'area');

            // Update text layers
            document.querySelectorAll('.text-layer').forEach(layer => {
                layer.classList.toggle('selectable', mode === 'text');
            });

            // Re-setup area selectors
            if (pdfDoc) {
                document.querySelectorAll('.area-selector').forEach((layer, index) => {
                    const pageNum = index + 1;
                    // Remove existing listeners by replacing the element
                    const newLayer = layer.cloneNode(true);
                    layer.parentNode.replaceChild(newLayer, layer);

                    if (mode === 'area') {
                        setupAreaSelector(newLayer, pageNum);
                    }
                });
            }

            console.log('Mode set to:', mode);
        }

        // Panning functionality (Lector-inspired)
        function setupPanning() {
            const container = document.getElementById('pdfContainer');

            container.addEventListener('mousedown', (e) => {
                if (e.button === 0 && currentMode !== 'area') { // Left mouse button
                    isPanning = true;
                    panStart = { x: e.clientX, y: e.clientY };
                    container.classList.add('panning');
                    e.preventDefault();
                }
            });

            container.addEventListener('mousemove', (e) => {
                if (isPanning) {
                    const deltaX = e.clientX - panStart.x;
                    const deltaY = e.clientY - panStart.y;

                    panOffset.x += deltaX;
                    panOffset.y += deltaY;

                    const pdfPages = document.getElementById('pdfPages');
                    pdfPages.style.transform = `translate(${panOffset.x}px, ${panOffset.y}px)`;

                    panStart = { x: e.clientX, y: e.clientY };
                }
            });

            container.addEventListener('mouseup', () => {
                isPanning = false;
                container.classList.remove('panning');
            });

            container.addEventListener('mouseleave', () => {
                isPanning = false;
                container.classList.remove('panning');
            });

            // Mouse wheel zoom
            container.addEventListener('wheel', (e) => {
                if (e.ctrlKey || e.metaKey) {
                    e.preventDefault();
                    const delta = e.deltaY > 0 ? 0.9 : 1.1;
                    currentScale = Math.max(0.1, Math.min(5.0, currentScale * delta));
                    updateZoom();
                }
            }, { passive: false });
        }

        // File upload handler
        document.getElementById('fileInput').addEventListener('change', (e) => {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                const url = URL.createObjectURL(file);
                loadPDF(url);
            } else {
                alert('Please select a valid PDF file.');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            setupPanning();
            updateZoom();
            updateRotation();
            setMode('area');
            console.log('Enhanced PDF Viewer ready!');
        });
    </script>
</body>
</html>
