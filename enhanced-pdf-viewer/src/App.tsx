import React, { useState } from 'react';
import WorkingPDFViewer from './components/WorkingPDFViewer';
import './App.css';

function App() {
  const [highlights, setHighlights] = useState<any[]>([]);
  const [pdfUrl, setPdfUrl] = useState<string>('');
  const [fileInputKey, setFileInputKey] = useState<number>(0);

  // Sample PDF URLs for testing (using known working URLs)
  const samplePDFs = [
    {
      name: 'ArXiv Paper (Working)',
      url: 'https://arxiv.org/pdf/2203.11115',
    },
    {
      name: 'Mozilla PDF.js Demo',
      url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
    },
    {
      name: 'Local Sample PDF',
      url: '/sample.pdf',
    },
  ];

  const handleHighlightAdd = (highlight: any) => {
    console.log('Adding highlight:', highlight);
    setHighlights(prev => [...prev, highlight]);
  };

  const handleHighlightRemove = (highlightId: string) => {
    console.log('Removing highlight:', highlightId);
    setHighlights(prev => prev.filter(h => h.id !== highlightId));
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      const url = URL.createObjectURL(file);
      setPdfUrl(url);
      setHighlights([]); // Clear highlights when loading new PDF
    } else {
      alert('Please select a valid PDF file.');
    }
  };

  const loadSamplePDF = (url: string) => {
    setPdfUrl(url);
    setHighlights([]); // Clear highlights when loading new PDF
    setFileInputKey(prev => prev + 1); // Reset file input
  };

  const clearHighlights = () => {
    setHighlights([]);
  };

  return (
    <div className="app">
      <header className="app-header">
        <h1>🚀 Enhanced PDF Viewer</h1>
        <p>Combining Lector's panning/rotation with react-pdf-highlighter-extended's rectangle highlighting</p>
        
        <div className="controls">
          <div className="file-controls">
            <input
              key={fileInputKey}
              type="file"
              accept=".pdf"
              onChange={handleFileUpload}
              className="file-input"
              id="pdf-upload"
            />
            <label htmlFor="pdf-upload" className="file-label">
              📁 Upload PDF
            </label>
          </div>

          <div className="sample-controls">
            <span className="control-label">Or try a sample:</span>
            {samplePDFs.map((sample, index) => (
              <button
                key={index}
                onClick={() => loadSamplePDF(sample.url)}
                className="sample-button"
              >
                📄 {sample.name}
              </button>
            ))}
          </div>

          {highlights.length > 0 && (
            <div className="highlight-controls">
              <span className="highlight-count">
                {highlights.length} highlight{highlights.length !== 1 ? 's' : ''}
              </span>
              <button onClick={clearHighlights} className="clear-button">
                🗑️ Clear All
              </button>
            </div>
          )}
        </div>
      </header>

      <main className="app-main">
        {pdfUrl ? (
          <WorkingPDFViewer
            url={pdfUrl}
            highlights={highlights}
            onHighlightAdd={handleHighlightAdd}
            onHighlightRemove={handleHighlightRemove}
            className="pdf-viewer"
          />
        ) : (
          <div className="no-pdf">
            <div className="no-pdf-content">
              <div className="no-pdf-icon">📄</div>
              <h2>No PDF loaded</h2>
              <p>Upload a PDF file or select a sample to get started</p>
              
              <div className="features-list">
                <h3>✨ Features:</h3>
                <ul>
                  <li>🖱️ <strong>Panning & Zooming</strong> - Mouse wheel zoom, drag to pan</li>
                  <li>🔄 <strong>Rotation</strong> - Rotate pages left/right</li>
                  <li>⬜ <strong>Rectangle Highlighting</strong> - Click and drag to create highlights</li>
                  <li>📝 <strong>Text Selection</strong> - Select and copy text from PDFs</li>
                  <li>🎯 <strong>High Quality Rendering</strong> - Crisp PDF display at any zoom</li>
                  <li>📱 <strong>Responsive Design</strong> - Works on desktop and mobile</li>
                </ul>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
