import { create } from 'zustand';
import { PDFStore, Highlight, GhostHighlight, AreaSelection, ViewportTransform } from '../types';
import { PDFDocumentProxy } from 'pdfjs-dist';

export const usePDFStore = create<PDFStore>((set, get) => ({
  // Document state
  document: null,
  isLoading: false,
  error: null,
  
  // Viewer state
  currentPage: 1,
  totalPages: 0,
  scale: 1.0,
  rotation: 0,
  
  // Interaction state
  highlights: [],
  ghostHighlight: null,
  selectedText: null,
  areaSelection: null,
  
  // Viewport state
  viewportTransform: {
    scale: 1.0,
    rotation: 0,
    offsetX: 0,
    offsetY: 0,
  },
  
  // Actions
  setDocument: (document: PDFDocumentProxy | null) => {
    set({
      document,
      totalPages: document?.numPages || 0,
      currentPage: 1,
      error: null,
    });
  },
  
  setLoading: (isLoading: boolean) => {
    set({ isLoading });
  },
  
  setError: (error: string | null) => {
    set({ error, isLoading: false });
  },
  
  setCurrentPage: (currentPage: number) => {
    const { totalPages } = get();
    if (currentPage >= 1 && currentPage <= totalPages) {
      set({ currentPage });
    }
  },
  
  setScale: (scale: number) => {
    const clampedScale = Math.max(0.1, Math.min(5.0, scale));
    set({ scale: clampedScale });
    
    // Update viewport transform
    const { viewportTransform } = get();
    set({
      viewportTransform: {
        ...viewportTransform,
        scale: clampedScale,
      },
    });
  },
  
  setRotation: (rotation: number) => {
    const normalizedRotation = ((rotation % 360) + 360) % 360;
    set({ rotation: normalizedRotation });
    
    // Update viewport transform
    const { viewportTransform } = get();
    set({
      viewportTransform: {
        ...viewportTransform,
        rotation: normalizedRotation,
      },
    });
  },
  
  addHighlight: (highlight: Highlight) => {
    const { highlights } = get();
    set({
      highlights: [...highlights, highlight],
    });
  },
  
  removeHighlight: (id: string) => {
    const { highlights } = get();
    set({
      highlights: highlights.filter(h => h.id !== id),
    });
  },
  
  setGhostHighlight: (ghostHighlight: GhostHighlight | null) => {
    set({ ghostHighlight });
  },
  
  setAreaSelection: (areaSelection: AreaSelection | null) => {
    set({ areaSelection });
  },
  
  updateViewportTransform: (transform: Partial<ViewportTransform>) => {
    const { viewportTransform } = get();
    set({
      viewportTransform: {
        ...viewportTransform,
        ...transform,
      },
    });
  },
}));

// Selector hooks for better performance
export const useDocument = () => usePDFStore(state => state.document);
export const useIsLoading = () => usePDFStore(state => state.isLoading);
export const useError = () => usePDFStore(state => state.error);
export const useCurrentPage = () => usePDFStore(state => state.currentPage);
export const useTotalPages = () => usePDFStore(state => state.totalPages);
export const useScale = () => usePDFStore(state => state.scale);
export const useRotation = () => usePDFStore(state => state.rotation);
export const useHighlights = () => usePDFStore(state => state.highlights);
export const useGhostHighlight = () => usePDFStore(state => state.ghostHighlight);
export const useAreaSelection = () => usePDFStore(state => state.areaSelection);
export const useViewportTransform = () => usePDFStore(state => state.viewportTransform);

// Action hooks
export const usePDFActions = () => usePDFStore(state => ({
  setDocument: state.setDocument,
  setLoading: state.setLoading,
  setError: state.setError,
  setCurrentPage: state.setCurrentPage,
  setScale: state.setScale,
  setRotation: state.setRotation,
  addHighlight: state.addHighlight,
  removeHighlight: state.removeHighlight,
  setGhostHighlight: state.setGhostHighlight,
  setAreaSelection: state.setAreaSelection,
  updateViewportTransform: state.updateViewportTransform,
}));
