/* App Styles */
.app {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header */
.app-header {
  background: white;
  border-bottom: 1px solid #e1e5e9;
  padding: 20px 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 700;
  color: #2c3e50;
}

.app-header p {
  margin: 0 0 20px 0;
  color: #6c757d;
  font-size: 14px;
}

/* Controls */
.controls {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 24px;
}

.file-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-input {
  display: none;
}

.file-label {
  padding: 10px 16px;
  background: #007bff;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.file-label:hover {
  background: #0056b3;
}

.sample-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-label {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.sample-button {
  padding: 8px 14px;
  background: #28a745;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.sample-button:hover {
  background: #1e7e34;
}

.highlight-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-left: auto;
}

.highlight-count {
  font-size: 14px;
  color: #495057;
  font-weight: 600;
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.clear-button {
  padding: 8px 14px;
  background: #dc3545;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 6px;
}

.clear-button:hover {
  background: #c82333;
}

/* Main Content */
.app-main {
  flex: 1;
  overflow: hidden;
}

.pdf-viewer {
  height: 100%;
}

/* No PDF State */
.no-pdf {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
}

.no-pdf-content {
  text-align: center;
  max-width: 600px;
}

.no-pdf-icon {
  font-size: 80px;
  margin-bottom: 24px;
  opacity: 0.6;
}

.no-pdf-content h2 {
  margin: 0 0 12px 0;
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
}

.no-pdf-content p {
  margin: 0 0 32px 0;
  font-size: 16px;
  color: #6c757d;
  line-height: 1.5;
}

/* Features List */
.features-list {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: left;
}

.features-list h3 {
  margin: 0 0 16px 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.features-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.features-list li {
  padding: 8px 0;
  font-size: 14px;
  color: #495057;
  line-height: 1.5;
  border-bottom: 1px solid #f1f3f4;
}

.features-list li:last-child {
  border-bottom: none;
}

.features-list strong {
  color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    padding: 16px 20px;
  }
  
  .app-header h1 {
    font-size: 20px;
  }
  
  .controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .sample-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .highlight-controls {
    margin-left: 0;
    justify-content: space-between;
  }
  
  .no-pdf {
    padding: 20px;
  }
  
  .no-pdf-icon {
    font-size: 60px;
  }
  
  .no-pdf-content h2 {
    font-size: 24px;
  }
  
  .features-list {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 12px 16px;
  }
  
  .controls {
    gap: 12px;
  }
  
  .file-label,
  .sample-button,
  .clear-button {
    padding: 10px 14px;
    font-size: 13px;
  }
  
  .no-pdf-content h2 {
    font-size: 20px;
  }
  
  .no-pdf-content p {
    font-size: 14px;
  }
}
