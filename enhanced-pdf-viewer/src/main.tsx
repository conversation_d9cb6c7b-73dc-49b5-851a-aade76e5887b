import React from 'react'
import <PERSON>actDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// No worker configuration needed - react-pdf-highlighter-extended handles it
console.log('Using react-pdf-highlighter-extended - worker handled automatically');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
