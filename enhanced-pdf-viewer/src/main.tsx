import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure PDF.js worker exactly like react-pdf-highlighter-extended
import { GlobalWorkerOptions } from 'pdfjs-dist';

// Use react-pdf-highlighter-extended's exact worker configuration
GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs';

console.log('PDF.js worker configured (react-pdf-highlighter-extended style):', GlobalWorkerOptions.workerSrc);

// Test worker setup
try {
  const testWorker = new Worker(GlobalWorkerOptions.workerSrc);
  testWorker.postMessage({ test: true });
  testWorker.terminate();
  console.log('✅ PDF.js worker test successful');
} catch (error) {
  console.log('❌ PDF.js worker test failed:', error);
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
