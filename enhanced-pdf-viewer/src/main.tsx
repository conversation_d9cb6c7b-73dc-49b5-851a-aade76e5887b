import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure PDF.js worker exactly like react-pdf-highlighter-extended
import { GlobalWorkerOptions } from 'pdfjs-dist';

// Use react-pdf-highlighter-extended's exact worker configuration
GlobalWorkerOptions.workerSrc = 'https://unpkg.com/pdfjs-dist@4.4.168/build/pdf.worker.min.mjs';

console.log('PDF.js worker configured (react-pdf-highlighter-extended style):', GlobalWorkerOptions.workerSrc);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
