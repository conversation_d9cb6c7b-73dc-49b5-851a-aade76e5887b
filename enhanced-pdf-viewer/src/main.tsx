import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure PDF.js worker exactly like <PERSON><PERSON> does
import { GlobalWorkerOptions } from 'pdfjs-dist';

// Use Lector's exact worker configuration
GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.mjs',
  import.meta.url
).toString();

console.log('PDF.js worker configured (Lector style):', GlobalWorkerOptions.workerSrc);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
