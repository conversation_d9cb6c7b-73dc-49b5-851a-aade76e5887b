import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure PDF.js worker early - multiple fallback strategies
import * as pdfjsLib from 'pdfjs-dist';

// Strategy 1: Try CDN worker
try {
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
  console.log('PDF.js worker configured (CDN):', pdfjsLib.GlobalWorkerOptions.workerSrc);
} catch (error) {
  console.warn('CDN worker failed, trying alternative...');

  // Strategy 2: Disable worker (slower but more reliable for development)
  pdfjsLib.GlobalWorkerOptions.workerSrc = '';
  console.log('PDF.js worker disabled, using main thread');
}

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
