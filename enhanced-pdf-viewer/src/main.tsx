import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure react-pdf worker using their recommended approach
import { pdfjs } from 'react-pdf';

// Use react-pdf's recommended worker configuration
pdfjs.GlobalWorkerOptions.workerSrc = `//unpkg.com/pdfjs-dist@${pdfjs.version}/build/pdf.worker.min.js`;

console.log('react-pdf worker configured:', pdfjs.GlobalWorkerOptions.workerSrc);
console.log('react-pdf version:', pdfjs.version);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
