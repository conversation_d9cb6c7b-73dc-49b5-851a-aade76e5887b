import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Disable PDF.js worker for development (more reliable with Vite)
import { pdfjs } from 'react-pdf';

// Disable worker entirely - runs on main thread (slower but reliable)
pdfjs.GlobalWorkerOptions.workerSrc = '';

console.log('PDF.js worker disabled - using main thread for reliability');
console.log('react-pdf version:', pdfjs.version);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
