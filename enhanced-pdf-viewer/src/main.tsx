import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure react-pdf worker (much more reliable)
import { pdfjs } from 'react-pdf';

// react-pdf handles worker configuration automatically
pdfjs.GlobalWorkerOptions.workerSrc = new URL(
  'pdfjs-dist/build/pdf.worker.min.js',
  import.meta.url,
).toString();

console.log('react-pdf worker configured:', pdfjs.GlobalWorkerOptions.workerSrc);

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
