import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.tsx'
import './index.css'

// Configure PDF.js to work without worker (more reliable for development)
import { GlobalWorkerOptions } from 'pdfjs-dist';

// Disable worker entirely - this is slower but 100% reliable
GlobalWorkerOptions.workerSrc = false as any;
console.log('PDF.js worker disabled - using main thread (slower but reliable)');

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
