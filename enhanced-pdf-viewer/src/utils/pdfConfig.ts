import * as pdfjsLib from 'pdfjs-dist';

// Configure PDF.js worker for different environments
export const configurePDFJS = () => {
  if (typeof window !== 'undefined') {
    // Try different worker configurations based on environment
    if (import.meta.env.DEV) {
      // Development mode - use CDN worker
      pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    } else {
      // Production mode - use local worker
      try {
        pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
          'pdfjs-dist/build/pdf.worker.min.js',
          import.meta.url
        ).toString();
      } catch (error) {
        // Fallback to CDN
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
      }
    }
    
    console.log('PDF.js worker configured:', pdfjsLib.GlobalWorkerOptions.workerSrc);
    console.log('PDF.js version:', pdfjsLib.version);
  }
};

// Initialize PDF.js configuration
configurePDFJS();
