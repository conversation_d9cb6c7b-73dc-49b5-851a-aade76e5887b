import * as pdfjsLib from 'pdfjs-dist';

// Simple and reliable PDF.js worker configuration
export const configurePDFJS = () => {
  if (typeof window !== 'undefined') {
    // Use a simple, reliable worker configuration
    pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

    console.log('PDF.js worker configured:', pdfjsLib.GlobalWorkerOptions.workerSrc);
    console.log('PDF.js version:', pdfjsLib.version);
  }
};

// Initialize PDF.js configuration
configurePDFJS();
