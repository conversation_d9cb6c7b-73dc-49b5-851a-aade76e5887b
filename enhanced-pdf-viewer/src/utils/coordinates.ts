import { BoundingRect, ViewportPosition, ScaledPosition } from '../types';

/**
 * Convert viewport coordinates to scaled coordinates
 */
export const viewportToScaled = (
  viewportPosition: ViewportPosition,
  scale: number,
  pageNumber: number
): ScaledPosition => {
  const scaledBoundingRect: BoundingRect = {
    ...viewportPosition.boundingRect,
    x1: viewportPosition.boundingRect.x1 / scale,
    y1: viewportPosition.boundingRect.y1 / scale,
    x2: viewportPosition.boundingRect.x2 / scale,
    y2: viewportPosition.boundingRect.y2 / scale,
    width: viewportPosition.boundingRect.width / scale,
    height: viewportPosition.boundingRect.height / scale,
    pageNumber,
  };

  const scaledRects = viewportPosition.rects.map(rect => ({
    ...rect,
    x1: rect.x1 / scale,
    y1: rect.y1 / scale,
    x2: rect.x2 / scale,
    y2: rect.y2 / scale,
    width: rect.width / scale,
    height: rect.height / scale,
    pageNumber,
  }));

  return {
    boundingRect: scaledBoundingRect,
    rects: scaledRects,
  };
};

/**
 * Convert scaled coordinates to viewport coordinates
 */
export const scaledToViewport = (
  scaledPosition: ScaledPosition,
  scale: number
): ViewportPosition => {
  const viewportBoundingRect: BoundingRect = {
    ...scaledPosition.boundingRect,
    x1: scaledPosition.boundingRect.x1 * scale,
    y1: scaledPosition.boundingRect.y1 * scale,
    x2: scaledPosition.boundingRect.x2 * scale,
    y2: scaledPosition.boundingRect.y2 * scale,
    width: scaledPosition.boundingRect.width * scale,
    height: scaledPosition.boundingRect.height * scale,
  };

  const viewportRects = scaledPosition.rects.map(rect => ({
    ...rect,
    x1: rect.x1 * scale,
    y1: rect.y1 * scale,
    x2: rect.x2 * scale,
    y2: rect.y2 * scale,
    width: rect.width * scale,
    height: rect.height * scale,
  }));

  return {
    boundingRect: viewportBoundingRect,
    rects: viewportRects,
  };
};

/**
 * Get bounding rectangle from multiple rectangles
 */
export const getBoundingRect = (rects: BoundingRect[]): BoundingRect => {
  if (rects.length === 0) {
    throw new Error('Cannot get bounding rect from empty array');
  }

  const pageNumber = rects[0].pageNumber;
  let x1 = Infinity;
  let y1 = Infinity;
  let x2 = -Infinity;
  let y2 = -Infinity;

  rects.forEach(rect => {
    x1 = Math.min(x1, rect.x1);
    y1 = Math.min(y1, rect.y1);
    x2 = Math.max(x2, rect.x2);
    y2 = Math.max(y2, rect.y2);
  });

  return {
    x1,
    y1,
    x2,
    y2,
    width: x2 - x1,
    height: y2 - y1,
    pageNumber,
  };
};

/**
 * Convert mouse event coordinates to page coordinates
 */
export const mouseEventToPageCoordinates = (
  event: MouseEvent,
  pageElement: HTMLElement,
  scale: number,
  rotation: number = 0
): { x: number; y: number } => {
  const rect = pageElement.getBoundingClientRect();
  let x = event.clientX - rect.left;
  let y = event.clientY - rect.top;

  // Apply rotation transformation
  if (rotation !== 0) {
    const centerX = rect.width / 2;
    const centerY = rect.height / 2;
    const radians = (rotation * Math.PI) / 180;
    
    const translatedX = x - centerX;
    const translatedY = y - centerY;
    
    x = translatedX * Math.cos(-radians) - translatedY * Math.sin(-radians) + centerX;
    y = translatedX * Math.sin(-radians) + translatedY * Math.cos(-radians) + centerY;
  }

  // Scale coordinates
  x = x / scale;
  y = y / scale;

  return { x, y };
};

/**
 * Check if a point is inside a rectangle
 */
export const isPointInRect = (
  point: { x: number; y: number },
  rect: BoundingRect
): boolean => {
  return (
    point.x >= rect.x1 &&
    point.x <= rect.x2 &&
    point.y >= rect.y1 &&
    point.y <= rect.y2
  );
};

/**
 * Calculate the distance between two points
 */
export const getDistance = (
  point1: { x: number; y: number },
  point2: { x: number; y: number }
): number => {
  const dx = point2.x - point1.x;
  const dy = point2.y - point1.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * Clamp a value between min and max
 */
export const clamp = (value: number, min: number, max: number): number => {
  return Math.min(Math.max(value, min), max);
};

/**
 * Create a rectangle from two points
 */
export const createRectFromPoints = (
  start: { x: number; y: number },
  end: { x: number; y: number },
  pageNumber: number
): BoundingRect => {
  const x1 = Math.min(start.x, end.x);
  const y1 = Math.min(start.y, end.y);
  const x2 = Math.max(start.x, end.x);
  const y2 = Math.max(start.y, end.y);

  return {
    x1,
    y1,
    x2,
    y2,
    width: x2 - x1,
    height: y2 - y1,
    pageNumber,
  };
};
