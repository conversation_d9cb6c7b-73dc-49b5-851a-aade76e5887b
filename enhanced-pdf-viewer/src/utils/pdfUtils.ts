import * as pdfjsLib from 'pdfjs-dist';
import { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Configure PDF.js worker
pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

/**
 * Load a PDF document from URL
 */
export const loadPDFDocument = async (url: string): Promise<PDFDocumentProxy> => {
  try {
    const loadingTask = pdfjsLib.getDocument(url);
    const document = await loadingTask.promise;
    return document;
  } catch (error) {
    console.error('Error loading PDF document:', error);
    throw new Error(`Failed to load PDF: ${error}`);
  }
};

/**
 * Get a specific page from the PDF document
 */
export const getPDFPage = async (
  document: PDFDocumentProxy,
  pageNumber: number
): Promise<PDFPageProxy> => {
  try {
    const page = await document.getPage(pageNumber);
    return page;
  } catch (error) {
    console.error(`Error loading page ${pageNumber}:`, error);
    throw new Error(`Failed to load page ${pageNumber}: ${error}`);
  }
};

/**
 * Render a PDF page to a canvas
 */
export const renderPageToCanvas = async (
  page: PDFPageProxy,
  canvas: HTMLCanvasElement,
  scale: number = 1.0,
  rotation: number = 0
): Promise<void> => {
  const context = canvas.getContext('2d');
  if (!context) {
    throw new Error('Canvas context not available');
  }

  const viewport = page.getViewport({ scale, rotation });
  
  // Set canvas dimensions
  canvas.width = viewport.width;
  canvas.height = viewport.height;
  canvas.style.width = `${viewport.width}px`;
  canvas.style.height = `${viewport.height}px`;

  // Clear canvas
  context.clearRect(0, 0, canvas.width, canvas.height);

  // Render page
  const renderContext = {
    canvasContext: context,
    viewport: viewport,
  };

  await page.render(renderContext).promise;
};

/**
 * Get text content from a PDF page
 */
export const getPageTextContent = async (page: PDFPageProxy) => {
  try {
    const textContent = await page.getTextContent();
    return textContent;
  } catch (error) {
    console.error('Error getting text content:', error);
    throw new Error(`Failed to get text content: ${error}`);
  }
};

/**
 * Search for text in a PDF page
 */
export const searchTextInPage = async (
  page: PDFPageProxy,
  searchTerm: string,
  scale: number = 1.0
) => {
  const textContent = await getPageTextContent(page);
  const viewport = page.getViewport({ scale });
  const results: Array<{
    text: string;
    x: number;
    y: number;
    width: number;
    height: number;
  }> = [];

  textContent.items.forEach((item: any) => {
    if (item.str.toLowerCase().includes(searchTerm.toLowerCase())) {
      const transform = item.transform;
      const x = transform[4];
      const y = viewport.height - transform[5];
      const width = item.width;
      const height = item.height;

      results.push({
        text: item.str,
        x,
        y: y - height,
        width,
        height,
      });
    }
  });

  return results;
};

/**
 * Get PDF document metadata
 */
export const getPDFMetadata = async (document: PDFDocumentProxy) => {
  try {
    const metadata = await document.getMetadata();
    return {
      title: metadata.info?.Title || 'Untitled',
      author: metadata.info?.Author || 'Unknown',
      subject: metadata.info?.Subject || '',
      creator: metadata.info?.Creator || '',
      producer: metadata.info?.Producer || '',
      creationDate: metadata.info?.CreationDate || null,
      modificationDate: metadata.info?.ModDate || null,
      numPages: document.numPages,
    };
  } catch (error) {
    console.error('Error getting PDF metadata:', error);
    return {
      title: 'Untitled',
      author: 'Unknown',
      subject: '',
      creator: '',
      producer: '',
      creationDate: null,
      modificationDate: null,
      numPages: document.numPages,
    };
  }
};

/**
 * Calculate optimal scale for fitting page to container
 */
export const calculateFitScale = (
  pageWidth: number,
  pageHeight: number,
  containerWidth: number,
  containerHeight: number,
  padding: number = 20
): number => {
  const availableWidth = containerWidth - padding * 2;
  const availableHeight = containerHeight - padding * 2;
  
  const scaleX = availableWidth / pageWidth;
  const scaleY = availableHeight / pageHeight;
  
  return Math.min(scaleX, scaleY, 2.0); // Cap at 2x scale
};

/**
 * Get page dimensions at a given scale
 */
export const getPageDimensions = (
  page: PDFPageProxy,
  scale: number,
  rotation: number = 0
) => {
  const viewport = page.getViewport({ scale, rotation });
  return {
    width: viewport.width,
    height: viewport.height,
  };
};

/**
 * Check if PDF.js is properly initialized
 */
export const isPDFJSReady = (): boolean => {
  return !!pdfjsLib.GlobalWorkerOptions.workerSrc;
};

/**
 * Create a blob URL from PDF data
 */
export const createPDFBlobUrl = (pdfData: ArrayBuffer | Uint8Array): string => {
  const blob = new Blob([pdfData], { type: 'application/pdf' });
  return URL.createObjectURL(blob);
};

/**
 * Cleanup blob URL
 */
export const cleanupBlobUrl = (url: string): void => {
  if (url.startsWith('blob:')) {
    URL.revokeObjectURL(url);
  }
};
