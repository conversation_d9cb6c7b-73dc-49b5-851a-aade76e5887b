import React, { useState, useRef, useCallback } from 'react';

interface SimplePDFViewerProps {
  url: string;
  highlights?: any[];
  onHighlightAdd?: (highlight: any) => void;
  onHighlightRemove?: (highlightId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

const SimplePDFViewer: React.FC<SimplePDFViewerProps> = ({
  url,
  highlights = [],
  onHighlightAdd,
  onHighlightRemove,
  className = '',
  style = {},
}) => {
  const [scale, setScale] = useState(100);
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState<{ x: number; y: number } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{ x: number; y: number } | null>(null);
  const overlayRef = useRef<HTMLDivElement>(null);

  const getNextId = () => String(Math.random()).slice(2);

  // Handle mouse events for rectangle selection
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!overlayRef.current) return;
    
    const rect = overlayRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setIsSelecting(true);
    setSelectionStart({ x, y });
    setSelectionEnd({ x, y });
    event.preventDefault();
  }, []);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart || !overlayRef.current) return;
    
    const rect = overlayRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setSelectionEnd({ x, y });
  }, [isSelecting, selectionStart]);

  const handleMouseUp = useCallback(() => {
    if (!isSelecting || !selectionStart || !selectionEnd) {
      setIsSelecting(false);
      return;
    }

    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Only create highlight if selection is large enough
    if (width > 10 && height > 10) {
      const highlight = {
        id: `highlight-${Date.now()}-${getNextId()}`,
        x: Math.min(selectionStart.x, selectionStart.x),
        y: Math.min(selectionStart.y, selectionEnd.y),
        width,
        height,
        timestamp: new Date().toLocaleString(),
      };

      if (onHighlightAdd) {
        onHighlightAdd(highlight);
      }
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
  }, [isSelecting, selectionStart, selectionEnd, onHighlightAdd]);

  // Render selection preview
  const renderSelectionPreview = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) return null;

    const x = Math.min(selectionStart.x, selectionEnd.x);
    const y = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    return (
      <div
        style={{
          position: 'absolute',
          left: x,
          top: y,
          width,
          height,
          border: '2px dashed rgba(255, 152, 0, 0.8)',
          backgroundColor: 'rgba(255, 235, 59, 0.2)',
          pointerEvents: 'none',
          zIndex: 1000,
        }}
      />
    );
  };

  // Render highlights
  const renderHighlights = () => {
    return highlights.map(highlight => (
      <div
        key={highlight.id}
        style={{
          position: 'absolute',
          left: highlight.x,
          top: highlight.y,
          width: highlight.width,
          height: highlight.height,
          backgroundColor: 'rgba(255, 235, 59, 0.4)',
          border: '2px solid rgba(255, 152, 0, 0.8)',
          borderRadius: '2px',
          cursor: 'pointer',
          zIndex: 999,
        }}
        onClick={() => onHighlightRemove && onHighlightRemove(highlight.id)}
        title={`Highlight - Click to remove`}
      />
    ));
  };

  return (
    <div className={`simple-pdf-viewer ${className}`} style={style}>
      {/* Toolbar */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        padding: '12px 24px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e1e5e9',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ margin: 0, fontSize: '18px', fontWeight: 600, color: '#2c3e50' }}>
          📄 Simple PDF Viewer (No Worker Issues!)
        </h2>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <label style={{ fontSize: '14px', fontWeight: 500, color: '#495057' }}>
            Zoom:
          </label>
          <select
            value={scale}
            onChange={(e) => setScale(parseInt(e.target.value))}
            style={{
              padding: '4px 8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            <option value="50">50%</option>
            <option value="75">75%</option>
            <option value="100">100%</option>
            <option value="125">125%</option>
            <option value="150">150%</option>
            <option value="200">200%</option>
          </select>
        </div>

        <div style={{ marginLeft: 'auto', fontSize: '14px', color: '#6c757d' }}>
          {highlights.length} highlight{highlights.length !== 1 ? 's' : ''}
        </div>

        {highlights.length > 0 && (
          <button
            onClick={() => highlights.forEach(h => onHighlightRemove && onHighlightRemove(h.id))}
            style={{
              padding: '6px 12px',
              background: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🗑️ Clear All
          </button>
        )}
      </div>

      {/* PDF Container */}
      <div style={{ 
        height: 'calc(100vh - 80px)', 
        overflow: 'auto',
        position: 'relative',
        backgroundColor: '#f1f3f4',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'flex-start',
        padding: '20px'
      }}>
        <div style={{ 
          position: 'relative',
          backgroundColor: 'white',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          borderRadius: '8px',
          overflow: 'hidden'
        }}>
          {/* PDF Iframe */}
          <iframe
            src={`${url}#zoom=${scale}`}
            style={{
              width: `${8.5 * scale}px`, // Standard letter width scaled
              height: `${11 * scale}px`, // Standard letter height scaled
              border: 'none',
              display: 'block'
            }}
            title="PDF Viewer"
          />
          
          {/* Overlay for selection */}
          <div
            ref={overlayRef}
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              cursor: isSelecting ? 'crosshair' : 'crosshair',
              zIndex: 10,
            }}
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Render highlights */}
            {renderHighlights()}
            
            {/* Render selection preview */}
            {renderSelectionPreview()}
          </div>
        </div>
      </div>

      {/* Instructions */}
      <div style={{
        position: 'fixed',
        bottom: '20px',
        right: '20px',
        background: 'rgba(0, 0, 0, 0.8)',
        color: 'white',
        padding: '12px',
        borderRadius: '8px',
        fontSize: '14px',
        maxWidth: '300px'
      }}>
        <strong>✨ How to use:</strong><br />
        • Click and drag to create rectangle highlights<br />
        • Click highlights to remove them<br />
        • Use zoom controls to resize<br />
        • No PDF.js worker issues! 🎉
      </div>
    </div>
  );
};

export default SimplePDFViewer;
