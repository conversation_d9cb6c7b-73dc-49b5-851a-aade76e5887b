/* Enhanced PDF Viewer Styles */
.enhanced-pdf-viewer {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
}

/* Loading State */
.enhanced-pdf-viewer.loading {
  justify-content: center;
  align-items: center;
}

.loading-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e1e5e9;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error State */
.enhanced-pdf-viewer.error {
  justify-content: center;
  align-items: center;
}

.error-message {
  text-align: center;
  padding: 32px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.error-message h3 {
  color: #dc3545;
  margin-bottom: 16px;
}

.error-message button {
  margin-top: 16px;
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

/* Toolbar */
.pdf-toolbar {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 12px 24px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.toolbar-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-button {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  color: #495057;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.toolbar-button:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.toolbar-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-label {
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.page-info,
.zoom-display,
.rotation-display {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  background: #f8f9fa;
  padding: 6px 12px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  min-width: 60px;
  text-align: center;
}

.toolbar-info {
  margin-left: auto;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* PDF Container */
.pdf-container {
  flex: 1;
  overflow: auto;
  background: #f1f3f4;
  position: relative;
  user-select: none;
}

.pdf-pages {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
  padding: 20px;
  transition: transform 0.1s ease;
}

/* Page Container */
.page-container {
  position: relative;
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
  overflow: hidden;
  transition: box-shadow 0.2s ease;
}

.page-container:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* PDF Page */
.pdf-page {
  position: relative;
  display: block;
}

.pdf-page.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: #f8f9fa;
}

.page-loading-spinner {
  color: #6c757d;
  font-size: 14px;
}

.pdf-page-canvas {
  display: block;
  max-width: 100%;
  height: auto;
}

/* Text Layer */
.pdf-text-layer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  opacity: 0.2;
  line-height: 1.0;
  pointer-events: auto;
}

.pdf-text-layer span {
  position: absolute;
  color: transparent;
  white-space: pre;
  cursor: text;
  transform-origin: 0% 0%;
  user-select: text;
}

.pdf-text-layer span::selection {
  background: rgba(33, 150, 243, 0.3);
}

/* Highlight Layer */
.highlight-layer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 5;
}

.highlight {
  position: absolute;
  pointer-events: auto;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 2px;
}

.highlight-area {
  background: rgba(255, 235, 59, 0.4);
  border: 2px solid rgba(255, 152, 0, 0.8);
}

.highlight-text {
  background: rgba(33, 150, 243, 0.3);
  border: 1px solid rgba(33, 150, 243, 0.6);
}

.highlight-remove-button {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 16px;
  height: 16px;
  background: #dc3545;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  opacity: 0;
  transition: opacity 0.2s ease;
  cursor: pointer;
}

.highlight:hover .highlight-remove-button {
  opacity: 1;
}

/* Area Selector */
.area-selector-layer {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  cursor: crosshair;
  pointer-events: auto;
  z-index: 10;
}

.selection-preview {
  position: absolute;
  border: 2px dashed rgba(255, 152, 0, 0.8);
  background: rgba(255, 235, 59, 0.2);
  border-radius: 2px;
  pointer-events: none;
}

@keyframes selectionPulse {
  from {
    opacity: 0.3;
    transform: scale(1);
  }
  to {
    opacity: 0.6;
    transform: scale(1.01);
  }
}

/* Page Rendering Overlay */
.page-rendering-overlay {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
}

.rendering-spinner {
  color: #6c757d;
  font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .pdf-toolbar {
    flex-wrap: wrap;
    gap: 12px;
    padding: 8px 16px;
  }
  
  .toolbar-section {
    gap: 6px;
  }
  
  .toolbar-button {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .pdf-pages {
    padding: 10px;
    gap: 15px;
  }
}

/* High DPI Support */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .pdf-page-canvas {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Tooltip */
.highlight-tooltip {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  z-index: 1000;
}

.highlight:hover .highlight-tooltip {
  opacity: 1;
}
