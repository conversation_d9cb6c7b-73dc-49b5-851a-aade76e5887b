import React, { useState } from 'react';
import {
  PdfHighlight<PERSON>,
  PdfLoader,
  Highlight,
  GhostHighlight,
  ViewportHighlight,
} from 'react-pdf-highlighter-extended';

interface WorkingPDFViewerProps {
  url: string;
  highlights?: any[];
  onHighlightAdd?: (highlight: any) => void;
  onHighlightRemove?: (highlightId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

const WorkingPDFViewer: React.FC<WorkingPDFViewerProps> = ({
  url,
  highlights = [],
  onHighlightAdd,
  onHighlightRemove,
  className = '',
  style = {},
}) => {
  const [pdfScaleValue, setPdfScaleValue] = useState<number | undefined>(undefined);

  const getNextId = () => String(Math.random()).slice(2);

  const addHighlight = (highlight: GhostHighlight, comment: string = '') => {
    console.log('Adding highlight:', highlight);
    const newHighlight = { ...highlight, comment, id: getNextId() };
    if (onHighlightAdd) {
      onHighlightAdd(newHighlight);
    }
  };

  const deleteHighlight = (highlight: ViewportHighlight | Highlight) => {
    console.log('Deleting highlight:', highlight);
    if (onHighlightRemove) {
      onHighlightRemove(highlight.id);
    }
  };

  const ExpandableTip = ({ addHighlight }: { addHighlight: (highlight: GhostHighlight, comment: string) => void }) => (
    <div style={{
      background: 'white',
      padding: '8px',
      borderRadius: '4px',
      boxShadow: '0 2px 8px rgba(0,0,0,0.2)',
      border: '1px solid #ccc'
    }}>
      <button
        onClick={() => {
          // This will be called with the current selection
          const selection = (window as any).currentSelection;
          if (selection) {
            addHighlight(selection.makeGhostHighlight(), '');
          }
        }}
        style={{
          background: '#007bff',
          color: 'white',
          border: 'none',
          padding: '6px 12px',
          borderRadius: '4px',
          cursor: 'pointer'
        }}
      >
        Add Highlight
      </button>
    </div>
  );

  const HighlightContainer = ({ 
    editHighlight, 
    onContextMenu 
  }: { 
    editHighlight: any; 
    onContextMenu: any; 
  }) => (
    <div>
      {/* This component will render highlights */}
    </div>
  );

  return (
    <div className={`working-pdf-viewer ${className}`} style={style}>
      {/* Toolbar */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        padding: '12px 24px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e1e5e9',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{ margin: 0, fontSize: '18px', fontWeight: 600, color: '#2c3e50' }}>
          🚀 Working PDF Viewer
        </h2>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <label style={{ fontSize: '14px', fontWeight: 500, color: '#495057' }}>
            Zoom:
          </label>
          <select
            value={pdfScaleValue || 'auto'}
            onChange={(e) => {
              const value = e.target.value;
              setPdfScaleValue(value === 'auto' ? undefined : parseFloat(value));
            }}
            style={{
              padding: '4px 8px',
              border: '1px solid #dee2e6',
              borderRadius: '4px',
              fontSize: '14px'
            }}
          >
            <option value="auto">Auto</option>
            <option value="0.5">50%</option>
            <option value="0.75">75%</option>
            <option value="1">100%</option>
            <option value="1.25">125%</option>
            <option value="1.5">150%</option>
            <option value="2">200%</option>
          </select>
        </div>

        <div style={{ marginLeft: 'auto', fontSize: '14px', color: '#6c757d' }}>
          {highlights.length} highlight{highlights.length !== 1 ? 's' : ''}
        </div>

        {highlights.length > 0 && (
          <button
            onClick={() => highlights.forEach(h => onHighlightRemove && onHighlightRemove(h.id))}
            style={{
              padding: '6px 12px',
              background: '#dc3545',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '14px'
            }}
          >
            🗑️ Clear All
          </button>
        )}
      </div>

      {/* PDF Viewer */}
      <div style={{ 
        height: 'calc(100vh - 80px)', 
        overflow: 'hidden',
        position: 'relative'
      }}>
        <PdfLoader document={url}>
          {(pdfDocument) => (
            <PdfHighlighter
              enableAreaSelection={(event) => event.altKey || true} // Always enable area selection
              pdfDocument={pdfDocument}
              pdfScaleValue={pdfScaleValue}
              onSelection={(selection) => {
                // Store selection globally for the tip
                (window as any).currentSelection = selection;
                return true;
              }}
              selectionTip={<ExpandableTip addHighlight={addHighlight} />}
              highlights={highlights}
              style={{
                height: '100%',
              }}
            >
              <HighlightContainer
                editHighlight={() => {}}
                onContextMenu={(event: any, highlight: any) => {
                  event.preventDefault();
                  deleteHighlight(highlight);
                }}
              />
            </PdfHighlighter>
          )}
        </PdfLoader>
      </div>
    </div>
  );
};

export default WorkingPDFViewer;
