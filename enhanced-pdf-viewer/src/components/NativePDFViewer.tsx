import React, { useEffect, useRef, useState } from 'react';
import { getDocument, GlobalWorkerOptions, type PDFDocumentProxy, type PDFPageProxy } from 'pdfjs-dist';

interface NativePDFViewerProps {
  url: string;
  highlights?: any[];
  onHighlightAdd?: (highlight: any) => void;
  onHighlightRemove?: (highlightId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

const NativePDFViewer: React.FC<NativePDFViewerProps> = ({
  url,
  highlights = [],
  onHighlightAdd,
  onHighlightRemove,
  className = '',
  style = {},
}) => {
  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [scale, setScale] = useState(1.0);
  const [rotation, setRotation] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Selection state
  const [isSelecting, setIsSelecting] = useState(false);
  const [selectionStart, setSelectionStart] = useState<{ x: number; y: number } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{ x: number; y: number } | null>(null);

  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load PDF using react-pdf-highlighter-extended's exact approach
  const loadPDF = async (pdfUrl: string) => {
    if (!pdfUrl) return;

    setIsLoading(true);
    setError(null);

    try {
      console.log('Loading PDF with react-pdf-highlighter-extended approach:', pdfUrl);
      
      // Use the exact same approach as react-pdf-highlighter-extended
      const loadingTask = getDocument(pdfUrl);
      const document = await loadingTask.promise;
      
      console.log('PDF loaded successfully:', document.numPages, 'pages');
      setPdfDoc(document);
      setTotalPages(document.numPages);
      setCurrentPage(1);
    } catch (err) {
      console.error('Error loading PDF:', err);
      setError(err instanceof Error ? err.message : 'Failed to load PDF');
    } finally {
      setIsLoading(false);
    }
  };

  // Load PDF when URL changes
  useEffect(() => {
    console.log('NativePDFViewer rendering with URL:', url, 'zoom:', scale);
    if (url) {
      loadPDF(url);
    }
  }, [url]);

  // Render current page
  const renderPage = async () => {
    if (!pdfDoc || !canvasRef.current) return;

    try {
      const page = await pdfDoc.getPage(currentPage);
      const viewport = page.getViewport({ scale, rotation });
      
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      if (!context) return;

      canvas.height = viewport.height;
      canvas.width = viewport.width;
      canvas.style.width = `${viewport.width}px`;
      canvas.style.height = `${viewport.height}px`;

      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };

      await page.render(renderContext).promise;
      console.log(`Page ${currentPage} rendered successfully`);
    } catch (err) {
      console.error('Error rendering page:', err);
    }
  };

  // Re-render when page, scale, or rotation changes
  useEffect(() => {
    if (pdfDoc) {
      renderPage();
    }
  }, [pdfDoc, currentPage, scale, rotation]);

  // Navigation functions
  const goToPrevPage = () => setCurrentPage(prev => Math.max(1, prev - 1));
  const goToNextPage = () => setCurrentPage(prev => Math.min(totalPages, prev + 1));

  // Zoom functions
  const zoomIn = () => setScale(prev => Math.min(3.0, prev * 1.2));
  const zoomOut = () => setScale(prev => Math.max(0.5, prev / 1.2));
  const resetZoom = () => setScale(1.0);

  // Rotation functions
  const rotateLeft = () => setRotation(prev => (prev - 90 + 360) % 360);
  const rotateRight = () => setRotation(prev => (prev + 90) % 360);

  // Mouse event handlers for rectangle selection
  const handleMouseDown = (event: React.MouseEvent) => {
    if (!containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setIsSelecting(true);
    setSelectionStart({ x, y });
    setSelectionEnd({ x, y });
    event.preventDefault();
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart || !containerRef.current) return;
    
    const rect = containerRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setSelectionEnd({ x, y });
  };

  const handleMouseUp = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) {
      setIsSelecting(false);
      return;
    }

    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Only create highlight if selection is large enough
    if (width > 10 && height > 10) {
      const highlight = {
        id: `highlight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        pageNumber: currentPage,
        x: Math.min(selectionStart.x, selectionEnd.x),
        y: Math.min(selectionStart.y, selectionEnd.y),
        width,
        height,
        timestamp: new Date().toLocaleString(),
      };

      if (onHighlightAdd) {
        onHighlightAdd(highlight);
      }
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  // Render selection preview
  const renderSelectionPreview = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) return null;

    const x = Math.min(selectionStart.x, selectionEnd.x);
    const y = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    return (
      <div
        style={{
          position: 'absolute',
          left: x,
          top: y,
          width,
          height,
          border: '2px dashed rgba(255, 152, 0, 0.8)',
          backgroundColor: 'rgba(255, 235, 59, 0.2)',
          pointerEvents: 'none',
          zIndex: 1000,
        }}
      />
    );
  };

  // Render highlights for current page
  const renderHighlights = () => {
    const pageHighlights = highlights.filter(h => h.pageNumber === currentPage);
    
    return pageHighlights.map(highlight => (
      <div
        key={highlight.id}
        style={{
          position: 'absolute',
          left: highlight.x,
          top: highlight.y,
          width: highlight.width,
          height: highlight.height,
          backgroundColor: 'rgba(255, 235, 59, 0.4)',
          border: '2px solid rgba(255, 152, 0, 0.8)',
          borderRadius: '2px',
          cursor: 'pointer',
          zIndex: 999,
        }}
        onClick={() => onHighlightRemove && onHighlightRemove(highlight.id)}
        title={`Highlight on page ${highlight.pageNumber} - Click to remove`}
      />
    ));
  };

  if (error) {
    return (
      <div className={`native-pdf-viewer-error ${className}`} style={style}>
        <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
          <h3>❌ Error loading PDF</h3>
          <p>{error}</p>
          <p><strong>URL:</strong> {url}</p>
          <button onClick={() => loadPDF(url)}>Retry</button>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className={`native-pdf-viewer-loading ${className}`} style={style}>
        <div style={{ padding: '40px', textAlign: 'center' }}>
          <div>Loading PDF...</div>
          <p>Using react-pdf-highlighter-extended approach</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`native-pdf-viewer ${className}`} style={style}>
      {/* Toolbar */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '12px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e1e5e9',
        flexWrap: 'wrap'
      }}>
        {/* Page Navigation */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={goToPrevPage} disabled={currentPage <= 1}>
            ← Prev
          </button>
          <span>Page {currentPage} of {totalPages}</span>
          <button onClick={goToNextPage} disabled={currentPage >= totalPages}>
            Next →
          </button>
        </div>

        {/* Zoom Controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={zoomOut} disabled={scale <= 0.5}>🔍−</button>
          <span>{Math.round(scale * 100)}%</span>
          <button onClick={zoomIn} disabled={scale >= 3.0}>🔍+</button>
          <button onClick={resetZoom}>📐 Fit</button>
        </div>

        {/* Rotation Controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={rotateLeft}>↺</button>
          <span>{rotation}°</span>
          <button onClick={rotateRight}>↻</button>
        </div>

        {/* Highlight Info */}
        <div style={{ marginLeft: 'auto' }}>
          <span>{highlights.length} highlights</span>
        </div>
      </div>

      {/* PDF Container */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        backgroundColor: '#f1f3f4',
        padding: '20px',
        display: 'flex',
        justifyContent: 'center'
      }}>
        <div
          ref={containerRef}
          style={{
            position: 'relative',
            cursor: isSelecting ? 'crosshair' : 'default',
            backgroundColor: 'white',
            boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
            borderRadius: '4px',
            overflow: 'hidden',
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <canvas ref={canvasRef} style={{ display: 'block' }} />
          
          {/* Render highlights */}
          {renderHighlights()}
          
          {/* Render selection preview */}
          {renderSelectionPreview()}
        </div>
      </div>
    </div>
  );
};

export default NativePDFViewer;
