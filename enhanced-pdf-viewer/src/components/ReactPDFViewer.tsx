import React, { useState, useCallback } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import 'react-pdf/dist/Page/AnnotationLayer.css';
import 'react-pdf/dist/Page/TextLayer.css';

interface ReactPDFViewerProps {
  url: string;
  onHighlightAdd?: (highlight: any) => void;
  onHighlightRemove?: (highlightId: string) => void;
  highlights?: any[];
  className?: string;
  style?: React.CSSProperties;
}

const ReactPDFViewer: React.FC<ReactPDFViewerProps> = ({
  url,
  onHighlightAdd,
  onHighlightRemove,
  highlights = [],
  className = '',
  style = {},
}) => {
  const [numPages, setNumPages] = useState<number>(0);
  const [pageNumber, setPageNumber] = useState<number>(1);
  const [scale, setScale] = useState<number>(1.0);
  const [rotation, setRotation] = useState<number>(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Selection state for rectangle highlighting
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const [selectionStart, setSelectionStart] = useState<{ x: number; y: number } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{ x: number; y: number } | null>(null);

  const onDocumentLoadSuccess = useCallback(({ numPages }: { numPages: number }) => {
    setNumPages(numPages);
    setPageNumber(1);
    setError(null);
    setIsLoading(false);
    console.log('PDF loaded successfully:', numPages, 'pages');
  }, []);

  const onDocumentLoadError = useCallback((error: Error) => {
    console.error('Error loading PDF:', error);
    setError(error.message);
    setIsLoading(false);
  }, []);

  const onDocumentLoadStart = useCallback(() => {
    setIsLoading(true);
    setError(null);
    console.log('Loading PDF:', url);
  }, [url]);

  // Navigation functions
  const goToPrevPage = () => setPageNumber(prev => Math.max(1, prev - 1));
  const goToNextPage = () => setPageNumber(prev => Math.min(numPages, prev + 1));

  // Zoom functions
  const zoomIn = () => setScale(prev => Math.min(3.0, prev * 1.2));
  const zoomOut = () => setScale(prev => Math.max(0.5, prev / 1.2));
  const resetZoom = () => setScale(1.0);

  // Rotation functions
  const rotateLeft = () => setRotation(prev => (prev - 90 + 360) % 360);
  const rotateRight = () => setRotation(prev => (prev + 90) % 360);

  // Mouse event handlers for rectangle selection
  const handleMouseDown = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setIsSelecting(true);
    setSelectionStart({ x, y });
    setSelectionEnd({ x, y });
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setSelectionEnd({ x, y });
  };

  const handleMouseUp = (event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart || !selectionEnd) {
      setIsSelecting(false);
      return;
    }

    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Only create highlight if selection is large enough
    if (width > 10 && height > 10) {
      const highlight = {
        id: `highlight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        pageNumber,
        x: Math.min(selectionStart.x, selectionEnd.x),
        y: Math.min(selectionStart.y, selectionEnd.y),
        width,
        height,
        timestamp: new Date().toLocaleString(),
      };

      if (onHighlightAdd) {
        onHighlightAdd(highlight);
      }
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  // Render selection preview
  const renderSelectionPreview = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) return null;

    const x = Math.min(selectionStart.x, selectionEnd.x);
    const y = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    return (
      <div
        style={{
          position: 'absolute',
          left: x,
          top: y,
          width,
          height,
          border: '2px dashed rgba(255, 152, 0, 0.8)',
          backgroundColor: 'rgba(255, 235, 59, 0.2)',
          pointerEvents: 'none',
          zIndex: 10,
        }}
      />
    );
  };

  // Render highlights for current page
  const renderHighlights = () => {
    const pageHighlights = highlights.filter(h => h.pageNumber === pageNumber);
    
    return pageHighlights.map(highlight => (
      <div
        key={highlight.id}
        style={{
          position: 'absolute',
          left: highlight.x,
          top: highlight.y,
          width: highlight.width,
          height: highlight.height,
          backgroundColor: 'rgba(255, 235, 59, 0.4)',
          border: '2px solid rgba(255, 152, 0, 0.8)',
          borderRadius: '2px',
          cursor: 'pointer',
          zIndex: 5,
        }}
        onClick={() => onHighlightRemove && onHighlightRemove(highlight.id)}
        title={`Highlight - Click to remove`}
      />
    ));
  };

  if (error) {
    return (
      <div className={`pdf-viewer-error ${className}`} style={style}>
        <div style={{ padding: '20px', textAlign: 'center', color: 'red' }}>
          <h3>❌ Error loading PDF</h3>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className={`react-pdf-viewer ${className}`} style={style}>
      {/* Toolbar */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        gap: '12px',
        padding: '12px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e1e5e9',
        flexWrap: 'wrap'
      }}>
        {/* Page Navigation */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={goToPrevPage} disabled={pageNumber <= 1}>
            ← Prev
          </button>
          <span>
            Page {pageNumber} of {numPages}
          </span>
          <button onClick={goToNextPage} disabled={pageNumber >= numPages}>
            Next →
          </button>
        </div>

        {/* Zoom Controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={zoomOut} disabled={scale <= 0.5}>
            🔍−
          </button>
          <span>{Math.round(scale * 100)}%</span>
          <button onClick={zoomIn} disabled={scale >= 3.0}>
            🔍+
          </button>
          <button onClick={resetZoom}>
            📐 Fit
          </button>
        </div>

        {/* Rotation Controls */}
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <button onClick={rotateLeft}>↺</button>
          <span>{rotation}°</span>
          <button onClick={rotateRight}>↻</button>
        </div>

        {/* Highlight Info */}
        <div style={{ marginLeft: 'auto' }}>
          <span>{highlights.length} highlights</span>
        </div>
      </div>

      {/* PDF Container */}
      <div style={{
        flex: 1,
        overflow: 'auto',
        backgroundColor: '#f1f3f4',
        padding: '20px',
        display: 'flex',
        justifyContent: 'center'
      }}>
        <div
          style={{
            position: 'relative',
            cursor: isSelecting ? 'crosshair' : 'default'
          }}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={handleMouseUp}
        >
          <Document
            file={url}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={onDocumentLoadError}
            onLoadStart={onDocumentLoadStart}
            loading={
              <div style={{ padding: '40px', textAlign: 'center' }}>
                <div>Loading PDF...</div>
              </div>
            }
          >
            <Page
              pageNumber={pageNumber}
              scale={scale}
              rotate={rotation}
              renderTextLayer={true}
              renderAnnotationLayer={true}
            />
          </Document>
          
          {/* Render highlights */}
          {renderHighlights()}
          
          {/* Render selection preview */}
          {renderSelectionPreview()}
        </div>
      </div>
    </div>
  );
};

export default ReactPDFViewer;
