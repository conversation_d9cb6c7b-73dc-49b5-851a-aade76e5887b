import React from 'react';
import { Highlight } from '../types';
import { scaledToViewport } from '../utils/coordinates';

interface HighlightLayerProps {
  pageNumber: number;
  highlights: Highlight[];
  scale: number;
  rotation: number;
  onHighlightClick?: (highlightId: string) => void;
}

const HighlightLayer: React.FC<HighlightLayerProps> = ({
  pageNumber,
  highlights,
  scale,
  rotation,
  onHighlightClick,
}) => {
  const handleHighlightClick = (highlight: Highlight, event: React.MouseEvent) => {
    event.stopPropagation();
    if (onHighlightClick) {
      onHighlightClick(highlight.id);
    }
  };

  return (
    <div className="highlight-layer">
      {highlights.map((highlight) => {
        // Convert scaled position to viewport position
        const viewportPosition = scaledToViewport(highlight.position, scale);
        const rect = viewportPosition.boundingRect;

        // Apply rotation transformation if needed
        let transform = '';
        if (rotation !== 0) {
          transform = `rotate(${rotation}deg)`;
        }

        return (
          <div
            key={highlight.id}
            className={`highlight highlight-${highlight.type}`}
            style={{
              position: 'absolute',
              left: `${rect.x1}px`,
              top: `${rect.y1}px`,
              width: `${rect.width}px`,
              height: `${rect.height}px`,
              backgroundColor: highlight.color || 'rgba(255, 255, 0, 0.3)',
              border: `2px solid ${highlight.color ? highlight.color.replace('0.3', '0.8') : 'rgba(255, 255, 0, 0.8)'}`,
              borderRadius: '2px',
              cursor: 'pointer',
              transform,
              transformOrigin: 'center',
              transition: 'all 0.2s ease',
              pointerEvents: 'auto',
            }}
            onClick={(e) => handleHighlightClick(highlight, e)}
            title={`${highlight.type} highlight - Click to remove`}
            onMouseEnter={(e) => {
              e.currentTarget.style.transform = `${transform} scale(1.05)`;
              e.currentTarget.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.3)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.transform = transform;
              e.currentTarget.style.boxShadow = 'none';
            }}
          >
            {/* Optional highlight content */}
            {highlight.content.text && (
              <div className="highlight-tooltip">
                {highlight.content.text}
              </div>
            )}
            
            {/* Remove button */}
            <div className="highlight-remove-button">
              ×
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default HighlightLayer;
