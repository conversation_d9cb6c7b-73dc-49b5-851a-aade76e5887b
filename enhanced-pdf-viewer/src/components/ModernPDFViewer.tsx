import React, { useState } from 'react';
import { Worker, Viewer } from '@react-pdf-viewer/core';
import { defaultLayoutPlugin } from '@react-pdf-viewer/default-layout';

// Import styles
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/default-layout/lib/styles/index.css';

interface ModernPDFViewerProps {
  url: string;
  highlights?: any[];
  onHighlightAdd?: (highlight: any) => void;
  onHighlightRemove?: (highlightId: string) => void;
  className?: string;
  style?: React.CSSProperties;
}

const ModernPDFViewer: React.FC<ModernPDFViewerProps> = ({
  url,
  highlights = [],
  onHighlightAdd,
  onHighlightRemove,
  className = '',
  style = {},
}) => {
  // Create default layout plugin
  const defaultLayoutPluginInstance = defaultLayoutPlugin({
    sidebarTabs: (defaultTabs) => [
      // Keep only the thumbnail tab
      defaultTabs[0],
    ],
  });

  // Selection state for rectangle highlighting
  const [isSelecting, setIsSelecting] = useState<boolean>(false);
  const [selectionStart, setSelectionStart] = useState<{ x: number; y: number } | null>(null);
  const [selectionEnd, setSelectionEnd] = useState<{ x: number; y: number } | null>(null);

  // Mouse event handlers for rectangle selection
  const handleMouseDown = (event: React.MouseEvent) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setIsSelecting(true);
    setSelectionStart({ x, y });
    setSelectionEnd({ x, y });
    
    event.preventDefault();
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart) return;
    
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    setSelectionEnd({ x, y });
  };

  const handleMouseUp = (event: React.MouseEvent) => {
    if (!isSelecting || !selectionStart || !selectionEnd) {
      setIsSelecting(false);
      return;
    }

    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    // Only create highlight if selection is large enough
    if (width > 10 && height > 10) {
      const highlight = {
        id: `highlight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        x: Math.min(selectionStart.x, selectionEnd.x),
        y: Math.min(selectionStart.y, selectionEnd.y),
        width,
        height,
        timestamp: new Date().toLocaleString(),
      };

      if (onHighlightAdd) {
        onHighlightAdd(highlight);
      }
    }

    setIsSelecting(false);
    setSelectionStart(null);
    setSelectionEnd(null);
  };

  // Render selection preview
  const renderSelectionPreview = () => {
    if (!isSelecting || !selectionStart || !selectionEnd) return null;

    const x = Math.min(selectionStart.x, selectionEnd.x);
    const y = Math.min(selectionStart.y, selectionEnd.y);
    const width = Math.abs(selectionEnd.x - selectionStart.x);
    const height = Math.abs(selectionEnd.y - selectionStart.y);

    return (
      <div
        style={{
          position: 'absolute',
          left: x,
          top: y,
          width,
          height,
          border: '2px dashed rgba(255, 152, 0, 0.8)',
          backgroundColor: 'rgba(255, 235, 59, 0.2)',
          pointerEvents: 'none',
          zIndex: 1000,
        }}
      />
    );
  };

  // Render highlights
  const renderHighlights = () => {
    return highlights.map(highlight => (
      <div
        key={highlight.id}
        style={{
          position: 'absolute',
          left: highlight.x,
          top: highlight.y,
          width: highlight.width,
          height: highlight.height,
          backgroundColor: 'rgba(255, 235, 59, 0.4)',
          border: '2px solid rgba(255, 152, 0, 0.8)',
          borderRadius: '2px',
          cursor: 'pointer',
          zIndex: 999,
        }}
        onClick={() => onHighlightRemove && onHighlightRemove(highlight.id)}
        title={`Highlight - Click to remove`}
      />
    ));
  };

  return (
    <div className={`modern-pdf-viewer ${className}`} style={style}>
      {/* Custom toolbar */}
      <div style={{
        padding: '12px',
        backgroundColor: 'white',
        borderBottom: '1px solid #e1e5e9',
        display: 'flex',
        alignItems: 'center',
        gap: '16px',
        flexWrap: 'wrap'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontWeight: 600, color: '#2c3e50' }}>
            🚀 Enhanced PDF Viewer
          </span>
        </div>
        
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '14px', color: '#6c757d' }}>
            Rectangle Selection: Click and drag to highlight
          </span>
        </div>

        {highlights.length > 0 && (
          <div style={{ marginLeft: 'auto', display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span style={{ 
              fontSize: '14px', 
              fontWeight: 600, 
              color: '#495057',
              background: '#f8f9fa',
              padding: '4px 8px',
              borderRadius: '4px',
              border: '1px solid #dee2e6'
            }}>
              {highlights.length} highlight{highlights.length !== 1 ? 's' : ''}
            </span>
            <button
              onClick={() => highlights.forEach(h => onHighlightRemove && onHighlightRemove(h.id))}
              style={{
                padding: '6px 12px',
                background: '#dc3545',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                fontSize: '14px'
              }}
            >
              🗑️ Clear All
            </button>
          </div>
        )}
      </div>

      {/* PDF Viewer Container */}
      <div 
        style={{ 
          flex: 1, 
          position: 'relative',
          cursor: isSelecting ? 'crosshair' : 'default'
        }}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
      >
        <Worker workerUrl="https://unpkg.com/pdfjs-dist@3.4.120/build/pdf.worker.min.js">
          <Viewer
            fileUrl={url}
            plugins={[defaultLayoutPluginInstance]}
            defaultScale={1.0}
          />
        </Worker>
        
        {/* Render highlights */}
        {renderHighlights()}
        
        {/* Render selection preview */}
        {renderSelectionPreview()}
      </div>
    </div>
  );
};

export default ModernPDFViewer;
