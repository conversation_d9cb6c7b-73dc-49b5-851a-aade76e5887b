import React, { useEffect, useRef, useState, useCallback } from 'react';
import { PDFDocumentProxy } from 'pdfjs-dist';
import { usePDFStore, usePDFActions } from '../store/pdfStore';
import { loadPDFDocument } from '../utils/pdfUtils';
import { EnhancedPDFViewerProps } from '../types';
import PDFPage from './PDFPage';
import Toolbar from './Toolbar';
import HighlightLayer from './HighlightLayer';
import AreaSelector from './AreaSelector';
import './EnhancedPDFViewer.css';

const EnhancedPDFViewer: React.FC<EnhancedPDFViewerProps> = ({
  url,
  highlights = [],
  onHighlightAdd,
  onHighlightRemove,
  onSelection,
  enableAreaSelection = true,
  enableTextSelection = true,
  enablePanning = true,
  enableRotation = true,
  initialScale = 1.0,
  selectionTip,
  className = '',
  style = {},
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isPanning, setIsPanning] = useState(false);
  const [panStart, setPanStart] = useState({ x: 0, y: 0 });
  
  // Store state
  const {
    document,
    isLoading,
    error,
    currentPage,
    totalPages,
    scale,
    rotation,
    viewportTransform,
  } = usePDFStore();
  
  // Store actions
  const {
    setDocument,
    setLoading,
    setError,
    setScale,
    setRotation,
    updateViewportTransform,
  } = usePDFActions();

  // Load PDF document
  useEffect(() => {
    const loadDocument = async () => {
      if (!url) return;

      console.log('Loading PDF from URL:', url);
      setLoading(true);
      setError(null);

      try {
        console.log('Calling loadPDFDocument...');
        const pdfDocument = await loadPDFDocument(url);
        console.log('PDF loaded successfully:', pdfDocument.numPages, 'pages');
        setDocument(pdfDocument);
        setScale(initialScale);
      } catch (err) {
        console.error('Error loading PDF:', err);
        const errorMessage = err instanceof Error ? err.message : 'Failed to load PDF';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    loadDocument();
  }, [url, initialScale, setDocument, setLoading, setError, setScale]);

  // Handle mouse wheel for zooming
  const handleWheel = useCallback((event: WheelEvent) => {
    if (event.ctrlKey || event.metaKey) {
      event.preventDefault();
      const delta = event.deltaY > 0 ? 0.9 : 1.1;
      const newScale = Math.max(0.1, Math.min(5.0, scale * delta));
      setScale(newScale);
    }
  }, [scale, setScale]);

  // Handle mouse events for panning
  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (!enablePanning || event.button !== 0) return;
    
    setIsPanning(true);
    setPanStart({ x: event.clientX, y: event.clientY });
    event.preventDefault();
  }, [enablePanning]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!isPanning || !enablePanning) return;
    
    const deltaX = event.clientX - panStart.x;
    const deltaY = event.clientY - panStart.y;
    
    updateViewportTransform({
      offsetX: viewportTransform.offsetX + deltaX,
      offsetY: viewportTransform.offsetY + deltaY,
    });
    
    setPanStart({ x: event.clientX, y: event.clientY });
  }, [isPanning, enablePanning, panStart, viewportTransform, updateViewportTransform]);

  const handleMouseUp = useCallback(() => {
    setIsPanning(false);
  }, []);

  // Add event listeners
  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    container.addEventListener('wheel', handleWheel, { passive: false });
    
    return () => {
      container.removeEventListener('wheel', handleWheel);
    };
  }, [handleWheel]);

  // Handle zoom controls
  const handleZoomIn = useCallback(() => {
    setScale(Math.min(5.0, scale * 1.25));
  }, [scale, setScale]);

  const handleZoomOut = useCallback(() => {
    setScale(Math.max(0.1, scale * 0.8));
  }, [scale, setScale]);

  const handleZoomFit = useCallback(() => {
    if (!containerRef.current || !document) return;
    
    // Calculate fit scale based on container size
    const containerRect = containerRef.current.getBoundingClientRect();
    // This would need page dimensions - simplified for now
    setScale(1.0);
  }, [document, setScale]);

  // Handle rotation
  const handleRotateLeft = useCallback(() => {
    setRotation((rotation - 90 + 360) % 360);
  }, [rotation, setRotation]);

  const handleRotateRight = useCallback(() => {
    setRotation((rotation + 90) % 360);
  }, [rotation, setRotation]);

  // Render loading state
  if (isLoading) {
    return (
      <div className={`enhanced-pdf-viewer loading ${className}`} style={style}>
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading PDF...</p>
        </div>
      </div>
    );
  }

  // Render error state
  if (error) {
    return (
      <div className={`enhanced-pdf-viewer error ${className}`} style={style}>
        <div className="error-message">
          <h3>Error Loading PDF</h3>
          <p>{error}</p>
          <button onClick={() => window.location.reload()}>Retry</button>
        </div>
      </div>
    );
  }

  // Render main viewer
  return (
    <div className={`enhanced-pdf-viewer ${className}`} style={style}>
      <Toolbar
        scale={scale}
        rotation={rotation}
        currentPage={currentPage}
        totalPages={totalPages}
        onZoomIn={handleZoomIn}
        onZoomOut={handleZoomOut}
        onZoomFit={handleZoomFit}
        onRotateLeft={enableRotation ? handleRotateLeft : undefined}
        onRotateRight={enableRotation ? handleRotateRight : undefined}
        enableRotation={enableRotation}
      />
      
      <div
        ref={containerRef}
        className="pdf-container"
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        style={{
          cursor: isPanning ? 'grabbing' : enablePanning ? 'grab' : 'default',
        }}
      >
        {document && (
          <div
            className="pdf-pages"
            style={{
              transform: `translate(${viewportTransform.offsetX}px, ${viewportTransform.offsetY}px)`,
            }}
          >
            {Array.from({ length: totalPages }, (_, index) => (
              <div key={index + 1} className="page-container">
                <PDFPage
                  document={document}
                  pageNumber={index + 1}
                  scale={scale}
                  rotation={rotation}
                />
                
                <HighlightLayer
                  pageNumber={index + 1}
                  highlights={highlights.filter(h => h.position.boundingRect.pageNumber === index + 1)}
                  scale={scale}
                  rotation={rotation}
                  onHighlightClick={onHighlightRemove}
                />
                
                {enableAreaSelection && (
                  <AreaSelector
                    pageNumber={index + 1}
                    scale={scale}
                    rotation={rotation}
                    onSelection={onSelection}
                    onHighlightAdd={onHighlightAdd}
                  />
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedPDFViewer;
