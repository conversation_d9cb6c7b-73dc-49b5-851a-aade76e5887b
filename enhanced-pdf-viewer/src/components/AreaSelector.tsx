import React, { useState, useRef, useCallback } from 'react';
import { Highlight, PdfSelection, GhostHighlight, ScaledPosition } from '../types';
import { mouseEventToPageCoordinates, createRectFromPoints, viewportToScaled } from '../utils/coordinates';

interface AreaSelectorProps {
  pageNumber: number;
  scale: number;
  rotation: number;
  onSelection?: (selection: PdfSelection) => void;
  onHighlightAdd?: (highlight: Highlight) => void;
}

interface SelectionState {
  isSelecting: boolean;
  startPoint: { x: number; y: number } | null;
  currentPoint: { x: number; y: number } | null;
}

const AreaSelector: React.FC<AreaSelectorProps> = ({
  pageNumber,
  scale,
  rotation,
  onSelection,
  onHighlightAdd,
}) => {
  const [selection, setSelection] = useState<SelectionState>({
    isSelecting: false,
    startPoint: null,
    currentPoint: null,
  });
  
  const layerRef = useRef<HTMLDivElement>(null);

  const handleMouseDown = useCallback((event: React.MouseEvent) => {
    if (event.button !== 0) return; // Only left mouse button
    
    const layerElement = layerRef.current;
    if (!layerElement) return;

    const coordinates = mouseEventToPageCoordinates(event.nativeEvent, layerElement, scale, rotation);
    
    setSelection({
      isSelecting: true,
      startPoint: coordinates,
      currentPoint: coordinates,
    });

    event.preventDefault();
    event.stopPropagation();
  }, [scale, rotation]);

  const handleMouseMove = useCallback((event: React.MouseEvent) => {
    if (!selection.isSelecting || !selection.startPoint) return;

    const layerElement = layerRef.current;
    if (!layerElement) return;

    const coordinates = mouseEventToPageCoordinates(event.nativeEvent, layerElement, scale, rotation);
    
    setSelection(prev => ({
      ...prev,
      currentPoint: coordinates,
    }));

    event.preventDefault();
  }, [selection.isSelecting, selection.startPoint, scale, rotation]);

  const handleMouseUp = useCallback((event: React.MouseEvent) => {
    if (!selection.isSelecting || !selection.startPoint || !selection.currentPoint) {
      setSelection({
        isSelecting: false,
        startPoint: null,
        currentPoint: null,
      });
      return;
    }

    const layerElement = layerRef.current;
    if (!layerElement) return;

    const endCoordinates = mouseEventToPageCoordinates(event.nativeEvent, layerElement, scale, rotation);
    
    // Create bounding rectangle
    const boundingRect = createRectFromPoints(selection.startPoint, endCoordinates, pageNumber);
    
    // Only create highlight if selection is large enough
    const minSize = 10;
    if (boundingRect.width >= minSize && boundingRect.height >= minSize) {
      // Create scaled position
      const scaledPosition: ScaledPosition = {
        boundingRect,
        rects: [boundingRect],
      };

      // Create ghost highlight function
      const makeGhostHighlight = (): GhostHighlight => ({
        position: scaledPosition,
        content: { text: '' },
        type: 'area' as const,
      });

      // Create selection object
      const pdfSelection: PdfSelection = {
        content: { text: '' },
        position: scaledPosition,
        type: 'area',
        makeGhostHighlight,
      };

      // Create highlight
      const highlight: Highlight = {
        id: `highlight-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        position: scaledPosition,
        content: { text: '' },
        type: 'area',
        color: 'rgba(255, 255, 0, 0.3)',
      };

      // Call callbacks
      if (onSelection) {
        onSelection(pdfSelection);
      }
      
      if (onHighlightAdd) {
        onHighlightAdd(highlight);
      }
    }

    // Reset selection state
    setSelection({
      isSelecting: false,
      startPoint: null,
      currentPoint: null,
    });

    event.preventDefault();
    event.stopPropagation();
  }, [selection, pageNumber, scale, rotation, onSelection, onHighlightAdd]);

  // Calculate preview rectangle
  const getPreviewRect = () => {
    if (!selection.isSelecting || !selection.startPoint || !selection.currentPoint) {
      return null;
    }

    const rect = createRectFromPoints(selection.startPoint, selection.currentPoint, pageNumber);
    return rect;
  };

  const previewRect = getPreviewRect();

  return (
    <div
      ref={layerRef}
      className="area-selector-layer"
      style={{
        position: 'absolute',
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
        cursor: selection.isSelecting ? 'crosshair' : 'crosshair',
        pointerEvents: 'auto',
        zIndex: 10,
      }}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseUp}
    >
      {/* Preview rectangle during selection */}
      {previewRect && (
        <div
          className="selection-preview"
          style={{
            position: 'absolute',
            left: `${previewRect.x1 * scale}px`,
            top: `${previewRect.y1 * scale}px`,
            width: `${previewRect.width * scale}px`,
            height: `${previewRect.height * scale}px`,
            border: '2px dashed rgba(255, 152, 0, 0.8)',
            backgroundColor: 'rgba(255, 235, 59, 0.2)',
            borderRadius: '2px',
            pointerEvents: 'none',
            animation: 'selectionPulse 1s ease-in-out infinite alternate',
          }}
        />
      )}
    </div>
  );
};

export default AreaSelector;
