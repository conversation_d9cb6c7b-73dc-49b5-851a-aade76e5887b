import React, { useEffect, useRef, useState } from 'react';
import { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';
import { getPDFPage, renderPageToCanvas, getPageTextContent } from '../utils/pdfUtils';

interface PDFPageProps {
  document: PDFDocumentProxy;
  pageNumber: number;
  scale: number;
  rotation: number;
  enableTextLayer?: boolean;
}

const PDFPage: React.FC<PDFPageProps> = ({
  document,
  pageNumber,
  scale,
  rotation,
  enableTextLayer = true,
}) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const textLayerRef = useRef<HTMLDivElement>(null);
  const [page, setPage] = useState<PDFPageProxy | null>(null);
  const [isRendering, setIsRendering] = useState(false);
  const [textContent, setTextContent] = useState<any>(null);

  // Load page
  useEffect(() => {
    const loadPage = async () => {
      try {
        const pdfPage = await getPDFPage(document, pageNumber);
        setPage(pdfPage);
      } catch (error) {
        console.error(`Error loading page ${pageNumber}:`, error);
      }
    };

    if (document) {
      loadPage();
    }
  }, [document, pageNumber]);

  // Render page to canvas
  useEffect(() => {
    const renderPage = async () => {
      if (!page || !canvasRef.current || isRendering) return;

      setIsRendering(true);
      try {
        await renderPageToCanvas(page, canvasRef.current, scale, rotation);
      } catch (error) {
        console.error(`Error rendering page ${pageNumber}:`, error);
      } finally {
        setIsRendering(false);
      }
    };

    renderPage();
  }, [page, scale, rotation, pageNumber, isRendering]);

  // Load text content for text layer
  useEffect(() => {
    const loadTextContent = async () => {
      if (!page || !enableTextLayer) return;

      try {
        const content = await getPageTextContent(page);
        setTextContent(content);
      } catch (error) {
        console.error(`Error loading text content for page ${pageNumber}:`, error);
      }
    };

    loadTextContent();
  }, [page, pageNumber, enableTextLayer]);

  // Render text layer
  useEffect(() => {
    if (!textContent || !textLayerRef.current || !page) return;

    const textLayer = textLayerRef.current;
    const viewport = page.getViewport({ scale, rotation });

    // Clear existing text layer
    textLayer.innerHTML = '';
    textLayer.style.width = `${viewport.width}px`;
    textLayer.style.height = `${viewport.height}px`;

    // Render text items
    textContent.items.forEach((item: any, index: number) => {
      const textSpan = document.createElement('span');
      textSpan.textContent = item.str;
      textSpan.style.position = 'absolute';
      textSpan.style.color = 'transparent';
      textSpan.style.userSelect = 'text';
      textSpan.style.cursor = 'text';
      
      // Apply transformation
      const transform = item.transform;
      const x = transform[4];
      const y = transform[5];
      const fontSize = Math.sqrt(transform[0] * transform[0] + transform[1] * transform[1]);
      
      textSpan.style.left = `${x}px`;
      textSpan.style.top = `${viewport.height - y - fontSize}px`;
      textSpan.style.fontSize = `${fontSize}px`;
      textSpan.style.fontFamily = item.fontName || 'sans-serif';
      textSpan.style.transformOrigin = '0% 0%';
      
      // Apply rotation if needed
      if (rotation !== 0) {
        textSpan.style.transform = `rotate(${rotation}deg)`;
      }

      textLayer.appendChild(textSpan);
    });
  }, [textContent, page, scale, rotation]);

  if (!page) {
    return (
      <div className="pdf-page loading">
        <div className="page-loading-spinner">Loading page {pageNumber}...</div>
      </div>
    );
  }

  return (
    <div className="pdf-page" data-page-number={pageNumber}>
      <canvas
        ref={canvasRef}
        className="pdf-page-canvas"
        style={{
          display: 'block',
          maxWidth: '100%',
          height: 'auto',
        }}
      />
      
      {enableTextLayer && (
        <div
          ref={textLayerRef}
          className="pdf-text-layer"
          style={{
            position: 'absolute',
            left: 0,
            top: 0,
            right: 0,
            bottom: 0,
            overflow: 'hidden',
            opacity: 0.2,
            lineHeight: 1.0,
            pointerEvents: 'auto',
          }}
        />
      )}
      
      {isRendering && (
        <div className="page-rendering-overlay">
          <div className="rendering-spinner">Rendering...</div>
        </div>
      )}
    </div>
  );
};

export default PDFPage;
