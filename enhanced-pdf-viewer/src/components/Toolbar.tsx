import React from 'react';

interface ToolbarProps {
  scale: number;
  rotation: number;
  currentPage: number;
  totalPages: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onZoomFit: () => void;
  onRotateLeft?: () => void;
  onRotateRight?: () => void;
  enableRotation?: boolean;
}

const Toolbar: React.FC<ToolbarProps> = ({
  scale,
  rotation,
  currentPage,
  totalPages,
  onZoomIn,
  onZoomOut,
  onZoomFit,
  onRotateLeft,
  onRotateRight,
  enableRotation = true,
}) => {
  const formatScale = (scale: number): string => {
    return `${Math.round(scale * 100)}%`;
  };

  return (
    <div className="pdf-toolbar">
      <div className="toolbar-section">
        <span className="toolbar-label">Page:</span>
        <span className="page-info">
          {currentPage} / {totalPages}
        </span>
      </div>

      <div className="toolbar-section">
        <button
          className="toolbar-button"
          onClick={onZoomOut}
          disabled={scale <= 0.1}
          title="Zoom Out"
        >
          🔍−
        </button>
        
        <span className="zoom-display" title="Current Zoom">
          {formatScale(scale)}
        </span>
        
        <button
          className="toolbar-button"
          onClick={onZoomIn}
          disabled={scale >= 5.0}
          title="Zoom In"
        >
          🔍+
        </button>
        
        <button
          className="toolbar-button"
          onClick={onZoomFit}
          title="Fit to Width"
        >
          📐 Fit
        </button>
      </div>

      {enableRotation && (
        <div className="toolbar-section">
          <button
            className="toolbar-button"
            onClick={onRotateLeft}
            title="Rotate Left"
          >
            ↺
          </button>
          
          <span className="rotation-display" title="Current Rotation">
            {rotation}°
          </span>
          
          <button
            className="toolbar-button"
            onClick={onRotateRight}
            title="Rotate Right"
          >
            ↻
          </button>
        </div>
      )}

      <div className="toolbar-section">
        <span className="toolbar-info">
          Enhanced PDF Viewer
        </span>
      </div>
    </div>
  );
};

export default Toolbar;
