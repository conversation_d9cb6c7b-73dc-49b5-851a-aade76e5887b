import { PDFDocumentProxy } from 'pdfjs-dist';

// Position and coordinate types
export interface BoundingRect {
  x1: number;
  y1: number;
  x2: number;
  y2: number;
  width: number;
  height: number;
  pageNumber: number;
}

export interface ViewportPosition {
  boundingRect: BoundingRect;
  rects: Array<BoundingRect>;
}

export interface ScaledPosition {
  boundingRect: BoundingRect;
  rects: Array<BoundingRect>;
  usePdfCoordinates?: boolean;
}

// Content types
export interface Content {
  text?: string;
  image?: string;
}

// Highlight types
export interface Highlight {
  id: string;
  position: ScaledPosition;
  content: Content;
  type: 'text' | 'area';
  comment?: string;
  color?: string;
}

export interface GhostHighlight {
  position: ScaledPosition;
  content: Content;
  type: 'text' | 'area';
}

// Selection types
export interface PdfSelection {
  content: Content;
  position: ScaledPosition;
  type: 'text' | 'area';
  makeGhostHighlight: () => GhostHighlight;
}

// Tip types
export interface Tip {
  position: ViewportPosition;
  content: React.ReactNode;
}

// PDF viewer types
export type PdfScaleValue = string | number;

export interface PDFViewerState {
  document: PDFDocumentProxy | null;
  currentPage: number;
  totalPages: number;
  scale: number;
  rotation: number;
  isLoading: boolean;
  error: string | null;
}

// Viewport and transformation types
export interface ViewportTransform {
  scale: number;
  rotation: number;
  offsetX: number;
  offsetY: number;
}

// Mouse and interaction types
export interface MousePosition {
  x: number;
  y: number;
  pageNumber: number;
}

export interface AreaSelection {
  startPosition: MousePosition;
  endPosition: MousePosition;
  isActive: boolean;
}

// Component props types
export interface EnhancedPDFViewerProps {
  url: string;
  highlights: Highlight[];
  onHighlightAdd?: (highlight: Highlight) => void;
  onHighlightRemove?: (highlightId: string) => void;
  onSelection?: (selection: PdfSelection) => void;
  enableAreaSelection?: boolean;
  enableTextSelection?: boolean;
  enablePanning?: boolean;
  enableRotation?: boolean;
  initialScale?: number;
  selectionTip?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

// Store types for state management
export interface PDFStore {
  // Document state
  document: PDFDocumentProxy | null;
  isLoading: boolean;
  error: string | null;
  
  // Viewer state
  currentPage: number;
  totalPages: number;
  scale: number;
  rotation: number;
  
  // Interaction state
  highlights: Highlight[];
  ghostHighlight: GhostHighlight | null;
  selectedText: string | null;
  areaSelection: AreaSelection | null;
  
  // Viewport state
  viewportTransform: ViewportTransform;
  
  // Actions
  setDocument: (document: PDFDocumentProxy | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setCurrentPage: (page: number) => void;
  setScale: (scale: number) => void;
  setRotation: (rotation: number) => void;
  addHighlight: (highlight: Highlight) => void;
  removeHighlight: (id: string) => void;
  setGhostHighlight: (highlight: GhostHighlight | null) => void;
  setAreaSelection: (selection: AreaSelection | null) => void;
  updateViewportTransform: (transform: Partial<ViewportTransform>) => void;
}

// Event types
export interface PDFViewerEvents {
  onDocumentLoad?: (document: PDFDocumentProxy) => void;
  onPageChange?: (page: number) => void;
  onScaleChange?: (scale: number) => void;
  onRotationChange?: (rotation: number) => void;
  onError?: (error: string) => void;
}
