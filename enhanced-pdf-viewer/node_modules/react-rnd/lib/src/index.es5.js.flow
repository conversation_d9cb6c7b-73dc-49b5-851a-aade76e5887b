/* @flow */

import * as React from 'react';
import type { ResizeDirection, ResizeCallback, ResizeStartCallback } from 're-resizable';

export type Grid = [number, number];

export type Position = {
  x: number,
  y: number,
};

export type DraggableData = {
  node: HTMLElement,
  deltaX: number,
  deltaY: number,
  lastX: number,
  lastY: number,
} & Position;

export type RndDragCallback = (e: Event, data: DraggableData) => void | false;

export type RndResizeStartCallback = (
  e: SyntheticMouseEvent<HTMLDivElement> | SyntheticTouchEvent<HTMLDivElement>,
  dir: ResizeDirection,
  refToElement: React.ElementRef<'div'>,
) => void;

export type ResizableDelta = {
  width: number,
  height: number,
};

export type RndResizeCallback = (
  e: MouseEvent | TouchEvent,
  dir: ResizeDirection,
  refToElement: React.ElementRef<'div'>,
  delta: ResizableDelta,
  position: Position,
) => void;

type Size = {
  width: string | number,
  height: string | number,
};

type State = {
  original: Position,
  bounds: {
    top: number,
    right: number,
    bottom: number,
    left: number,
  },
  maxWidth?: number | string,
  maxHeight?: number | string,
};

export type ResizeEnable = {
  bottom?: boolean,
  bottomLeft?: boolean,
  bottomRight?: boolean,
  left?: boolean,
  right?: boolean,
  top?: boolean,
  topLeft?: boolean,
  topRight?: boolean,
};

export type HandleClasses = {
  bottom?: string,
  bottomLeft?: string,
  bottomRight?: string,
  left?: string,
  right?: string,
  top?: string,
  topLeft?: string,
  topRight?: string,
};

type Style = {
  [key: string]: string | number,
};

export type HandleStyles = {
  bottom?: Style,
  bottomLeft?: Style,
  bottomRight?: Style,
  left?: Style,
  right?: Style,
  top?: Style,
  topLeft?: Style,
  topRight?: Style,
};

export type HandleComponent = {
  top?: React.ReactElement<any>;
  right?: React.ReactElement<any>;
  bottom?: React.ReactElement<any>;
  left?: React.ReactElement<any>;
  topRight?: React.ReactElement<any>;
  bottomRight?: React.ReactElement<any>;
  bottomLeft?: React.ReactElement<any>;
  topLeft?: React.ReactElement<any>;
}

export type Props = {
  dragGrid?: Grid,
  default?: {
    x: number,
    y: number,
  } & Size,
  position?: {
    x: number,
    y: number,
  },
  size?: Size,
  resizeGrid?: Grid,
  bounds?: string,
  onResizeStart?: RndResizeStartCallback,
  onResize?: RndResizeCallback,
  onResizeStop?: RndResizeCallback,
  onDragStart?: RndDragCallback,
  onDrag?: RndDragCallback,
  onDragStop?: RndDragCallback,
  className?: string,
  style?: Style,
  children?: React.Node,
  enableResizing?: ResizeEnable,
  extendsProps?: { [key: string]: any },
  resizeHandleClasses?: HandleClasses,
  resizeHandleStyles?: HandleStyles,
  resizeHandleComponent?: HandleComponent,
  resizeHandleWrapperClass?: string,
  resizeHandleWrapperStyle?: Style,
  lockAspectRatio?: boolean | number,
  lockAspectRatioExtraWidth?: number,
  lockAspectRatioExtraHeight?: number,
  maxHeight?: number | string,
  maxWidth?: number | string,
  minHeight?: number | string,
  minWidth?: number | string,
  dragAxis?: 'x' | 'y' | 'both' | 'none',
  dragHandleClassName?: string,
  disableDragging?: boolean,
  cancel?: boolean,
  enableUserSelectHack?: boolean,
  scale?: number,
};

export class Rnd extends React.Component<Props, State> {
  static defaultProps = {
    maxWidth: Number.MAX_SAFE_INTEGER,
    maxHeight: Number.MAX_SAFE_INTEGER,
    onResizeStart: () => {},
    onResize: () => {},
    onResizeStop: () => {},
    onDragStart: () => {},
    onDrag: () => {},
    onDragStop: () => {},
    scale: 1,
  };

  updateSize(size: { width: number | string, height: number | string }) {
  }

  updatePosition(position: Position) {
  }
}
