{"name": "react-rnd", "version": "10.5.2", "description": "A draggable and resizable React Component", "title": "react-rnd", "main": "./lib/index.es5.js", "module": "./lib/index.js", "jsnext:main": "./lib/index.js", "types": "./lib/index.d.ts", "keywords": ["react", "resize", "resizable", "draggable", "component"], "scripts": {"lint": "tslint -c tslint.json 'src/index.tsx'", "flow": "flow", "tsc": "tsc -p tsconfig.json --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build:prod:main": "rollup -c scripts/prod.js", "build:prod:es5": "rollup -c scripts/prod.es5.js", "build": "npm-run-all --serial build:prod:* copy:flow", "test": "cross-env NODE_ENV='test' npm run tsc && avaron lib/index.test.js --renderer", "copy:flow": "cpy src/index.js.flow lib && cpy src/index.js.flow lib --rename index.es5.js.flow", "test:ci": "npm run flow && npm run build", "prepare": "npm run build", "storybook": "start-storybook -p 6016", "build-storybook": "build-storybook -o docs/stories", "deploy": "npm run build-storybook && gh-pages -d docs"}, "repository": {"type": "git", "url": "https://github.com/bokuweb/react-rnd.git"}, "author": "bokuweb", "license": "MIT", "bugs": {"url": "https://github.com/bokuweb/react-rnd/issues"}, "homepage": "https://github.com/bokuweb/react-rnd", "devDependencies": {"@babel/cli": "7.17.6", "@babel/core": "7.17.5", "@babel/plugin-proposal-class-properties": "7.16.7", "@babel/plugin-transform-modules-commonjs": "7.16.8", "@babel/preset-react": "7.16.7", "@babel/preset-typescript": "7.16.7", "@babel/traverse": "7.17.3", "@babel/types": "7.17.0", "@emotion/core": "11.0.0", "@storybook/addon-actions": "6.5.16", "@storybook/addon-info": "5.3.21", "@storybook/addon-links": "6.5.16", "@storybook/addon-options": "5.3.21", "@storybook/react": "6.5.16", "@types/enzyme": "3.1.16", "@types/enzyme-adapter-react-16": "1.0.6", "@types/node": "16.18.96", "@types/react": "17.0.80", "@types/react-dom": "17.0.25", "@types/sinon": "10.0.11", "@types/storybook__addon-actions": "5.2.1", "@types/storybook__react": "5.2.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.1.0", "babel-loader": "8.2.3", "cpy-cli": "4.2.0", "cross-env": "7.0.3", "enzyme": "3.11.0", "enzyme-adapter-react-16": "1.9.1", "gh-pages": "3.2.3", "light-ts-loader": "1.1.2", "npm-run-all2": "5.0.2", "prettier": "2.6.2", "react": "16.14.0", "react-dom": "16.14.0", "react-test-renderer": "16.14.0", "rollup": "1.32.1", "@rollup/plugin-babel": "5.0.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-globals": "1.4.0", "@rollup/plugin-node-resolve": "6.0.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.22.0", "rollup-watch": "4.3.1", "sinon": "13.0.1", "tslint": "6.1.3", "tslint-eslint-rules": "5.4.0", "tslint-plugin-prettier": "2.3.0", "tslint-react": "5.0.0", "typescript": "5.4.5"}, "files": ["lib"], "avaron": {"fixture": "./fixture.html"}, "dependencies": {"re-resizable": "6.11.2", "react-draggable": "4.4.6", "tslib": "2.6.2"}, "peerDependencies": {"react": ">=16.3.0", "react-dom": ">=16.3.0"}}