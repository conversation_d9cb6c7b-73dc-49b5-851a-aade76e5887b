/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=e.pdfjsImageDecoders=t():"function"==typeof define&&define.amd?define("pdfjs-dist/image_decoders/pdf.image_decoders",[],(()=>e.pdfjsImageDecoders=t())):"object"==typeof exports?exports["pdfjs-dist/image_decoders/pdf.image_decoders"]=e.pdfjsImageDecoders=t():e["pdfjs-dist/image_decoders/pdf.image_decoders"]=e.pdfjsImageDecoders=t()}(globalThis,(()=>(()=>{"use strict";var e=[,(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.VerbosityLevel=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.RenderingIntentFlag=t.PromiseCapability=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.PageActionEventType=t.OPS=t.MissingPDFException=t.MAX_IMAGE_SIZE_TO_CACHE=t.LINE_FACTOR=t.LINE_DESCENT_FACTOR=t.InvalidPDFException=t.ImageKind=t.IDENTITY_MATRIX=t.FormatError=t.FeatureTest=t.FONT_IDENTITY_MATRIX=t.DocumentActionEventType=t.CMapCompressionType=t.BaseException=t.BASELINE_FACTOR=t.AnnotationType=t.AnnotationReplyType=t.AnnotationPrefix=t.AnnotationMode=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationEditorType=t.AnnotationEditorPrefix=t.AnnotationEditorParamsType=t.AnnotationBorderStyleType=t.AnnotationActionEventType=t.AbortException=void 0;t.assert=function assert(e,t){e||unreachable(t)};t.bytesToString=bytesToString;t.createValidAbsoluteUrl=function createValidAbsoluteUrl(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!e)return null;try{if(n&&"string"==typeof e){if(n.addDefaultProtocol&&e.startsWith("www.")){const t=e.match(/\./g);t?.length>=2&&(e=`http://${e}`)}if(n.tryConvertEncoding)try{e=stringToUTF8String(e)}catch{}}const r=t?new URL(e,t):new URL(e);if(function _isValidProtocol(e){switch(e?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch{}return null};t.getModificationDate=function getModificationDate(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),e.getUTCDate().toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")].join("")};t.getUuid=function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const e=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(e);else for(let t=0;t<32;t++)e[t]=Math.floor(255*Math.random());return bytesToString(e)};t.getVerbosityLevel=function getVerbosityLevel(){return o};t.info=function info(e){o>=i.INFOS&&console.log(`Info: ${e}`)};t.isArrayBuffer=function isArrayBuffer(e){return"object"==typeof e&&void 0!==e?.byteLength};t.isArrayEqual=function isArrayEqual(e,t){if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0};t.isNodeJS=void 0;t.normalizeUnicode=function normalizeUnicode(e){if(!l){l=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;f=new Map([["ﬅ","ſt"]])}return e.replaceAll(l,((e,t,n)=>t?t.normalize("NFKC"):f.get(n)))};t.objectFromMap=function objectFromMap(e){const t=Object.create(null);for(const[n,r]of e)t[n]=r;return t};t.objectSize=function objectSize(e){return Object.keys(e).length};t.setVerbosityLevel=function setVerbosityLevel(e){Number.isInteger(e)&&(o=e)};t.shadow=shadow;t.string32=function string32(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)};t.stringToBytes=stringToBytes;t.stringToPDFString=function stringToPDFString(e){if(e[0]>="ï"){let t;"þ"===e[0]&&"ÿ"===e[1]?t="utf-16be":"ÿ"===e[0]&&"þ"===e[1]?t="utf-16le":"ï"===e[0]&&"»"===e[1]&&"¿"===e[2]&&(t="utf-8");if(t)try{const n=new TextDecoder(t,{fatal:!0}),r=stringToBytes(e);return n.decode(r)}catch(e){warn(`stringToPDFString: "${e}".`)}}const t=[];for(let n=0,r=e.length;n<r;n++){const r=c[e.charCodeAt(n)];t.push(r?String.fromCharCode(r):e.charAt(n))}return t.join("")};t.stringToUTF8String=stringToUTF8String;t.unreachable=unreachable;t.utf8StringToString=function utf8StringToString(e){return unescape(encodeURIComponent(e))};t.warn=warn;n(2);n(84);n(86);n(87);n(89);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);const r=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=r;t.IDENTITY_MATRIX=[1,0,0,1,0,0];t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];t.MAX_IMAGE_SIZE_TO_CACHE=1e7;t.LINE_FACTOR=1.35;t.LINE_DESCENT_FACTOR=.35;t.BASELINE_FACTOR=.25925925925925924;t.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};t.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};t.AnnotationEditorPrefix="pdfjs_internal_editor_";t.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};t.AnnotationEditorParamsType={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationReplyType={GROUP:"Group",REPLY:"R"};t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};t.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};t.PageActionEventType={O:"PageOpen",C:"PageClose"};const i={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=i;t.CMapCompressionType={NONE:0,BINARY:1};t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let o=i.WARNINGS;function warn(e){o>=i.WARNINGS&&console.log(`Warning: ${e}`)}function unreachable(e){throw new Error(e)}function shadow(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]&&arguments[3];Object.defineProperty(e,t,{value:n,enumerable:!r,configurable:!0,writable:!1});return n}const s=function BaseExceptionClosure(){function BaseException(e,t){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=e;this.name=t}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();t.BaseException=s;t.PasswordException=class PasswordException extends s{constructor(e,t){super(e,"PasswordException");this.code=t}};t.UnknownErrorException=class UnknownErrorException extends s{constructor(e,t){super(e,"UnknownErrorException");this.details=t}};t.InvalidPDFException=class InvalidPDFException extends s{constructor(e){super(e,"InvalidPDFException")}};t.MissingPDFException=class MissingPDFException extends s{constructor(e){super(e,"MissingPDFException")}};t.UnexpectedResponseException=class UnexpectedResponseException extends s{constructor(e,t){super(e,"UnexpectedResponseException");this.status=t}};t.FormatError=class FormatError extends s{constructor(e){super(e,"FormatError")}};t.AbortException=class AbortException extends s{constructor(e){super(e,"AbortException")}};function bytesToString(e){"object"==typeof e&&void 0!==e?.length||unreachable("Invalid argument for bytesToString");const t=e.length,n=8192;if(t<n)return String.fromCharCode.apply(null,e);const r=[];for(let i=0;i<t;i+=n){const o=Math.min(i+n,t),s=e.subarray(i,o);r.push(String.fromCharCode.apply(null,s))}return r.join("")}function stringToBytes(e){"string"!=typeof e&&unreachable("Invalid argument for stringToBytes");const t=e.length,n=new Uint8Array(t);for(let r=0;r<t;++r)n[r]=255&e.charCodeAt(r);return n}t.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"==typeof navigator?shadow(this,"platform",{isWin:!1,isMac:!1}):shadow(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}};const a=[...Array(256).keys()].map((e=>e.toString(16).padStart(2,"0")));t.Util=class Util{static makeHexColor(e,t,n){return`#${a[e]}${a[t]}${a[n]}`}static scaleMinMax(e,t){let n;if(e[0]){if(e[0]<0){n=t[0];t[0]=t[1];t[1]=n}t[0]*=e[0];t[1]*=e[0];if(e[3]<0){n=t[2];t[2]=t[3];t[3]=n}t[2]*=e[3];t[3]*=e[3]}else{n=t[0];t[0]=t[2];t[2]=n;n=t[1];t[1]=t[3];t[3]=n;if(e[1]<0){n=t[2];t[2]=t[3];t[3]=n}t[2]*=e[1];t[3]*=e[1];if(e[2]<0){n=t[0];t[0]=t[1];t[1]=n}t[0]*=e[2];t[1]*=e[2]}t[0]+=e[4];t[1]+=e[4];t[2]+=e[5];t[3]+=e[5]}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]}static applyInverseTransform(e,t){const n=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/n,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/n]}static getAxialAlignedBoundingBox(e,t){const n=this.applyTransform(e,t),r=this.applyTransform(e.slice(2,4),t),i=this.applyTransform([e[0],e[3]],t),o=this.applyTransform([e[2],e[1]],t);return[Math.min(n[0],r[0],i[0],o[0]),Math.min(n[1],r[1],i[1],o[1]),Math.max(n[0],r[0],i[0],o[0]),Math.max(n[1],r[1],i[1],o[1])]}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static singularValueDecompose2dScale(e){const t=[e[0],e[2],e[1],e[3]],n=e[0]*t[0]+e[1]*t[2],r=e[0]*t[1]+e[1]*t[3],i=e[2]*t[0]+e[3]*t[2],o=e[2]*t[1]+e[3]*t[3],s=(n+o)/2,a=Math.sqrt((n+o)**2-4*(n*o-i*r))/2,c=s+a||1,l=s-a||1;return[Math.sqrt(c),Math.sqrt(l)]}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){const n=Math.max(Math.min(e[0],e[2]),Math.min(t[0],t[2])),r=Math.min(Math.max(e[0],e[2]),Math.max(t[0],t[2]));if(n>r)return null;const i=Math.max(Math.min(e[1],e[3]),Math.min(t[1],t[3])),o=Math.min(Math.max(e[1],e[3]),Math.max(t[1],t[3]));return i>o?null:[n,i,r,o]}static bezierBoundingBox(e,t,n,r,i,o,s,a){const c=[],l=[[],[]];let f,u,h,d,p,g,m,b;for(let l=0;l<2;++l){if(0===l){u=6*e-12*n+6*i;f=-3*e+9*n-9*i+3*s;h=3*n-3*e}else{u=6*t-12*r+6*o;f=-3*t+9*r-9*o+3*a;h=3*r-3*t}if(Math.abs(f)<1e-12){if(Math.abs(u)<1e-12)continue;d=-h/u;0<d&&d<1&&c.push(d)}else{m=u*u-4*h*f;b=Math.sqrt(m);if(!(m<0)){p=(-u+b)/(2*f);0<p&&p<1&&c.push(p);g=(-u-b)/(2*f);0<g&&g<1&&c.push(g)}}}let y,x=c.length;const w=x;for(;x--;){d=c[x];y=1-d;l[0][x]=y*y*y*e+3*y*y*d*n+3*y*d*d*i+d*d*d*s;l[1][x]=y*y*y*t+3*y*y*d*r+3*y*d*d*o+d*d*d*a}l[0][w]=e;l[1][w]=t;l[0][w+1]=s;l[1][w+1]=a;l[0].length=l[1].length=w+2;return[Math.min(...l[0]),Math.min(...l[1]),Math.max(...l[0]),Math.max(...l[1])]}};const c=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(e){return decodeURIComponent(escape(e))}t.PromiseCapability=class PromiseCapability{#e=!1;constructor(){this.promise=new Promise(((e,t)=>{this.resolve=t=>{this.#e=!0;e(t)};this.reject=e=>{this.#e=!0;t(e)}}))}get settled(){return this.#e}};let l=null,f=null;t.AnnotationPrefix="pdfjs_internal_id_"},(e,t,n)=>{var r=n(3),i=n(4),o=n(69),s=n(70),a="WebAssembly",c=i[a],l=7!==Error("e",{cause:7}).cause,exportGlobalErrorCauseWrapper=function(e,t){var n={};n[e]=s(e,t,l);r({global:!0,constructor:!0,arity:1,forced:l},n)},exportWebAssemblyErrorCauseWrapper=function(e,t){if(c&&c[e]){var n={};n[e]=s(a+"."+e,t,l);r({target:a,stat:!0,constructor:!0,arity:1,forced:l},n)}};exportGlobalErrorCauseWrapper("Error",(function(e){return function Error(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("EvalError",(function(e){return function EvalError(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("RangeError",(function(e){return function RangeError(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("ReferenceError",(function(e){return function ReferenceError(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("SyntaxError",(function(e){return function SyntaxError(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("TypeError",(function(e){return function TypeError(t){return o(e,this,arguments)}}));exportGlobalErrorCauseWrapper("URIError",(function(e){return function URIError(t){return o(e,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("CompileError",(function(e){return function CompileError(t){return o(e,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("LinkError",(function(e){return function LinkError(t){return o(e,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("RuntimeError",(function(e){return function RuntimeError(t){return o(e,this,arguments)}}))},(e,t,n)=>{var r=n(4),i=n(5).f,o=n(44),s=n(48),a=n(38),c=n(56),l=n(68);e.exports=function(e,t){var n,f,u,h,d,p=e.target,g=e.global,m=e.stat;if(n=g?r:m?r[p]||a(p,{}):(r[p]||{}).prototype)for(f in t){h=t[f];u=e.dontCallGetSet?(d=i(n,f))&&d.value:n[f];if(!l(g?f:p+(m?".":"#")+f,e.forced)&&void 0!==u){if(typeof h==typeof u)continue;c(h,u)}(e.sham||u&&u.sham)&&o(h,"sham",!0);s(n,f,h,e)}}},function(e){var check=function(e){return e&&e.Math===Math&&e};e.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||function(){return this}()||this||Function("return this")()},(e,t,n)=>{var r=n(6),i=n(8),o=n(10),s=n(11),a=n(12),c=n(18),l=n(39),f=n(42),u=Object.getOwnPropertyDescriptor;t.f=r?u:function getOwnPropertyDescriptor(e,t){e=a(e);t=c(t);if(f)try{return u(e,t)}catch(e){}if(l(e,t))return s(!i(o.f,e,t),e[t])}},(e,t,n)=>{var r=n(7);e.exports=!r((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},e=>{e.exports=function(e){try{return!!e()}catch(e){return!0}}},(e,t,n)=>{var r=n(9),i=Function.prototype.call;e.exports=r?i.bind(i):function(){return i.apply(i,arguments)}},(e,t,n)=>{var r=n(7);e.exports=!r((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},(e,t)=>{var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);t.f=i?function propertyIsEnumerable(e){var t=r(this,e);return!!t&&t.enumerable}:n},e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},(e,t,n)=>{var r=n(13),i=n(16);e.exports=function(e){return r(i(e))}},(e,t,n)=>{var r=n(14),i=n(7),o=n(15),s=Object,a=r("".split);e.exports=i((function(){return!s("z").propertyIsEnumerable(0)}))?function(e){return"String"===o(e)?a(e,""):s(e)}:s},(e,t,n)=>{var r=n(9),i=Function.prototype,o=i.call,s=r&&i.bind.bind(o,o);e.exports=r?s:function(e){return function(){return o.apply(e,arguments)}}},(e,t,n)=>{var r=n(14),i=r({}.toString),o=r("".slice);e.exports=function(e){return o(i(e),8,-1)}},(e,t,n)=>{var r=n(17),i=TypeError;e.exports=function(e){if(r(e))throw i("Can't call method on "+e);return e}},e=>{e.exports=function(e){return null==e}},(e,t,n)=>{var r=n(19),i=n(23);e.exports=function(e){var t=r(e,"string");return i(t)?t:t+""}},(e,t,n)=>{var r=n(8),i=n(20),o=n(23),s=n(30),a=n(33),c=n(34),l=TypeError,f=c("toPrimitive");e.exports=function(e,t){if(!i(e)||o(e))return e;var n,c=s(e,f);if(c){void 0===t&&(t="default");n=r(c,e,t);if(!i(n)||o(n))return n;throw l("Can't convert object to primitive value")}void 0===t&&(t="number");return a(e,t)}},(e,t,n)=>{var r=n(21),i=n(22),o=i.all;e.exports=i.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:r(e)||e===o}:function(e){return"object"==typeof e?null!==e:r(e)}},(e,t,n)=>{var r=n(22),i=r.all;e.exports=r.IS_HTMLDDA?function(e){return"function"==typeof e||e===i}:function(e){return"function"==typeof e}},e=>{var t="object"==typeof document&&document.all,n=void 0===t&&void 0!==t;e.exports={all:t,IS_HTMLDDA:n}},(e,t,n)=>{var r=n(24),i=n(21),o=n(25),s=n(26),a=Object;e.exports=s?function(e){return"symbol"==typeof e}:function(e){var t=r("Symbol");return i(t)&&o(t.prototype,a(e))}},(e,t,n)=>{var r=n(4),i=n(21);e.exports=function(e,t){return arguments.length<2?(n=r[e],i(n)?n:void 0):r[e]&&r[e][t];var n}},(e,t,n)=>{var r=n(14);e.exports=r({}.isPrototypeOf)},(e,t,n)=>{var r=n(27);e.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},(e,t,n)=>{var r=n(28),i=n(7),o=n(4).String;e.exports=!!Object.getOwnPropertySymbols&&!i((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},(e,t,n)=>{var r,i,o=n(4),s=n(29),a=o.process,c=o.Deno,l=a&&a.versions||c&&c.version,f=l&&l.v8;f&&(i=(r=f.split("."))[0]>0&&r[0]<4?1:+(r[0]+r[1]));!i&&s&&(!(r=s.match(/Edge\/(\d+)/))||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/))&&(i=+r[1]);e.exports=i},e=>{e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},(e,t,n)=>{var r=n(31),i=n(17);e.exports=function(e,t){var n=e[t];return i(n)?void 0:r(n)}},(e,t,n)=>{var r=n(21),i=n(32),o=TypeError;e.exports=function(e){if(r(e))return e;throw o(i(e)+" is not a function")}},e=>{var t=String;e.exports=function(e){try{return t(e)}catch(e){return"Object"}}},(e,t,n)=>{var r=n(8),i=n(21),o=n(20),s=TypeError;e.exports=function(e,t){var n,a;if("string"===t&&i(n=e.toString)&&!o(a=r(n,e)))return a;if(i(n=e.valueOf)&&!o(a=r(n,e)))return a;if("string"!==t&&i(n=e.toString)&&!o(a=r(n,e)))return a;throw s("Can't convert object to primitive value")}},(e,t,n)=>{var r=n(4),i=n(35),o=n(39),s=n(41),a=n(27),c=n(26),l=r.Symbol,f=i("wks"),u=c?l.for||l:l&&l.withoutSetter||s;e.exports=function(e){o(f,e)||(f[e]=a&&o(l,e)?l[e]:u("Symbol."+e));return f[e]}},(e,t,n)=>{var r=n(36),i=n(37);(e.exports=function(e,t){return i[e]||(i[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.32.2",mode:r?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.2/LICENSE",source:"https://github.com/zloirock/core-js"})},e=>{e.exports=!1},(e,t,n)=>{var r=n(4),i=n(38),o="__core-js_shared__",s=r[o]||i(o,{});e.exports=s},(e,t,n)=>{var r=n(4),i=Object.defineProperty;e.exports=function(e,t){try{i(r,e,{value:t,configurable:!0,writable:!0})}catch(n){r[e]=t}return t}},(e,t,n)=>{var r=n(14),i=n(40),o=r({}.hasOwnProperty);e.exports=Object.hasOwn||function hasOwn(e,t){return o(i(e),t)}},(e,t,n)=>{var r=n(16),i=Object;e.exports=function(e){return i(r(e))}},(e,t,n)=>{var r=n(14),i=0,o=Math.random(),s=r(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+s(++i+o,36)}},(e,t,n)=>{var r=n(6),i=n(7),o=n(43);e.exports=!r&&!i((function(){return 7!==Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},(e,t,n)=>{var r=n(4),i=n(20),o=r.document,s=i(o)&&i(o.createElement);e.exports=function(e){return s?o.createElement(e):{}}},(e,t,n)=>{var r=n(6),i=n(45),o=n(11);e.exports=r?function(e,t,n){return i.f(e,t,o(1,n))}:function(e,t,n){e[t]=n;return e}},(e,t,n)=>{var r=n(6),i=n(42),o=n(46),s=n(47),a=n(18),c=TypeError,l=Object.defineProperty,f=Object.getOwnPropertyDescriptor,u="enumerable",h="configurable",d="writable";t.f=r?o?function defineProperty(e,t,n){s(e);t=a(t);s(n);if("function"==typeof e&&"prototype"===t&&"value"in n&&d in n&&!n[d]){var r=f(e,t);if(r&&r[d]){e[t]=n.value;n={configurable:h in n?n[h]:r[h],enumerable:u in n?n[u]:r[u],writable:!1}}}return l(e,t,n)}:l:function defineProperty(e,t,n){s(e);t=a(t);s(n);if(i)try{return l(e,t,n)}catch(e){}if("get"in n||"set"in n)throw c("Accessors not supported");"value"in n&&(e[t]=n.value);return e}},(e,t,n)=>{var r=n(6),i=n(7);e.exports=r&&i((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},(e,t,n)=>{var r=n(20),i=String,o=TypeError;e.exports=function(e){if(r(e))return e;throw o(i(e)+" is not an object")}},(e,t,n)=>{var r=n(21),i=n(45),o=n(49),s=n(38);e.exports=function(e,t,n,a){a||(a={});var c=a.enumerable,l=void 0!==a.name?a.name:t;r(n)&&o(n,l,a);if(a.global)c?e[t]=n:s(t,n);else{try{a.unsafe?e[t]&&(c=!0):delete e[t]}catch(e){}c?e[t]=n:i.f(e,t,{value:n,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},(e,t,n)=>{var r=n(14),i=n(7),o=n(21),s=n(39),a=n(6),c=n(50).CONFIGURABLE,l=n(51),f=n(52),u=f.enforce,h=f.get,d=String,p=Object.defineProperty,g=r("".slice),m=r("".replace),b=r([].join),y=a&&!i((function(){return 8!==p((function(){}),"length",{value:8}).length})),x=String(String).split("String"),w=e.exports=function(e,t,n){"Symbol("===g(d(t),0,7)&&(t="["+m(d(t),/^Symbol\(([^)]*)\)/,"$1")+"]");n&&n.getter&&(t="get "+t);n&&n.setter&&(t="set "+t);(!s(e,"name")||c&&e.name!==t)&&(a?p(e,"name",{value:t,configurable:!0}):e.name=t);y&&n&&s(n,"arity")&&e.length!==n.arity&&p(e,"length",{value:n.arity});try{n&&s(n,"constructor")&&n.constructor?a&&p(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(e){}var r=u(e);s(r,"source")||(r.source=b(x,"string"==typeof t?t:""));return e};Function.prototype.toString=w((function toString(){return o(this)&&h(this).source||l(this)}),"toString")},(e,t,n)=>{var r=n(6),i=n(39),o=Function.prototype,s=r&&Object.getOwnPropertyDescriptor,a=i(o,"name"),c=a&&"something"===function something(){}.name,l=a&&(!r||r&&s(o,"name").configurable);e.exports={EXISTS:a,PROPER:c,CONFIGURABLE:l}},(e,t,n)=>{var r=n(14),i=n(21),o=n(37),s=r(Function.toString);i(o.inspectSource)||(o.inspectSource=function(e){return s(e)});e.exports=o.inspectSource},(e,t,n)=>{var r,i,o,s=n(53),a=n(4),c=n(20),l=n(44),f=n(39),u=n(37),h=n(54),d=n(55),p="Object already initialized",g=a.TypeError,m=a.WeakMap;if(s||u.state){var b=u.state||(u.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;r=function(e,t){if(b.has(e))throw g(p);t.facade=e;b.set(e,t);return t};i=function(e){return b.get(e)||{}};o=function(e){return b.has(e)}}else{var y=h("state");d[y]=!0;r=function(e,t){if(f(e,y))throw g(p);t.facade=e;l(e,y,t);return t};i=function(e){return f(e,y)?e[y]:{}};o=function(e){return f(e,y)}}e.exports={set:r,get:i,has:o,enforce:function(e){return o(e)?i(e):r(e,{})},getterFor:function(e){return function(t){var n;if(!c(t)||(n=i(t)).type!==e)throw g("Incompatible receiver, "+e+" required");return n}}}},(e,t,n)=>{var r=n(4),i=n(21),o=r.WeakMap;e.exports=i(o)&&/native code/.test(String(o))},(e,t,n)=>{var r=n(35),i=n(41),o=r("keys");e.exports=function(e){return o[e]||(o[e]=i(e))}},e=>{e.exports={}},(e,t,n)=>{var r=n(39),i=n(57),o=n(5),s=n(45);e.exports=function(e,t,n){for(var a=i(t),c=s.f,l=o.f,f=0;f<a.length;f++){var u=a[f];r(e,u)||n&&r(n,u)||c(e,u,l(t,u))}}},(e,t,n)=>{var r=n(24),i=n(14),o=n(58),s=n(67),a=n(47),c=i([].concat);e.exports=r("Reflect","ownKeys")||function ownKeys(e){var t=o.f(a(e)),n=s.f;return n?c(t,n(e)):t}},(e,t,n)=>{var r=n(59),i=n(66).concat("length","prototype");t.f=Object.getOwnPropertyNames||function getOwnPropertyNames(e){return r(e,i)}},(e,t,n)=>{var r=n(14),i=n(39),o=n(12),s=n(60).indexOf,a=n(55),c=r([].push);e.exports=function(e,t){var n,r=o(e),l=0,f=[];for(n in r)!i(a,n)&&i(r,n)&&c(f,n);for(;t.length>l;)i(r,n=t[l++])&&(~s(f,n)||c(f,n));return f}},(e,t,n)=>{var r=n(12),i=n(61),o=n(64),createMethod=function(e){return function(t,n,s){var a,c=r(t),l=o(c),f=i(s,l);if(e&&n!=n){for(;l>f;)if((a=c[f++])!=a)return!0}else for(;l>f;f++)if((e||f in c)&&c[f]===n)return e||f||0;return!e&&-1}};e.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},(e,t,n)=>{var r=n(62),i=Math.max,o=Math.min;e.exports=function(e,t){var n=r(e);return n<0?i(n+t,0):o(n,t)}},(e,t,n)=>{var r=n(63);e.exports=function(e){var t=+e;return t!=t||0===t?0:r(t)}},e=>{var t=Math.ceil,n=Math.floor;e.exports=Math.trunc||function trunc(e){var r=+e;return(r>0?n:t)(r)}},(e,t,n)=>{var r=n(65);e.exports=function(e){return r(e.length)}},(e,t,n)=>{var r=n(62),i=Math.min;e.exports=function(e){return e>0?i(r(e),9007199254740991):0}},e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},(e,t)=>{t.f=Object.getOwnPropertySymbols},(e,t,n)=>{var r=n(7),i=n(21),o=/#|\.prototype\./,isForced=function(e,t){var n=a[s(e)];return n===l||n!==c&&(i(t)?r(t):!!t)},s=isForced.normalize=function(e){return String(e).replace(o,".").toLowerCase()},a=isForced.data={},c=isForced.NATIVE="N",l=isForced.POLYFILL="P";e.exports=isForced},(e,t,n)=>{var r=n(9),i=Function.prototype,o=i.apply,s=i.call;e.exports="object"==typeof Reflect&&Reflect.apply||(r?s.bind(o):function(){return s.apply(o,arguments)})},(e,t,n)=>{var r=n(24),i=n(39),o=n(44),s=n(25),a=n(71),c=n(56),l=n(74),f=n(75),u=n(76),h=n(80),d=n(81),p=n(6),g=n(36);e.exports=function(e,t,n,m){var b="stackTraceLimit",y=m?2:1,x=e.split("."),w=x[x.length-1],v=r.apply(null,x);if(v){var T=v.prototype;!g&&i(T,"cause")&&delete T.cause;if(!n)return v;var S=r("Error"),E=t((function(e,t){var n=u(m?t:e,void 0),r=m?new v(e):new v;void 0!==n&&o(r,"message",n);d(r,E,r.stack,2);this&&s(T,this)&&f(r,this,E);arguments.length>y&&h(r,arguments[y]);return r}));E.prototype=T;if("Error"!==w)a?a(E,S):c(E,S,{name:!0});else if(p&&b in v){l(E,v,b);l(E,v,"prepareStackTrace")}c(E,v);if(!g)try{T.name!==w&&o(T,"name",w);T.constructor=E}catch(e){}return E}}},(e,t,n)=>{var r=n(72),i=n(47),o=n(73);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,n={};try{(e=r(Object.prototype,"__proto__","set"))(n,[]);t=n instanceof Array}catch(e){}return function setPrototypeOf(n,r){i(n);o(r);t?e(n,r):n.__proto__=r;return n}}():void 0)},(e,t,n)=>{var r=n(14),i=n(31);e.exports=function(e,t,n){try{return r(i(Object.getOwnPropertyDescriptor(e,t)[n]))}catch(e){}}},(e,t,n)=>{var r=n(21),i=String,o=TypeError;e.exports=function(e){if("object"==typeof e||r(e))return e;throw o("Can't set "+i(e)+" as a prototype")}},(e,t,n)=>{var r=n(45).f;e.exports=function(e,t,n){n in e||r(e,n,{configurable:!0,get:function(){return t[n]},set:function(e){t[n]=e}})}},(e,t,n)=>{var r=n(21),i=n(20),o=n(71);e.exports=function(e,t,n){var s,a;o&&r(s=t.constructor)&&s!==n&&i(a=s.prototype)&&a!==n.prototype&&o(e,a);return e}},(e,t,n)=>{var r=n(77);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:r(e)}},(e,t,n)=>{var r=n(78),i=String;e.exports=function(e){if("Symbol"===r(e))throw TypeError("Cannot convert a Symbol value to a string");return i(e)}},(e,t,n)=>{var r=n(79),i=n(21),o=n(15),s=n(34)("toStringTag"),a=Object,c="Arguments"===o(function(){return arguments}());e.exports=r?o:function(e){var t,n,r;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(n=function(e,t){try{return e[t]}catch(e){}}(t=a(e),s))?n:c?o(t):"Object"===(r=o(t))&&i(t.callee)?"Arguments":r}},(e,t,n)=>{var r={};r[n(34)("toStringTag")]="z";e.exports="[object z]"===String(r)},(e,t,n)=>{var r=n(20),i=n(44);e.exports=function(e,t){r(t)&&"cause"in t&&i(e,"cause",t.cause)}},(e,t,n)=>{var r=n(44),i=n(82),o=n(83),s=Error.captureStackTrace;e.exports=function(e,t,n,a){o&&(s?s(e,t):r(e,"stack",i(n,a)))}},(e,t,n)=>{var r=n(14),i=Error,o=r("".replace),s=String(i("zxcasd").stack),a=/\n\s*at [^:]*:[^\n]*/,c=a.test(s);e.exports=function(e,t){if(c&&"string"==typeof e&&!i.prepareStackTrace)for(;t--;)e=o(e,a,"");return e}},(e,t,n)=>{var r=n(7),i=n(11);e.exports=!r((function(){var e=Error("a");if(!("stack"in e))return!0;Object.defineProperty(e,"stack",i(1,7));return 7!==e.stack}))},(e,t,n)=>{var r=n(48),i=n(14),o=n(77),s=n(85),a=URLSearchParams,c=a.prototype,l=i(c.append),f=i(c.delete),u=i(c.forEach),h=i([].push),d=new a("a=1&a=2&b=3");d.delete("a",1);d.delete("b",void 0);d+""!="a=2"&&r(c,"delete",(function(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return f(this,e);var r=[];u(this,(function(e,t){h(r,{key:t,value:e})}));s(t,1);for(var i,a=o(e),c=o(n),d=0,p=0,g=!1,m=r.length;d<m;){i=r[d++];if(g||i.key===a){g=!0;f(this,i.key)}else p++}for(;p<m;)(i=r[p++]).key===a&&i.value===c||l(this,i.key,i.value)}),{enumerable:!0,unsafe:!0})},e=>{var t=TypeError;e.exports=function(e,n){if(e<n)throw t("Not enough arguments");return e}},(e,t,n)=>{var r=n(48),i=n(14),o=n(77),s=n(85),a=URLSearchParams,c=a.prototype,l=i(c.getAll),f=i(c.has),u=new a("a=1");!u.has("a",2)&&u.has("a",void 0)||r(c,"has",(function has(e){var t=arguments.length,n=t<2?void 0:arguments[1];if(t&&void 0===n)return f(this,e);var r=l(this,e);s(t,1);for(var i=o(n),a=0;a<r.length;)if(r[a++]===i)return!0;return!1}),{enumerable:!0,unsafe:!0})},(e,t,n)=>{var r=n(6),i=n(14),o=n(88),s=URLSearchParams.prototype,a=i(s.forEach);r&&!("size"in s)&&o(s,"size",{get:function size(){var e=0;a(this,(function(){e++}));return e},configurable:!0,enumerable:!0})},(e,t,n)=>{var r=n(49),i=n(45);e.exports=function(e,t,n){n.get&&r(n.get,t,{getter:!0});n.set&&r(n.set,t,{setter:!0});return i.f(e,t,n)}},(e,t,n)=>{var r=n(3),i=n(40),o=n(64),s=n(90),a=n(92);r({target:"Array",proto:!0,arity:1,forced:n(7)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}}()},{push:function push(e){var t=i(this),n=o(t),r=arguments.length;a(n+r);for(var c=0;c<r;c++){t[n]=arguments[c];n++}s(t,n);return n}})},(e,t,n)=>{var r=n(6),i=n(91),o=TypeError,s=Object.getOwnPropertyDescriptor,a=r&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(i(e)&&!s(e,"length").writable)throw o("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},(e,t,n)=>{var r=n(15);e.exports=Array.isArray||function isArray(e){return"Array"===r(e)}},e=>{var t=TypeError;e.exports=function(e){if(e>9007199254740991)throw t("Maximum allowed index exceeded");return e}},(e,t,n)=>{var r=n(94),i=n(98).findLast,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("findLast",(function findLast(e){return i(o(this),e,arguments.length>1?arguments[1]:void 0)}))},(e,t,n)=>{var r,i,o,s=n(95),a=n(6),c=n(4),l=n(21),f=n(20),u=n(39),h=n(78),d=n(32),p=n(44),g=n(48),m=n(88),b=n(25),y=n(96),x=n(71),w=n(34),v=n(41),T=n(52),S=T.enforce,E=T.get,I=c.Int8Array,C=I&&I.prototype,P=c.Uint8ClampedArray,A=P&&P.prototype,k=I&&y(I),B=C&&y(C),_=Object.prototype,O=c.TypeError,R=w("toStringTag"),M=v("TYPED_ARRAY_TAG"),L="TypedArrayConstructor",D=s&&!!x&&"Opera"!==h(c.opera),U=!1,N={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},j={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(e){var t=y(e);if(f(t)){var n=E(t);return n&&u(n,L)?n[L]:getTypedArrayConstructor(t)}},isTypedArray=function(e){if(!f(e))return!1;var t=h(e);return u(N,t)||u(j,t)};for(r in N)(o=(i=c[r])&&i.prototype)?S(o)[L]=i:D=!1;for(r in j)(o=(i=c[r])&&i.prototype)&&(S(o)[L]=i);if(!D||!l(k)||k===Function.prototype){k=function TypedArray(){throw O("Incorrect invocation")};if(D)for(r in N)c[r]&&x(c[r],k)}if(!D||!B||B===_){B=k.prototype;if(D)for(r in N)c[r]&&x(c[r].prototype,B)}D&&y(A)!==B&&x(A,B);if(a&&!u(B,R)){U=!0;m(B,R,{configurable:!0,get:function(){return f(this)?this[M]:void 0}});for(r in N)c[r]&&p(c[r],M,r)}e.exports={NATIVE_ARRAY_BUFFER_VIEWS:D,TYPED_ARRAY_TAG:U&&M,aTypedArray:function(e){if(isTypedArray(e))return e;throw O("Target is not a typed array")},aTypedArrayConstructor:function(e){if(l(e)&&(!x||b(k,e)))return e;throw O(d(e)+" is not a typed array constructor")},exportTypedArrayMethod:function(e,t,n,r){if(a){if(n)for(var i in N){var o=c[i];if(o&&u(o.prototype,e))try{delete o.prototype[e]}catch(n){try{o.prototype[e]=t}catch(e){}}}B[e]&&!n||g(B,e,n?t:D&&C[e]||t,r)}},exportTypedArrayStaticMethod:function(e,t,n){var r,i;if(a){if(x){if(n)for(r in N)if((i=c[r])&&u(i,e))try{delete i[e]}catch(e){}if(k[e]&&!n)return;try{return g(k,e,n?t:D&&k[e]||t)}catch(e){}}for(r in N)!(i=c[r])||i[e]&&!n||g(i,e,t)}},getTypedArrayConstructor:getTypedArrayConstructor,isView:function isView(e){if(!f(e))return!1;var t=h(e);return"DataView"===t||u(N,t)||u(j,t)},isTypedArray:isTypedArray,TypedArray:k,TypedArrayPrototype:B}},e=>{e.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},(e,t,n)=>{var r=n(39),i=n(21),o=n(40),s=n(54),a=n(97),c=s("IE_PROTO"),l=Object,f=l.prototype;e.exports=a?l.getPrototypeOf:function(e){var t=o(e);if(r(t,c))return t[c];var n=t.constructor;return i(n)&&t instanceof n?n.prototype:t instanceof l?f:null}},(e,t,n)=>{var r=n(7);e.exports=!r((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},(e,t,n)=>{var r=n(99),i=n(13),o=n(40),s=n(64),createMethod=function(e){var t=1===e;return function(n,a,c){for(var l,f=o(n),u=i(f),h=r(a,c),d=s(u);d-- >0;)if(h(l=u[d],d,f))switch(e){case 0:return l;case 1:return d}return t?-1:void 0}};e.exports={findLast:createMethod(0),findLastIndex:createMethod(1)}},(e,t,n)=>{var r=n(100),i=n(31),o=n(9),s=r(r.bind);e.exports=function(e,t){i(e);return void 0===t?e:o?s(e,t):function(){return e.apply(t,arguments)}}},(e,t,n)=>{var r=n(15),i=n(14);e.exports=function(e){if("Function"===r(e))return i(e)}},(e,t,n)=>{var r=n(94),i=n(98).findLastIndex,o=r.aTypedArray;(0,r.exportTypedArrayMethod)("findLastIndex",(function findLastIndex(e){return i(o(this),e,arguments.length>1?arguments[1]:void 0)}))},(e,t,n)=>{var r=n(4),i=n(8),o=n(94),s=n(64),a=n(103),c=n(40),l=n(7),f=r.RangeError,u=r.Int8Array,h=u&&u.prototype,d=h&&h.set,p=o.aTypedArray,g=o.exportTypedArrayMethod,m=!l((function(){var e=new Uint8ClampedArray(2);i(d,e,{length:1,0:3},1);return 3!==e[1]})),b=m&&o.NATIVE_ARRAY_BUFFER_VIEWS&&l((function(){var e=new u(2);e.set(1);e.set("2",1);return 0!==e[0]||2!==e[1]}));g("set",(function set(e){p(this);var t=a(arguments.length>1?arguments[1]:void 0,1),n=c(e);if(m)return i(d,this,n,t);var r=this.length,o=s(n),l=0;if(o+t>r)throw f("Wrong length");for(;l<o;)this[t+l]=n[l++]}),!m||b)},(e,t,n)=>{var r=n(104),i=RangeError;e.exports=function(e,t){var n=r(e);if(n%t)throw i("Wrong offset");return n}},(e,t,n)=>{var r=n(62),i=RangeError;e.exports=function(e){var t=r(e);if(t<0)throw i("The argument can't be less than 0");return t}},(e,t,n)=>{var r=n(106),i=n(94),o=i.aTypedArray,s=i.exportTypedArrayMethod,a=i.getTypedArrayConstructor;s("toReversed",(function toReversed(){return r(o(this),a(this))}))},(e,t,n)=>{var r=n(64);e.exports=function(e,t){for(var n=r(e),i=new t(n),o=0;o<n;o++)i[o]=e[n-o-1];return i}},(e,t,n)=>{var r=n(94),i=n(14),o=n(31),s=n(108),a=r.aTypedArray,c=r.getTypedArrayConstructor,l=r.exportTypedArrayMethod,f=i(r.TypedArrayPrototype.sort);l("toSorted",(function toSorted(e){void 0!==e&&o(e);var t=a(this),n=s(c(t),t);return f(n,e)}))},(e,t,n)=>{var r=n(64);e.exports=function(e,t){for(var n=0,i=r(t),o=new e(i);i>n;)o[n]=t[n++];return o}},(e,t,n)=>{var r=n(110),i=n(94),o=n(111),s=n(62),a=n(112),c=i.aTypedArray,l=i.getTypedArrayConstructor,f=i.exportTypedArrayMethod,u=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(e){return 8===e}}();f("with",{with:function(e,t){var n=c(this),i=s(e),f=o(n)?a(t):+t;return r(n,l(n),i,f)}}.with,!u)},(e,t,n)=>{var r=n(64),i=n(62),o=RangeError;e.exports=function(e,t,n,s){var a=r(e),c=i(n),l=c<0?a+c:c;if(l>=a||l<0)throw o("Incorrect index");for(var f=new t(a),u=0;u<a;u++)f[u]=u===l?s:e[u];return f}},(e,t,n)=>{var r=n(78);e.exports=function(e){var t=r(e);return"BigInt64Array"===t||"BigUint64Array"===t}},(e,t,n)=>{var r=n(19),i=TypeError;e.exports=function(e){var t=r(e,"number");if("number"==typeof t)throw i("Can't convert number to bigint");return BigInt(t)}},(e,t,n)=>{var r=n(6),i=n(88),o=n(114),s=ArrayBuffer.prototype;r&&!("detached"in s)&&i(s,"detached",{configurable:!0,get:function detached(){return o(this)}})},(e,t,n)=>{var r=n(14),i=n(115),o=r(ArrayBuffer.prototype.slice);e.exports=function(e){if(0!==i(e))return!1;try{o(e,0,0);return!1}catch(e){return!0}}},(e,t,n)=>{var r=n(72),i=n(15),o=TypeError;e.exports=r(ArrayBuffer.prototype,"byteLength","get")||function(e){if("ArrayBuffer"!==i(e))throw o("ArrayBuffer expected");return e.byteLength}},(e,t,n)=>{var r=n(3),i=n(117);i&&r({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return i(this,arguments.length?arguments[0]:void 0,!0)}})},(e,t,n)=>{var r=n(4),i=n(14),o=n(72),s=n(118),a=n(114),c=n(115),l=n(119),f=r.TypeError,u=r.structuredClone,h=r.ArrayBuffer,d=r.DataView,p=Math.min,g=h.prototype,m=d.prototype,b=i(g.slice),y=o(g,"resizable","get"),x=o(g,"maxByteLength","get"),w=i(m.getInt8),v=i(m.setInt8);e.exports=l&&function(e,t,n){var r=c(e),i=void 0===t?r:s(t),o=!y||!y(e);if(a(e))throw f("ArrayBuffer is detached");var l=u(e,{transfer:[e]});if(r===i&&(n||o))return l;if(r>=i&&(!n||o))return b(l,0,i);for(var g=n&&!o&&x?{maxByteLength:x(l)}:void 0,m=new h(i,g),T=new d(l),S=new d(m),E=p(i,r),I=0;I<E;I++)v(S,I,w(T,I));return m}},(e,t,n)=>{var r=n(62),i=n(65),o=RangeError;e.exports=function(e){if(void 0===e)return 0;var t=r(e),n=i(t);if(t!==n)throw o("Wrong length or index");return n}},(e,t,n)=>{var r=n(4),i=n(7),o=n(28),s=n(120),a=n(121),c=n(122),l=r.structuredClone;e.exports=!!l&&!i((function(){if(a&&o>92||c&&o>94||s&&o>97)return!1;var e=new ArrayBuffer(8),t=l(e,{transfer:[e]});return 0!==e.byteLength||8!==t.byteLength}))},(e,t,n)=>{var r=n(121),i=n(122);e.exports=!r&&!i&&"object"==typeof window&&"object"==typeof document},e=>{e.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},(e,t,n)=>{var r=n(4),i=n(15);e.exports="process"===i(r.process)},(e,t,n)=>{var r=n(3),i=n(117);i&&r({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return i(this,arguments.length?arguments[0]:void 0,!1)}})},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.Jbig2Image=void 0;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);var r=n(1),i=n(125),o=n(152),s=n(153);class Jbig2Error extends r.BaseException{constructor(e){super(`JBIG2 error: ${e}`,"Jbig2Error")}}class ContextCache{getContexts(e){return e in this?this[e]:this[e]=new Int8Array(65536)}}class DecodingContext{constructor(e,t,n){this.data=e;this.start=t;this.end=n}get decoder(){const e=new o.ArithmeticDecoder(this.data,this.start,this.end);return(0,r.shadow)(this,"decoder",e)}get contextCache(){const e=new ContextCache;return(0,r.shadow)(this,"contextCache",e)}}const a=2**31-1,c=-(2**31);function decodeInteger(e,t,n){const r=e.getContexts(t);let i=1;function readBits(e){let t=0;for(let o=0;o<e;o++){const e=n.readBit(r,i);i=i<256?i<<1|e:511&(i<<1|e)|256;t=t<<1|e}return t>>>0}const o=readBits(1),s=readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(1)?readBits(32)+4436:readBits(12)+340:readBits(8)+84:readBits(6)+20:readBits(4)+4:readBits(2);let l;0===o?l=s:s>0&&(l=-s);return l>=c&&l<=a?l:null}function decodeIAID(e,t,n){const r=e.getContexts("IAID");let i=1;for(let e=0;e<n;e++){i=i<<1|t.readBit(r,i)}return n<31?i&(1<<n)-1:2147483647&i}const l=["SymbolDictionary",null,null,null,"IntermediateTextRegion",null,"ImmediateTextRegion","ImmediateLosslessTextRegion",null,null,null,null,null,null,null,null,"PatternDictionary",null,null,null,"IntermediateHalftoneRegion",null,"ImmediateHalftoneRegion","ImmediateLosslessHalftoneRegion",null,null,null,null,null,null,null,null,null,null,null,null,"IntermediateGenericRegion",null,"ImmediateGenericRegion","ImmediateLosslessGenericRegion","IntermediateGenericRefinementRegion",null,"ImmediateGenericRefinementRegion","ImmediateLosslessGenericRefinementRegion",null,null,null,null,"PageInformation","EndOfPage","EndOfStripe","EndOfFile","Profiles","Tables",null,null,null,null,null,null,null,null,"Extension"],f=[[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:2,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:2,y:-1},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}],[{x:-1,y:-2},{x:0,y:-2},{x:1,y:-2},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-2,y:0},{x:-1,y:0}],[{x:-3,y:-1},{x:-2,y:-1},{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-4,y:0},{x:-3,y:0},{x:-2,y:0},{x:-1,y:0}]],u=[{coding:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:1,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:-1,y:1},{x:0,y:1},{x:1,y:1}]},{coding:[{x:-1,y:-1},{x:0,y:-1},{x:1,y:-1},{x:-1,y:0}],reference:[{x:0,y:-1},{x:-1,y:0},{x:0,y:0},{x:1,y:0},{x:0,y:1},{x:1,y:1}]}],h=[39717,1941,229,405],d=[32,8];function decodeBitmap(e,t,n,r,i,o,s,a){if(e){return decodeMMRBitmap(new Reader(a.data,a.start,a.end),t,n,!1)}if(0===r&&!o&&!i&&4===s.length&&3===s[0].x&&-1===s[0].y&&-3===s[1].x&&-1===s[1].y&&2===s[2].x&&-2===s[2].y&&-2===s[3].x&&-2===s[3].y)return function decodeBitmapTemplate0(e,t,n){const r=n.decoder,i=n.contextCache.getContexts("GB"),o=[];let s,a,c,l,f,u,h;for(a=0;a<t;a++){f=o[a]=new Uint8Array(e);u=a<1?f:o[a-1];h=a<2?f:o[a-2];s=h[0]<<13|h[1]<<12|h[2]<<11|u[0]<<7|u[1]<<6|u[2]<<5|u[3]<<4;for(c=0;c<e;c++){f[c]=l=r.readBit(i,s);s=(31735&s)<<1|(c+3<e?h[c+3]<<11:0)|(c+4<e?u[c+4]<<4:0)|l}}return o}(t,n,a);const c=!!o,l=f[r].concat(s);l.sort((function(e,t){return e.y-t.y||e.x-t.x}));const u=l.length,d=new Int8Array(u),p=new Int8Array(u),g=[];let m,b,y=0,x=0,w=0,v=0;for(b=0;b<u;b++){d[b]=l[b].x;p[b]=l[b].y;x=Math.min(x,l[b].x);w=Math.max(w,l[b].x);v=Math.min(v,l[b].y);b<u-1&&l[b].y===l[b+1].y&&l[b].x===l[b+1].x-1?y|=1<<u-1-b:g.push(b)}const T=g.length,S=new Int8Array(T),E=new Int8Array(T),I=new Uint16Array(T);for(m=0;m<T;m++){b=g[m];S[m]=l[b].x;E[m]=l[b].y;I[m]=1<<u-1-b}const C=-x,P=-v,A=t-w,k=h[r];let B=new Uint8Array(t);const _=[],O=a.decoder,R=a.contextCache.getContexts("GB");let M,L,D,U,N,j=0,H=0;for(let e=0;e<n;e++){if(i){j^=O.readBit(R,k);if(j){_.push(B);continue}}B=new Uint8Array(B);_.push(B);for(M=0;M<t;M++){if(c&&o[e][M]){B[M]=0;continue}if(M>=C&&M<A&&e>=P){H=H<<1&y;for(b=0;b<T;b++){L=e+E[b];D=M+S[b];U=_[L][D];if(U){U=I[b];H|=U}}}else{H=0;N=u-1;for(b=0;b<u;b++,N--){D=M+d[b];if(D>=0&&D<t){L=e+p[b];if(L>=0){U=_[L][D];U&&(H|=U<<N)}}}}const n=O.readBit(R,H);B[M]=n}}return _}function decodeRefinement(e,t,n,r,i,o,s,a,c){let l=u[n].coding;0===n&&(l=l.concat([a[0]]));const f=l.length,h=new Int32Array(f),p=new Int32Array(f);let g;for(g=0;g<f;g++){h[g]=l[g].x;p[g]=l[g].y}let m=u[n].reference;0===n&&(m=m.concat([a[1]]));const b=m.length,y=new Int32Array(b),x=new Int32Array(b);for(g=0;g<b;g++){y[g]=m[g].x;x[g]=m[g].y}const w=r[0].length,v=r.length,T=d[n],S=[],E=c.decoder,I=c.contextCache.getContexts("GR");let C=0;for(let n=0;n<t;n++){if(s){C^=E.readBit(I,T);if(C)throw new Jbig2Error("prediction is not supported")}const t=new Uint8Array(e);S.push(t);for(let s=0;s<e;s++){let a,c,l=0;for(g=0;g<f;g++){a=n+p[g];c=s+h[g];a<0||c<0||c>=e?l<<=1:l=l<<1|S[a][c]}for(g=0;g<b;g++){a=n+x[g]-o;c=s+y[g]-i;a<0||a>=v||c<0||c>=w?l<<=1:l=l<<1|r[a][c]}const u=E.readBit(I,l);t[s]=u}}return S}function decodeTextRegion(e,t,n,r,i,o,s,a,c,l,f,u,h,d,p,g,m,b,y){if(e&&t)throw new Jbig2Error("refinement with Huffman is not supported");const x=[];let w,v;for(w=0;w<r;w++){v=new Uint8Array(n);if(i)for(let e=0;e<n;e++)v[e]=i;x.push(v)}const T=m.decoder,S=m.contextCache;let E=e?-d.tableDeltaT.decode(y):-decodeInteger(S,"IADT",T),I=0;w=0;for(;w<o;){E+=e?d.tableDeltaT.decode(y):decodeInteger(S,"IADT",T);I+=e?d.tableFirstS.decode(y):decodeInteger(S,"IAFS",T);let r=I;for(;;){let i=0;s>1&&(i=e?y.readBits(b):decodeInteger(S,"IAIT",T));const o=s*E+i,I=e?d.symbolIDTable.decode(y):decodeIAID(S,T,c),C=t&&(e?y.readBit():decodeInteger(S,"IARI",T));let P=a[I],A=P[0].length,k=P.length;if(C){const e=decodeInteger(S,"IARDW",T),t=decodeInteger(S,"IARDH",T);A+=e;k+=t;P=decodeRefinement(A,k,p,P,(e>>1)+decodeInteger(S,"IARDX",T),(t>>1)+decodeInteger(S,"IARDY",T),!1,g,m)}const B=o-(1&u?0:k-1),_=r-(2&u?A-1:0);let O,R,M;if(l){for(O=0;O<k;O++){v=x[_+O];if(!v)continue;M=P[O];const e=Math.min(n-B,A);switch(h){case 0:for(R=0;R<e;R++)v[B+R]|=M[R];break;case 2:for(R=0;R<e;R++)v[B+R]^=M[R];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}r+=k-1}else{for(R=0;R<k;R++){v=x[B+R];if(v){M=P[R];switch(h){case 0:for(O=0;O<A;O++)v[_+O]|=M[O];break;case 2:for(O=0;O<A;O++)v[_+O]^=M[O];break;default:throw new Jbig2Error(`operator ${h} is not supported`)}}}r+=A-1}w++;const L=e?d.tableDeltaS.decode(y):decodeInteger(S,"IADS",T);if(null===L)break;r+=L+f}}return x}function readSegmentHeader(e,t){const n={};n.number=(0,i.readUint32)(e,t);const r=e[t+4],o=63&r;if(!l[o])throw new Jbig2Error("invalid segment type: "+o);n.type=o;n.typeName=l[o];n.deferredNonRetain=!!(128&r);const s=!!(64&r),a=e[t+5];let c=a>>5&7;const f=[31&a];let u=t+6;if(7===a){c=536870911&(0,i.readUint32)(e,u-1);u+=3;let t=c+7>>3;f[0]=e[u++];for(;--t>0;)f.push(e[u++])}else if(5===a||6===a)throw new Jbig2Error("invalid referred-to flags");n.retainBits=f;let h=4;n.number<=256?h=1:n.number<=65536&&(h=2);const d=[];let g,m;for(g=0;g<c;g++){let t;t=1===h?e[u]:2===h?(0,i.readUint16)(e,u):(0,i.readUint32)(e,u);d.push(t);u+=h}n.referredTo=d;if(s){n.pageAssociation=(0,i.readUint32)(e,u);u+=4}else n.pageAssociation=e[u++];n.length=(0,i.readUint32)(e,u);u+=4;if(4294967295===n.length){if(38!==o)throw new Jbig2Error("invalid unknown segment length");{const t=readRegionSegmentInformation(e,u),r=!!(1&e[u+p]),i=6,o=new Uint8Array(i);if(!r){o[0]=255;o[1]=172}o[2]=t.height>>>24&255;o[3]=t.height>>16&255;o[4]=t.height>>8&255;o[5]=255&t.height;for(g=u,m=e.length;g<m;g++){let t=0;for(;t<i&&o[t]===e[g+t];)t++;if(t===i){n.length=g+i;break}}if(4294967295===n.length)throw new Jbig2Error("segment end was not found")}}n.headerEnd=u;return n}function readSegments(e,t,n,r){const i=[];let o=n;for(;o<r;){const n=readSegmentHeader(t,o);o=n.headerEnd;const r={header:n,data:t};if(!e.randomAccess){r.start=o;o+=n.length;r.end=o}i.push(r);if(51===n.type)break}if(e.randomAccess)for(let e=0,t=i.length;e<t;e++){i[e].start=o;o+=i[e].header.length;i[e].end=o}return i}function readRegionSegmentInformation(e,t){return{width:(0,i.readUint32)(e,t),height:(0,i.readUint32)(e,t+4),x:(0,i.readUint32)(e,t+8),y:(0,i.readUint32)(e,t+12),combinationOperator:7&e[t+16]}}const p=17;function processSegment(e,t){const n=e.header,r=e.data,o=e.end;let s,a,c,l,f=e.start;switch(n.type){case 0:const e={},t=(0,i.readUint16)(r,f);e.huffman=!!(1&t);e.refinement=!!(2&t);e.huffmanDHSelector=t>>2&3;e.huffmanDWSelector=t>>4&3;e.bitmapSizeSelector=t>>6&1;e.aggregationInstancesSelector=t>>7&1;e.bitmapCodingContextUsed=!!(256&t);e.bitmapCodingContextRetained=!!(512&t);e.template=t>>10&3;e.refinementTemplate=t>>12&1;f+=2;if(!e.huffman){l=0===e.template?4:1;a=[];for(c=0;c<l;c++){a.push({x:(0,i.readInt8)(r,f),y:(0,i.readInt8)(r,f+1)});f+=2}e.at=a}if(e.refinement&&!e.refinementTemplate){a=[];for(c=0;c<2;c++){a.push({x:(0,i.readInt8)(r,f),y:(0,i.readInt8)(r,f+1)});f+=2}e.refinementAt=a}e.numberOfExportedSymbols=(0,i.readUint32)(r,f);f+=4;e.numberOfNewSymbols=(0,i.readUint32)(r,f);f+=4;s=[e,n.number,n.referredTo,r,f,o];break;case 6:case 7:const u={};u.info=readRegionSegmentInformation(r,f);f+=p;const h=(0,i.readUint16)(r,f);f+=2;u.huffman=!!(1&h);u.refinement=!!(2&h);u.logStripSize=h>>2&3;u.stripSize=1<<u.logStripSize;u.referenceCorner=h>>4&3;u.transposed=!!(64&h);u.combinationOperator=h>>7&3;u.defaultPixelValue=h>>9&1;u.dsOffset=h<<17>>27;u.refinementTemplate=h>>15&1;if(u.huffman){const e=(0,i.readUint16)(r,f);f+=2;u.huffmanFS=3&e;u.huffmanDS=e>>2&3;u.huffmanDT=e>>4&3;u.huffmanRefinementDW=e>>6&3;u.huffmanRefinementDH=e>>8&3;u.huffmanRefinementDX=e>>10&3;u.huffmanRefinementDY=e>>12&3;u.huffmanRefinementSizeSelector=!!(16384&e)}if(u.refinement&&!u.refinementTemplate){a=[];for(c=0;c<2;c++){a.push({x:(0,i.readInt8)(r,f),y:(0,i.readInt8)(r,f+1)});f+=2}u.refinementAt=a}u.numberOfSymbolInstances=(0,i.readUint32)(r,f);f+=4;s=[u,n.referredTo,r,f,o];break;case 16:const d={},g=r[f++];d.mmr=!!(1&g);d.template=g>>1&3;d.patternWidth=r[f++];d.patternHeight=r[f++];d.maxPatternIndex=(0,i.readUint32)(r,f);f+=4;s=[d,n.number,r,f,o];break;case 22:case 23:const m={};m.info=readRegionSegmentInformation(r,f);f+=p;const b=r[f++];m.mmr=!!(1&b);m.template=b>>1&3;m.enableSkip=!!(8&b);m.combinationOperator=b>>4&7;m.defaultPixelValue=b>>7&1;m.gridWidth=(0,i.readUint32)(r,f);f+=4;m.gridHeight=(0,i.readUint32)(r,f);f+=4;m.gridOffsetX=4294967295&(0,i.readUint32)(r,f);f+=4;m.gridOffsetY=4294967295&(0,i.readUint32)(r,f);f+=4;m.gridVectorX=(0,i.readUint16)(r,f);f+=2;m.gridVectorY=(0,i.readUint16)(r,f);f+=2;s=[m,n.referredTo,r,f,o];break;case 38:case 39:const y={};y.info=readRegionSegmentInformation(r,f);f+=p;const x=r[f++];y.mmr=!!(1&x);y.template=x>>1&3;y.prediction=!!(8&x);if(!y.mmr){l=0===y.template?4:1;a=[];for(c=0;c<l;c++){a.push({x:(0,i.readInt8)(r,f),y:(0,i.readInt8)(r,f+1)});f+=2}y.at=a}s=[y,r,f,o];break;case 48:const w={width:(0,i.readUint32)(r,f),height:(0,i.readUint32)(r,f+4),resolutionX:(0,i.readUint32)(r,f+8),resolutionY:(0,i.readUint32)(r,f+12)};4294967295===w.height&&delete w.height;const v=r[f+16];(0,i.readUint16)(r,f+17);w.lossless=!!(1&v);w.refinement=!!(2&v);w.defaultPixelValue=v>>2&1;w.combinationOperator=v>>3&3;w.requiresBuffer=!!(32&v);w.combinationOperatorOverride=!!(64&v);s=[w];break;case 49:case 50:case 51:case 62:break;case 53:s=[n.number,r,f,o];break;default:throw new Jbig2Error(`segment type ${n.typeName}(${n.type}) is not implemented`)}const u="on"+n.typeName;u in t&&t[u].apply(t,s)}function processSegments(e,t){for(let n=0,r=e.length;n<r;n++)processSegment(e[n],t)}class SimpleSegmentVisitor{onPageInformation(e){this.currentPageInfo=e;const t=e.width+7>>3,n=new Uint8ClampedArray(t*e.height);e.defaultPixelValue&&n.fill(255);this.buffer=n}drawBitmap(e,t){const n=this.currentPageInfo,r=e.width,i=e.height,o=n.width+7>>3,s=n.combinationOperatorOverride?e.combinationOperator:n.combinationOperator,a=this.buffer,c=128>>(7&e.x);let l,f,u,h,d=e.y*o+(e.x>>3);switch(s){case 0:for(l=0;l<i;l++){u=c;h=d;for(f=0;f<r;f++){t[l][f]&&(a[h]|=u);u>>=1;if(!u){u=128;h++}}d+=o}break;case 2:for(l=0;l<i;l++){u=c;h=d;for(f=0;f<r;f++){t[l][f]&&(a[h]^=u);u>>=1;if(!u){u=128;h++}}d+=o}break;default:throw new Jbig2Error(`operator ${s} is not supported`)}}onImmediateGenericRegion(e,t,n,r){const i=e.info,o=new DecodingContext(t,n,r),s=decodeBitmap(e.mmr,i.width,i.height,e.template,e.prediction,null,e.at,o);this.drawBitmap(i,s)}onImmediateLosslessGenericRegion(){this.onImmediateGenericRegion(...arguments)}onSymbolDictionary(e,t,n,r,o,s){let a,c;if(e.huffman){a=function getSymbolDictionaryHuffmanTables(e,t,n){let r,i,o,s,a=0;switch(e.huffmanDHSelector){case 0:case 1:r=getStandardTable(e.huffmanDHSelector+4);break;case 3:r=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DH selector")}switch(e.huffmanDWSelector){case 0:case 1:i=getStandardTable(e.huffmanDWSelector+2);break;case 3:i=getCustomHuffmanTable(a,t,n);a++;break;default:throw new Jbig2Error("invalid Huffman DW selector")}if(e.bitmapSizeSelector){o=getCustomHuffmanTable(a,t,n);a++}else o=getStandardTable(1);s=e.aggregationInstancesSelector?getCustomHuffmanTable(a,t,n):getStandardTable(1);return{tableDeltaHeight:r,tableDeltaWidth:i,tableBitmapSize:o,tableAggregateInstances:s}}(e,n,this.customTables);c=new Reader(r,o,s)}let l=this.symbols;l||(this.symbols=l={});const f=[];for(const e of n){const t=l[e];t&&f.push(...t)}const u=new DecodingContext(r,o,s);l[t]=function decodeSymbolDictionary(e,t,n,r,o,s,a,c,l,f,u,h){if(e&&t)throw new Jbig2Error("symbol refinement with Huffman is not supported");const d=[];let p=0,g=(0,i.log2)(n.length+r);const m=u.decoder,b=u.contextCache;let y,x;if(e){y=getStandardTable(1);x=[];g=Math.max(g,1)}for(;d.length<r;){p+=e?s.tableDeltaHeight.decode(h):decodeInteger(b,"IADH",m);let r=0,i=0;const o=e?x.length:0;for(;;){const o=e?s.tableDeltaWidth.decode(h):decodeInteger(b,"IADW",m);if(null===o)break;r+=o;i+=r;let y;if(t){const i=decodeInteger(b,"IAAI",m);if(i>1)y=decodeTextRegion(e,t,r,p,0,i,1,n.concat(d),g,0,0,1,0,s,l,f,u,0,h);else{const e=decodeIAID(b,m,g),t=decodeInteger(b,"IARDX",m),i=decodeInteger(b,"IARDY",m);y=decodeRefinement(r,p,l,e<n.length?n[e]:d[e-n.length],t,i,!1,f,u)}d.push(y)}else if(e)x.push(r);else{y=decodeBitmap(!1,r,p,a,!1,null,c,u);d.push(y)}}if(e&&!t){const e=s.tableBitmapSize.decode(h);h.byteAlign();let t;if(0===e)t=readUncompressedBitmap(h,i,p);else{const n=h.end,r=h.position+e;h.end=r;t=decodeMMRBitmap(h,i,p,!1);h.end=n;h.position=r}const n=x.length;if(o===n-1)d.push(t);else{let e,r,i,s,a,c=0;for(e=o;e<n;e++){s=x[e];i=c+s;a=[];for(r=0;r<p;r++)a.push(t[r].subarray(c,i));d.push(a);c=i}}}}const w=[],v=[];let T,S,E=!1;const I=n.length+r;for(;v.length<I;){let t=e?y.decode(h):decodeInteger(b,"IAEX",m);for(;t--;)v.push(E);E=!E}for(T=0,S=n.length;T<S;T++)v[T]&&w.push(n[T]);for(let e=0;e<r;T++,e++)v[T]&&w.push(d[e]);return w}(e.huffman,e.refinement,f,e.numberOfNewSymbols,e.numberOfExportedSymbols,a,e.template,e.at,e.refinementTemplate,e.refinementAt,u,c)}onImmediateTextRegion(e,t,n,r,o){const s=e.info;let a,c;const l=this.symbols,f=[];for(const e of t){const t=l[e];t&&f.push(...t)}const u=(0,i.log2)(f.length);if(e.huffman){c=new Reader(n,r,o);a=function getTextRegionHuffmanTables(e,t,n,r,i){const o=[];for(let e=0;e<=34;e++){const t=i.readBits(4);o.push(new HuffmanLine([e,t,0,0]))}const s=new HuffmanTable(o,!1);o.length=0;for(let e=0;e<r;){const t=s.decode(i);if(t>=32){let n,r,s;switch(t){case 32:if(0===e)throw new Jbig2Error("no previous value in symbol ID table");r=i.readBits(2)+3;n=o[e-1].prefixLength;break;case 33:r=i.readBits(3)+3;n=0;break;case 34:r=i.readBits(7)+11;n=0;break;default:throw new Jbig2Error("invalid code length in symbol ID table")}for(s=0;s<r;s++){o.push(new HuffmanLine([e,n,0,0]));e++}}else{o.push(new HuffmanLine([e,t,0,0]));e++}}i.byteAlign();const a=new HuffmanTable(o,!1);let c,l,f,u=0;switch(e.huffmanFS){case 0:case 1:c=getStandardTable(e.huffmanFS+6);break;case 3:c=getCustomHuffmanTable(u,t,n);u++;break;default:throw new Jbig2Error("invalid Huffman FS selector")}switch(e.huffmanDS){case 0:case 1:case 2:l=getStandardTable(e.huffmanDS+8);break;case 3:l=getCustomHuffmanTable(u,t,n);u++;break;default:throw new Jbig2Error("invalid Huffman DS selector")}switch(e.huffmanDT){case 0:case 1:case 2:f=getStandardTable(e.huffmanDT+11);break;case 3:f=getCustomHuffmanTable(u,t,n);u++;break;default:throw new Jbig2Error("invalid Huffman DT selector")}if(e.refinement)throw new Jbig2Error("refinement with Huffman is not supported");return{symbolIDTable:a,tableFirstS:c,tableDeltaS:l,tableDeltaT:f}}(e,t,this.customTables,f.length,c)}const h=new DecodingContext(n,r,o),d=decodeTextRegion(e.huffman,e.refinement,s.width,s.height,e.defaultPixelValue,e.numberOfSymbolInstances,e.stripSize,f,u,e.transposed,e.dsOffset,e.referenceCorner,e.combinationOperator,a,e.refinementTemplate,e.refinementAt,h,e.logStripSize,c);this.drawBitmap(s,d)}onImmediateLosslessTextRegion(){this.onImmediateTextRegion(...arguments)}onPatternDictionary(e,t,n,r,i){let o=this.patterns;o||(this.patterns=o={});const s=new DecodingContext(n,r,i);o[t]=function decodePatternDictionary(e,t,n,r,i,o){const s=[];if(!e){s.push({x:-t,y:0});0===i&&s.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const a=decodeBitmap(e,(r+1)*t,n,i,!1,null,s,o),c=[];for(let e=0;e<=r;e++){const r=[],i=t*e,o=i+t;for(let e=0;e<n;e++)r.push(a[e].subarray(i,o));c.push(r)}return c}(e.mmr,e.patternWidth,e.patternHeight,e.maxPatternIndex,e.template,s)}onImmediateHalftoneRegion(e,t,n,r,o){const s=this.patterns[t[0]],a=e.info,c=new DecodingContext(n,r,o),l=function decodeHalftoneRegion(e,t,n,r,o,s,a,c,l,f,u,h,d,p,g){if(a)throw new Jbig2Error("skip is not supported");if(0!==c)throw new Jbig2Error(`operator "${c}" is not supported in halftone region`);const m=[];let b,y,x;for(b=0;b<o;b++){x=new Uint8Array(r);if(s)for(y=0;y<r;y++)x[y]=s;m.push(x)}const w=t.length,v=t[0],T=v[0].length,S=v.length,E=(0,i.log2)(w),I=[];if(!e){I.push({x:n<=1?3:2,y:-1});0===n&&I.push({x:-3,y:-1},{x:2,y:-2},{x:-2,y:-2})}const C=[];let P,A,k,B,_,O,R,M,L,D,U;e&&(P=new Reader(g.data,g.start,g.end));for(b=E-1;b>=0;b--){A=e?decodeMMRBitmap(P,l,f,!0):decodeBitmap(!1,l,f,n,!1,null,I,g);C[b]=A}for(k=0;k<f;k++)for(B=0;B<l;B++){_=0;O=0;for(y=E-1;y>=0;y--){_^=C[y][k][B];O|=_<<y}R=t[O];M=u+k*p+B*d>>8;L=h+k*d-B*p>>8;if(M>=0&&M+T<=r&&L>=0&&L+S<=o)for(b=0;b<S;b++){U=m[L+b];D=R[b];for(y=0;y<T;y++)U[M+y]|=D[y]}else{let e,t;for(b=0;b<S;b++){t=L+b;if(!(t<0||t>=o)){U=m[t];D=R[b];for(y=0;y<T;y++){e=M+y;e>=0&&e<r&&(U[e]|=D[y])}}}}}return m}(e.mmr,s,e.template,a.width,a.height,e.defaultPixelValue,e.enableSkip,e.combinationOperator,e.gridWidth,e.gridHeight,e.gridOffsetX,e.gridOffsetY,e.gridVectorX,e.gridVectorY,c);this.drawBitmap(a,l)}onImmediateLosslessHalftoneRegion(){this.onImmediateHalftoneRegion(...arguments)}onTables(e,t,n,r){let o=this.customTables;o||(this.customTables=o={});o[e]=function decodeTablesSegment(e,t,n){const r=e[t],o=4294967295&(0,i.readUint32)(e,t+1),s=4294967295&(0,i.readUint32)(e,t+5),a=new Reader(e,t+9,n),c=1+(r>>1&7),l=1+(r>>4&7),f=[];let u,h,d=o;do{u=a.readBits(c);h=a.readBits(l);f.push(new HuffmanLine([d,u,h,0]));d+=1<<h}while(d<s);u=a.readBits(c);f.push(new HuffmanLine([o-1,u,32,0,"lower"]));u=a.readBits(c);f.push(new HuffmanLine([s,u,32,0]));if(1&r){u=a.readBits(c);f.push(new HuffmanLine([u,0]))}return new HuffmanTable(f,!1)}(t,n,r)}}class HuffmanLine{constructor(e){if(2===e.length){this.isOOB=!0;this.rangeLow=0;this.prefixLength=e[0];this.rangeLength=0;this.prefixCode=e[1];this.isLowerRange=!1}else{this.isOOB=!1;this.rangeLow=e[0];this.prefixLength=e[1];this.rangeLength=e[2];this.prefixCode=e[3];this.isLowerRange="lower"===e[4]}}}class HuffmanTreeNode{constructor(e){this.children=[];if(e){this.isLeaf=!0;this.rangeLength=e.rangeLength;this.rangeLow=e.rangeLow;this.isLowerRange=e.isLowerRange;this.isOOB=e.isOOB}else this.isLeaf=!1}buildTree(e,t){const n=e.prefixCode>>t&1;if(t<=0)this.children[n]=new HuffmanTreeNode(e);else{let r=this.children[n];r||(this.children[n]=r=new HuffmanTreeNode(null));r.buildTree(e,t-1)}}decodeNode(e){if(this.isLeaf){if(this.isOOB)return null;const t=e.readBits(this.rangeLength);return this.rangeLow+(this.isLowerRange?-t:t)}const t=this.children[e.readBit()];if(!t)throw new Jbig2Error("invalid Huffman data");return t.decodeNode(e)}}class HuffmanTable{constructor(e,t){t||this.assignPrefixCodes(e);this.rootNode=new HuffmanTreeNode(null);for(let t=0,n=e.length;t<n;t++){const n=e[t];n.prefixLength>0&&this.rootNode.buildTree(n,n.prefixLength-1)}}decode(e){return this.rootNode.decodeNode(e)}assignPrefixCodes(e){const t=e.length;let n=0;for(let r=0;r<t;r++)n=Math.max(n,e[r].prefixLength);const r=new Uint32Array(n+1);for(let n=0;n<t;n++)r[e[n].prefixLength]++;let i,o,s,a=1,c=0;r[0]=0;for(;a<=n;){c=c+r[a-1]<<1;i=c;o=0;for(;o<t;){s=e[o];if(s.prefixLength===a){s.prefixCode=i;i++}o++}a++}}}const g={};function getStandardTable(e){let t,n=g[e];if(n)return n;switch(e){case 1:t=[[0,1,4,0],[16,2,8,2],[272,3,16,6],[65808,3,32,7]];break;case 2:t=[[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[75,6,32,62],[6,63]];break;case 3:t=[[-256,8,8,254],[0,1,0,0],[1,2,0,2],[2,3,0,6],[3,4,3,14],[11,5,6,30],[-257,8,32,255,"lower"],[75,7,32,126],[6,62]];break;case 4:t=[[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[76,5,32,31]];break;case 5:t=[[-255,7,8,126],[1,1,0,0],[2,2,0,2],[3,3,0,6],[4,4,3,14],[12,5,6,30],[-256,7,32,127,"lower"],[76,6,32,62]];break;case 6:t=[[-2048,5,10,28],[-1024,4,9,8],[-512,4,8,9],[-256,4,7,10],[-128,5,6,29],[-64,5,5,30],[-32,4,5,11],[0,2,7,0],[128,3,7,2],[256,3,8,3],[512,4,9,12],[1024,4,10,13],[-2049,6,32,62,"lower"],[2048,6,32,63]];break;case 7:t=[[-1024,4,9,8],[-512,3,8,0],[-256,4,7,9],[-128,5,6,26],[-64,5,5,27],[-32,4,5,10],[0,4,5,11],[32,5,5,28],[64,5,6,29],[128,4,7,12],[256,3,8,1],[512,3,9,2],[1024,3,10,3],[-1025,5,32,30,"lower"],[2048,5,32,31]];break;case 8:t=[[-15,8,3,252],[-7,9,1,508],[-5,8,1,253],[-3,9,0,509],[-2,7,0,124],[-1,4,0,10],[0,2,1,0],[2,5,0,26],[3,6,0,58],[4,3,4,4],[20,6,1,59],[22,4,4,11],[38,4,5,12],[70,5,6,27],[134,5,7,28],[262,6,7,60],[390,7,8,125],[646,6,10,61],[-16,9,32,510,"lower"],[1670,9,32,511],[2,1]];break;case 9:t=[[-31,8,4,252],[-15,9,2,508],[-11,8,2,253],[-7,9,1,509],[-5,7,1,124],[-3,4,1,10],[-1,3,1,2],[1,3,1,3],[3,5,1,26],[5,6,1,58],[7,3,5,4],[39,6,2,59],[43,4,5,11],[75,4,6,12],[139,5,7,27],[267,5,8,28],[523,6,8,60],[779,7,9,125],[1291,6,11,61],[-32,9,32,510,"lower"],[3339,9,32,511],[2,0]];break;case 10:t=[[-21,7,4,122],[-5,8,0,252],[-4,7,0,123],[-3,5,0,24],[-2,2,2,0],[2,5,0,25],[3,6,0,54],[4,7,0,124],[5,8,0,253],[6,2,6,1],[70,5,5,26],[102,6,5,55],[134,6,6,56],[198,6,7,57],[326,6,8,58],[582,6,9,59],[1094,6,10,60],[2118,7,11,125],[-22,8,32,254,"lower"],[4166,8,32,255],[2,2]];break;case 11:t=[[1,1,0,0],[2,2,1,2],[4,4,0,12],[5,4,1,13],[7,5,1,28],[9,5,2,29],[13,6,2,60],[17,7,2,122],[21,7,3,123],[29,7,4,124],[45,7,5,125],[77,7,6,126],[141,7,32,127]];break;case 12:t=[[1,1,0,0],[2,2,0,2],[3,3,1,6],[5,5,0,28],[6,5,1,29],[8,6,1,60],[10,7,0,122],[11,7,1,123],[13,7,2,124],[17,7,3,125],[25,7,4,126],[41,8,5,254],[73,8,32,255]];break;case 13:t=[[1,1,0,0],[2,3,0,4],[3,4,0,12],[4,5,0,28],[5,4,1,13],[7,3,3,5],[15,6,1,58],[17,6,2,59],[21,6,3,60],[29,6,4,61],[45,6,5,62],[77,7,6,126],[141,7,32,127]];break;case 14:t=[[-2,3,0,4],[-1,3,0,5],[0,1,0,0],[1,3,0,6],[2,3,0,7]];break;case 15:t=[[-24,7,4,124],[-8,6,2,60],[-4,5,1,28],[-2,4,0,12],[-1,3,0,4],[0,1,0,0],[1,3,0,5],[2,4,0,13],[3,5,1,29],[5,6,2,61],[9,7,4,125],[-25,7,32,126,"lower"],[25,7,32,127]];break;default:throw new Jbig2Error(`standard table B.${e} does not exist`)}for(let e=0,n=t.length;e<n;e++)t[e]=new HuffmanLine(t[e]);n=new HuffmanTable(t,!0);g[e]=n;return n}class Reader{constructor(e,t,n){this.data=e;this.start=t;this.end=n;this.position=t;this.shift=-1;this.currentByte=0}readBit(){if(this.shift<0){if(this.position>=this.end)throw new Jbig2Error("end of data while reading bit");this.currentByte=this.data[this.position++];this.shift=7}const e=this.currentByte>>this.shift&1;this.shift--;return e}readBits(e){let t,n=0;for(t=e-1;t>=0;t--)n|=this.readBit()<<t;return n}byteAlign(){this.shift=-1}next(){return this.position>=this.end?-1:this.data[this.position++]}}function getCustomHuffmanTable(e,t,n){let r=0;for(let i=0,o=t.length;i<o;i++){const o=n[t[i]];if(o){if(e===r)return o;r++}}throw new Jbig2Error("can't find custom Huffman table")}function readUncompressedBitmap(e,t,n){const r=[];for(let i=0;i<n;i++){const n=new Uint8Array(t);r.push(n);for(let r=0;r<t;r++)n[r]=e.readBit();e.byteAlign()}return r}function decodeMMRBitmap(e,t,n,r){const i={K:-1,Columns:t,Rows:n,BlackIs1:!0,EndOfBlock:r},o=new s.CCITTFaxDecoder(e,i),a=[];let c,l=!1;for(let e=0;e<n;e++){const e=new Uint8Array(t);a.push(e);let n=-1;for(let r=0;r<t;r++){if(n<0){c=o.readNextChar();if(-1===c){c=0;l=!0}n=7}e[r]=c>>n&1;n--}}if(r&&!l){const e=5;for(let t=0;t<e&&-1!==o.readNextChar();t++);}return a}t.Jbig2Image=class Jbig2Image{parseChunks(e){return function parseJbig2Chunks(e){const t=new SimpleSegmentVisitor;for(let n=0,r=e.length;n<r;n++){const r=e[n];processSegments(readSegments({},r.data,r.start,r.end),t)}return t.buffer}(e)}parse(e){const{imgData:t,width:n,height:r}=function parseJbig2(e){const t=e.length;let n=0;if(151!==e[n]||74!==e[n+1]||66!==e[n+2]||50!==e[n+3]||13!==e[n+4]||10!==e[n+5]||26!==e[n+6]||10!==e[n+7])throw new Jbig2Error("parseJbig2 - invalid header.");const r=Object.create(null);n+=8;const o=e[n++];r.randomAccess=!(1&o);if(!(2&o)){r.numberOfPages=(0,i.readUint32)(e,n);n+=4}const s=readSegments(r,e,n,t),a=new SimpleSegmentVisitor;processSegments(s,a);const{width:c,height:l}=a.currentPageInfo,f=a.buffer,u=new Uint8ClampedArray(c*l);let h=0,d=0;for(let e=0;e<l;e++){let e,t=0;for(let n=0;n<c;n++){if(!t){t=128;e=f[d++]}u[h++]=e&t?0:255;t>>=1}}return{imgData:u,width:c,height:l}}(e);this.width=n;this.height=r;return t}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.XRefParseException=t.XRefEntryException=t.ParserEOFException=t.PDF_VERSION_REGEXP=t.MissingDataException=void 0;t.arrayBuffersToBytes=function arrayBuffersToBytes(e){const t=e.length;if(0===t)return new Uint8Array(0);if(1===t)return new Uint8Array(e[0]);let n=0;for(let r=0;r<t;r++)n+=e[r].byteLength;const r=new Uint8Array(n);let i=0;for(let n=0;n<t;n++){const t=new Uint8Array(e[n]);r.set(t,i);i+=t.byteLength}return r};t.collectActions=function collectActions(e,t,n){const o=Object.create(null),s=getInheritableProperty({dict:t,key:"AA",stopWhenFound:!1});if(s)for(let t=s.length-1;t>=0;t--){const r=s[t];if(r instanceof i.Dict)for(const t of r.getKeys()){const s=n[t];if(!s)continue;const a=[];_collectJS(r.getRaw(t),e,a,new i.RefSet);a.length>0&&(o[s]=a)}}if(t.has("A")){const n=[];_collectJS(t.get("A"),e,n,new i.RefSet);n.length>0&&(o.Action=n)}return(0,r.objectSize)(o)>0?o:null};t.encodeToXmlString=function encodeToXmlString(e){const t=[];let n=0;for(let r=0,i=e.length;r<i;r++){const i=e.codePointAt(r);if(32<=i&&i<=126){const o=a[i];if(o){n<r&&t.push(e.substring(n,r));t.push(o);n=r+1}}else{n<r&&t.push(e.substring(n,r));t.push(`&#x${i.toString(16).toUpperCase()};`);i>55295&&(i<57344||i>65533)&&r++;n=r+1}}if(0===t.length)return e;n<e.length&&t.push(e.substring(n,e.length));return t.join("")};t.escapePDFName=function escapePDFName(e){const t=[];let n=0;for(let r=0,i=e.length;r<i;r++){const i=e.charCodeAt(r);if(i<33||i>126||35===i||40===i||41===i||60===i||62===i||91===i||93===i||123===i||125===i||47===i||37===i){n<r&&t.push(e.substring(n,r));t.push(`#${i.toString(16)}`);n=r+1}}if(0===t.length)return e;n<e.length&&t.push(e.substring(n,e.length));return t.join("")};t.escapeString=function escapeString(e){return e.replaceAll(/([()\\\n\r])/g,(e=>"\n"===e?"\\n":"\r"===e?"\\r":`\\${e}`))};t.getInheritableProperty=getInheritableProperty;t.getLookupTableFactory=function getLookupTableFactory(e){let t;return function(){if(e){t=Object.create(null);e(t);e=null}return t}};t.getNewAnnotationsMap=function getNewAnnotationsMap(e){if(!e)return null;const t=new Map;for(const[n,i]of e){if(!n.startsWith(r.AnnotationEditorPrefix))continue;let e=t.get(i.pageIndex);if(!e){e=[];t.set(i.pageIndex,e)}e.push(i)}return t.size>0?t:null};t.getRotationMatrix=function getRotationMatrix(e,t,n){switch(e){case 90:return[0,1,-1,0,t,0];case 180:return[-1,0,0,-1,t,n];case 270:return[0,-1,1,0,0,n];default:throw new Error("Invalid rotation")}};t.isAscii=function isAscii(e){return/^[\x00-\x7F]*$/.test(e)};t.isWhiteSpace=function isWhiteSpace(e){return 32===e||9===e||13===e||10===e};t.log2=function log2(e){if(e<=0)return 0;return Math.ceil(Math.log2(e))};t.numberToString=function numberToString(e){if(Number.isInteger(e))return e.toString();const t=Math.round(100*e);if(t%100==0)return(t/100).toString();if(t%10==0)return e.toFixed(1);return e.toFixed(2)};t.parseXFAPath=function parseXFAPath(e){const t=/(.+)\[(\d+)\]$/;return e.split(".").map((e=>{const n=e.match(t);return n?{name:n[1],pos:parseInt(n[2],10)}:{name:e,pos:0}}))};t.readInt8=function readInt8(e,t){return e[t]<<24>>24};t.readUint16=function readUint16(e,t){return e[t]<<8|e[t+1]};t.readUint32=function readUint32(e,t){return(e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0};t.recoverJsURL=function recoverJsURL(e){const t=new RegExp("^\\s*("+["app.launchURL","window.open","xfa.host.gotoURL"].join("|").replaceAll(".","\\.")+")\\((?:'|\")([^'\"]*)(?:'|\")(?:,\\s*(\\w+)\\)|\\))","i").exec(e);if(t?.[2]){const e=t[2];let n=!1;"true"===t[3]&&"app.launchURL"===t[1]&&(n=!0);return{url:e,newWindow:n}}return null};t.stringToUTF16HexString=function stringToUTF16HexString(e){const t=[];for(let n=0,r=e.length;n<r;n++){const r=e.charCodeAt(n);t.push((r>>8&255).toString(16).padStart(2,"0"),(255&r).toString(16).padStart(2,"0"))}return t.join("")};t.stringToUTF16String=function stringToUTF16String(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=[];t&&n.push("þÿ");for(let t=0,r=e.length;t<r;t++){const r=e.charCodeAt(t);n.push(String.fromCharCode(r>>8&255),String.fromCharCode(255&r))}return n.join("")};t.toRomanNumerals=function toRomanNumerals(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(0,r.assert)(Number.isInteger(e)&&e>0,"The number should be a positive integer.");const n=[];let i;for(;e>=1e3;){e-=1e3;n.push("M")}i=e/100|0;e%=100;n.push(s[i]);i=e/10|0;e%=10;n.push(s[10+i]);n.push(s[20+e]);const o=n.join("");return t?o.toLowerCase():o};t.validateCSSFont=function validateCSSFont(e){const t=new Set(["100","200","300","400","500","600","700","800","900","1000","normal","bold","bolder","lighter"]),{fontFamily:n,fontWeight:r,italicAngle:i}=e;if(!validateFontName(n,!0))return!1;const o=r?r.toString():"";e.fontWeight=t.has(o)?o:"400";const s=parseFloat(i);e.italicAngle=isNaN(s)||s<-90||s>90?"14":i.toString();return!0};t.validateFontName=validateFontName;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);n(126);n(137);n(139);n(142);n(144);n(146);n(148);n(2);var r=n(1),i=n(150),o=n(151);t.PDF_VERSION_REGEXP=/^[1-9]\.\d$/;class MissingDataException extends r.BaseException{constructor(e,t){super(`Missing data [${e}, ${t})`,"MissingDataException");this.begin=e;this.end=t}}t.MissingDataException=MissingDataException;class ParserEOFException extends r.BaseException{constructor(e){super(e,"ParserEOFException")}}t.ParserEOFException=ParserEOFException;class XRefEntryException extends r.BaseException{constructor(e){super(e,"XRefEntryException")}}t.XRefEntryException=XRefEntryException;class XRefParseException extends r.BaseException{constructor(e){super(e,"XRefParseException")}}t.XRefParseException=XRefParseException;function getInheritableProperty(e){let t,{dict:n,key:r,getArray:o=!1,stopWhenFound:s=!0}=e;const a=new i.RefSet;for(;n instanceof i.Dict&&(!n.objId||!a.has(n.objId));){n.objId&&a.put(n.objId);const e=o?n.getArray(r):n.get(r);if(void 0!==e){if(s)return e;(t||=[]).push(e)}n=n.get("Parent")}return t}const s=["","C","CC","CCC","CD","D","DC","DCC","DCCC","CM","","X","XX","XXX","XL","L","LX","LXX","LXXX","XC","","I","II","III","IV","V","VI","VII","VIII","IX"];function _collectJS(e,t,n,s){if(!e)return;let a=null;if(e instanceof i.Ref){if(s.has(e))return;a=e;s.put(a);e=t.fetch(e)}if(Array.isArray(e))for(const r of e)_collectJS(r,t,n,s);else if(e instanceof i.Dict){if((0,i.isName)(e.get("S"),"JavaScript")){const t=e.get("JS");let i;t instanceof o.BaseStream?i=t.getString():"string"==typeof t&&(i=t);i&&=(0,r.stringToPDFString)(i).replaceAll("\0","");i&&n.push(i)}_collectJS(e.getRaw("Next"),t,n,s)}a&&s.remove(a)}const a={60:"&lt;",62:"&gt;",38:"&amp;",34:"&quot;",39:"&apos;"};function validateFontName(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const n=/^("|').*("|')$/.exec(e);if(n&&n[1]===n[2]){if(new RegExp(`[^\\\\]${n[1]}`).test(e.slice(1,-1))){t&&(0,r.warn)(`FontFamily contains unescaped ${n[1]}: ${e}.`);return!1}}else for(const n of e.split(/[ \t]+/))if(/^(\d|(-(\d|-)))/.test(n)||!/^[\w-\\]+$/.test(n)){t&&(0,r.warn)(`FontFamily contains invalid <custom-ident>: ${e}.`);return!1}return!0}},(e,t,n)=>{var r=n(3),i=n(127);r({target:"Set",proto:!0,real:!0,forced:!n(136)("difference")},{difference:i})},(e,t,n)=>{var r=n(128),i=n(129),o=n(130),s=n(133),a=n(134),c=n(131),l=n(132),f=i.has,u=i.remove;e.exports=function difference(e){var t=r(this),n=a(e),i=o(t);s(t)<=n.size?c(t,(function(e){n.includes(e)&&u(i,e)})):l(n.getIterator(),(function(e){f(t,e)&&u(i,e)}));return i}},(e,t,n)=>{var r=n(129).has;e.exports=function(e){r(e);return e}},(e,t,n)=>{var r=n(14),i=Set.prototype;e.exports={Set:Set,add:r(i.add),has:r(i.has),remove:r(i.delete),proto:i}},(e,t,n)=>{var r=n(129),i=n(131),o=r.Set,s=r.add;e.exports=function(e){var t=new o;i(e,(function(e){s(t,e)}));return t}},(e,t,n)=>{var r=n(14),i=n(132),o=n(129),s=o.Set,a=o.proto,c=r(a.forEach),l=r(a.keys),f=l(new s).next;e.exports=function(e,t,n){return n?i({iterator:l(e),next:f},t):c(e,t)}},(e,t,n)=>{var r=n(8);e.exports=function(e,t,n){for(var i,o,s=n?e:e.iterator,a=e.next;!(i=r(a,s)).done;)if(void 0!==(o=t(i.value)))return o}},(e,t,n)=>{var r=n(72),i=n(129);e.exports=r(i.proto,"size","get")||function(e){return e.size}},(e,t,n)=>{var r=n(31),i=n(47),o=n(8),s=n(62),a=n(135),c="Invalid size",l=RangeError,f=TypeError,u=Math.max,SetRecord=function(e,t,n,r){this.set=e;this.size=t;this.has=n;this.keys=r};SetRecord.prototype={getIterator:function(){return a(i(o(this.keys,this.set)))},includes:function(e){return o(this.has,this.set,e)}};e.exports=function(e){i(e);var t=+e.size;if(t!=t)throw f(c);var n=s(t);if(n<0)throw l(c);return new SetRecord(e,u(n,0),r(e.has),r(e.keys))}},e=>{e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},(e,t,n)=>{var r=n(24),createSetLike=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};e.exports=function(e){var t=r("Set");try{(new t)[e](createSetLike(0));try{(new t)[e](createSetLike(-1));return!1}catch(e){return!0}}catch(e){return!1}}},(e,t,n)=>{var r=n(3),i=n(7),o=n(138);r({target:"Set",proto:!0,real:!0,forced:!n(136)("intersection")||i((function(){return"3,2"!==Array.from(new Set([1,2,3]).intersection(new Set([3,2])))}))},{intersection:o})},(e,t,n)=>{var r=n(128),i=n(129),o=n(133),s=n(134),a=n(131),c=n(132),l=i.Set,f=i.add,u=i.has;e.exports=function intersection(e){var t=r(this),n=s(e),i=new l;o(t)>n.size?c(n.getIterator(),(function(e){u(t,e)&&f(i,e)})):a(t,(function(e){n.includes(e)&&f(i,e)}));return i}},(e,t,n)=>{var r=n(3),i=n(140);r({target:"Set",proto:!0,real:!0,forced:!n(136)("isDisjointFrom")},{isDisjointFrom:i})},(e,t,n)=>{var r=n(128),i=n(129).has,o=n(133),s=n(134),a=n(131),c=n(132),l=n(141);e.exports=function isDisjointFrom(e){var t=r(this),n=s(e);if(o(t)<=n.size)return!1!==a(t,(function(e){if(n.includes(e))return!1}),!0);var f=n.getIterator();return!1!==c(f,(function(e){if(i(t,e))return l(f,"normal",!1)}))}},(e,t,n)=>{var r=n(8),i=n(47),o=n(30);e.exports=function(e,t,n){var s,a;i(e);try{if(!(s=o(e,"return"))){if("throw"===t)throw n;return n}s=r(s,e)}catch(e){a=!0;s=e}if("throw"===t)throw n;if(a)throw s;i(s);return n}},(e,t,n)=>{var r=n(3),i=n(143);r({target:"Set",proto:!0,real:!0,forced:!n(136)("isSubsetOf")},{isSubsetOf:i})},(e,t,n)=>{var r=n(128),i=n(133),o=n(131),s=n(134);e.exports=function isSubsetOf(e){var t=r(this),n=s(e);return!(i(t)>n.size)&&!1!==o(t,(function(e){if(!n.includes(e))return!1}),!0)}},(e,t,n)=>{var r=n(3),i=n(145);r({target:"Set",proto:!0,real:!0,forced:!n(136)("isSupersetOf")},{isSupersetOf:i})},(e,t,n)=>{var r=n(128),i=n(129).has,o=n(133),s=n(134),a=n(132),c=n(141);e.exports=function isSupersetOf(e){var t=r(this),n=s(e);if(o(t)<n.size)return!1;var l=n.getIterator();return!1!==a(l,(function(e){if(!i(t,e))return c(l,"normal",!1)}))}},(e,t,n)=>{var r=n(3),i=n(147);r({target:"Set",proto:!0,real:!0,forced:!n(136)("symmetricDifference")},{symmetricDifference:i})},(e,t,n)=>{var r=n(128),i=n(129),o=n(130),s=n(134),a=n(132),c=i.add,l=i.has,f=i.remove;e.exports=function symmetricDifference(e){var t=r(this),n=s(e).getIterator(),i=o(t);a(n,(function(e){l(t,e)?f(i,e):c(i,e)}));return i}},(e,t,n)=>{var r=n(3),i=n(149);r({target:"Set",proto:!0,real:!0,forced:!n(136)("union")},{union:i})},(e,t,n)=>{var r=n(128),i=n(129).add,o=n(130),s=n(134),a=n(132);e.exports=function union(e){var t=r(this),n=s(e).getIterator(),c=o(t);a(n,(function(e){i(c,e)}));return c}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.RefSetCache=t.RefSet=t.Ref=t.Name=t.EOF=t.Dict=t.Cmd=t.CIRCULAR_REF=void 0;t.clearPrimitiveCaches=function clearPrimitiveCaches(){s=Object.create(null);a=Object.create(null);c=Object.create(null)};t.isCmd=function isCmd(e,t){return e instanceof Cmd&&(void 0===t||e.cmd===t)};t.isDict=function isDict(e,t){return e instanceof Dict&&(void 0===t||isName(e.get("Type"),t))};t.isName=isName;t.isRefsEqual=function isRefsEqual(e,t){return e.num===t.num&&e.gen===t.gen};n(89);n(126);n(137);n(139);n(142);n(144);n(146);n(148);var r=n(1);const i=Symbol("CIRCULAR_REF");t.CIRCULAR_REF=i;const o=Symbol("EOF");t.EOF=o;let s=Object.create(null),a=Object.create(null),c=Object.create(null);class Name{constructor(e){this.name=e}static get(e){return a[e]||=new Name(e)}}t.Name=Name;class Cmd{constructor(e){this.cmd=e}static get(e){return s[e]||=new Cmd(e)}}t.Cmd=Cmd;const l=function nonSerializableClosure(){return l};class Dict{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this._map=Object.create(null);this.xref=e;this.objId=null;this.suppressEncryption=!1;this.__nonSerializable__=l}assignXref(e){this.xref=e}get size(){return Object.keys(this._map).length}get(e,t,n){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==n&&(r=this._map[n])}return r instanceof Ref&&this.xref?this.xref.fetch(r,this.suppressEncryption):r}async getAsync(e,t,n){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==n&&(r=this._map[n])}return r instanceof Ref&&this.xref?this.xref.fetchAsync(r,this.suppressEncryption):r}getArray(e,t,n){let r=this._map[e];if(void 0===r&&void 0!==t){r=this._map[t];void 0===r&&void 0!==n&&(r=this._map[n])}r instanceof Ref&&this.xref&&(r=this.xref.fetch(r,this.suppressEncryption));if(Array.isArray(r)){r=r.slice();for(let e=0,t=r.length;e<t;e++)r[e]instanceof Ref&&this.xref&&(r[e]=this.xref.fetch(r[e],this.suppressEncryption))}return r}getRaw(e){return this._map[e]}getKeys(){return Object.keys(this._map)}getRawValues(){return Object.values(this._map)}set(e,t){this._map[e]=t}has(e){return void 0!==this._map[e]}forEach(e){for(const t in this._map)e(t,this.get(t))}static get empty(){const e=new Dict(null);e.set=(e,t)=>{(0,r.unreachable)("Should not call `set` on the empty dictionary.")};return(0,r.shadow)(this,"empty",e)}static merge(e){let{xref:t,dictArray:n,mergeSubDicts:r=!1}=e;const i=new Dict(t),o=new Map;for(const e of n)if(e instanceof Dict)for(const[t,n]of Object.entries(e._map)){let e=o.get(t);if(void 0===e){e=[];o.set(t,e)}else if(!(r&&n instanceof Dict))continue;e.push(n)}for(const[e,n]of o){if(1===n.length||!(n[0]instanceof Dict)){i._map[e]=n[0];continue}const r=new Dict(t);for(const e of n)for(const[t,n]of Object.entries(e._map))void 0===r._map[t]&&(r._map[t]=n);r.size>0&&(i._map[e]=r)}o.clear();return i.size>0?i:Dict.empty}clone(){const e=new Dict(this.xref);for(const t of this.getKeys())e.set(t,this.getRaw(t));return e}}t.Dict=Dict;class Ref{constructor(e,t){this.num=e;this.gen=t}toString(){return 0===this.gen?`${this.num}R`:`${this.num}R${this.gen}`}static fromString(e){const t=c[e];if(t)return t;const n=/^(\d+)R(\d*)$/.exec(e);return n&&"0"!==n[1]?c[e]=new Ref(parseInt(n[1]),n[2]?parseInt(n[2]):0):null}static get(e,t){const n=0===t?`${e}R`:`${e}R${t}`;return c[n]||=new Ref(e,t)}}t.Ref=Ref;class RefSet{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this._set=new Set(e?._set)}has(e){return this._set.has(e.toString())}put(e){this._set.add(e.toString())}remove(e){this._set.delete(e.toString())}[Symbol.iterator](){return this._set.values()}clear(){this._set.clear()}}t.RefSet=RefSet;class RefSetCache{constructor(){this._map=new Map}get size(){return this._map.size}get(e){return this._map.get(e.toString())}has(e){return this._map.has(e.toString())}put(e,t){this._map.set(e.toString(),t)}putAlias(e,t){this._map.set(e.toString(),this.get(t))}[Symbol.iterator](){return this._map.values()}clear(){this._map.clear()}}t.RefSetCache=RefSetCache;function isName(e,t){return e instanceof Name&&(void 0===t||e.name===t)}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.BaseStream=void 0;var r=n(1);class BaseStream{constructor(){this.constructor===BaseStream&&(0,r.unreachable)("Cannot initialize BaseStream.")}get length(){(0,r.unreachable)("Abstract getter `length` accessed")}get isEmpty(){(0,r.unreachable)("Abstract getter `isEmpty` accessed")}get isDataLoaded(){return(0,r.shadow)(this,"isDataLoaded",!0)}getByte(){(0,r.unreachable)("Abstract method `getByte` called")}getBytes(e){(0,r.unreachable)("Abstract method `getBytes` called")}peekByte(){const e=this.getByte();-1!==e&&this.pos--;return e}peekBytes(e){const t=this.getBytes(e);this.pos-=t.length;return t}getUint16(){const e=this.getByte(),t=this.getByte();return-1===e||-1===t?-1:(e<<8)+t}getInt32(){return(this.getByte()<<24)+(this.getByte()<<16)+(this.getByte()<<8)+this.getByte()}getByteRange(e,t){(0,r.unreachable)("Abstract method `getByteRange` called")}getString(e){return(0,r.bytesToString)(this.getBytes(e))}skip(e){this.pos+=e||1}reset(){(0,r.unreachable)("Abstract method `reset` called")}moveStart(){(0,r.unreachable)("Abstract method `moveStart` called")}makeSubStream(e,t){(0,r.unreachable)("Abstract method `makeSubStream` called")}getBaseStreams(){return null}}t.BaseStream=BaseStream},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.ArithmeticDecoder=void 0;const n=[{qe:22017,nmps:1,nlps:1,switchFlag:1},{qe:13313,nmps:2,nlps:6,switchFlag:0},{qe:6145,nmps:3,nlps:9,switchFlag:0},{qe:2753,nmps:4,nlps:12,switchFlag:0},{qe:1313,nmps:5,nlps:29,switchFlag:0},{qe:545,nmps:38,nlps:33,switchFlag:0},{qe:22017,nmps:7,nlps:6,switchFlag:1},{qe:21505,nmps:8,nlps:14,switchFlag:0},{qe:18433,nmps:9,nlps:14,switchFlag:0},{qe:14337,nmps:10,nlps:14,switchFlag:0},{qe:12289,nmps:11,nlps:17,switchFlag:0},{qe:9217,nmps:12,nlps:18,switchFlag:0},{qe:7169,nmps:13,nlps:20,switchFlag:0},{qe:5633,nmps:29,nlps:21,switchFlag:0},{qe:22017,nmps:15,nlps:14,switchFlag:1},{qe:21505,nmps:16,nlps:14,switchFlag:0},{qe:20737,nmps:17,nlps:15,switchFlag:0},{qe:18433,nmps:18,nlps:16,switchFlag:0},{qe:14337,nmps:19,nlps:17,switchFlag:0},{qe:13313,nmps:20,nlps:18,switchFlag:0},{qe:12289,nmps:21,nlps:19,switchFlag:0},{qe:10241,nmps:22,nlps:19,switchFlag:0},{qe:9217,nmps:23,nlps:20,switchFlag:0},{qe:8705,nmps:24,nlps:21,switchFlag:0},{qe:7169,nmps:25,nlps:22,switchFlag:0},{qe:6145,nmps:26,nlps:23,switchFlag:0},{qe:5633,nmps:27,nlps:24,switchFlag:0},{qe:5121,nmps:28,nlps:25,switchFlag:0},{qe:4609,nmps:29,nlps:26,switchFlag:0},{qe:4353,nmps:30,nlps:27,switchFlag:0},{qe:2753,nmps:31,nlps:28,switchFlag:0},{qe:2497,nmps:32,nlps:29,switchFlag:0},{qe:2209,nmps:33,nlps:30,switchFlag:0},{qe:1313,nmps:34,nlps:31,switchFlag:0},{qe:1089,nmps:35,nlps:32,switchFlag:0},{qe:673,nmps:36,nlps:33,switchFlag:0},{qe:545,nmps:37,nlps:34,switchFlag:0},{qe:321,nmps:38,nlps:35,switchFlag:0},{qe:273,nmps:39,nlps:36,switchFlag:0},{qe:133,nmps:40,nlps:37,switchFlag:0},{qe:73,nmps:41,nlps:38,switchFlag:0},{qe:37,nmps:42,nlps:39,switchFlag:0},{qe:21,nmps:43,nlps:40,switchFlag:0},{qe:9,nmps:44,nlps:41,switchFlag:0},{qe:5,nmps:45,nlps:42,switchFlag:0},{qe:1,nmps:45,nlps:43,switchFlag:0},{qe:22017,nmps:46,nlps:46,switchFlag:0}];t.ArithmeticDecoder=class ArithmeticDecoder{constructor(e,t,n){this.data=e;this.bp=t;this.dataEnd=n;this.chigh=e[t];this.clow=0;this.byteIn();this.chigh=this.chigh<<7&65535|this.clow>>9&127;this.clow=this.clow<<7&65535;this.ct-=7;this.a=32768}byteIn(){const e=this.data;let t=this.bp;if(255===e[t])if(e[t+1]>143){this.clow+=65280;this.ct=8}else{t++;this.clow+=e[t]<<9;this.ct=7;this.bp=t}else{t++;this.clow+=t<this.dataEnd?e[t]<<8:65280;this.ct=8;this.bp=t}if(this.clow>65535){this.chigh+=this.clow>>16;this.clow&=65535}}readBit(e,t){let r=e[t]>>1,i=1&e[t];const o=n[r],s=o.qe;let a,c=this.a-s;if(this.chigh<s)if(c<s){c=s;a=i;r=o.nmps}else{c=s;a=1^i;1===o.switchFlag&&(i=a);r=o.nlps}else{this.chigh-=s;if(0!=(32768&c)){this.a=c;return i}if(c<s){a=1^i;1===o.switchFlag&&(i=a);r=o.nlps}else{a=i;r=o.nmps}}do{0===this.ct&&this.byteIn();c<<=1;this.chigh=this.chigh<<1&65535|this.clow>>15&1;this.clow=this.clow<<1&65535;this.ct--}while(0==(32768&c));this.a=c;e[t]=r<<1|i;return a}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.CCITTFaxDecoder=void 0;n(2);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var r=n(1);const i=-1,o=[[-1,-1],[-1,-1],[7,8],[7,7],[6,6],[6,6],[6,5],[6,5],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[4,0],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[3,3],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2],[1,2]],s=[[-1,-1],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[12,1984],[12,2048],[12,2112],[12,2176],[12,2240],[12,2304],[11,1856],[11,1856],[11,1920],[11,1920],[12,2368],[12,2432],[12,2496],[12,2560]],a=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[8,29],[8,29],[8,30],[8,30],[8,45],[8,45],[8,46],[8,46],[7,22],[7,22],[7,22],[7,22],[7,23],[7,23],[7,23],[7,23],[8,47],[8,47],[8,48],[8,48],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[6,13],[7,20],[7,20],[7,20],[7,20],[8,33],[8,33],[8,34],[8,34],[8,35],[8,35],[8,36],[8,36],[8,37],[8,37],[8,38],[8,38],[7,19],[7,19],[7,19],[7,19],[8,31],[8,31],[8,32],[8,32],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,1],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[6,12],[8,53],[8,53],[8,54],[8,54],[7,26],[7,26],[7,26],[7,26],[8,39],[8,39],[8,40],[8,40],[8,41],[8,41],[8,42],[8,42],[8,43],[8,43],[8,44],[8,44],[7,21],[7,21],[7,21],[7,21],[7,28],[7,28],[7,28],[7,28],[8,61],[8,61],[8,62],[8,62],[8,63],[8,63],[8,0],[8,0],[8,320],[8,320],[8,384],[8,384],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,10],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[5,11],[7,27],[7,27],[7,27],[7,27],[8,59],[8,59],[8,60],[8,60],[9,1472],[9,1536],[9,1600],[9,1728],[7,18],[7,18],[7,18],[7,18],[7,24],[7,24],[7,24],[7,24],[8,49],[8,49],[8,50],[8,50],[8,51],[8,51],[8,52],[8,52],[7,25],[7,25],[7,25],[7,25],[8,55],[8,55],[8,56],[8,56],[8,57],[8,57],[8,58],[8,58],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,192],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[6,1664],[8,448],[8,448],[8,512],[8,512],[9,704],[9,768],[8,640],[8,640],[8,576],[8,576],[9,832],[9,896],[9,960],[9,1024],[9,1088],[9,1152],[9,1216],[9,1280],[9,1344],[9,1408],[7,256],[7,256],[7,256],[7,256],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,2],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[4,3],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,128],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,8],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[5,9],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,16],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[6,17],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,4],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[4,5],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,14],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[6,15],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[5,64],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,6],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7],[4,7]],c=[[-1,-1],[-1,-1],[12,-2],[12,-2],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[-1,-1],[11,1792],[11,1792],[11,1792],[11,1792],[12,1984],[12,1984],[12,2048],[12,2048],[12,2112],[12,2112],[12,2176],[12,2176],[12,2240],[12,2240],[12,2304],[12,2304],[11,1856],[11,1856],[11,1856],[11,1856],[11,1920],[11,1920],[11,1920],[11,1920],[12,2368],[12,2368],[12,2432],[12,2432],[12,2496],[12,2496],[12,2560],[12,2560],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[10,18],[12,52],[12,52],[13,640],[13,704],[13,768],[13,832],[12,55],[12,55],[12,56],[12,56],[13,1280],[13,1344],[13,1408],[13,1472],[12,59],[12,59],[12,60],[12,60],[13,1536],[13,1600],[11,24],[11,24],[11,24],[11,24],[11,25],[11,25],[11,25],[11,25],[13,1664],[13,1728],[12,320],[12,320],[12,384],[12,384],[12,448],[12,448],[13,512],[13,576],[12,53],[12,53],[12,54],[12,54],[13,896],[13,960],[13,1024],[13,1088],[13,1152],[13,1216],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64],[10,64]],l=[[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[8,13],[11,23],[11,23],[12,50],[12,51],[12,44],[12,45],[12,46],[12,47],[12,57],[12,58],[12,61],[12,256],[10,16],[10,16],[10,16],[10,16],[10,17],[10,17],[10,17],[10,17],[12,48],[12,49],[12,62],[12,63],[12,30],[12,31],[12,32],[12,33],[12,40],[12,41],[11,22],[11,22],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[8,14],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,10],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[7,11],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[9,15],[12,128],[12,192],[12,26],[12,27],[12,28],[12,29],[11,19],[11,19],[11,20],[11,20],[12,34],[12,35],[12,36],[12,37],[12,38],[12,39],[11,21],[11,21],[12,42],[12,43],[10,0],[10,0],[10,0],[10,0],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12],[7,12]],f=[[-1,-1],[-1,-1],[-1,-1],[-1,-1],[6,9],[6,8],[5,7],[5,7],[4,6],[4,6],[4,6],[4,6],[4,5],[4,5],[4,5],[4,5],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,1],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[3,4],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,3],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2],[2,2]];t.CCITTFaxDecoder=class CCITTFaxDecoder{constructor(e){let t,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!e||"function"!=typeof e.next)throw new Error('CCITTFaxDecoder - invalid "source" parameter.');this.source=e;this.eof=!1;this.encoding=n.K||0;this.eoline=n.EndOfLine||!1;this.byteAlign=n.EncodedByteAlign||!1;this.columns=n.Columns||1728;this.rows=n.Rows||0;this.eoblock=n.EndOfBlock??!0;this.black=n.BlackIs1||!1;this.codingLine=new Uint32Array(this.columns+1);this.refLine=new Uint32Array(this.columns+2);this.codingLine[0]=this.columns;this.codingPos=0;this.row=0;this.nextLine2D=this.encoding<0;this.inputBits=0;this.inputBuf=0;this.outputBits=0;this.rowsDone=!1;for(;0===(t=this._lookBits(12));)this._eatBits(1);1===t&&this._eatBits(12);if(this.encoding>0){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}}readNextChar(){if(this.eof)return-1;const e=this.refLine,t=this.codingLine,n=this.columns;let o,s,a,c,l;if(0===this.outputBits){this.rowsDone&&(this.eof=!0);if(this.eof)return-1;this.err=!1;let a,l,f;if(this.nextLine2D){for(c=0;t[c]<n;++c)e[c]=t[c];e[c++]=n;e[c]=n;t[0]=0;this.codingPos=0;o=0;s=0;for(;t[this.codingPos]<n;){a=this._getTwoDimCode();switch(a){case 0:this._addPixels(e[o+1],s);e[o+1]<n&&(o+=2);break;case 1:a=l=0;if(s){do{a+=f=this._getBlackCode()}while(f>=64);do{l+=f=this._getWhiteCode()}while(f>=64)}else{do{a+=f=this._getWhiteCode()}while(f>=64);do{l+=f=this._getBlackCode()}while(f>=64)}this._addPixels(t[this.codingPos]+a,s);t[this.codingPos]<n&&this._addPixels(t[this.codingPos]+l,1^s);for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2;break;case 7:this._addPixels(e[o]+3,s);s^=1;if(t[this.codingPos]<n){++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 5:this._addPixels(e[o]+2,s);s^=1;if(t[this.codingPos]<n){++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 3:this._addPixels(e[o]+1,s);s^=1;if(t[this.codingPos]<n){++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 2:this._addPixels(e[o],s);s^=1;if(t[this.codingPos]<n){++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 8:this._addPixelsNeg(e[o]-3,s);s^=1;if(t[this.codingPos]<n){o>0?--o:++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 6:this._addPixelsNeg(e[o]-2,s);s^=1;if(t[this.codingPos]<n){o>0?--o:++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case 4:this._addPixelsNeg(e[o]-1,s);s^=1;if(t[this.codingPos]<n){o>0?--o:++o;for(;e[o]<=t[this.codingPos]&&e[o]<n;)o+=2}break;case i:this._addPixels(n,0);this.eof=!0;break;default:(0,r.info)("bad 2d code");this._addPixels(n,0);this.err=!0}}}else{t[0]=0;this.codingPos=0;s=0;for(;t[this.codingPos]<n;){a=0;if(s)do{a+=f=this._getBlackCode()}while(f>=64);else do{a+=f=this._getWhiteCode()}while(f>=64);this._addPixels(t[this.codingPos]+a,s);s^=1}}let u=!1;this.byteAlign&&(this.inputBits&=-8);if(this.eoblock||this.row!==this.rows-1){a=this._lookBits(12);if(this.eoline)for(;a!==i&&1!==a;){this._eatBits(1);a=this._lookBits(12)}else for(;0===a;){this._eatBits(1);a=this._lookBits(12)}if(1===a){this._eatBits(12);u=!0}else a===i&&(this.eof=!0)}else this.rowsDone=!0;if(!this.eof&&this.encoding>0&&!this.rowsDone){this.nextLine2D=!this._lookBits(1);this._eatBits(1)}if(this.eoblock&&u&&this.byteAlign){a=this._lookBits(12);if(1===a){this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}if(this.encoding>=0)for(c=0;c<4;++c){a=this._lookBits(12);1!==a&&(0,r.info)("bad rtc code: "+a);this._eatBits(12);if(this.encoding>0){this._lookBits(1);this._eatBits(1)}}this.eof=!0}}else if(this.err&&this.eoline){for(;;){a=this._lookBits(13);if(a===i){this.eof=!0;return-1}if(a>>1==1)break;this._eatBits(1)}this._eatBits(12);if(this.encoding>0){this._eatBits(1);this.nextLine2D=!(1&a)}}this.outputBits=t[0]>0?t[this.codingPos=0]:t[this.codingPos=1];this.row++}if(this.outputBits>=8){l=1&this.codingPos?0:255;this.outputBits-=8;if(0===this.outputBits&&t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}}else{a=8;l=0;do{if("number"!=typeof this.outputBits)throw new r.FormatError('Invalid /CCITTFaxDecode data, "outputBits" must be a number.');if(this.outputBits>a){l<<=a;1&this.codingPos||(l|=255>>8-a);this.outputBits-=a;a=0}else{l<<=this.outputBits;1&this.codingPos||(l|=255>>8-this.outputBits);a-=this.outputBits;this.outputBits=0;if(t[this.codingPos]<n){this.codingPos++;this.outputBits=t[this.codingPos]-t[this.codingPos-1]}else if(a>0){l<<=a;a=0}}}while(a)}this.black&&(l^=255);return l}_addPixels(e,t){const n=this.codingLine;let i=this.codingPos;if(e>n[i]){if(e>this.columns){(0,r.info)("row is wrong length");this.err=!0;e=this.columns}1&i^t&&++i;n[i]=e}this.codingPos=i}_addPixelsNeg(e,t){const n=this.codingLine;let i=this.codingPos;if(e>n[i]){if(e>this.columns){(0,r.info)("row is wrong length");this.err=!0;e=this.columns}1&i^t&&++i;n[i]=e}else if(e<n[i]){if(e<0){(0,r.info)("invalid code");this.err=!0;e=0}for(;i>0&&e<n[i-1];)--i;n[i]=e}this.codingPos=i}_findTableCode(e,t,n,r){const o=r||0;for(let r=e;r<=t;++r){let e=this._lookBits(r);if(e===i)return[!0,1,!1];r<t&&(e<<=t-r);if(!o||e>=o){const t=n[e-o];if(t[0]===r){this._eatBits(r);return[!0,t[1],!0]}}}return[!1,0,!1]}_getTwoDimCode(){let e,t=0;if(this.eoblock){t=this._lookBits(7);e=o[t];if(e?.[0]>0){this._eatBits(e[0]);return e[1]}}else{const e=this._findTableCode(1,7,o);if(e[0]&&e[2])return e[1]}(0,r.info)("Bad two dim code");return i}_getWhiteCode(){let e,t=0;if(this.eoblock){t=this._lookBits(12);if(t===i)return 1;e=t>>5==0?s[t]:a[t>>3];if(e[0]>0){this._eatBits(e[0]);return e[1]}}else{let e=this._findTableCode(1,9,a);if(e[0])return e[1];e=this._findTableCode(11,12,s);if(e[0])return e[1]}(0,r.info)("bad white code");this._eatBits(1);return 1}_getBlackCode(){let e,t;if(this.eoblock){e=this._lookBits(13);if(e===i)return 1;t=e>>7==0?c[e]:e>>9==0&&e>>7!=0?l[(e>>1)-64]:f[e>>7];if(t[0]>0){this._eatBits(t[0]);return t[1]}}else{let e=this._findTableCode(2,6,f);if(e[0])return e[1];e=this._findTableCode(7,12,l,64);if(e[0])return e[1];e=this._findTableCode(10,13,c);if(e[0])return e[1]}(0,r.info)("bad black code");this._eatBits(1);return 1}_lookBits(e){let t;for(;this.inputBits<e;){if(-1===(t=this.source.next()))return 0===this.inputBits?i:this.inputBuf<<e-this.inputBits&65535>>16-e;this.inputBuf=this.inputBuf<<8|t;this.inputBits+=8}return this.inputBuf>>this.inputBits-e&65535>>16-e}_eatBits(e){(this.inputBits-=e)<0&&(this.inputBits=0)}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.JpegImage=void 0;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);var r=n(1),i=n(155),o=n(125);class JpegError extends r.BaseException{constructor(e){super(`JPEG error: ${e}`,"JpegError")}}class DNLMarkerError extends r.BaseException{constructor(e,t){super(e,"DNLMarkerError");this.scanLines=t}}class EOIMarkerError extends r.BaseException{constructor(e){super(e,"EOIMarkerError")}}const s=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]),a=4017,c=799,l=3406,f=2276,u=1567,h=3784,d=5793,p=2896;function buildHuffmanTable(e,t){let n,r,i=0,o=16;for(;o>0&&!e[o-1];)o--;const s=[{children:[],index:0}];let a,c=s[0];for(n=0;n<o;n++){for(r=0;r<e[n];r++){c=s.pop();c.children[c.index]=t[i];for(;c.index>0;)c=s.pop();c.index++;s.push(c);for(;s.length<=n;){s.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}i++}if(n+1<o){s.push(a={children:[],index:0});c.children[c.index]=a.children;c=a}}return s[0].children}function getBlockBufferOffset(e,t,n){return 64*((e.blocksPerLine+1)*t+n)}function decodeScan(e,t,n,i,a,c,l,f,u){let h=arguments.length>9&&void 0!==arguments[9]&&arguments[9];const d=n.mcusPerLine,p=n.progressive,g=t;let m=0,b=0;function readBit(){if(b>0){b--;return m>>b&1}m=e[t++];if(255===m){const r=e[t++];if(r){if(220===r&&h){t+=2;const r=(0,o.readUint16)(e,t);t+=2;if(r>0&&r!==n.scanLines)throw new DNLMarkerError("Found DNL marker (0xFFDC) while parsing scan data",r)}else if(217===r){if(h){const e=v*(8===n.precision?8:0);if(e>0&&Math.round(n.scanLines/e)>=5)throw new DNLMarkerError("Found EOI marker (0xFFD9) while parsing scan data, possibly caused by incorrect `scanLines` parameter",e)}throw new EOIMarkerError("Found EOI marker (0xFFD9) while parsing scan data")}throw new JpegError(`unexpected marker ${(m<<8|r).toString(16)}`)}}b=7;return m>>>7}function decodeHuffman(e){let t=e;for(;;){t=t[readBit()];switch(typeof t){case"number":return t;case"object":continue}throw new JpegError("invalid huffman sequence")}}function receive(e){let t=0;for(;e>0;){t=t<<1|readBit();e--}return t}function receiveAndExtend(e){if(1===e)return 1===readBit()?1:-1;const t=receive(e);return t>=1<<e-1?t:t+(-1<<e)+1}let y=0;let x,w=0;let v=0;function decodeMcu(e,t,n,r,i){const o=n%d;v=(n/d|0)*e.v+r;const s=o*e.h+i;t(e,getBlockBufferOffset(e,v,s))}function decodeBlock(e,t,n){v=n/e.blocksPerLine|0;const r=n%e.blocksPerLine;t(e,getBlockBufferOffset(e,v,r))}const T=i.length;let S,E,I,C,P,A;A=p?0===c?0===f?function decodeDCFirst(e,t){const n=decodeHuffman(e.huffmanTableDC),r=0===n?0:receiveAndExtend(n)<<u;e.blockData[t]=e.pred+=r}:function decodeDCSuccessive(e,t){e.blockData[t]|=readBit()<<u}:0===f?function decodeACFirst(e,t){if(y>0){y--;return}let n=c;const r=l;for(;n<=r;){const r=decodeHuffman(e.huffmanTableAC),i=15&r,o=r>>4;if(0===i){if(o<15){y=receive(o)+(1<<o)-1;break}n+=16;continue}n+=o;const a=s[n];e.blockData[t+a]=receiveAndExtend(i)*(1<<u);n++}}:function decodeACSuccessive(e,t){let n=c;const r=l;let i,o,a=0;for(;n<=r;){const r=t+s[n],c=e.blockData[r]<0?-1:1;switch(w){case 0:o=decodeHuffman(e.huffmanTableAC);i=15&o;a=o>>4;if(0===i)if(a<15){y=receive(a)+(1<<a);w=4}else{a=16;w=1}else{if(1!==i)throw new JpegError("invalid ACn encoding");x=receiveAndExtend(i);w=a?2:3}continue;case 1:case 2:if(e.blockData[r])e.blockData[r]+=c*(readBit()<<u);else{a--;0===a&&(w=2===w?3:0)}break;case 3:if(e.blockData[r])e.blockData[r]+=c*(readBit()<<u);else{e.blockData[r]=x<<u;w=0}break;case 4:e.blockData[r]&&(e.blockData[r]+=c*(readBit()<<u))}n++}if(4===w){y--;0===y&&(w=0)}}:function decodeBaseline(e,t){const n=decodeHuffman(e.huffmanTableDC),r=0===n?0:receiveAndExtend(n);e.blockData[t]=e.pred+=r;let i=1;for(;i<64;){const n=decodeHuffman(e.huffmanTableAC),r=15&n,o=n>>4;if(0===r){if(o<15)break;i+=16;continue}i+=o;const a=s[i];e.blockData[t+a]=receiveAndExtend(r);i++}};let k,B=0;const _=1===T?i[0].blocksPerLine*i[0].blocksPerColumn:d*n.mcusPerColumn;let O,R;for(;B<=_;){const n=a?Math.min(_-B,a):_;if(n>0){for(E=0;E<T;E++)i[E].pred=0;y=0;if(1===T){S=i[0];for(P=0;P<n;P++){decodeBlock(S,A,B);B++}}else for(P=0;P<n;P++){for(E=0;E<T;E++){S=i[E];O=S.h;R=S.v;for(I=0;I<R;I++)for(C=0;C<O;C++)decodeMcu(S,A,B,I,C)}B++}}b=0;k=findNextFileMarker(e,t);if(!k)break;if(k.invalid){const e=n>0?"unexpected":"excessive";(0,r.warn)(`decodeScan - ${e} MCU data, current marker is: ${k.invalid}`);t=k.offset}if(!(k.marker>=65488&&k.marker<=65495))break;t+=2}return t-g}function quantizeAndInverse(e,t,n){const r=e.quantizationTable,i=e.blockData;let o,s,g,m,b,y,x,w,v,T,S,E,I,C,P,A,k;if(!r)throw new JpegError("missing required Quantization Table.");for(let e=0;e<64;e+=8){v=i[t+e];T=i[t+e+1];S=i[t+e+2];E=i[t+e+3];I=i[t+e+4];C=i[t+e+5];P=i[t+e+6];A=i[t+e+7];v*=r[e];if(0!=(T|S|E|I|C|P|A)){T*=r[e+1];S*=r[e+2];E*=r[e+3];I*=r[e+4];C*=r[e+5];P*=r[e+6];A*=r[e+7];o=d*v+128>>8;s=d*I+128>>8;g=S;m=P;b=p*(T-A)+128>>8;w=p*(T+A)+128>>8;y=E<<4;x=C<<4;o=o+s+1>>1;s=o-s;k=g*h+m*u+128>>8;g=g*u-m*h+128>>8;m=k;b=b+x+1>>1;x=b-x;w=w+y+1>>1;y=w-y;o=o+m+1>>1;m=o-m;s=s+g+1>>1;g=s-g;k=b*f+w*l+2048>>12;b=b*l-w*f+2048>>12;w=k;k=y*c+x*a+2048>>12;y=y*a-x*c+2048>>12;x=k;n[e]=o+w;n[e+7]=o-w;n[e+1]=s+x;n[e+6]=s-x;n[e+2]=g+y;n[e+5]=g-y;n[e+3]=m+b;n[e+4]=m-b}else{k=d*v+512>>10;n[e]=k;n[e+1]=k;n[e+2]=k;n[e+3]=k;n[e+4]=k;n[e+5]=k;n[e+6]=k;n[e+7]=k}}for(let e=0;e<8;++e){v=n[e];T=n[e+8];S=n[e+16];E=n[e+24];I=n[e+32];C=n[e+40];P=n[e+48];A=n[e+56];if(0!=(T|S|E|I|C|P|A)){o=d*v+2048>>12;s=d*I+2048>>12;g=S;m=P;b=p*(T-A)+2048>>12;w=p*(T+A)+2048>>12;y=E;x=C;o=4112+(o+s+1>>1);s=o-s;k=g*h+m*u+2048>>12;g=g*u-m*h+2048>>12;m=k;b=b+x+1>>1;x=b-x;w=w+y+1>>1;y=w-y;o=o+m+1>>1;m=o-m;s=s+g+1>>1;g=s-g;k=b*f+w*l+2048>>12;b=b*l-w*f+2048>>12;w=k;k=y*c+x*a+2048>>12;y=y*a-x*c+2048>>12;x=k;v=o+w;A=o-w;T=s+x;P=s-x;S=g+y;C=g-y;E=m+b;I=m-b;v<16?v=0:v>=4080?v=255:v>>=4;T<16?T=0:T>=4080?T=255:T>>=4;S<16?S=0:S>=4080?S=255:S>>=4;E<16?E=0:E>=4080?E=255:E>>=4;I<16?I=0:I>=4080?I=255:I>>=4;C<16?C=0:C>=4080?C=255:C>>=4;P<16?P=0:P>=4080?P=255:P>>=4;A<16?A=0:A>=4080?A=255:A>>=4;i[t+e]=v;i[t+e+8]=T;i[t+e+16]=S;i[t+e+24]=E;i[t+e+32]=I;i[t+e+40]=C;i[t+e+48]=P;i[t+e+56]=A}else{k=d*v+8192>>14;k=k<-2040?0:k>=2024?255:k+2056>>4;i[t+e]=k;i[t+e+8]=k;i[t+e+16]=k;i[t+e+24]=k;i[t+e+32]=k;i[t+e+40]=k;i[t+e+48]=k;i[t+e+56]=k}}}function buildComponentData(e,t){const n=t.blocksPerLine,r=t.blocksPerColumn,i=new Int16Array(64);for(let e=0;e<r;e++)for(let r=0;r<n;r++){quantizeAndInverse(t,getBlockBufferOffset(t,e,r),i)}return t.blockData}function findNextFileMarker(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t;const r=e.length-1;let i=n<t?n:t;if(t>=r)return null;const s=(0,o.readUint16)(e,t);if(s>=65472&&s<=65534)return{invalid:null,marker:s,offset:t};let a=(0,o.readUint16)(e,i);for(;!(a>=65472&&a<=65534);){if(++i>=r)return null;a=(0,o.readUint16)(e,i)}return{invalid:s.toString(16),marker:a,offset:i}}t.JpegImage=class JpegImage{constructor(){let{decodeTransform:e=null,colorTransform:t=-1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this._decodeTransform=e;this._colorTransform=t}parse(e){let{dnlScanLines:t=null}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};function readDataBlock(){const t=(0,o.readUint16)(e,a);a+=2;let n=a+t-2;const i=findNextFileMarker(e,n,a);if(i?.invalid){(0,r.warn)("readDataBlock - incorrect length, current marker is: "+i.invalid);n=i.offset}const s=e.subarray(a,n);a+=s.length;return s}function prepareComponents(e){const t=Math.ceil(e.samplesPerLine/8/e.maxH),n=Math.ceil(e.scanLines/8/e.maxV);for(const r of e.components){const i=Math.ceil(Math.ceil(e.samplesPerLine/8)*r.h/e.maxH),o=Math.ceil(Math.ceil(e.scanLines/8)*r.v/e.maxV),s=t*r.h,a=64*(n*r.v)*(s+1);r.blockData=new Int16Array(a);r.blocksPerLine=i;r.blocksPerColumn=o}e.mcusPerLine=t;e.mcusPerColumn=n}let n,i,a=0,c=null,l=null,f=0;const u=[],h=[],d=[];let p=(0,o.readUint16)(e,a);a+=2;if(65496!==p)throw new JpegError("SOI not found");p=(0,o.readUint16)(e,a);a+=2;e:for(;65497!==p;){let g,m,b;switch(p){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:const y=readDataBlock();65504===p&&74===y[0]&&70===y[1]&&73===y[2]&&70===y[3]&&0===y[4]&&(c={version:{major:y[5],minor:y[6]},densityUnits:y[7],xDensity:y[8]<<8|y[9],yDensity:y[10]<<8|y[11],thumbWidth:y[12],thumbHeight:y[13],thumbData:y.subarray(14,14+3*y[12]*y[13])});65518===p&&65===y[0]&&100===y[1]&&111===y[2]&&98===y[3]&&101===y[4]&&(l={version:y[5]<<8|y[6],flags0:y[7]<<8|y[8],flags1:y[9]<<8|y[10],transformCode:y[11]});break;case 65499:const x=(0,o.readUint16)(e,a);a+=2;const w=x+a-2;let v;for(;a<w;){const t=e[a++],n=new Uint16Array(64);if(t>>4==0)for(m=0;m<64;m++){v=s[m];n[v]=e[a++]}else{if(t>>4!=1)throw new JpegError("DQT - invalid table spec");for(m=0;m<64;m++){v=s[m];n[v]=(0,o.readUint16)(e,a);a+=2}}u[15&t]=n}break;case 65472:case 65473:case 65474:if(n)throw new JpegError("Only single frame JPEGs supported");a+=2;n={};n.extended=65473===p;n.progressive=65474===p;n.precision=e[a++];const T=(0,o.readUint16)(e,a);a+=2;n.scanLines=t||T;n.samplesPerLine=(0,o.readUint16)(e,a);a+=2;n.components=[];n.componentIds={};const S=e[a++];let E=0,I=0;for(g=0;g<S;g++){const t=e[a],r=e[a+1]>>4,i=15&e[a+1];E<r&&(E=r);I<i&&(I=i);const o=e[a+2];b=n.components.push({h:r,v:i,quantizationId:o,quantizationTable:null});n.componentIds[t]=b-1;a+=3}n.maxH=E;n.maxV=I;prepareComponents(n);break;case 65476:const C=(0,o.readUint16)(e,a);a+=2;for(g=2;g<C;){const t=e[a++],n=new Uint8Array(16);let r=0;for(m=0;m<16;m++,a++)r+=n[m]=e[a];const i=new Uint8Array(r);for(m=0;m<r;m++,a++)i[m]=e[a];g+=17+r;(t>>4==0?d:h)[15&t]=buildHuffmanTable(n,i)}break;case 65501:a+=2;i=(0,o.readUint16)(e,a);a+=2;break;case 65498:const P=1==++f&&!t;a+=2;const A=e[a++],k=[];for(g=0;g<A;g++){const t=e[a++],r=n.componentIds[t],i=n.components[r];i.index=t;const o=e[a++];i.huffmanTableDC=d[o>>4];i.huffmanTableAC=h[15&o];k.push(i)}const B=e[a++],_=e[a++],O=e[a++];try{const t=decodeScan(e,a,n,k,i,B,_,O>>4,15&O,P);a+=t}catch(t){if(t instanceof DNLMarkerError){(0,r.warn)(`${t.message} -- attempting to re-parse the JPEG image.`);return this.parse(e,{dnlScanLines:t.scanLines})}if(t instanceof EOIMarkerError){(0,r.warn)(`${t.message} -- ignoring the rest of the image data.`);break e}throw t}break;case 65500:a+=4;break;case 65535:255!==e[a]&&a--;break;default:const R=findNextFileMarker(e,a-2,a-3);if(R?.invalid){(0,r.warn)("JpegImage.parse - unexpected data, current marker is: "+R.invalid);a=R.offset;break}if(!R||a>=e.length-1){(0,r.warn)("JpegImage.parse - reached the end of the image data without finding an EOI marker (0xFFD9).");break e}throw new JpegError("JpegImage.parse - unknown marker: "+p.toString(16))}p=(0,o.readUint16)(e,a);a+=2}this.width=n.samplesPerLine;this.height=n.scanLines;this.jfif=c;this.adobe=l;this.components=[];for(const e of n.components){const t=u[e.quantizationId];t&&(e.quantizationTable=t);this.components.push({index:e.index,output:buildComponentData(0,e),scaleX:e.h/n.maxH,scaleY:e.v/n.maxV,blocksPerLine:e.blocksPerLine,blocksPerColumn:e.blocksPerColumn})}this.numComponents=this.components.length}_getLinearizedBlockData(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=this.width/e,i=this.height/t;let o,s,a,c,l,f,u,h,d,p,g,m=0;const b=this.components.length,y=e*t*b,x=new Uint8ClampedArray(y),w=new Uint32Array(e),v=4294967288;let T;for(u=0;u<b;u++){o=this.components[u];s=o.scaleX*r;a=o.scaleY*i;m=u;g=o.output;c=o.blocksPerLine+1<<3;if(s!==T){for(l=0;l<e;l++){h=0|l*s;w[l]=(h&v)<<3|7&h}T=s}for(f=0;f<t;f++){h=0|f*a;p=c*(h&v)|(7&h)<<3;for(l=0;l<e;l++){x[m]=g[p+w[l]];m+=b}}}let S=this._decodeTransform;n||4!==b||S||(S=new Int32Array([-256,255,-256,255,-256,255,-256,255]));if(S)for(u=0;u<y;)for(h=0,d=0;h<b;h++,u++,d+=2)x[u]=(x[u]*S[d]>>8)+S[d+1];return x}get _isColorConversionNeeded(){return this.adobe?!!this.adobe.transformCode:3===this.numComponents?0!==this._colorTransform&&(82!==this.components[0].index||71!==this.components[1].index||66!==this.components[2].index):1===this._colorTransform}_convertYccToRgb(e){let t,n,r;for(let i=0,o=e.length;i<o;i+=3){t=e[i];n=e[i+1];r=e[i+2];e[i]=t-179.456+1.402*r;e[i+1]=t+135.459-.344*n-.714*r;e[i+2]=t-226.816+1.772*n}return e}_convertYccToRgba(e,t){for(let n=0,r=0,i=e.length;n<i;n+=3,r+=4){const i=e[n],o=e[n+1],s=e[n+2];t[r]=i-179.456+1.402*s;t[r+1]=i+135.459-.344*o-.714*s;t[r+2]=i-226.816+1.772*o;t[r+3]=255}return t}_convertYcckToRgb(e){let t,n,r,i,o=0;for(let s=0,a=e.length;s<a;s+=4){t=e[s];n=e[s+1];r=e[s+2];i=e[s+3];e[o++]=n*(-660635669420364e-19*n+.000437130475926232*r-54080610064599e-18*t+.00048449797120281*i-.154362151871126)-122.67195406894+r*(-.000957964378445773*r+.000817076911346625*t-.00477271405408747*i+1.53380253221734)+t*(.000961250184130688*t-.00266257332283933*i+.48357088451265)+i*(-.000336197177618394*i+.484791561490776);e[o++]=107.268039397724+n*(219927104525741e-19*n-.000640992018297945*r+.000659397001245577*t+.000426105652938837*i-.176491792462875)+r*(-.000778269941513683*r+.00130872261408275*t+.000770482631801132*i-.151051492775562)+t*(.00126935368114843*t-.00265090189010898*i+.25802910206845)+i*(-.000318913117588328*i-.213742400323665);e[o++]=n*(-.000570115196973677*n-263409051004589e-19*r+.0020741088115012*t-.00288260236853442*i+.814272968359295)-20.810012546947+r*(-153496057440975e-19*r-.000132689043961446*t+.000560833691242812*i-.195152027534049)+t*(.00174418132927582*t-.00255243321439347*i+.116935020465145)+i*(-.000343531996510555*i+.24165260232407)}return e.subarray(0,o)}_convertYcckToRgba(e){for(let t=0,n=e.length;t<n;t+=4){const n=e[t],r=e[t+1],i=e[t+2],o=e[t+3];e[t]=r*(-660635669420364e-19*r+.000437130475926232*i-54080610064599e-18*n+.00048449797120281*o-.154362151871126)-122.67195406894+i*(-.000957964378445773*i+.000817076911346625*n-.00477271405408747*o+1.53380253221734)+n*(.000961250184130688*n-.00266257332283933*o+.48357088451265)+o*(-.000336197177618394*o+.484791561490776);e[t+1]=107.268039397724+r*(219927104525741e-19*r-.000640992018297945*i+.000659397001245577*n+.000426105652938837*o-.176491792462875)+i*(-.000778269941513683*i+.00130872261408275*n+.000770482631801132*o-.151051492775562)+n*(.00126935368114843*n-.00265090189010898*o+.25802910206845)+o*(-.000318913117588328*o-.213742400323665);e[t+2]=r*(-.000570115196973677*r-263409051004589e-19*i+.0020741088115012*n-.00288260236853442*o+.814272968359295)-20.810012546947+i*(-153496057440975e-19*i-.000132689043961446*n+.000560833691242812*o-.195152027534049)+n*(.00174418132927582*n-.00255243321439347*o+.116935020465145)+o*(-.000343531996510555*o+.24165260232407);e[t+3]=255}return e}_convertYcckToCmyk(e){let t,n,r;for(let i=0,o=e.length;i<o;i+=4){t=e[i];n=e[i+1];r=e[i+2];e[i]=434.456-t-1.402*r;e[i+1]=119.541-t+.344*n+.714*r;e[i+2]=481.816-t-1.772*n}return e}_convertCmykToRgb(e){let t,n,r,i,o=0;for(let s=0,a=e.length;s<a;s+=4){t=e[s];n=e[s+1];r=e[s+2];i=e[s+3];e[o++]=255+t*(-6747147073602441e-20*t+.0008379262121013727*n+.0002894718188643294*r+.003264231057537806*i-1.1185611867203937)+n*(26374107616089405e-21*n-8626949158638572e-20*r-.0002748769067499491*i-.02155688794978967)+r*(-3878099212869363e-20*r-.0003267808279485286*i+.0686742238595345)-i*(.0003361971776183937*i+.7430659151342254);e[o++]=255+t*(.00013596372813588848*t+.000924537132573585*n+.00010567359618683593*r+.0004791864687436512*i-.3109689587515875)+n*(-.00023545346108370344*n+.0002702845253534714*r+.0020200308977307156*i-.7488052167015494)+r*(6834815998235662e-20*r+.00015168452363460973*i-.09751927774728933)-i*(.0003189131175883281*i+.7364883807733168);e[o++]=255+t*(13598650411385307e-21*t+.00012423956175490851*n+.0004751985097583589*r-36729317476630422e-22*i-.05562186980264034)+n*(.00016141380598724676*n+.0009692239130725186*r+.0007782692450036253*i-.44015232367526463)+r*(5.068882914068769e-7*r+.0017778369011375071*i-.7591454649749609)-i*(.0003435319965105553*i+.7063770186160144)}return e.subarray(0,o)}_convertCmykToRgba(e){for(let t=0,n=e.length;t<n;t+=4){const n=e[t],r=e[t+1],i=e[t+2],o=e[t+3];e[t]=255+n*(-6747147073602441e-20*n+.0008379262121013727*r+.0002894718188643294*i+.003264231057537806*o-1.1185611867203937)+r*(26374107616089405e-21*r-8626949158638572e-20*i-.0002748769067499491*o-.02155688794978967)+i*(-3878099212869363e-20*i-.0003267808279485286*o+.0686742238595345)-o*(.0003361971776183937*o+.7430659151342254);e[t+1]=255+n*(.00013596372813588848*n+.000924537132573585*r+.00010567359618683593*i+.0004791864687436512*o-.3109689587515875)+r*(-.00023545346108370344*r+.0002702845253534714*i+.0020200308977307156*o-.7488052167015494)+i*(6834815998235662e-20*i+.00015168452363460973*o-.09751927774728933)-o*(.0003189131175883281*o+.7364883807733168);e[t+2]=255+n*(13598650411385307e-21*n+.00012423956175490851*r+.0004751985097583589*i-36729317476630422e-22*o-.05562186980264034)+r*(.00016141380598724676*r+.0009692239130725186*i+.0007782692450036253*o-.44015232367526463)+i*(5.068882914068769e-7*i+.0017778369011375071*o-.7591454649749609)-o*(.0003435319965105553*o+.7063770186160144);e[t+3]=255}return e}getData(e){let{width:t,height:n,forceRGBA:r=!1,forceRGB:o=!1,isSourcePDF:s=!1}=e;if(this.numComponents>4)throw new JpegError("Unsupported color mode");const a=this._getLinearizedBlockData(t,n,s);if(1===this.numComponents&&(r||o)){const e=a.length*(r?4:3),t=new Uint8ClampedArray(e);let n=0;if(r)(0,i.grayToRGBA)(a,new Uint32Array(t.buffer));else for(const e of a){t[n++]=e;t[n++]=e;t[n++]=e}return t}if(3===this.numComponents&&this._isColorConversionNeeded){if(r){const e=new Uint8ClampedArray(a.length/3*4);return this._convertYccToRgba(a,e)}return this._convertYccToRgb(a)}if(4===this.numComponents){if(this._isColorConversionNeeded)return r?this._convertYcckToRgba(a):o?this._convertYcckToRgb(a):this._convertYcckToCmyk(a);if(r)return this._convertCmykToRgba(a);if(o)return this._convertCmykToRgb(a)}return a}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.convertBlackAndWhiteToRGBA=convertBlackAndWhiteToRGBA;t.convertToRGBA=function convertToRGBA(e){switch(e.kind){case r.ImageKind.GRAYSCALE_1BPP:return convertBlackAndWhiteToRGBA(e);case r.ImageKind.RGB_24BPP:return function convertRGBToRGBA(e){let{src:t,srcPos:n=0,dest:i,destPos:o=0,width:s,height:a}=e,c=0;const l=t.length>>2,f=new Uint32Array(t.buffer,n,l);if(r.FeatureTest.isLittleEndian){for(;c<l-2;c+=3,o+=4){const e=f[c],t=f[c+1],n=f[c+2];i[o]=4278190080|e;i[o+1]=e>>>24|t<<8|4278190080;i[o+2]=t>>>16|n<<16|4278190080;i[o+3]=n>>>8|4278190080}for(let e=4*c,n=t.length;e<n;e+=3)i[o++]=t[e]|t[e+1]<<8|t[e+2]<<16|4278190080}else{for(;c<l-2;c+=3,o+=4){const e=f[c],t=f[c+1],n=f[c+2];i[o]=255|e;i[o+1]=e<<24|t>>>8|255;i[o+2]=t<<16|n>>>16|255;i[o+3]=n<<8|255}for(let e=4*c,n=t.length;e<n;e+=3)i[o++]=t[e]<<24|t[e+1]<<16|t[e+2]<<8|255}return{srcPos:n,destPos:o}}(e)}return null};t.grayToRGBA=function grayToRGBA(e,t){if(r.FeatureTest.isLittleEndian)for(let n=0,r=e.length;n<r;n++)t[n]=65793*e[n]|4278190080;else for(let n=0,r=e.length;n<r;n++)t[n]=16843008*e[n]|255};n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var r=n(1);function convertBlackAndWhiteToRGBA(e){let{src:t,srcPos:n=0,dest:i,width:o,height:s,nonBlackColor:a=4294967295,inverseDecode:c=!1}=e;const l=r.FeatureTest.isLittleEndian?4278190080:255,[f,u]=c?[a,l]:[l,a],h=o>>3,d=7&o,p=t.length;i=new Uint32Array(i.buffer);let g=0;for(let e=0;e<s;e++){for(const e=n+h;n<e;n++){const e=n<p?t[n]:255;i[g++]=128&e?u:f;i[g++]=64&e?u:f;i[g++]=32&e?u:f;i[g++]=16&e?u:f;i[g++]=8&e?u:f;i[g++]=4&e?u:f;i[g++]=2&e?u:f;i[g++]=1&e?u:f}if(0===d)continue;const e=n<p?t[n++]:255;for(let t=0;t<d;t++)i[g++]=e&1<<7-t?u:f}return{srcPos:n,destPos:g}}},(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0});t.JpxImage=void 0;n(89);n(2);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var r=n(1),i=n(125),o=n(152);class JpxError extends r.BaseException{constructor(e){super(`JPX error: ${e}`,"JpxError")}}const s={LL:0,LH:1,HL:1,HH:2};t.JpxImage=class JpxImage{constructor(){this.failOnCorruptedImage=!1}parse(e){if(65359===(0,i.readUint16)(e,0)){this.parseCodestream(e,0,e.length);return}const t=e.length;let n=0;for(;n<t;){let o=8,s=(0,i.readUint32)(e,n);const a=(0,i.readUint32)(e,n+4);n+=o;if(1===s){s=4294967296*(0,i.readUint32)(e,n)+(0,i.readUint32)(e,n+4);n+=8;o+=8}0===s&&(s=t-n+o);if(s<o)throw new JpxError("Invalid box field size");const c=s-o;let l=!0;switch(a){case 1785737832:l=!1;break;case 1668246642:const t=e[n];if(1===t){const t=(0,i.readUint32)(e,n+3);switch(t){case 16:case 17:case 18:break;default:(0,r.warn)("Unknown colorspace "+t)}}else 2===t&&(0,r.info)("ICC profile not supported");break;case 1785737827:this.parseCodestream(e,n,n+c);break;case 1783636e3:218793738!==(0,i.readUint32)(e,n)&&(0,r.warn)("Invalid JP2 signature");break;case 1783634458:case 1718909296:case 1920099697:case 1919251232:case 1768449138:break;default:const o=String.fromCharCode(a>>24&255,a>>16&255,a>>8&255,255&a);(0,r.warn)(`Unsupported header type ${a} (${o}).`)}l&&(n+=c)}}parseImageProperties(e){let t=e.getByte();for(;t>=0;){const n=t;t=e.getByte();if(65361===(n<<8|t)){e.skip(4);const t=e.getInt32()>>>0,n=e.getInt32()>>>0,r=e.getInt32()>>>0,i=e.getInt32()>>>0;e.skip(16);const o=e.getUint16();this.width=t-r;this.height=n-i;this.componentsCount=o;this.bitsPerComponent=8;return}}throw new JpxError("No size marker found in JPX stream")}parseCodestream(e,t,n){const o={};let s=!1;try{let a=t;for(;a+1<n;){const t=(0,i.readUint16)(e,a);a+=2;let n,c,l,f,u,h,d=0;switch(t){case 65359:o.mainHeader=!0;break;case 65497:break;case 65361:d=(0,i.readUint16)(e,a);const p={};p.Xsiz=(0,i.readUint32)(e,a+4);p.Ysiz=(0,i.readUint32)(e,a+8);p.XOsiz=(0,i.readUint32)(e,a+12);p.YOsiz=(0,i.readUint32)(e,a+16);p.XTsiz=(0,i.readUint32)(e,a+20);p.YTsiz=(0,i.readUint32)(e,a+24);p.XTOsiz=(0,i.readUint32)(e,a+28);p.YTOsiz=(0,i.readUint32)(e,a+32);const g=(0,i.readUint16)(e,a+36);p.Csiz=g;const m=[];n=a+38;for(let t=0;t<g;t++){const t={precision:1+(127&e[n]),isSigned:!!(128&e[n]),XRsiz:e[n+1],YRsiz:e[n+2]};n+=3;calculateComponentDimensions(t,p);m.push(t)}o.SIZ=p;o.components=m;calculateTileGrids(o,m);o.QCC=[];o.COC=[];break;case 65372:d=(0,i.readUint16)(e,a);const b={};n=a+2;c=e[n++];switch(31&c){case 0:f=8;u=!0;break;case 1:f=16;u=!1;break;case 2:f=16;u=!0;break;default:throw new Error("Invalid SQcd value "+c)}b.noQuantization=8===f;b.scalarExpounded=u;b.guardBits=c>>5;l=[];for(;n<d+a;){const t={};if(8===f){t.epsilon=e[n++]>>3;t.mu=0}else{t.epsilon=e[n]>>3;t.mu=(7&e[n])<<8|e[n+1];n+=2}l.push(t)}b.SPqcds=l;if(o.mainHeader)o.QCD=b;else{o.currentTile.QCD=b;o.currentTile.QCC=[]}break;case 65373:d=(0,i.readUint16)(e,a);const y={};n=a+2;let x;if(o.SIZ.Csiz<257)x=e[n++];else{x=(0,i.readUint16)(e,n);n+=2}c=e[n++];switch(31&c){case 0:f=8;u=!0;break;case 1:f=16;u=!1;break;case 2:f=16;u=!0;break;default:throw new Error("Invalid SQcd value "+c)}y.noQuantization=8===f;y.scalarExpounded=u;y.guardBits=c>>5;l=[];for(;n<d+a;){const t={};if(8===f){t.epsilon=e[n++]>>3;t.mu=0}else{t.epsilon=e[n]>>3;t.mu=(7&e[n])<<8|e[n+1];n+=2}l.push(t)}y.SPqcds=l;o.mainHeader?o.QCC[x]=y:o.currentTile.QCC[x]=y;break;case 65362:d=(0,i.readUint16)(e,a);const w={};n=a+2;const v=e[n++];w.entropyCoderWithCustomPrecincts=!!(1&v);w.sopMarkerUsed=!!(2&v);w.ephMarkerUsed=!!(4&v);w.progressionOrder=e[n++];w.layersCount=(0,i.readUint16)(e,n);n+=2;w.multipleComponentTransform=e[n++];w.decompositionLevelsCount=e[n++];w.xcb=2+(15&e[n++]);w.ycb=2+(15&e[n++]);const T=e[n++];w.selectiveArithmeticCodingBypass=!!(1&T);w.resetContextProbabilities=!!(2&T);w.terminationOnEachCodingPass=!!(4&T);w.verticallyStripe=!!(8&T);w.predictableTermination=!!(16&T);w.segmentationSymbolUsed=!!(32&T);w.reversibleTransformation=e[n++];if(w.entropyCoderWithCustomPrecincts){const t=[];for(;n<d+a;){const r=e[n++];t.push({PPx:15&r,PPy:r>>4})}w.precinctsSizes=t}const S=[];w.selectiveArithmeticCodingBypass&&S.push("selectiveArithmeticCodingBypass");w.terminationOnEachCodingPass&&S.push("terminationOnEachCodingPass");w.verticallyStripe&&S.push("verticallyStripe");w.predictableTermination&&S.push("predictableTermination");if(S.length>0){s=!0;(0,r.warn)(`JPX: Unsupported COD options (${S.join(", ")}).`)}if(o.mainHeader)o.COD=w;else{o.currentTile.COD=w;o.currentTile.COC=[]}break;case 65424:d=(0,i.readUint16)(e,a);h={};h.index=(0,i.readUint16)(e,a+2);h.length=(0,i.readUint32)(e,a+4);h.dataEnd=h.length+a-2;h.partIndex=e[a+8];h.partsCount=e[a+9];o.mainHeader=!1;if(0===h.partIndex){h.COD=o.COD;h.COC=o.COC.slice(0);h.QCD=o.QCD;h.QCC=o.QCC.slice(0)}o.currentTile=h;break;case 65427:h=o.currentTile;if(0===h.partIndex){initializeTile(o,h.index);buildPackets(o)}d=h.dataEnd-a;parseTilePackets(o,e,a,d);break;case 65363:(0,r.warn)("JPX: Codestream code 0xFF53 (COC) is not implemented.");case 65365:case 65367:case 65368:case 65380:d=(0,i.readUint16)(e,a);break;default:throw new Error("Unknown codestream code: "+t.toString(16))}a+=d}}catch(e){if(s||this.failOnCorruptedImage)throw new JpxError(e.message);(0,r.warn)(`JPX: Trying to recover from: "${e.message}".`)}this.tiles=function transformComponents(e){const t=e.SIZ,n=e.components,r=t.Csiz,i=[];for(let t=0,o=e.tiles.length;t<o;t++){const o=e.tiles[t],s=[];for(let t=0;t<r;t++)s[t]=transformTile(e,o,t);const a=s[0],c=new Uint8ClampedArray(a.items.length*r),l={left:a.left,top:a.top,width:a.width,height:a.height,items:c};let f,u,h,d,p,g,m,b=0;if(o.codingStyleDefaultParameters.multipleComponentTransform){const e=4===r,t=s[0].items,i=s[1].items,a=s[2].items,l=e?s[3].items:null;f=n[0].precision-8;u=.5+(128<<f);const y=o.components[0],x=r-3;d=t.length;if(y.codingStyleParameters.reversibleTransformation)for(h=0;h<d;h++,b+=x){p=t[h]+u;g=i[h];m=a[h];const e=p-(m+g>>2);c[b++]=e+m>>f;c[b++]=e>>f;c[b++]=e+g>>f}else for(h=0;h<d;h++,b+=x){p=t[h]+u;g=i[h];m=a[h];c[b++]=p+1.402*m>>f;c[b++]=p-.34413*g-.71414*m>>f;c[b++]=p+1.772*g>>f}if(e)for(h=0,b=3;h<d;h++,b+=4)c[b]=l[h]+u>>f}else for(let e=0;e<r;e++){const t=s[e].items;f=n[e].precision-8;u=.5+(128<<f);for(b=e,h=0,d=t.length;h<d;h++){c[b]=t[h]+u>>f;b+=r}}i.push(l)}return i}(o);this.width=o.SIZ.Xsiz-o.SIZ.XOsiz;this.height=o.SIZ.Ysiz-o.SIZ.YOsiz;this.componentsCount=o.SIZ.Csiz}};function calculateComponentDimensions(e,t){e.x0=Math.ceil(t.XOsiz/e.XRsiz);e.x1=Math.ceil(t.Xsiz/e.XRsiz);e.y0=Math.ceil(t.YOsiz/e.YRsiz);e.y1=Math.ceil(t.Ysiz/e.YRsiz);e.width=e.x1-e.x0;e.height=e.y1-e.y0}function calculateTileGrids(e,t){const n=e.SIZ,r=[];let i;const o=Math.ceil((n.Xsiz-n.XTOsiz)/n.XTsiz),s=Math.ceil((n.Ysiz-n.YTOsiz)/n.YTsiz);for(let e=0;e<s;e++)for(let t=0;t<o;t++){i={};i.tx0=Math.max(n.XTOsiz+t*n.XTsiz,n.XOsiz);i.ty0=Math.max(n.YTOsiz+e*n.YTsiz,n.YOsiz);i.tx1=Math.min(n.XTOsiz+(t+1)*n.XTsiz,n.Xsiz);i.ty1=Math.min(n.YTOsiz+(e+1)*n.YTsiz,n.Ysiz);i.width=i.tx1-i.tx0;i.height=i.ty1-i.ty0;i.components=[];r.push(i)}e.tiles=r;for(let e=0,o=n.Csiz;e<o;e++){const n=t[e];for(let t=0,o=r.length;t<o;t++){const o={};i=r[t];o.tcx0=Math.ceil(i.tx0/n.XRsiz);o.tcy0=Math.ceil(i.ty0/n.YRsiz);o.tcx1=Math.ceil(i.tx1/n.XRsiz);o.tcy1=Math.ceil(i.ty1/n.YRsiz);o.width=o.tcx1-o.tcx0;o.height=o.tcy1-o.tcy0;i.components[e]=o}}}function getBlocksDimensions(e,t,n){const r=t.codingStyleParameters,i={};if(r.entropyCoderWithCustomPrecincts){i.PPx=r.precinctsSizes[n].PPx;i.PPy=r.precinctsSizes[n].PPy}else{i.PPx=15;i.PPy=15}i.xcb_=n>0?Math.min(r.xcb,i.PPx-1):Math.min(r.xcb,i.PPx);i.ycb_=n>0?Math.min(r.ycb,i.PPy-1):Math.min(r.ycb,i.PPy);return i}function buildPrecincts(e,t,n){const r=1<<n.PPx,i=1<<n.PPy,o=0===t.resLevel,s=1<<n.PPx+(o?0:-1),a=1<<n.PPy+(o?0:-1),c=t.trx1>t.trx0?Math.ceil(t.trx1/r)-Math.floor(t.trx0/r):0,l=t.try1>t.try0?Math.ceil(t.try1/i)-Math.floor(t.try0/i):0,f=c*l;t.precinctParameters={precinctWidth:r,precinctHeight:i,numprecinctswide:c,numprecinctshigh:l,numprecincts:f,precinctWidthInSubband:s,precinctHeightInSubband:a}}function buildCodeblocks(e,t,n){const r=n.xcb_,i=n.ycb_,o=1<<r,s=1<<i,a=t.tbx0>>r,c=t.tby0>>i,l=t.tbx1+o-1>>r,f=t.tby1+s-1>>i,u=t.resolution.precinctParameters,h=[],d=[];let p,g,m,b;for(g=c;g<f;g++)for(p=a;p<l;p++){m={cbx:p,cby:g,tbx0:o*p,tby0:s*g,tbx1:o*(p+1),tby1:s*(g+1)};m.tbx0_=Math.max(t.tbx0,m.tbx0);m.tby0_=Math.max(t.tby0,m.tby0);m.tbx1_=Math.min(t.tbx1,m.tbx1);m.tby1_=Math.min(t.tby1,m.tby1);b=Math.floor((m.tbx0_-t.tbx0)/u.precinctWidthInSubband)+Math.floor((m.tby0_-t.tby0)/u.precinctHeightInSubband)*u.numprecinctswide;m.precinctNumber=b;m.subbandType=t.type;m.Lblock=3;if(m.tbx1_<=m.tbx0_||m.tby1_<=m.tby0_)continue;h.push(m);let e=d[b];if(void 0!==e){p<e.cbxMin?e.cbxMin=p:p>e.cbxMax&&(e.cbxMax=p);g<e.cbyMin?e.cbxMin=g:g>e.cbyMax&&(e.cbyMax=g)}else d[b]=e={cbxMin:p,cbyMin:g,cbxMax:p,cbyMax:g};m.precinct=e}t.codeblockParameters={codeblockWidth:r,codeblockHeight:i,numcodeblockwide:l-a+1,numcodeblockhigh:f-c+1};t.codeblocks=h;t.precincts=d}function createPacket(e,t,n){const r=[],i=e.subbands;for(let e=0,n=i.length;e<n;e++){const n=i[e].codeblocks;for(let e=0,i=n.length;e<i;e++){const i=n[e];i.precinctNumber===t&&r.push(i)}}return{layerNumber:n,codeblocks:r}}function LayerResolutionComponentPositionIterator(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=r.codingStyleDefaultParameters.layersCount,o=t.Csiz;let s=0;for(let e=0;e<o;e++)s=Math.max(s,r.components[e].codingStyleParameters.decompositionLevelsCount);let a=0,c=0,l=0,f=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<i;a++){for(;c<=s;c++){for(;l<o;l++){const e=r.components[l];if(c>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[c],n=t.precinctParameters.numprecincts;for(;f<n;){const e=createPacket(t,f,a);f++;return e}f=0}l=0}c=0}throw new JpxError("Out of packets")}}function ResolutionLayerComponentPositionIterator(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=r.codingStyleDefaultParameters.layersCount,o=t.Csiz;let s=0;for(let e=0;e<o;e++)s=Math.max(s,r.components[e].codingStyleParameters.decompositionLevelsCount);let a=0,c=0,l=0,f=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<=s;a++){for(;c<i;c++){for(;l<o;l++){const e=r.components[l];if(a>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[a],n=t.precinctParameters.numprecincts;for(;f<n;){const e=createPacket(t,f,c);f++;return e}f=0}l=0}c=0}throw new JpxError("Out of packets")}}function ResolutionPositionComponentLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=r.codingStyleDefaultParameters.layersCount,o=t.Csiz;let s,a,c,l,f=0;for(c=0;c<o;c++){const e=r.components[c];f=Math.max(f,e.codingStyleParameters.decompositionLevelsCount)}const u=new Int32Array(f+1);for(a=0;a<=f;++a){let e=0;for(c=0;c<o;++c){const t=r.components[c].resolutions;a<t.length&&(e=Math.max(e,t[a].precinctParameters.numprecincts))}u[a]=e}s=0;a=0;c=0;l=0;this.nextPacket=function JpxImage_nextPacket(){for(;a<=f;a++){for(;l<u[a];l++){for(;c<o;c++){const e=r.components[c];if(a>e.codingStyleParameters.decompositionLevelsCount)continue;const t=e.resolutions[a],n=t.precinctParameters.numprecincts;if(!(l>=n)){for(;s<i;){const e=createPacket(t,l,s);s++;return e}s=0}}c=0}l=0}throw new JpxError("Out of packets")}}function PositionComponentResolutionLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=r.codingStyleDefaultParameters.layersCount,o=t.Csiz,s=getPrecinctSizesInImageScale(r),a=s;let c=0,l=0,f=0,u=0,h=0;this.nextPacket=function JpxImage_nextPacket(){for(;h<a.maxNumHigh;h++){for(;u<a.maxNumWide;u++){for(;f<o;f++){const e=r.components[f],t=e.codingStyleParameters.decompositionLevelsCount;for(;l<=t;l++){const t=e.resolutions[l],n=s.components[f].resolutions[l],r=getPrecinctIndexIfExist(u,h,n,a,t);if(null!==r){for(;c<i;){const e=createPacket(t,r,c);c++;return e}c=0}}l=0}f=0}u=0}throw new JpxError("Out of packets")}}function ComponentPositionResolutionLayerIterator(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=r.codingStyleDefaultParameters.layersCount,o=t.Csiz,s=getPrecinctSizesInImageScale(r);let a=0,c=0,l=0,f=0,u=0;this.nextPacket=function JpxImage_nextPacket(){for(;l<o;++l){const e=r.components[l],t=s.components[l],n=e.codingStyleParameters.decompositionLevelsCount;for(;u<t.maxNumHigh;u++){for(;f<t.maxNumWide;f++){for(;c<=n;c++){const n=e.resolutions[c],r=t.resolutions[c],o=getPrecinctIndexIfExist(f,u,r,t,n);if(null!==o){for(;a<i;){const e=createPacket(n,o,a);a++;return e}a=0}}c=0}f=0}u=0}throw new JpxError("Out of packets")}}function getPrecinctIndexIfExist(e,t,n,r,i){const o=e*r.minWidth,s=t*r.minHeight;if(o%n.width!=0||s%n.height!=0)return null;const a=s/n.width*i.precinctParameters.numprecinctswide;return o/n.height+a}function getPrecinctSizesInImageScale(e){const t=e.components.length;let n=Number.MAX_VALUE,r=Number.MAX_VALUE,i=0,o=0;const s=new Array(t);for(let a=0;a<t;a++){const t=e.components[a],c=t.codingStyleParameters.decompositionLevelsCount,l=new Array(c+1);let f=Number.MAX_VALUE,u=Number.MAX_VALUE,h=0,d=0,p=1;for(let e=c;e>=0;--e){const n=t.resolutions[e],r=p*n.precinctParameters.precinctWidth,i=p*n.precinctParameters.precinctHeight;f=Math.min(f,r);u=Math.min(u,i);h=Math.max(h,n.precinctParameters.numprecinctswide);d=Math.max(d,n.precinctParameters.numprecinctshigh);l[e]={width:r,height:i};p<<=1}n=Math.min(n,f);r=Math.min(r,u);i=Math.max(i,h);o=Math.max(o,d);s[a]={resolutions:l,minWidth:f,minHeight:u,maxNumWide:h,maxNumHigh:d}}return{components:s,minWidth:n,minHeight:r,maxNumWide:i,maxNumHigh:o}}function buildPackets(e){const t=e.SIZ,n=e.currentTile.index,r=e.tiles[n],i=t.Csiz;for(let e=0;e<i;e++){const t=r.components[e],n=t.codingStyleParameters.decompositionLevelsCount,i=[],o=[];for(let e=0;e<=n;e++){const r=getBlocksDimensions(0,t,e),s={},a=1<<n-e;s.trx0=Math.ceil(t.tcx0/a);s.try0=Math.ceil(t.tcy0/a);s.trx1=Math.ceil(t.tcx1/a);s.try1=Math.ceil(t.tcy1/a);s.resLevel=e;buildPrecincts(0,s,r);i.push(s);let c;if(0===e){c={};c.type="LL";c.tbx0=Math.ceil(t.tcx0/a);c.tby0=Math.ceil(t.tcy0/a);c.tbx1=Math.ceil(t.tcx1/a);c.tby1=Math.ceil(t.tcy1/a);c.resolution=s;buildCodeblocks(0,c,r);o.push(c);s.subbands=[c]}else{const i=1<<n-e+1,a=[];c={};c.type="HL";c.tbx0=Math.ceil(t.tcx0/i-.5);c.tby0=Math.ceil(t.tcy0/i);c.tbx1=Math.ceil(t.tcx1/i-.5);c.tby1=Math.ceil(t.tcy1/i);c.resolution=s;buildCodeblocks(0,c,r);o.push(c);a.push(c);c={};c.type="LH";c.tbx0=Math.ceil(t.tcx0/i);c.tby0=Math.ceil(t.tcy0/i-.5);c.tbx1=Math.ceil(t.tcx1/i);c.tby1=Math.ceil(t.tcy1/i-.5);c.resolution=s;buildCodeblocks(0,c,r);o.push(c);a.push(c);c={};c.type="HH";c.tbx0=Math.ceil(t.tcx0/i-.5);c.tby0=Math.ceil(t.tcy0/i-.5);c.tbx1=Math.ceil(t.tcx1/i-.5);c.tby1=Math.ceil(t.tcy1/i-.5);c.resolution=s;buildCodeblocks(0,c,r);o.push(c);a.push(c);s.subbands=a}}t.resolutions=i;t.subbands=o}const o=r.codingStyleDefaultParameters.progressionOrder;switch(o){case 0:r.packetsIterator=new LayerResolutionComponentPositionIterator(e);break;case 1:r.packetsIterator=new ResolutionLayerComponentPositionIterator(e);break;case 2:r.packetsIterator=new ResolutionPositionComponentLayerIterator(e);break;case 3:r.packetsIterator=new PositionComponentResolutionLayerIterator(e);break;case 4:r.packetsIterator=new ComponentPositionResolutionLayerIterator(e);break;default:throw new JpxError(`Unsupported progression order ${o}`)}}function parseTilePackets(e,t,n,r){let o,s=0,a=0,c=!1;function readBits(e){for(;a<e;){const e=t[n+s];s++;if(c){o=o<<7|e;a+=7;c=!1}else{o=o<<8|e;a+=8}255===e&&(c=!0)}a-=e;return o>>>a&(1<<e)-1}function skipMarkerIfEqual(e){if(255===t[n+s-1]&&t[n+s]===e){skipBytes(1);return!0}if(255===t[n+s]&&t[n+s+1]===e){skipBytes(2);return!0}return!1}function skipBytes(e){s+=e}function alignToByte(){a=0;if(c){s++;c=!1}}function readCodingpasses(){if(0===readBits(1))return 1;if(0===readBits(1))return 2;let e=readBits(2);if(e<3)return e+3;e=readBits(5);if(e<31)return e+6;e=readBits(7);return e+37}const l=e.currentTile.index,f=e.tiles[l],u=e.COD.sopMarkerUsed,h=e.COD.ephMarkerUsed,d=f.packetsIterator;for(;s<r;){alignToByte();u&&skipMarkerIfEqual(145)&&skipBytes(4);const e=d.nextPacket();if(!readBits(1))continue;const r=e.layerNumber,o=[];let a;for(let t=0,n=e.codeblocks.length;t<n;t++){a=e.codeblocks[t];let n=a.precinct;const s=a.cbx-n.cbxMin,c=a.cby-n.cbyMin;let l,f,u=!1,h=!1;if(void 0!==a.included)u=!!readBits(1);else{n=a.precinct;let e;if(void 0!==n.inclusionTree)e=n.inclusionTree;else{const t=n.cbxMax-n.cbxMin+1,i=n.cbyMax-n.cbyMin+1;e=new InclusionTree(t,i,r);f=new TagTree(t,i);n.inclusionTree=e;n.zeroBitPlanesTree=f;for(let e=0;e<r;e++)if(0!==readBits(1))throw new JpxError("Invalid tag tree")}if(e.reset(s,c,r))for(;;){if(!readBits(1)){e.incrementValue(r);break}l=!e.nextLevel();if(l){a.included=!0;u=h=!0;break}}}if(!u)continue;if(h){f=n.zeroBitPlanesTree;f.reset(s,c);for(;;)if(readBits(1)){l=!f.nextLevel();if(l)break}else f.incrementValue();a.zeroBitPlanes=f.value}const d=readCodingpasses();for(;readBits(1);)a.Lblock++;const p=(0,i.log2)(d),g=readBits((d<1<<p?p-1:p)+a.Lblock);o.push({codeblock:a,codingpasses:d,dataLength:g})}alignToByte();h&&skipMarkerIfEqual(146);for(;o.length>0;){const e=o.shift();a=e.codeblock;void 0===a.data&&(a.data=[]);a.data.push({data:t,start:n+s,end:n+s+e.dataLength,codingpasses:e.codingpasses});s+=e.dataLength}}return s}function copyCoefficients(e,t,n,r,i,s,a,c,l){const f=r.tbx0,u=r.tby0,h=r.tbx1-r.tbx0,d=r.codeblocks,p="H"===r.type.charAt(0)?1:0,g="H"===r.type.charAt(1)?t:0;for(let n=0,m=d.length;n<m;++n){const m=d[n],b=m.tbx1_-m.tbx0_,y=m.tby1_-m.tby0_;if(0===b||0===y)continue;if(void 0===m.data)continue;const x=new BitModel(b,y,m.subbandType,m.zeroBitPlanes,s);let w=2;const v=m.data;let T,S,E,I=0,C=0;for(T=0,S=v.length;T<S;T++){E=v[T];I+=E.end-E.start;C+=E.codingpasses}const P=new Uint8Array(I);let A=0;for(T=0,S=v.length;T<S;T++){E=v[T];const e=E.data.subarray(E.start,E.end);P.set(e,A);A+=e.length}const k=new o.ArithmeticDecoder(P,0,I);x.setDecoder(k);for(T=0;T<C;T++){switch(w){case 0:x.runSignificancePropagationPass();break;case 1:x.runMagnitudeRefinementPass();break;case 2:x.runCleanupPass();c&&x.checkSegmentationSymbol()}l&&x.reset();w=(w+1)%3}let B=m.tbx0_-f+(m.tby0_-u)*h;const _=x.coefficentsSign,O=x.coefficentsMagnitude,R=x.bitsDecoded,M=a?0:.5;let L,D,U;A=0;const N="LL"!==r.type;for(T=0;T<y;T++){const n=2*(B/h|0)*(t-h)+p+g;for(L=0;L<b;L++){D=O[A];if(0!==D){D=(D+M)*i;0!==_[A]&&(D=-D);U=R[A];e[N?n+(B<<1):B]=a&&U>=s?D:D*(1<<s-U)}B++;A++}B+=h-b}}}function transformTile(e,t,n){const r=t.components[n],i=r.codingStyleParameters,o=r.quantizationParameters,a=i.decompositionLevelsCount,c=o.SPqcds,l=o.scalarExpounded,f=o.guardBits,u=i.segmentationSymbolUsed,h=i.resetContextProbabilities,d=e.components[n].precision,p=i.reversibleTransformation,g=p?new ReversibleTransform:new IrreversibleTransform,m=[];let b=0;for(let e=0;e<=a;e++){const t=r.resolutions[e],n=t.trx1-t.trx0,i=t.try1-t.try0,o=new Float32Array(n*i);for(let r=0,i=t.subbands.length;r<i;r++){let i,a;if(l){i=c[b].mu;a=c[b].epsilon;b++}else{i=c[0].mu;a=c[0].epsilon+(e>0?1-e:0)}const g=t.subbands[r],m=s[g.type];copyCoefficients(o,n,0,g,p?1:2**(d+m-a)*(1+i/2048),f+a-1,p,u,h)}m.push({width:n,height:i,items:o})}const y=g.calculate(m,r.tcx0,r.tcy0);return{left:r.tcx0,top:r.tcy0,width:y.width,height:y.height,items:y.items}}function initializeTile(e,t){const n=e.SIZ.Csiz,r=e.tiles[t];for(let t=0;t<n;t++){const n=r.components[t],i=void 0!==e.currentTile.QCC[t]?e.currentTile.QCC[t]:e.currentTile.QCD;n.quantizationParameters=i;const o=void 0!==e.currentTile.COC[t]?e.currentTile.COC[t]:e.currentTile.COD;n.codingStyleParameters=o}r.codingStyleDefaultParameters=e.currentTile.COD}class TagTree{constructor(e,t){const n=(0,i.log2)(Math.max(e,t))+1;this.levels=[];for(let r=0;r<n;r++){const n={width:e,height:t,items:[]};this.levels.push(n);e=Math.ceil(e/2);t=Math.ceil(t/2)}}reset(e,t){let n,r=0,i=0;for(;r<this.levels.length;){n=this.levels[r];const o=e+t*n.width;if(void 0!==n.items[o]){i=n.items[o];break}n.index=o;e>>=1;t>>=1;r++}r--;n=this.levels[r];n.items[n.index]=i;this.currentLevel=r;delete this.value}incrementValue(){const e=this.levels[this.currentLevel];e.items[e.index]++}nextLevel(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];e--;if(e<0){this.value=n;return!1}this.currentLevel=e;t=this.levels[e];t.items[t.index]=n;return!0}}class InclusionTree{constructor(e,t,n){const r=(0,i.log2)(Math.max(e,t))+1;this.levels=[];for(let i=0;i<r;i++){const r=new Uint8Array(e*t);for(let e=0,t=r.length;e<t;e++)r[e]=n;const i={width:e,height:t,items:r};this.levels.push(i);e=Math.ceil(e/2);t=Math.ceil(t/2)}}reset(e,t,n){let r=0;for(;r<this.levels.length;){const i=this.levels[r],o=e+t*i.width;i.index=o;const s=i.items[o];if(255===s)break;if(s>n){this.currentLevel=r;this.propagateValues();return!1}e>>=1;t>>=1;r++}this.currentLevel=r-1;return!0}incrementValue(e){const t=this.levels[this.currentLevel];t.items[t.index]=e+1;this.propagateValues()}propagateValues(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];for(;--e>=0;){t=this.levels[e];t.items[t.index]=n}}nextLevel(){let e=this.currentLevel,t=this.levels[e];const n=t.items[t.index];t.items[t.index]=255;e--;if(e<0)return!1;this.currentLevel=e;t=this.levels[e];t.items[t.index]=n;return!0}}class BitModel{static UNIFORM_CONTEXT=17;static RUNLENGTH_CONTEXT=18;static LLAndLHContextsLabel=new Uint8Array([0,5,8,0,3,7,8,0,4,7,8,0,0,0,0,0,1,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8,0,0,0,0,0,2,6,8,0,3,7,8,0,4,7,8]);static HLContextLabel=new Uint8Array([0,3,4,0,5,7,7,0,8,8,8,0,0,0,0,0,1,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8,0,0,0,0,0,2,3,4,0,6,7,7,0,8,8,8]);static HHContextLabel=new Uint8Array([0,1,2,0,1,2,2,0,2,2,2,0,0,0,0,0,3,4,5,0,4,5,5,0,5,5,5,0,0,0,0,0,6,7,7,0,7,7,7,0,7,7,7,0,0,0,0,0,8,8,8,0,8,8,8,0,8,8,8,0,0,0,0,0,8,8,8,0,8,8,8,0,8,8,8]);constructor(e,t,n,r,i){this.width=e;this.height=t;let o;o="HH"===n?BitModel.HHContextLabel:"HL"===n?BitModel.HLContextLabel:BitModel.LLAndLHContextsLabel;this.contextLabelTable=o;const s=e*t;this.neighborsSignificance=new Uint8Array(s);this.coefficentsSign=new Uint8Array(s);let a;a=i>14?new Uint32Array(s):i>6?new Uint16Array(s):new Uint8Array(s);this.coefficentsMagnitude=a;this.processingFlags=new Uint8Array(s);const c=new Uint8Array(s);if(0!==r)for(let e=0;e<s;e++)c[e]=r;this.bitsDecoded=c;this.reset()}setDecoder(e){this.decoder=e}reset(){this.contexts=new Int8Array(19);this.contexts[0]=8;this.contexts[BitModel.UNIFORM_CONTEXT]=92;this.contexts[BitModel.RUNLENGTH_CONTEXT]=6}setNeighborsSignificance(e,t,n){const r=this.neighborsSignificance,i=this.width,o=this.height,s=t>0,a=t+1<i;let c;if(e>0){c=n-i;s&&(r[c-1]+=16);a&&(r[c+1]+=16);r[c]+=4}if(e+1<o){c=n+i;s&&(r[c-1]+=16);a&&(r[c+1]+=16);r[c]+=4}s&&(r[n-1]+=1);a&&(r[n+1]+=1);r[n]|=128}runSignificancePropagationPass(){const e=this.decoder,t=this.width,n=this.height,r=this.coefficentsMagnitude,i=this.coefficentsSign,o=this.neighborsSignificance,s=this.processingFlags,a=this.contexts,c=this.contextLabelTable,l=this.bitsDecoded;for(let f=0;f<n;f+=4)for(let u=0;u<t;u++){let h=f*t+u;for(let d=0;d<4;d++,h+=t){const t=f+d;if(t>=n)break;s[h]&=-2;if(r[h]||!o[h])continue;const p=c[o[h]];if(e.readBit(a,p)){const e=this.decodeSignBit(t,u,h);i[h]=e;r[h]=1;this.setNeighborsSignificance(t,u,h);s[h]|=2}l[h]++;s[h]|=1}}}decodeSignBit(e,t,n){const r=this.width,i=this.height,o=this.coefficentsMagnitude,s=this.coefficentsSign;let a,c,l,f,u,h;f=t>0&&0!==o[n-1];if(t+1<r&&0!==o[n+1]){l=s[n+1];if(f){c=s[n-1];a=1-l-c}else a=1-l-l}else if(f){c=s[n-1];a=1-c-c}else a=0;const d=3*a;f=e>0&&0!==o[n-r];if(e+1<i&&0!==o[n+r]){l=s[n+r];if(f){c=s[n-r];a=1-l-c+d}else a=1-l-l+d}else if(f){c=s[n-r];a=1-c-c+d}else a=d;if(a>=0){u=9+a;h=this.decoder.readBit(this.contexts,u)}else{u=9-a;h=1^this.decoder.readBit(this.contexts,u)}return h}runMagnitudeRefinementPass(){const e=this.decoder,t=this.width,n=this.height,r=this.coefficentsMagnitude,i=this.neighborsSignificance,o=this.contexts,s=this.bitsDecoded,a=this.processingFlags,c=t*n,l=4*t;for(let n,f=0;f<c;f=n){n=Math.min(c,f+l);for(let c=0;c<t;c++)for(let l=f+c;l<n;l+=t){if(!r[l]||0!=(1&a[l]))continue;let t=16;if(0!=(2&a[l])){a[l]^=2;t=0===(127&i[l])?15:14}const n=e.readBit(o,t);r[l]=r[l]<<1|n;s[l]++;a[l]|=1}}}runCleanupPass(){const e=this.decoder,t=this.width,n=this.height,r=this.neighborsSignificance,i=this.coefficentsMagnitude,o=this.coefficentsSign,s=this.contexts,a=this.contextLabelTable,c=this.bitsDecoded,l=this.processingFlags,f=t,u=2*t,h=3*t;let d;for(let p=0;p<n;p=d){d=Math.min(p+4,n);const g=p*t,m=p+3<n;for(let n=0;n<t;n++){const b=g+n;let y,x=0,w=b,v=p;if(m&&0===l[b]&&0===l[b+f]&&0===l[b+u]&&0===l[b+h]&&0===r[b]&&0===r[b+f]&&0===r[b+u]&&0===r[b+h]){if(!e.readBit(s,BitModel.RUNLENGTH_CONTEXT)){c[b]++;c[b+f]++;c[b+u]++;c[b+h]++;continue}x=e.readBit(s,BitModel.UNIFORM_CONTEXT)<<1|e.readBit(s,BitModel.UNIFORM_CONTEXT);if(0!==x){v=p+x;w+=x*t}y=this.decodeSignBit(v,n,w);o[w]=y;i[w]=1;this.setNeighborsSignificance(v,n,w);l[w]|=2;w=b;for(let e=p;e<=v;e++,w+=t)c[w]++;x++}for(v=p+x;v<d;v++,w+=t){if(i[w]||0!=(1&l[w]))continue;const t=a[r[w]];if(1===e.readBit(s,t)){y=this.decodeSignBit(v,n,w);o[w]=y;i[w]=1;this.setNeighborsSignificance(v,n,w);l[w]|=2}c[w]++}}}}checkSegmentationSymbol(){const e=this.decoder,t=this.contexts;if(10!==(e.readBit(t,BitModel.UNIFORM_CONTEXT)<<3|e.readBit(t,BitModel.UNIFORM_CONTEXT)<<2|e.readBit(t,BitModel.UNIFORM_CONTEXT)<<1|e.readBit(t,BitModel.UNIFORM_CONTEXT)))throw new JpxError("Invalid segmentation symbol")}}class Transform{constructor(){this.constructor===Transform&&(0,r.unreachable)("Cannot initialize Transform.")}calculate(e,t,n){let r=e[0];for(let i=1,o=e.length;i<o;i++)r=this.iterate(r,e[i],t,n);return r}extend(e,t,n){let r=t-1,i=t+1,o=t+n-2,s=t+n;e[r--]=e[i++];e[s++]=e[o--];e[r--]=e[i++];e[s++]=e[o--];e[r--]=e[i++];e[s++]=e[o--];e[r]=e[i];e[s]=e[o]}filter(e,t,n){(0,r.unreachable)("Abstract method `filter` called")}iterate(e,t,n,r){const i=e.width,o=e.height;let s=e.items;const a=t.width,c=t.height,l=t.items;let f,u,h,d,p,g;for(h=0,f=0;f<o;f++){d=2*f*a;for(u=0;u<i;u++,h++,d+=2)l[d]=s[h]}s=e.items=null;const m=new Float32Array(a+8);if(1===a){if(0!=(1&n))for(g=0,h=0;g<c;g++,h+=a)l[h]*=.5}else for(g=0,h=0;g<c;g++,h+=a){m.set(l.subarray(h,h+a),4);this.extend(m,4,a);this.filter(m,4,a);l.set(m.subarray(4,4+a),h)}let b=16;const y=[];for(f=0;f<b;f++)y.push(new Float32Array(c+8));let x,w=0;e=4+c;if(1===c){if(0!=(1&r))for(p=0;p<a;p++)l[p]*=.5}else for(p=0;p<a;p++){if(0===w){b=Math.min(a-p,b);for(h=p,d=4;d<e;h+=a,d++)for(x=0;x<b;x++)y[x][d]=l[h+x];w=b}w--;const t=y[w];this.extend(t,4,c);this.filter(t,4,c);if(0===w){h=p-b+1;for(d=4;d<e;h+=a,d++)for(x=0;x<b;x++)l[h+x]=y[x][d]}}return{width:a,height:c,items:l}}}class IrreversibleTransform extends Transform{filter(e,t,n){const r=n>>1;let i,o,s,a;const c=-1.586134342059924,l=-.052980118572961,f=.882911075530934,u=.443506852043971,h=1.230174104914001;i=(t|=0)-3;for(o=r+4;o--;i+=2)e[i]*=.8128930661159609;i=t-2;s=u*e[i-1];for(o=r+3;o--;i+=2){a=u*e[i+1];e[i]=h*e[i]-s-a;if(!o--)break;i+=2;s=u*e[i+1];e[i]=h*e[i]-s-a}i=t-1;s=f*e[i-1];for(o=r+2;o--;i+=2){a=f*e[i+1];e[i]-=s+a;if(!o--)break;i+=2;s=f*e[i+1];e[i]-=s+a}i=t;s=l*e[i-1];for(o=r+1;o--;i+=2){a=l*e[i+1];e[i]-=s+a;if(!o--)break;i+=2;s=l*e[i+1];e[i]-=s+a}if(0!==r){i=t+1;s=c*e[i-1];for(o=r;o--;i+=2){a=c*e[i+1];e[i]-=s+a;if(!o--)break;i+=2;s=c*e[i+1];e[i]-=s+a}}}}class ReversibleTransform extends Transform{filter(e,t,n){const r=n>>1;let i,o;for(i=t|=0,o=r+1;o--;i+=2)e[i]-=e[i-1]+e[i+1]+2>>2;for(i=t+1,o=r;o--;i+=2)e[i]+=e[i-1]+e[i+1]>>1}}}],t={};function __w_pdfjs_require__(n){var r=t[n];if(void 0!==r)return r.exports;var i=t[n]={exports:{}};e[n].call(i.exports,i,i.exports,__w_pdfjs_require__);return i.exports}var n={};(()=>{var e=n;Object.defineProperty(e,"__esModule",{value:!0});Object.defineProperty(e,"Jbig2Image",{enumerable:!0,get:function(){return r.Jbig2Image}});Object.defineProperty(e,"JpegImage",{enumerable:!0,get:function(){return i.JpegImage}});Object.defineProperty(e,"JpxImage",{enumerable:!0,get:function(){return o.JpxImage}});Object.defineProperty(e,"getVerbosityLevel",{enumerable:!0,get:function(){return t.getVerbosityLevel}});Object.defineProperty(e,"setVerbosityLevel",{enumerable:!0,get:function(){return t.setVerbosityLevel}});var t=__w_pdfjs_require__(1),r=__w_pdfjs_require__(124),i=__w_pdfjs_require__(154),o=__w_pdfjs_require__(156)})();return n})()));