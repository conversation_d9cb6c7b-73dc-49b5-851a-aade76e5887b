/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=t.pdfjsLib=e():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],(()=>t.pdfjsLib=e())):"object"==typeof exports?exports["pdfjs-dist/build/pdf"]=t.pdfjsLib=e():t["pdfjs-dist/build/pdf"]=t.pdfjsLib=e()}(globalThis,(()=>(()=>{"use strict";var __webpack_modules__=[,(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.TextRenderingMode=e.RenderingIntentFlag=e.PromiseCapability=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.MAX_IMAGE_SIZE_TO_CACHE=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.BASELINE_FACTOR=e.AnnotationType=e.AnnotationReplyType=e.AnnotationPrefix=e.AnnotationMode=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0;e.assert=function assert(t,e){t||unreachable(e)};e.bytesToString=bytesToString;e.createValidAbsoluteUrl=function createValidAbsoluteUrl(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!t)return null;try{if(n&&"string"==typeof t){if(n.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(n.tryConvertEncoding)try{t=stringToUTF8String(t)}catch{}}const i=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(i))return i}catch{}return null};e.getModificationDate=function getModificationDate(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;return[t.getUTCFullYear().toString(),(t.getUTCMonth()+1).toString().padStart(2,"0"),t.getUTCDate().toString().padStart(2,"0"),t.getUTCHours().toString().padStart(2,"0"),t.getUTCMinutes().toString().padStart(2,"0"),t.getUTCSeconds().toString().padStart(2,"0")].join("")};e.getUuid=function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return bytesToString(t)};e.getVerbosityLevel=function getVerbosityLevel(){return s};e.info=function info(t){s>=r.INFOS&&console.log(`Info: ${t}`)};e.isArrayBuffer=function isArrayBuffer(t){return"object"==typeof t&&void 0!==t?.byteLength};e.isArrayEqual=function isArrayEqual(t,e){if(t.length!==e.length)return!1;for(let n=0,i=t.length;n<i;n++)if(t[n]!==e[n])return!1;return!0};e.isNodeJS=void 0;e.normalizeUnicode=function normalizeUnicode(t){if(!c){c=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;h=new Map([["ﬅ","ſt"]])}return t.replaceAll(c,((t,e,n)=>e?e.normalize("NFKC"):h.get(n)))};e.objectFromMap=function objectFromMap(t){const e=Object.create(null);for(const[n,i]of t)e[n]=i;return e};e.objectSize=function objectSize(t){return Object.keys(t).length};e.setVerbosityLevel=function setVerbosityLevel(t){Number.isInteger(t)&&(s=t)};e.shadow=shadow;e.string32=function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)};e.stringToBytes=stringToBytes;e.stringToPDFString=function stringToPDFString(t){if(t[0]>="ï"){let e;"þ"===t[0]&&"ÿ"===t[1]?e="utf-16be":"ÿ"===t[0]&&"þ"===t[1]?e="utf-16le":"ï"===t[0]&&"»"===t[1]&&"¿"===t[2]&&(e="utf-8");if(e)try{const n=new TextDecoder(e,{fatal:!0}),i=stringToBytes(t);return n.decode(i)}catch(t){warn(`stringToPDFString: "${t}".`)}}const e=[];for(let n=0,i=t.length;n<i;n++){const i=l[t.charCodeAt(n)];e.push(i?String.fromCharCode(i):t.charAt(n))}return e.join("")};e.stringToUTF8String=stringToUTF8String;e.unreachable=unreachable;e.utf8StringToString=function utf8StringToString(t){return unescape(encodeURIComponent(t))};e.warn=warn;n(2);n(84);n(86);n(87);n(89);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);const i=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);e.isNodeJS=i;e.IDENTITY_MATRIX=[1,0,0,1,0,0];e.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];e.MAX_IMAGE_SIZE_TO_CACHE=1e7;e.LINE_FACTOR=1.35;e.LINE_DESCENT_FACTOR=.35;e.BASELINE_FACTOR=.25925925925925924;e.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};e.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};e.AnnotationEditorPrefix="pdfjs_internal_editor_";e.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};e.AnnotationEditorParamsType={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};e.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};e.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};e.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};e.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};e.AnnotationReplyType={GROUP:"Group",REPLY:"R"};e.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};e.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};e.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};e.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};e.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};e.PageActionEventType={O:"PageOpen",C:"PageClose"};const r={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=r;e.CMapCompressionType={NONE:0,BINARY:1};e.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};e.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let s=r.WARNINGS;function warn(t){s>=r.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function shadow(t,e,n){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];Object.defineProperty(t,e,{value:n,enumerable:!i,configurable:!0,writable:!1});return n}const a=function BaseExceptionClosure(){function BaseException(t,e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();e.BaseException=a;e.PasswordException=class PasswordException extends a{constructor(t,e){super(t,"PasswordException");this.code=e}};e.UnknownErrorException=class UnknownErrorException extends a{constructor(t,e){super(t,"UnknownErrorException");this.details=e}};e.InvalidPDFException=class InvalidPDFException extends a{constructor(t){super(t,"InvalidPDFException")}};e.MissingPDFException=class MissingPDFException extends a{constructor(t){super(t,"MissingPDFException")}};e.UnexpectedResponseException=class UnexpectedResponseException extends a{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}};e.FormatError=class FormatError extends a{constructor(t){super(t,"FormatError")}};e.AbortException=class AbortException extends a{constructor(t){super(t,"AbortException")}};function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,n=8192;if(e<n)return String.fromCharCode.apply(null,t);const i=[];for(let r=0;r<e;r+=n){const s=Math.min(r+n,e),a=t.subarray(r,s);i.push(String.fromCharCode.apply(null,a))}return i.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,n=new Uint8Array(e);for(let i=0;i<e;++i)n[i]=255&t.charCodeAt(i);return n}e.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"==typeof navigator?shadow(this,"platform",{isWin:!1,isMac:!1}):shadow(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}};const o=[...Array(256).keys()].map((t=>t.toString(16).padStart(2,"0")));e.Util=class Util{static makeHexColor(t,e,n){return`#${o[t]}${o[e]}${o[n]}`}static scaleMinMax(t,e){let n;if(t[0]){if(t[0]<0){n=e[0];e[0]=e[1];e[1]=n}e[0]*=t[0];e[1]*=t[0];if(t[3]<0){n=e[2];e[2]=e[3];e[3]=n}e[2]*=t[3];e[3]*=t[3]}else{n=e[0];e[0]=e[2];e[2]=n;n=e[1];e[1]=e[3];e[3]=n;if(t[1]<0){n=e[2];e[2]=e[3];e[3]=n}e[2]*=t[1];e[3]*=t[1];if(t[2]<0){n=e[0];e[0]=e[1];e[1]=n}e[0]*=t[2];e[1]*=t[2]}e[0]+=t[4];e[1]+=t[4];e[2]+=t[5];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const n=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/n,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/n]}static getAxialAlignedBoundingBox(t,e){const n=this.applyTransform(t,e),i=this.applyTransform(t.slice(2,4),e),r=this.applyTransform([t[0],t[3]],e),s=this.applyTransform([t[2],t[1]],e);return[Math.min(n[0],i[0],r[0],s[0]),Math.min(n[1],i[1],r[1],s[1]),Math.max(n[0],i[0],r[0],s[0]),Math.max(n[1],i[1],r[1],s[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],n=t[0]*e[0]+t[1]*e[2],i=t[0]*e[1]+t[1]*e[3],r=t[2]*e[0]+t[3]*e[2],s=t[2]*e[1]+t[3]*e[3],a=(n+s)/2,o=Math.sqrt((n+s)**2-4*(n*s-r*i))/2,l=a+o||1,c=a-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const n=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),i=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(n>i)return null;const r=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),s=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return r>s?null:[n,r,i,s]}static bezierBoundingBox(t,e,n,i,r,s,a,o){const l=[],c=[[],[]];let h,d,u,p,f,g,m,b;for(let c=0;c<2;++c){if(0===c){d=6*t-12*n+6*r;h=-3*t+9*n-9*r+3*a;u=3*n-3*t}else{d=6*e-12*i+6*s;h=-3*e+9*i-9*s+3*o;u=3*i-3*e}if(Math.abs(h)<1e-12){if(Math.abs(d)<1e-12)continue;p=-u/d;0<p&&p<1&&l.push(p)}else{m=d*d-4*u*h;b=Math.sqrt(m);if(!(m<0)){f=(-d+b)/(2*h);0<f&&f<1&&l.push(f);g=(-d-b)/(2*h);0<g&&g<1&&l.push(g)}}}let v,y=l.length;const _=y;for(;y--;){p=l[y];v=1-p;c[0][y]=v*v*v*t+3*v*v*p*n+3*v*p*p*r+p*p*p*a;c[1][y]=v*v*v*e+3*v*v*p*i+3*v*p*p*s+p*p*p*o}c[0][_]=t;c[1][_]=e;c[0][_+1]=a;c[1][_+1]=o;c[0].length=c[1].length=_+2;return[Math.min(...c[0]),Math.min(...c[1]),Math.max(...c[0]),Math.max(...c[1])]}};const l=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(t){return decodeURIComponent(escape(t))}e.PromiseCapability=class PromiseCapability{#t=!1;constructor(){this.promise=new Promise(((t,e)=>{this.resolve=e=>{this.#t=!0;t(e)};this.reject=t=>{this.#t=!0;e(t)}}))}get settled(){return this.#t}};let c=null,h=null;e.AnnotationPrefix="pdfjs_internal_id_"},(t,e,n)=>{var i=n(3),r=n(4),s=n(69),a=n(70),o="WebAssembly",l=r[o],c=7!==Error("e",{cause:7}).cause,exportGlobalErrorCauseWrapper=function(t,e){var n={};n[t]=a(t,e,c);i({global:!0,constructor:!0,arity:1,forced:c},n)},exportWebAssemblyErrorCauseWrapper=function(t,e){if(l&&l[t]){var n={};n[t]=a(o+"."+t,e,c);i({target:o,stat:!0,constructor:!0,arity:1,forced:c},n)}};exportGlobalErrorCauseWrapper("Error",(function(t){return function Error(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("EvalError",(function(t){return function EvalError(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("RangeError",(function(t){return function RangeError(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("ReferenceError",(function(t){return function ReferenceError(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("SyntaxError",(function(t){return function SyntaxError(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("TypeError",(function(t){return function TypeError(e){return s(t,this,arguments)}}));exportGlobalErrorCauseWrapper("URIError",(function(t){return function URIError(e){return s(t,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("CompileError",(function(t){return function CompileError(e){return s(t,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("LinkError",(function(t){return function LinkError(e){return s(t,this,arguments)}}));exportWebAssemblyErrorCauseWrapper("RuntimeError",(function(t){return function RuntimeError(e){return s(t,this,arguments)}}))},(t,e,n)=>{var i=n(4),r=n(5).f,s=n(44),a=n(48),o=n(38),l=n(56),c=n(68);t.exports=function(t,e){var n,h,d,u,p,f=t.target,g=t.global,m=t.stat;if(n=g?i:m?i[f]||o(f,{}):(i[f]||{}).prototype)for(h in e){u=e[h];d=t.dontCallGetSet?(p=r(n,h))&&p.value:n[h];if(!c(g?h:f+(m?".":"#")+h,t.forced)&&void 0!==d){if(typeof u==typeof d)continue;l(u,d)}(t.sham||d&&d.sham)&&s(u,"sham",!0);a(n,h,u,t)}}},function(t){var check=function(t){return t&&t.Math===Math&&t};t.exports=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof global&&global)||function(){return this}()||this||Function("return this")()},(t,e,n)=>{var i=n(6),r=n(8),s=n(10),a=n(11),o=n(12),l=n(18),c=n(39),h=n(42),d=Object.getOwnPropertyDescriptor;e.f=i?d:function getOwnPropertyDescriptor(t,e){t=o(t);e=l(e);if(h)try{return d(t,e)}catch(t){}if(c(t,e))return a(!r(s.f,t,e),t[e])}},(t,e,n)=>{var i=n(7);t.exports=!i((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},(t,e,n)=>{var i=n(9),r=Function.prototype.call;t.exports=i?r.bind(r):function(){return r.apply(r,arguments)}},(t,e,n)=>{var i=n(7);t.exports=!i((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},(t,e)=>{var n={}.propertyIsEnumerable,i=Object.getOwnPropertyDescriptor,r=i&&!n.call({1:2},1);e.f=r?function propertyIsEnumerable(t){var e=i(this,t);return!!e&&e.enumerable}:n},t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},(t,e,n)=>{var i=n(13),r=n(16);t.exports=function(t){return i(r(t))}},(t,e,n)=>{var i=n(14),r=n(7),s=n(15),a=Object,o=i("".split);t.exports=r((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===s(t)?o(t,""):a(t)}:a},(t,e,n)=>{var i=n(9),r=Function.prototype,s=r.call,a=i&&r.bind.bind(s,s);t.exports=i?a:function(t){return function(){return s.apply(t,arguments)}}},(t,e,n)=>{var i=n(14),r=i({}.toString),s=i("".slice);t.exports=function(t){return s(r(t),8,-1)}},(t,e,n)=>{var i=n(17),r=TypeError;t.exports=function(t){if(i(t))throw r("Can't call method on "+t);return t}},t=>{t.exports=function(t){return null==t}},(t,e,n)=>{var i=n(19),r=n(23);t.exports=function(t){var e=i(t,"string");return r(e)?e:e+""}},(t,e,n)=>{var i=n(8),r=n(20),s=n(23),a=n(30),o=n(33),l=n(34),c=TypeError,h=l("toPrimitive");t.exports=function(t,e){if(!r(t)||s(t))return t;var n,l=a(t,h);if(l){void 0===e&&(e="default");n=i(l,t,e);if(!r(n)||s(n))return n;throw c("Can't convert object to primitive value")}void 0===e&&(e="number");return o(t,e)}},(t,e,n)=>{var i=n(21),r=n(22),s=r.all;t.exports=r.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:i(t)||t===s}:function(t){return"object"==typeof t?null!==t:i(t)}},(t,e,n)=>{var i=n(22),r=i.all;t.exports=i.IS_HTMLDDA?function(t){return"function"==typeof t||t===r}:function(t){return"function"==typeof t}},t=>{var e="object"==typeof document&&document.all,n=void 0===e&&void 0!==e;t.exports={all:e,IS_HTMLDDA:n}},(t,e,n)=>{var i=n(24),r=n(21),s=n(25),a=n(26),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=i("Symbol");return r(e)&&s(e.prototype,o(t))}},(t,e,n)=>{var i=n(4),r=n(21);t.exports=function(t,e){return arguments.length<2?(n=i[t],r(n)?n:void 0):i[t]&&i[t][e];var n}},(t,e,n)=>{var i=n(14);t.exports=i({}.isPrototypeOf)},(t,e,n)=>{var i=n(27);t.exports=i&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},(t,e,n)=>{var i=n(28),r=n(7),s=n(4).String;t.exports=!!Object.getOwnPropertySymbols&&!r((function(){var t=Symbol("symbol detection");return!s(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&i&&i<41}))},(t,e,n)=>{var i,r,s=n(4),a=n(29),o=s.process,l=s.Deno,c=o&&o.versions||l&&l.version,h=c&&c.v8;h&&(r=(i=h.split("."))[0]>0&&i[0]<4?1:+(i[0]+i[1]));!r&&a&&(!(i=a.match(/Edge\/(\d+)/))||i[1]>=74)&&(i=a.match(/Chrome\/(\d+)/))&&(r=+i[1]);t.exports=r},t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},(t,e,n)=>{var i=n(31),r=n(17);t.exports=function(t,e){var n=t[e];return r(n)?void 0:i(n)}},(t,e,n)=>{var i=n(21),r=n(32),s=TypeError;t.exports=function(t){if(i(t))return t;throw s(r(t)+" is not a function")}},t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},(t,e,n)=>{var i=n(8),r=n(21),s=n(20),a=TypeError;t.exports=function(t,e){var n,o;if("string"===e&&r(n=t.toString)&&!s(o=i(n,t)))return o;if(r(n=t.valueOf)&&!s(o=i(n,t)))return o;if("string"!==e&&r(n=t.toString)&&!s(o=i(n,t)))return o;throw a("Can't convert object to primitive value")}},(t,e,n)=>{var i=n(4),r=n(35),s=n(39),a=n(41),o=n(27),l=n(26),c=i.Symbol,h=r("wks"),d=l?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){s(h,t)||(h[t]=o&&s(c,t)?c[t]:d("Symbol."+t));return h[t]}},(t,e,n)=>{var i=n(36),r=n(37);(t.exports=function(t,e){return r[t]||(r[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.32.2",mode:i?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.2/LICENSE",source:"https://github.com/zloirock/core-js"})},t=>{t.exports=!1},(t,e,n)=>{var i=n(4),r=n(38),s="__core-js_shared__",a=i[s]||r(s,{});t.exports=a},(t,e,n)=>{var i=n(4),r=Object.defineProperty;t.exports=function(t,e){try{r(i,t,{value:e,configurable:!0,writable:!0})}catch(n){i[t]=e}return e}},(t,e,n)=>{var i=n(14),r=n(40),s=i({}.hasOwnProperty);t.exports=Object.hasOwn||function hasOwn(t,e){return s(r(t),e)}},(t,e,n)=>{var i=n(16),r=Object;t.exports=function(t){return r(i(t))}},(t,e,n)=>{var i=n(14),r=0,s=Math.random(),a=i(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++r+s,36)}},(t,e,n)=>{var i=n(6),r=n(7),s=n(43);t.exports=!i&&!r((function(){return 7!==Object.defineProperty(s("div"),"a",{get:function(){return 7}}).a}))},(t,e,n)=>{var i=n(4),r=n(20),s=i.document,a=r(s)&&r(s.createElement);t.exports=function(t){return a?s.createElement(t):{}}},(t,e,n)=>{var i=n(6),r=n(45),s=n(11);t.exports=i?function(t,e,n){return r.f(t,e,s(1,n))}:function(t,e,n){t[e]=n;return t}},(t,e,n)=>{var i=n(6),r=n(42),s=n(46),a=n(47),o=n(18),l=TypeError,c=Object.defineProperty,h=Object.getOwnPropertyDescriptor,d="enumerable",u="configurable",p="writable";e.f=i?s?function defineProperty(t,e,n){a(t);e=o(e);a(n);if("function"==typeof t&&"prototype"===e&&"value"in n&&p in n&&!n[p]){var i=h(t,e);if(i&&i[p]){t[e]=n.value;n={configurable:u in n?n[u]:i[u],enumerable:d in n?n[d]:i[d],writable:!1}}}return c(t,e,n)}:c:function defineProperty(t,e,n){a(t);e=o(e);a(n);if(r)try{return c(t,e,n)}catch(t){}if("get"in n||"set"in n)throw l("Accessors not supported");"value"in n&&(t[e]=n.value);return t}},(t,e,n)=>{var i=n(6),r=n(7);t.exports=i&&r((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},(t,e,n)=>{var i=n(20),r=String,s=TypeError;t.exports=function(t){if(i(t))return t;throw s(r(t)+" is not an object")}},(t,e,n)=>{var i=n(21),r=n(45),s=n(49),a=n(38);t.exports=function(t,e,n,o){o||(o={});var l=o.enumerable,c=void 0!==o.name?o.name:e;i(n)&&s(n,c,o);if(o.global)l?t[e]=n:a(e,n);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=n:r.f(t,e,{value:n,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},(t,e,n)=>{var i=n(14),r=n(7),s=n(21),a=n(39),o=n(6),l=n(50).CONFIGURABLE,c=n(51),h=n(52),d=h.enforce,u=h.get,p=String,f=Object.defineProperty,g=i("".slice),m=i("".replace),b=i([].join),v=o&&!r((function(){return 8!==f((function(){}),"length",{value:8}).length})),y=String(String).split("String"),_=t.exports=function(t,e,n){"Symbol("===g(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\)/,"$1")+"]");n&&n.getter&&(e="get "+e);n&&n.setter&&(e="set "+e);(!a(t,"name")||l&&t.name!==e)&&(o?f(t,"name",{value:e,configurable:!0}):t.name=e);v&&n&&a(n,"arity")&&t.length!==n.arity&&f(t,"length",{value:n.arity});try{n&&a(n,"constructor")&&n.constructor?o&&f(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var i=d(t);a(i,"source")||(i.source=b(y,"string"==typeof e?e:""));return t};Function.prototype.toString=_((function toString(){return s(this)&&u(this).source||c(this)}),"toString")},(t,e,n)=>{var i=n(6),r=n(39),s=Function.prototype,a=i&&Object.getOwnPropertyDescriptor,o=r(s,"name"),l=o&&"something"===function something(){}.name,c=o&&(!i||i&&a(s,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:c}},(t,e,n)=>{var i=n(14),r=n(21),s=n(37),a=i(Function.toString);r(s.inspectSource)||(s.inspectSource=function(t){return a(t)});t.exports=s.inspectSource},(t,e,n)=>{var i,r,s,a=n(53),o=n(4),l=n(20),c=n(44),h=n(39),d=n(37),u=n(54),p=n(55),f="Object already initialized",g=o.TypeError,m=o.WeakMap;if(a||d.state){var b=d.state||(d.state=new m);b.get=b.get;b.has=b.has;b.set=b.set;i=function(t,e){if(b.has(t))throw g(f);e.facade=t;b.set(t,e);return e};r=function(t){return b.get(t)||{}};s=function(t){return b.has(t)}}else{var v=u("state");p[v]=!0;i=function(t,e){if(h(t,v))throw g(f);e.facade=t;c(t,v,e);return e};r=function(t){return h(t,v)?t[v]:{}};s=function(t){return h(t,v)}}t.exports={set:i,get:r,has:s,enforce:function(t){return s(t)?r(t):i(t,{})},getterFor:function(t){return function(e){var n;if(!l(e)||(n=r(e)).type!==t)throw g("Incompatible receiver, "+t+" required");return n}}}},(t,e,n)=>{var i=n(4),r=n(21),s=i.WeakMap;t.exports=r(s)&&/native code/.test(String(s))},(t,e,n)=>{var i=n(35),r=n(41),s=i("keys");t.exports=function(t){return s[t]||(s[t]=r(t))}},t=>{t.exports={}},(t,e,n)=>{var i=n(39),r=n(57),s=n(5),a=n(45);t.exports=function(t,e,n){for(var o=r(e),l=a.f,c=s.f,h=0;h<o.length;h++){var d=o[h];i(t,d)||n&&i(n,d)||l(t,d,c(e,d))}}},(t,e,n)=>{var i=n(24),r=n(14),s=n(58),a=n(67),o=n(47),l=r([].concat);t.exports=i("Reflect","ownKeys")||function ownKeys(t){var e=s.f(o(t)),n=a.f;return n?l(e,n(t)):e}},(t,e,n)=>{var i=n(59),r=n(66).concat("length","prototype");e.f=Object.getOwnPropertyNames||function getOwnPropertyNames(t){return i(t,r)}},(t,e,n)=>{var i=n(14),r=n(39),s=n(12),a=n(60).indexOf,o=n(55),l=i([].push);t.exports=function(t,e){var n,i=s(t),c=0,h=[];for(n in i)!r(o,n)&&r(i,n)&&l(h,n);for(;e.length>c;)r(i,n=e[c++])&&(~a(h,n)||l(h,n));return h}},(t,e,n)=>{var i=n(12),r=n(61),s=n(64),createMethod=function(t){return function(e,n,a){var o,l=i(e),c=s(l),h=r(a,c);if(t&&n!=n){for(;c>h;)if((o=l[h++])!=o)return!0}else for(;c>h;h++)if((t||h in l)&&l[h]===n)return t||h||0;return!t&&-1}};t.exports={includes:createMethod(!0),indexOf:createMethod(!1)}},(t,e,n)=>{var i=n(62),r=Math.max,s=Math.min;t.exports=function(t,e){var n=i(t);return n<0?r(n+e,0):s(n,e)}},(t,e,n)=>{var i=n(63);t.exports=function(t){var e=+t;return e!=e||0===e?0:i(e)}},t=>{var e=Math.ceil,n=Math.floor;t.exports=Math.trunc||function trunc(t){var i=+t;return(i>0?n:e)(i)}},(t,e,n)=>{var i=n(65);t.exports=function(t){return i(t.length)}},(t,e,n)=>{var i=n(62),r=Math.min;t.exports=function(t){return t>0?r(i(t),9007199254740991):0}},t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},(t,e)=>{e.f=Object.getOwnPropertySymbols},(t,e,n)=>{var i=n(7),r=n(21),s=/#|\.prototype\./,isForced=function(t,e){var n=o[a(t)];return n===c||n!==l&&(r(e)?i(e):!!e)},a=isForced.normalize=function(t){return String(t).replace(s,".").toLowerCase()},o=isForced.data={},l=isForced.NATIVE="N",c=isForced.POLYFILL="P";t.exports=isForced},(t,e,n)=>{var i=n(9),r=Function.prototype,s=r.apply,a=r.call;t.exports="object"==typeof Reflect&&Reflect.apply||(i?a.bind(s):function(){return a.apply(s,arguments)})},(t,e,n)=>{var i=n(24),r=n(39),s=n(44),a=n(25),o=n(71),l=n(56),c=n(74),h=n(75),d=n(76),u=n(80),p=n(81),f=n(6),g=n(36);t.exports=function(t,e,n,m){var b="stackTraceLimit",v=m?2:1,y=t.split("."),_=y[y.length-1],A=i.apply(null,y);if(A){var S=A.prototype;!g&&r(S,"cause")&&delete S.cause;if(!n)return A;var E=i("Error"),x=e((function(t,e){var n=d(m?e:t,void 0),i=m?new A(t):new A;void 0!==n&&s(i,"message",n);p(i,x,i.stack,2);this&&a(S,this)&&h(i,this,x);arguments.length>v&&u(i,arguments[v]);return i}));x.prototype=S;if("Error"!==_)o?o(x,E):l(x,E,{name:!0});else if(f&&b in A){c(x,A,b);c(x,A,"prepareStackTrace")}l(x,A);if(!g)try{S.name!==_&&s(S,"name",_);S.constructor=x}catch(t){}return x}}},(t,e,n)=>{var i=n(72),r=n(47),s=n(73);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{(t=i(Object.prototype,"__proto__","set"))(n,[]);e=n instanceof Array}catch(t){}return function setPrototypeOf(n,i){r(n);s(i);e?t(n,i):n.__proto__=i;return n}}():void 0)},(t,e,n)=>{var i=n(14),r=n(31);t.exports=function(t,e,n){try{return i(r(Object.getOwnPropertyDescriptor(t,e)[n]))}catch(t){}}},(t,e,n)=>{var i=n(21),r=String,s=TypeError;t.exports=function(t){if("object"==typeof t||i(t))return t;throw s("Can't set "+r(t)+" as a prototype")}},(t,e,n)=>{var i=n(45).f;t.exports=function(t,e,n){n in t||i(t,n,{configurable:!0,get:function(){return e[n]},set:function(t){e[n]=t}})}},(t,e,n)=>{var i=n(21),r=n(20),s=n(71);t.exports=function(t,e,n){var a,o;s&&i(a=e.constructor)&&a!==n&&r(o=a.prototype)&&o!==n.prototype&&s(t,o);return t}},(t,e,n)=>{var i=n(77);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:i(t)}},(t,e,n)=>{var i=n(78),r=String;t.exports=function(t){if("Symbol"===i(t))throw TypeError("Cannot convert a Symbol value to a string");return r(t)}},(t,e,n)=>{var i=n(79),r=n(21),s=n(15),a=n(34)("toStringTag"),o=Object,l="Arguments"===s(function(){return arguments}());t.exports=i?s:function(t){var e,n,i;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=function(t,e){try{return t[e]}catch(t){}}(e=o(t),a))?n:l?s(e):"Object"===(i=s(e))&&r(e.callee)?"Arguments":i}},(t,e,n)=>{var i={};i[n(34)("toStringTag")]="z";t.exports="[object z]"===String(i)},(t,e,n)=>{var i=n(20),r=n(44);t.exports=function(t,e){i(e)&&"cause"in e&&r(t,"cause",e.cause)}},(t,e,n)=>{var i=n(44),r=n(82),s=n(83),a=Error.captureStackTrace;t.exports=function(t,e,n,o){s&&(a?a(t,e):i(t,"stack",r(n,o)))}},(t,e,n)=>{var i=n(14),r=Error,s=i("".replace),a=String(r("zxcasd").stack),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!r.prepareStackTrace)for(;e--;)t=s(t,o,"");return t}},(t,e,n)=>{var i=n(7),r=n(11);t.exports=!i((function(){var t=Error("a");if(!("stack"in t))return!0;Object.defineProperty(t,"stack",r(1,7));return 7!==t.stack}))},(t,e,n)=>{var i=n(48),r=n(14),s=n(77),a=n(85),o=URLSearchParams,l=o.prototype,c=r(l.append),h=r(l.delete),d=r(l.forEach),u=r([].push),p=new o("a=1&a=2&b=3");p.delete("a",1);p.delete("b",void 0);p+""!="a=2"&&i(l,"delete",(function(t){var e=arguments.length,n=e<2?void 0:arguments[1];if(e&&void 0===n)return h(this,t);var i=[];d(this,(function(t,e){u(i,{key:e,value:t})}));a(e,1);for(var r,o=s(t),l=s(n),p=0,f=0,g=!1,m=i.length;p<m;){r=i[p++];if(g||r.key===o){g=!0;h(this,r.key)}else f++}for(;f<m;)(r=i[f++]).key===o&&r.value===l||c(this,r.key,r.value)}),{enumerable:!0,unsafe:!0})},t=>{var e=TypeError;t.exports=function(t,n){if(t<n)throw e("Not enough arguments");return t}},(t,e,n)=>{var i=n(48),r=n(14),s=n(77),a=n(85),o=URLSearchParams,l=o.prototype,c=r(l.getAll),h=r(l.has),d=new o("a=1");!d.has("a",2)&&d.has("a",void 0)||i(l,"has",(function has(t){var e=arguments.length,n=e<2?void 0:arguments[1];if(e&&void 0===n)return h(this,t);var i=c(this,t);a(e,1);for(var r=s(n),o=0;o<i.length;)if(i[o++]===r)return!0;return!1}),{enumerable:!0,unsafe:!0})},(t,e,n)=>{var i=n(6),r=n(14),s=n(88),a=URLSearchParams.prototype,o=r(a.forEach);i&&!("size"in a)&&s(a,"size",{get:function size(){var t=0;o(this,(function(){t++}));return t},configurable:!0,enumerable:!0})},(t,e,n)=>{var i=n(49),r=n(45);t.exports=function(t,e,n){n.get&&i(n.get,e,{getter:!0});n.set&&i(n.set,e,{setter:!0});return r.f(t,e,n)}},(t,e,n)=>{var i=n(3),r=n(40),s=n(64),a=n(90),o=n(92);i({target:"Array",proto:!0,arity:1,forced:n(7)((function(){return 4294967297!==[].push.call({length:4294967296},1)}))||!function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}}()},{push:function push(t){var e=r(this),n=s(e),i=arguments.length;o(n+i);for(var l=0;l<i;l++){e[n]=arguments[l];n++}a(e,n);return n}})},(t,e,n)=>{var i=n(6),r=n(91),s=TypeError,a=Object.getOwnPropertyDescriptor,o=i&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(r(t)&&!a(t,"length").writable)throw s("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},(t,e,n)=>{var i=n(15);t.exports=Array.isArray||function isArray(t){return"Array"===i(t)}},t=>{var e=TypeError;t.exports=function(t){if(t>9007199254740991)throw e("Maximum allowed index exceeded");return t}},(t,e,n)=>{var i=n(94),r=n(98).findLast,s=i.aTypedArray;(0,i.exportTypedArrayMethod)("findLast",(function findLast(t){return r(s(this),t,arguments.length>1?arguments[1]:void 0)}))},(t,e,n)=>{var i,r,s,a=n(95),o=n(6),l=n(4),c=n(21),h=n(20),d=n(39),u=n(78),p=n(32),f=n(44),g=n(48),m=n(88),b=n(25),v=n(96),y=n(71),_=n(34),A=n(41),S=n(52),E=S.enforce,x=S.get,w=l.Int8Array,C=w&&w.prototype,T=l.Uint8ClampedArray,P=T&&T.prototype,k=w&&v(w),M=C&&v(C),R=Object.prototype,D=l.TypeError,I=_("toStringTag"),O=A("TYPED_ARRAY_TAG"),L="TypedArrayConstructor",N=a&&!!y&&"Opera"!==u(l.opera),B=!1,j={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},U={BigInt64Array:8,BigUint64Array:8},getTypedArrayConstructor=function(t){var e=v(t);if(h(e)){var n=x(e);return n&&d(n,L)?n[L]:getTypedArrayConstructor(e)}},isTypedArray=function(t){if(!h(t))return!1;var e=u(t);return d(j,e)||d(U,e)};for(i in j)(s=(r=l[i])&&r.prototype)?E(s)[L]=r:N=!1;for(i in U)(s=(r=l[i])&&r.prototype)&&(E(s)[L]=r);if(!N||!c(k)||k===Function.prototype){k=function TypedArray(){throw D("Incorrect invocation")};if(N)for(i in j)l[i]&&y(l[i],k)}if(!N||!M||M===R){M=k.prototype;if(N)for(i in j)l[i]&&y(l[i].prototype,M)}N&&v(P)!==M&&y(P,M);if(o&&!d(M,I)){B=!0;m(M,I,{configurable:!0,get:function(){return h(this)?this[O]:void 0}});for(i in j)l[i]&&f(l[i],O,i)}t.exports={NATIVE_ARRAY_BUFFER_VIEWS:N,TYPED_ARRAY_TAG:B&&O,aTypedArray:function(t){if(isTypedArray(t))return t;throw D("Target is not a typed array")},aTypedArrayConstructor:function(t){if(c(t)&&(!y||b(k,t)))return t;throw D(p(t)+" is not a typed array constructor")},exportTypedArrayMethod:function(t,e,n,i){if(o){if(n)for(var r in j){var s=l[r];if(s&&d(s.prototype,t))try{delete s.prototype[t]}catch(n){try{s.prototype[t]=e}catch(t){}}}M[t]&&!n||g(M,t,n?e:N&&C[t]||e,i)}},exportTypedArrayStaticMethod:function(t,e,n){var i,r;if(o){if(y){if(n)for(i in j)if((r=l[i])&&d(r,t))try{delete r[t]}catch(t){}if(k[t]&&!n)return;try{return g(k,t,n?e:N&&k[t]||e)}catch(t){}}for(i in j)!(r=l[i])||r[t]&&!n||g(r,t,e)}},getTypedArrayConstructor:getTypedArrayConstructor,isView:function isView(t){if(!h(t))return!1;var e=u(t);return"DataView"===e||d(j,e)||d(U,e)},isTypedArray:isTypedArray,TypedArray:k,TypedArrayPrototype:M}},t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},(t,e,n)=>{var i=n(39),r=n(21),s=n(40),a=n(54),o=n(97),l=a("IE_PROTO"),c=Object,h=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=s(t);if(i(e,l))return e[l];var n=e.constructor;return r(n)&&e instanceof n?n.prototype:e instanceof c?h:null}},(t,e,n)=>{var i=n(7);t.exports=!i((function(){function F(){}F.prototype.constructor=null;return Object.getPrototypeOf(new F)!==F.prototype}))},(t,e,n)=>{var i=n(99),r=n(13),s=n(40),a=n(64),createMethod=function(t){var e=1===t;return function(n,o,l){for(var c,h=s(n),d=r(h),u=i(o,l),p=a(d);p-- >0;)if(u(c=d[p],p,h))switch(t){case 0:return c;case 1:return p}return e?-1:void 0}};t.exports={findLast:createMethod(0),findLastIndex:createMethod(1)}},(t,e,n)=>{var i=n(100),r=n(31),s=n(9),a=i(i.bind);t.exports=function(t,e){r(t);return void 0===e?t:s?a(t,e):function(){return t.apply(e,arguments)}}},(t,e,n)=>{var i=n(15),r=n(14);t.exports=function(t){if("Function"===i(t))return r(t)}},(t,e,n)=>{var i=n(94),r=n(98).findLastIndex,s=i.aTypedArray;(0,i.exportTypedArrayMethod)("findLastIndex",(function findLastIndex(t){return r(s(this),t,arguments.length>1?arguments[1]:void 0)}))},(t,e,n)=>{var i=n(4),r=n(8),s=n(94),a=n(64),o=n(103),l=n(40),c=n(7),h=i.RangeError,d=i.Int8Array,u=d&&d.prototype,p=u&&u.set,f=s.aTypedArray,g=s.exportTypedArrayMethod,m=!c((function(){var t=new Uint8ClampedArray(2);r(p,t,{length:1,0:3},1);return 3!==t[1]})),b=m&&s.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var t=new d(2);t.set(1);t.set("2",1);return 0!==t[0]||2!==t[1]}));g("set",(function set(t){f(this);var e=o(arguments.length>1?arguments[1]:void 0,1),n=l(t);if(m)return r(p,this,n,e);var i=this.length,s=a(n),c=0;if(s+e>i)throw h("Wrong length");for(;c<s;)this[e+c]=n[c++]}),!m||b)},(t,e,n)=>{var i=n(104),r=RangeError;t.exports=function(t,e){var n=i(t);if(n%e)throw r("Wrong offset");return n}},(t,e,n)=>{var i=n(62),r=RangeError;t.exports=function(t){var e=i(t);if(e<0)throw r("The argument can't be less than 0");return e}},(t,e,n)=>{var i=n(106),r=n(94),s=r.aTypedArray,a=r.exportTypedArrayMethod,o=r.getTypedArrayConstructor;a("toReversed",(function toReversed(){return i(s(this),o(this))}))},(t,e,n)=>{var i=n(64);t.exports=function(t,e){for(var n=i(t),r=new e(n),s=0;s<n;s++)r[s]=t[n-s-1];return r}},(t,e,n)=>{var i=n(94),r=n(14),s=n(31),a=n(108),o=i.aTypedArray,l=i.getTypedArrayConstructor,c=i.exportTypedArrayMethod,h=r(i.TypedArrayPrototype.sort);c("toSorted",(function toSorted(t){void 0!==t&&s(t);var e=o(this),n=a(l(e),e);return h(n,t)}))},(t,e,n)=>{var i=n(64);t.exports=function(t,e){for(var n=0,r=i(e),s=new t(r);r>n;)s[n]=e[n++];return s}},(t,e,n)=>{var i=n(110),r=n(94),s=n(111),a=n(62),o=n(112),l=r.aTypedArray,c=r.getTypedArrayConstructor,h=r.exportTypedArrayMethod,d=!!function(){try{new Int8Array(1).with(2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();h("with",{with:function(t,e){var n=l(this),r=a(t),h=s(n)?o(e):+e;return i(n,c(n),r,h)}}.with,!d)},(t,e,n)=>{var i=n(64),r=n(62),s=RangeError;t.exports=function(t,e,n,a){var o=i(t),l=r(n),c=l<0?o+l:l;if(c>=o||c<0)throw s("Incorrect index");for(var h=new e(o),d=0;d<o;d++)h[d]=d===c?a:t[d];return h}},(t,e,n)=>{var i=n(78);t.exports=function(t){var e=i(t);return"BigInt64Array"===e||"BigUint64Array"===e}},(t,e,n)=>{var i=n(19),r=TypeError;t.exports=function(t){var e=i(t,"number");if("number"==typeof e)throw r("Can't convert number to bigint");return BigInt(e)}},(t,e,n)=>{var i=n(6),r=n(88),s=n(114),a=ArrayBuffer.prototype;i&&!("detached"in a)&&r(a,"detached",{configurable:!0,get:function detached(){return s(this)}})},(t,e,n)=>{var i=n(14),r=n(115),s=i(ArrayBuffer.prototype.slice);t.exports=function(t){if(0!==r(t))return!1;try{s(t,0,0);return!1}catch(t){return!0}}},(t,e,n)=>{var i=n(72),r=n(15),s=TypeError;t.exports=i(ArrayBuffer.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==r(t))throw s("ArrayBuffer expected");return t.byteLength}},(t,e,n)=>{var i=n(3),r=n(117);r&&i({target:"ArrayBuffer",proto:!0},{transfer:function transfer(){return r(this,arguments.length?arguments[0]:void 0,!0)}})},(t,e,n)=>{var i=n(4),r=n(14),s=n(72),a=n(118),o=n(114),l=n(115),c=n(119),h=i.TypeError,d=i.structuredClone,u=i.ArrayBuffer,p=i.DataView,f=Math.min,g=u.prototype,m=p.prototype,b=r(g.slice),v=s(g,"resizable","get"),y=s(g,"maxByteLength","get"),_=r(m.getInt8),A=r(m.setInt8);t.exports=c&&function(t,e,n){var i=l(t),r=void 0===e?i:a(e),s=!v||!v(t);if(o(t))throw h("ArrayBuffer is detached");var c=d(t,{transfer:[t]});if(i===r&&(n||s))return c;if(i>=r&&(!n||s))return b(c,0,r);for(var g=n&&!s&&y?{maxByteLength:y(c)}:void 0,m=new u(r,g),S=new p(c),E=new p(m),x=f(r,i),w=0;w<x;w++)A(E,w,_(S,w));return m}},(t,e,n)=>{var i=n(62),r=n(65),s=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=i(t),n=r(e);if(e!==n)throw s("Wrong length or index");return n}},(t,e,n)=>{var i=n(4),r=n(7),s=n(28),a=n(120),o=n(121),l=n(122),c=i.structuredClone;t.exports=!!c&&!r((function(){if(o&&s>92||l&&s>94||a&&s>97)return!1;var t=new ArrayBuffer(8),e=c(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},(t,e,n)=>{var i=n(121),r=n(122);t.exports=!i&&!r&&"object"==typeof window&&"object"==typeof document},t=>{t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},(t,e,n)=>{var i=n(4),r=n(15);t.exports="process"===r(i.process)},(t,e,n)=>{var i=n(3),r=n(117);r&&i({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function transferToFixedLength(){return r(this,arguments.length?arguments[0]:void 0,!1)}})},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{Object.defineProperty(exports,"__esModule",{value:!0});exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0;Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}});exports.build=void 0;exports.getDocument=getDocument;exports.version=void 0;__w_pdfjs_require__(84);__w_pdfjs_require__(86);__w_pdfjs_require__(87);__w_pdfjs_require__(2);__w_pdfjs_require__(93);__w_pdfjs_require__(101);__w_pdfjs_require__(102);__w_pdfjs_require__(105);__w_pdfjs_require__(107);__w_pdfjs_require__(109);__w_pdfjs_require__(113);__w_pdfjs_require__(116);__w_pdfjs_require__(123);__w_pdfjs_require__(89);__w_pdfjs_require__(125);__w_pdfjs_require__(136);__w_pdfjs_require__(138);__w_pdfjs_require__(141);__w_pdfjs_require__(143);__w_pdfjs_require__(145);__w_pdfjs_require__(147);__w_pdfjs_require__(149);__w_pdfjs_require__(152);var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(163),_display_utils=__w_pdfjs_require__(168),_font_loader=__w_pdfjs_require__(171),_displayNode_utils=__w_pdfjs_require__(172),_canvas=__w_pdfjs_require__(173),_worker_options=__w_pdfjs_require__(176),_message_handler=__w_pdfjs_require__(177),_metadata=__w_pdfjs_require__(178),_optional_content_config=__w_pdfjs_require__(179),_transport_stream=__w_pdfjs_require__(180),_displayFetch_stream=__w_pdfjs_require__(181),_displayNetwork=__w_pdfjs_require__(184),_displayNode_stream=__w_pdfjs_require__(185),_displaySvg=__w_pdfjs_require__(186),_xfa_text=__w_pdfjs_require__(194);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;function getDocument(t){"string"==typeof t||t instanceof URL?t={url:t}:(0,_util.isArrayBuffer)(t)&&(t={data:t});if("object"!=typeof t)throw new Error("Invalid parameter in getDocument, need parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const e=new PDFDocumentLoadingTask,{docId:n}=e,i=t.url?getUrlProp(t.url):null,r=t.data?getDataProp(t.data):null,s=t.httpHeaders||null,a=!0===t.withCredentials,o=t.password??null,l=t.range instanceof PDFDataRangeTransport?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let h=t.worker instanceof PDFWorker?t.worker:null;const d=t.verbosity,u="string"!=typeof t.docBaseUrl||(0,_display_utils.isDataScheme)(t.docBaseUrl)?null:t.docBaseUrl,p="string"==typeof t.cMapUrl?t.cMapUrl:null,f=!1!==t.cMapPacked,g=t.CMapReaderFactory||DefaultCMapReaderFactory,m="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,b=t.StandardFontDataFactory||DefaultStandardFontDataFactory,v=!0!==t.stopAtErrors,y=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,_=!1!==t.isEvalSupported,A="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!_util.isNodeJS,S=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,E="boolean"==typeof t.disableFontFace?t.disableFontFace:_util.isNodeJS,x=!0===t.fontExtraProperties,w=!0===t.enableXfa,C=t.ownerDocument||globalThis.document,T=!0===t.disableRange,P=!0===t.disableStream,k=!0===t.disableAutoFetch,M=!0===t.pdfBug,R=l?l.length:t.length??NaN,D="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!_util.isNodeJS&&!E,I="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:g===_display_utils.DOMCMapReaderFactory&&b===_display_utils.DOMStandardFontDataFactory&&p&&m&&(0,_display_utils.isValidFetchUrl)(p,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(m,document.baseURI),O=t.canvasFactory||new DefaultCanvasFactory({ownerDocument:C}),L=t.filterFactory||new DefaultFilterFactory({docId:n,ownerDocument:C});(0,_util.setVerbosityLevel)(d);const N={canvasFactory:O,filterFactory:L};if(!I){N.cMapReaderFactory=new g({baseUrl:p,isCompressed:f});N.standardFontDataFactory=new b({baseUrl:m})}if(!h){const t={verbosity:d,port:_worker_options.GlobalWorkerOptions.workerPort};h=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=h}const B={docId:n,apiVersion:"3.11.174",data:r,password:o,disableAutoFetch:k,rangeChunkSize:c,length:R,docBaseUrl:u,enableXfa:w,evaluatorOptions:{maxImageSize:y,disableFontFace:E,ignoreErrors:v,isEvalSupported:_,isOffscreenCanvasSupported:A,canvasMaxAreaInBytes:S,fontExtraProperties:x,useSystemFonts:D,cMapUrl:I?p:null,standardFontDataUrl:I?m:null}},j={ignoreErrors:v,isEvalSupported:_,disableFontFace:E,fontExtraProperties:x,enableXfa:w,ownerDocument:C,disableAutoFetch:k,pdfBug:M,styleElement:null};h.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(h,B),o=new Promise((function(t){let e;if(l)e=new _transport_stream.PDFDataTransportStream({length:R,initialData:l.initialData,progressiveDone:l.progressiveDone,contentDispositionFilename:l.contentDispositionFilename,disableRange:T,disableStream:P},l);else if(!r){e=(t=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(t):(0,_display_utils.isValidFetchUrl)(t.url)?new _displayFetch_stream.PDFFetchStream(t):new _displayNetwork.PDFNetworkStream(t))({url:i,length:R,httpHeaders:s,withCredentials:a,rangeChunkSize:c,disableRange:T,disableStream:P})}t(e)}));return Promise.all([t,o]).then((function(t){let[i,r]=t;if(e.destroyed)throw new Error("Loading aborted");const s=new _message_handler.MessageHandler(n,i,h.port),a=new WorkerTransport(s,e,r,j,N);e._transport=a;s.send("Ready",null)}))})).catch(e._capability.reject);return e}async function _fetchDocument(t,e){if(t.destroyed)throw new Error("Worker was destroyed");const n=await t.messageHandler.sendWithPromise("GetDocRequest",e,e.data?[e.data.buffer]:null);if(t.destroyed)throw new Error("Worker was destroyed");return n}function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(_util.isNodeJS&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(t){if(_util.isNodeJS&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return(0,_util.stringToBytes)(t);if("object"==typeof t&&!isNaN(t?.length)||(0,_util.isArrayBuffer)(t))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}class PDFDocumentLoadingTask{static#e=0;constructor(){this._capability=new _util.PromiseCapability;this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#e++;this.destroyed=!1;this.onPassword=null;this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}}exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this.length=t;this.initialData=e;this.progressiveDone=n;this.contentDispositionFilename=i;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=new _util.PromiseCapability}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const n of this._rangeListeners)n(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const n of this._progressListeners)n(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e;Object.defineProperty(this,"getJavaScript",{value:()=>{(0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead.");return this.getJSActions().then((t=>{if(!t)return t;const e=[];for(const n in t)e.push(...t[n]);return e}))}})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{#n=null;#i=!1;constructor(t,e,n){let i=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this._pageIndex=t;this._pageInfo=e;this._transport=n;this._stats=i?new _display_utils.StatTimer:null;this._pdfBug=i;this.commonObjs=n.commonObjs;this.objs=new PDFObjects;this._maybeCleanupAfterRender=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport(){let{scale:t,rotation:e=this.rotate,offsetX:n=0,offsetY:i=0,dontFlip:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:n,offsetY:i,dontFlip:r})}getAnnotations(){let{intent:t="display"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render(t){let{canvasContext:e,viewport:n,intent:i="display",annotationMode:r=_util.AnnotationMode.ENABLE,transform:s=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:c=null,printAnnotationStorage:h=null}=t;this._stats?.time("Overall");const d=this._transport.getRenderingIntent(i,r,h);this.#i=!1;this.#r();o||(o=this._transport.getOptionalContentConfig());let u=this._intentStates.get(d.cacheKey);if(!u){u=Object.create(null);this._intentStates.set(d.cacheKey,u)}if(u.streamReaderCancelTimeout){clearTimeout(u.streamReaderCancelTimeout);u.streamReaderCancelTimeout=null}const p=!!(d.renderingIntent&_util.RenderingIntentFlag.PRINT);if(!u.displayReadyCapability){u.displayReadyCapability=new _util.PromiseCapability;u.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(d)}const complete=t=>{u.renderTasks.delete(f);(this._maybeCleanupAfterRender||p)&&(this.#i=!0);this.#s(!p);if(t){f.capability.reject(t);this._abortOperatorList({intentState:u,reason:t instanceof Error?t:new Error(t)})}else f.capability.resolve();this._stats?.timeEnd("Rendering");this._stats?.timeEnd("Overall")},f=new InternalRenderTask({callback:complete,params:{canvasContext:e,viewport:n,transform:s,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:u.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!p,pdfBug:this._pdfBug,pageColors:c});(u.renderTasks||=new Set).add(f);const g=f.task;Promise.all([u.displayReadyCapability.promise,o]).then((t=>{let[e,n]=t;if(this.destroyed)complete();else{this._stats?.time("Rendering");f.initializeGraphics({transparency:e,optionalContentConfig:n});f.operatorListChanged()}})).catch(complete);return g}getOperatorList(){let{intent:t="display",annotationMode:e=_util.AnnotationMode.ENABLE,printAnnotationStorage:n=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=this._transport.getRenderingIntent(t,e,n,!0);let r,s=this._intentStates.get(i.cacheKey);if(!s){s=Object.create(null);this._intentStates.set(i.cacheKey,s)}if(!s.opListReadCapability){r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(s.operatorList.lastChunk){s.opListReadCapability.resolve(s.operatorList);s.renderTasks.delete(r)}};s.opListReadCapability=new _util.PromiseCapability;(s.renderTasks||=new Set).add(r);s.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(i)}return s.opListReadCapability.promise}streamTextContent(){let{includeMarkedContent:t=!1,disableNormalization:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._transport._htmlForXfa)return this.getXfa().then((t=>_xfa_text.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,n){const i=e.getReader(),r={items:[],styles:Object.create(null)};!function pump(){i.read().then((function(e){let{value:n,done:i}=e;if(i)t(r);else{Object.assign(r.styles,n.styles);r.items.push(...n.items);pump()}}),n)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const n of e.renderTasks){t.push(n.completed);n.cancel()}}this.objs.clear();this.#i=!1;this.#r();return Promise.all(t)}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.#i=!0;const e=this.#s(!1);t&&e&&(this._stats&&=new _display_utils.StatTimer);return e}#s(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.#r();if(!this.#i||this.destroyed)return!1;if(t){this.#n=setTimeout((()=>{this.#n=null;this.#s(!1)}),DELAYED_CLEANUP_TIMEOUT);return!1}for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#i=!1;return!0}#r(){if(this.#n){clearTimeout(this.#n);this.#n=null}}_startRenderPage(t,e){const n=this._intentStates.get(e);if(n){this._stats?.timeEnd("Page Request");n.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let n=0,i=t.length;n<i;n++){e.operatorList.fnArray.push(t.fnArray[n]);e.operatorList.argsArray.push(t.argsArray[n])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#s(!0)}_pumpOperatorList(t){let{renderingIntent:e,cacheKey:n,annotationStorageSerializable:i}=t;const{map:r,transfers:s}=i,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:e,cacheKey:n,annotationStorage:r},s).getReader(),o=this._intentStates.get(n);o.streamReader=a;const pump=()=>{a.read().then((t=>{let{value:e,done:n}=t;if(n)o.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(e,o);pump()}}),(t=>{o.streamReader=null;if(!this._transport.destroyed){if(o.operatorList){o.operatorList.lastChunk=!0;for(const t of o.renderTasks)t.operatorListChanged();this.#s(!0)}if(o.displayReadyCapability)o.displayReadyCapability.reject(t);else{if(!o.opListReadCapability)throw t;o.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList(t){let{intentState:e,reason:n,force:i=!1}=t;if(e.streamReader){if(e.streamReaderCancelTimeout){clearTimeout(e.streamReaderCancelTimeout);e.streamReaderCancelTimeout=null}if(!i){if(e.renderTasks.size>0)return;if(n instanceof _display_utils.RenderingCancelledException){let t=RENDERING_CANCELLED_TIMEOUT;n.extraDelay>0&&n.extraDelay<1e3&&(t+=n.extraDelay);e.streamReaderCancelTimeout=setTimeout((()=>{e.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:e,reason:n,force:!0})}),t);return}}e.streamReader.cancel(new _util.AbortException(n.message)).catch((()=>{}));e.streamReader=null;if(!this._transport.destroyed){for(const[t,n]of this._intentStates)if(n===e){this._intentStates.delete(t);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{#a=new Set;#o=Promise.resolve();postMessage(t,e){const n={data:structuredClone(t,null)};this.#o.then((()=>{for(const t of this.#a)t.call(this,n)}))}addEventListener(t,e){this.#a.add(e)}removeEventListener(t,e){this.#a.delete(e)}terminate(){this.#a.clear()}}exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;if(_util.isNodeJS&&"function"==typeof require){PDFWorkerUtil.isWorkerDisabled=!0;PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js"}else if("object"==typeof document){const t=document?.currentScript?.src;t&&(PDFWorkerUtil.fallbackWorkerSrc=t.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(t,e){let n;try{n=new URL(t);if(!n.origin||"null"===n.origin)return!1}catch{return!1}const i=new URL(e,n);return n.origin===i.origin};PDFWorkerUtil.createCDNWrapper=function(t){const e=`importScripts("${t}");`;return URL.createObjectURL(new Blob([e]))};class PDFWorker{static#l;constructor(){let{name:t=null,port:e=null,verbosity:n=(0,_util.getVerbosityLevel)()}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.name=t;this.destroyed=!1;this.verbosity=n;this._readyCapability=new _util.PromiseCapability;this._port=null;this._webWorker=null;this._messageHandler=null;if(e){if(PDFWorker.#l?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(PDFWorker.#l||=new WeakMap).set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new _message_handler.MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:t}=PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,t)||(t=PDFWorkerUtil.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t),n=new _message_handler.MessageHandler("main","worker",e),terminateEarly=()=>{e.removeEventListener("error",onWorkerError);n.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},onWorkerError=()=>{this._webWorker||terminateEarly()};e.addEventListener("error",onWorkerError);n.on("test",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else if(t){this._messageHandler=n;this._port=e;this._webWorker=e;this._readyCapability.resolve();n.send("configure",{verbosity:this.verbosity})}else{this._setupFakeWorker();n.destroy();e.terminate()}}));n.on("ready",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else try{sendTest()}catch{this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;n.send("test",t,[t.buffer])};sendTest();return}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){if(!PDFWorkerUtil.isWorkerDisabled){(0,_util.warn)("Setting up fake worker.");PDFWorkerUtil.isWorkerDisabled=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const n="fake"+PDFWorkerUtil.fakeWorkerId++,i=new _message_handler.MessageHandler(n+"_worker",n,e);t.setup(i,e);const r=new _message_handler.MessageHandler(n,n+"_worker",e);this._messageHandler=r;this._readyCapability.resolve();r.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}PDFWorker.#l?.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#l?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc){_util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.');return PDFWorkerUtil.fallbackWorkerSrc}throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&"function"==typeof require){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}await(0,_display_utils.loadScript)(this.workerSrc);return window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}exports.PDFWorker=PDFWorker;class WorkerTransport{#c=new Map;#h=new Map;#d=new Map;#u=null;constructor(t,e,n,i,r){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new _font_loader.FontLoader({ownerDocument:i.ownerDocument,styleElement:i.styleElement});this._params=i;this.canvasFactory=r.canvasFactory;this.filterFactory=r.filterFactory;this.cMapReaderFactory=r.cMapReaderFactory;this.standardFontDataFactory=r.standardFontDataFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=n;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=new _util.PromiseCapability;this.setupMessageHandler()}#p(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=this.#c.get(t);if(n)return n;const i=this.messageHandler.sendWithPromise(t,e);this.#c.set(t,i);return i}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_util.AnnotationMode.ENABLE,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,i=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=_util.RenderingIntentFlag.DISPLAY,s=_annotation_storage.SerializableEmpty;switch(t){case"any":r=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":r=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case _util.AnnotationMode.DISABLE:r+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:r+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:r+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE;s=(r&_util.RenderingIntentFlag.PRINT&&n instanceof _annotation_storage.PrintAnnotationStorage?n:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${e}`)}i&&(r+=_util.RenderingIntentFlag.OPLIST);return{renderingIntent:r,cacheKey:`${r}_${s.hash}`,annotationStorageSerializable:s}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=new _util.PromiseCapability;this.#u?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#h.values())t.push(e._destroy());this.#h.clear();this.#d.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#c.clear();this.filterFactory.destroy();this._networkStream?.cancelAllRequests(new _util.AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function(t){let{value:n,done:i}=t;if(i)e.close();else{(0,_util.assert)(n instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(n),1,[n])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(t=>{const n=new _util.PromiseCapability,i=this._fullReader;i.headersReady.then((()=>{if(!i.isStreamingSupported||!i.isRangeSupported){this._lastProgress&&e.onProgress?.(this._lastProgress);i.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}n.resolve({isStreamingSupported:i.isStreamingSupported,isRangeSupported:i.isRangeSupported,contentLength:i.contentLength})}),n.reject);return n.promise}));t.on("GetRangeReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const n=this._networkStream.getRangeReader(t.begin,t.end);if(n){e.onPull=()=>{n.read().then((function(t){let{value:n,done:i}=t;if(i)e.close();else{(0,_util.assert)(n instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(n),1,[n])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{n.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(t=>{let{pdfInfo:n}=t;this._numPages=n.numPages;this._htmlForXfa=n.htmlForXfa;delete n.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(n,this))}));t.on("DocException",(function(t){let n;switch(t.name){case"PasswordException":n=new _util.PasswordException(t.message,t.code);break;case"InvalidPDFException":n=new _util.InvalidPDFException(t.message);break;case"MissingPDFException":n=new _util.MissingPDFException(t.message);break;case"UnexpectedResponseException":n=new _util.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":n=new _util.UnknownErrorException(t.message,t.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}e._capability.reject(n)}));t.on("PasswordRequest",(t=>{this.#u=new _util.PromiseCapability;if(e.onPassword){const updatePassword=t=>{t instanceof Error?this.#u.reject(t):this.#u.resolve({password:t})};try{e.onPassword(updatePassword,t.code)}catch(t){this.#u.reject(t)}}else this.#u.reject(new _util.PasswordException(t.message,t.code));return this.#u.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#h.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(e=>{let[n,i,r]=e;if(!this.destroyed&&!this.commonObjs.has(n))switch(i){case"Font":const e=this._params;if("error"in r){const t=r.error;(0,_util.warn)(`Error during font loading: ${t}`);this.commonObjs.resolve(n,t);break}const s=e.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,a=new _font_loader.FontFaceObject(r,{isEvalSupported:e.isEvalSupported,disableFontFace:e.disableFontFace,ignoreErrors:e.ignoreErrors,inspectFont:s});this.fontLoader.bind(a).catch((e=>t.sendWithPromise("FontFallback",{id:n}))).finally((()=>{!e.fontExtraProperties&&a.data&&(a.data=null);this.commonObjs.resolve(n,a)}));break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(n,r);break;default:throw new Error(`Got unknown common object type ${i}`)}}));t.on("obj",(t=>{let[e,n,i,r]=t;if(this.destroyed)return;const s=this.#h.get(n);if(!s.objs.has(e))switch(i){case"Image":s.objs.resolve(e,r);if(r){let t;if(r.bitmap){const{width:e,height:n}=r;t=e*n*4}else t=r.data?.length||0;t>_util.MAX_IMAGE_SIZE_TO_CACHE&&(s._maybeCleanupAfterRender=!0)}break;case"Pattern":s.objs.resolve(e,r);break;default:throw new Error(`Got unknown object type ${i}`)}}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfers:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,n=this.#d.get(e);if(n)return n;const i=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((t=>{if(this.destroyed)throw new Error("Transport destroyed");const n=new PDFPageProxy(e,t,this,this._params.pdfBug);this.#h.set(e,n);return n}));this.#d.set(e,i);return i}getPageIndex(t){return"object"!=typeof t||null===t||!Number.isInteger(t.num)||t.num<0||!Number.isInteger(t.gen)||t.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen})}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#p("GetFieldObjects")}hasJSActions(){return this.#p("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#p("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((t=>new _optional_content_config.OptionalContentConfig(t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#c.get(t);if(e)return e;const n=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#c.set(t,n);return n}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#h.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#c.clear();this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:t,enableXfa:e}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t,enableXfa:e})}}class PDFObjects{#f=Object.create(null);#g(t){return this.#f[t]||={capability:new _util.PromiseCapability,data:null}}get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e){const n=this.#g(t);n.capability.promise.then((()=>e(n.data)));return null}const n=this.#f[t];if(!n?.capability.settled)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return n.data}has(t){const e=this.#f[t];return e?.capability.settled||!1}resolve(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=this.#g(t);n.data=e;n.capability.resolve()}clear(){for(const t in this.#f){const{data:e}=this.#f[t];e?.bitmap?.close()}this.#f=Object.create(null)}}class RenderTask{#m=null;constructor(t){this.#m=t;this.onContinue=null}get promise(){return this.#m.capability.promise}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.#m.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#m.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#m;return t.form||t.canvas&&e?.size>0}}exports.RenderTask=RenderTask;class InternalRenderTask{static#b=new WeakSet;constructor(t){let{callback:e,params:n,objs:i,commonObjs:r,annotationCanvasMap:s,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:c,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:u=null}=t;this.callback=e;this.params=n;this.objs=i;this.commonObjs=r;this.annotationCanvasMap=s;this.operatorListIdx=null;this.operatorList=a;this._pageIndex=o;this.canvasFactory=l;this.filterFactory=c;this._pdfBug=d;this.pageColors=u;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=new _util.PromiseCapability;this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=n.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics(t){let{transparency:e=!1,optionalContentConfig:n}=t;if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#b.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#b.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:i,viewport:r,transform:s,background:a}=this.params;this.gfx=new _canvas.CanvasGraphics(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:n},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:s,viewport:r,transparency:e,background:a});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();InternalRenderTask.#b.delete(this._canvas);this.callback(t||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#b.delete(this._canvas);this.callback()}}}}}const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(t,e,n)=>{var i=n(3),r=n(126);i({target:"Set",proto:!0,real:!0,forced:!n(135)("difference")},{difference:r})},(t,e,n)=>{var i=n(127),r=n(128),s=n(129),a=n(132),o=n(133),l=n(130),c=n(131),h=r.has,d=r.remove;t.exports=function difference(t){var e=i(this),n=o(t),r=s(e);a(e)<=n.size?l(e,(function(t){n.includes(t)&&d(r,t)})):c(n.getIterator(),(function(t){h(e,t)&&d(r,t)}));return r}},(t,e,n)=>{var i=n(128).has;t.exports=function(t){i(t);return t}},(t,e,n)=>{var i=n(14),r=Set.prototype;t.exports={Set:Set,add:i(r.add),has:i(r.has),remove:i(r.delete),proto:r}},(t,e,n)=>{var i=n(128),r=n(130),s=i.Set,a=i.add;t.exports=function(t){var e=new s;r(t,(function(t){a(e,t)}));return e}},(t,e,n)=>{var i=n(14),r=n(131),s=n(128),a=s.Set,o=s.proto,l=i(o.forEach),c=i(o.keys),h=c(new a).next;t.exports=function(t,e,n){return n?r({iterator:c(t),next:h},e):l(t,e)}},(t,e,n)=>{var i=n(8);t.exports=function(t,e,n){for(var r,s,a=n?t:t.iterator,o=t.next;!(r=i(o,a)).done;)if(void 0!==(s=e(r.value)))return s}},(t,e,n)=>{var i=n(72),r=n(128);t.exports=i(r.proto,"size","get")||function(t){return t.size}},(t,e,n)=>{var i=n(31),r=n(47),s=n(8),a=n(62),o=n(134),l="Invalid size",c=RangeError,h=TypeError,d=Math.max,SetRecord=function(t,e,n,i){this.set=t;this.size=e;this.has=n;this.keys=i};SetRecord.prototype={getIterator:function(){return o(r(s(this.keys,this.set)))},includes:function(t){return s(this.has,this.set,t)}};t.exports=function(t){r(t);var e=+t.size;if(e!=e)throw h(l);var n=a(e);if(n<0)throw c(l);return new SetRecord(t,d(n,0),i(t.has),i(t.keys))}},t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},(t,e,n)=>{var i=n(24),createSetLike=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};t.exports=function(t){var e=i("Set");try{(new e)[t](createSetLike(0));try{(new e)[t](createSetLike(-1));return!1}catch(t){return!0}}catch(t){return!1}}},(t,e,n)=>{var i=n(3),r=n(7),s=n(137);i({target:"Set",proto:!0,real:!0,forced:!n(135)("intersection")||r((function(){return"3,2"!==Array.from(new Set([1,2,3]).intersection(new Set([3,2])))}))},{intersection:s})},(t,e,n)=>{var i=n(127),r=n(128),s=n(132),a=n(133),o=n(130),l=n(131),c=r.Set,h=r.add,d=r.has;t.exports=function intersection(t){var e=i(this),n=a(t),r=new c;s(e)>n.size?l(n.getIterator(),(function(t){d(e,t)&&h(r,t)})):o(e,(function(t){n.includes(t)&&h(r,t)}));return r}},(t,e,n)=>{var i=n(3),r=n(139);i({target:"Set",proto:!0,real:!0,forced:!n(135)("isDisjointFrom")},{isDisjointFrom:r})},(t,e,n)=>{var i=n(127),r=n(128).has,s=n(132),a=n(133),o=n(130),l=n(131),c=n(140);t.exports=function isDisjointFrom(t){var e=i(this),n=a(t);if(s(e)<=n.size)return!1!==o(e,(function(t){if(n.includes(t))return!1}),!0);var h=n.getIterator();return!1!==l(h,(function(t){if(r(e,t))return c(h,"normal",!1)}))}},(t,e,n)=>{var i=n(8),r=n(47),s=n(30);t.exports=function(t,e,n){var a,o;r(t);try{if(!(a=s(t,"return"))){if("throw"===e)throw n;return n}a=i(a,t)}catch(t){o=!0;a=t}if("throw"===e)throw n;if(o)throw a;r(a);return n}},(t,e,n)=>{var i=n(3),r=n(142);i({target:"Set",proto:!0,real:!0,forced:!n(135)("isSubsetOf")},{isSubsetOf:r})},(t,e,n)=>{var i=n(127),r=n(132),s=n(130),a=n(133);t.exports=function isSubsetOf(t){var e=i(this),n=a(t);return!(r(e)>n.size)&&!1!==s(e,(function(t){if(!n.includes(t))return!1}),!0)}},(t,e,n)=>{var i=n(3),r=n(144);i({target:"Set",proto:!0,real:!0,forced:!n(135)("isSupersetOf")},{isSupersetOf:r})},(t,e,n)=>{var i=n(127),r=n(128).has,s=n(132),a=n(133),o=n(131),l=n(140);t.exports=function isSupersetOf(t){var e=i(this),n=a(t);if(s(e)<n.size)return!1;var c=n.getIterator();return!1!==o(c,(function(t){if(!r(e,t))return l(c,"normal",!1)}))}},(t,e,n)=>{var i=n(3),r=n(146);i({target:"Set",proto:!0,real:!0,forced:!n(135)("symmetricDifference")},{symmetricDifference:r})},(t,e,n)=>{var i=n(127),r=n(128),s=n(129),a=n(133),o=n(131),l=r.add,c=r.has,h=r.remove;t.exports=function symmetricDifference(t){var e=i(this),n=a(t).getIterator(),r=s(e);o(n,(function(t){c(e,t)?h(r,t):l(r,t)}));return r}},(t,e,n)=>{var i=n(3),r=n(148);i({target:"Set",proto:!0,real:!0,forced:!n(135)("union")},{union:r})},(t,e,n)=>{var i=n(127),r=n(128).add,s=n(129),a=n(133),o=n(131);t.exports=function union(t){var e=i(this),n=a(t).getIterator(),l=s(e);o(n,(function(t){r(l,t)}));return l}},(t,e,n)=>{var i=n(3),r=n(4),s=n(24),a=n(11),o=n(45).f,l=n(39),c=n(150),h=n(75),d=n(76),u=n(151),p=n(82),f=n(6),g=n(36),m="DOMException",b=s("Error"),v=s(m),y=function DOMException(){c(this,_);var t=arguments.length,e=d(t<1?void 0:arguments[0]),n=d(t<2?void 0:arguments[1],"Error"),i=new v(e,n),r=b(e);r.name=m;o(i,"stack",a(1,p(r.stack,1)));h(i,this,y);return i},_=y.prototype=v.prototype,A="stack"in b(m),S="stack"in new v(1,2),E=v&&f&&Object.getOwnPropertyDescriptor(r,m),x=!(!E||E.writable&&E.configurable),w=A&&!x&&!S;i({global:!0,constructor:!0,forced:g||w},{DOMException:w?y:v});var C=s(m),T=C.prototype;if(T.constructor!==C){g||o(T,"constructor",a(1,C));for(var P in u)if(l(u,P)){var k=u[P],M=k.s;l(C,M)||o(C,M,a(6,k.c))}}},(t,e,n)=>{var i=n(25),r=TypeError;t.exports=function(t,e){if(i(e,t))return t;throw r("Incorrect invocation")}},t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},(t,e,n)=>{var i,r=n(36),s=n(3),a=n(4),o=n(24),l=n(14),c=n(7),h=n(41),d=n(21),u=n(153),p=n(17),f=n(20),g=n(23),m=n(154),b=n(47),v=n(78),y=n(39),_=n(159),A=n(44),S=n(64),E=n(85),x=n(160),w=n(162),C=n(128),T=n(83),P=n(119),k=a.Object,M=a.Array,R=a.Date,D=a.Error,I=a.EvalError,O=a.RangeError,L=a.ReferenceError,N=a.SyntaxError,B=a.TypeError,j=a.URIError,U=a.PerformanceMark,z=a.WebAssembly,W=z&&z.CompileError||D,H=z&&z.LinkError||D,q=z&&z.RuntimeError||D,G=o("DOMException"),V=w.Map,$=w.has,X=w.get,K=w.set,Y=C.Set,J=C.add,Q=o("Object","keys"),Z=l([].push),tt=l((!0).valueOf),et=l(1..valueOf),nt=l("".valueOf),it=l(R.prototype.getTime),rt=h("structuredClone"),st="DataCloneError",at="Transferring",checkBasicSemantic=function(t){return!c((function(){var e=new a.Set([7]),n=t(e),i=t(k(7));return n===e||!n.has(7)||"object"!=typeof i||7!=+i}))&&t},checkErrorsCloning=function(t,e){return!c((function(){var n=new e,i=t({a:n,b:n});return!(i&&i.a===i.b&&i.a instanceof e&&i.a.stack===n.stack)}))},ot=a.structuredClone,lt=r||!checkErrorsCloning(ot,D)||!checkErrorsCloning(ot,G)||!(i=ot,!c((function(){var t=i(new a.AggregateError([1],rt,{cause:3}));return"AggregateError"!==t.name||1!==t.errors[0]||t.message!==rt||3!==t.cause}))),ct=!ot&&checkBasicSemantic((function(t){return new U(rt,{detail:t}).detail})),ht=checkBasicSemantic(ot)||ct,throwUncloneable=function(t){throw new G("Uncloneable type: "+t,st)},throwUnpolyfillable=function(t,e){throw new G((e||"Cloning")+" of "+t+" cannot be properly polyfilled in this engine",st)},tryNativeRestrictedStructuredClone=function(t,e){ht||throwUnpolyfillable(e);return ht(t)},cloneBuffer=function(t,e,n){if($(e,t))return X(e,t);var i,r,s,o,l,c;if("SharedArrayBuffer"===(n||v(t)))i=ht?ht(t):t;else{var h=a.DataView;h||"function"==typeof t.slice||throwUnpolyfillable("ArrayBuffer");try{if("function"!=typeof t.slice||t.resizable){r=t.byteLength;s="maxByteLength"in t?{maxByteLength:t.maxByteLength}:void 0;i=new ArrayBuffer(r,s);o=new h(t);l=new h(i);for(c=0;c<r;c++)l.setUint8(c,o.getUint8(c))}else i=t.slice(0)}catch(t){throw new G("ArrayBuffer is detached",st)}}K(e,t,i);return i},cloneView=function(t,e,n,i,r){var s=a[e];f(s)||throwUnpolyfillable(e);return new s(cloneBuffer(t.buffer,r),n,i)},Placeholder=function(t,e,n){this.object=t;this.type=e;this.metadata=n},structuredCloneInternal=function(t,e,n){g(t)&&throwUncloneable("Symbol");if(!f(t))return t;if(e){if($(e,t))return X(e,t)}else e=new V;var i,r,s,l,c,h,u,p,m=v(t);switch(m){case"Array":s=M(S(t));break;case"Object":s={};break;case"Map":s=new V;break;case"Set":s=new Y;break;case"RegExp":s=new RegExp(t.source,x(t));break;case"Error":switch(r=t.name){case"AggregateError":s=o("AggregateError")([]);break;case"EvalError":s=I();break;case"RangeError":s=O();break;case"ReferenceError":s=L();break;case"SyntaxError":s=N();break;case"TypeError":s=B();break;case"URIError":s=j();break;case"CompileError":s=W();break;case"LinkError":s=H();break;case"RuntimeError":s=q();break;default:s=D()}break;case"DOMException":s=new G(t.message,t.name);break;case"ArrayBuffer":case"SharedArrayBuffer":s=n?new Placeholder(t,m):cloneBuffer(t,e,m);break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float16Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":h="DataView"===m?t.byteLength:t.length;s=n?new Placeholder(t,m,{offset:t.byteOffset,length:h}):cloneView(t,m,t.byteOffset,h,e);break;case"DOMQuad":try{s=new DOMQuad(structuredCloneInternal(t.p1,e,n),structuredCloneInternal(t.p2,e,n),structuredCloneInternal(t.p3,e,n),structuredCloneInternal(t.p4,e,n))}catch(e){s=tryNativeRestrictedStructuredClone(t,m)}break;case"File":if(ht)try{s=ht(t);v(s)!==m&&(s=void 0)}catch(t){}if(!s)try{s=new File([t],t.name,t)}catch(t){}s||throwUnpolyfillable(m);break;case"FileList":if(l=function(){var t;try{t=new a.DataTransfer}catch(e){try{t=new a.ClipboardEvent("").clipboardData}catch(t){}}return t&&t.items&&t.files?t:null}()){for(c=0,h=S(t);c<h;c++)l.items.add(structuredCloneInternal(t[c],e,n));s=l.files}else s=tryNativeRestrictedStructuredClone(t,m);break;case"ImageData":try{s=new ImageData(structuredCloneInternal(t.data,e,n),t.width,t.height,{colorSpace:t.colorSpace})}catch(e){s=tryNativeRestrictedStructuredClone(t,m)}break;default:if(ht)s=ht(t);else switch(m){case"BigInt":s=k(t.valueOf());break;case"Boolean":s=k(tt(t));break;case"Number":s=k(et(t));break;case"String":s=k(nt(t));break;case"Date":s=new R(it(t));break;case"Blob":try{s=t.slice(0,t.size,t.type)}catch(t){throwUnpolyfillable(m)}break;case"DOMPoint":case"DOMPointReadOnly":i=a[m];try{s=i.fromPoint?i.fromPoint(t):new i(t.x,t.y,t.z,t.w)}catch(t){throwUnpolyfillable(m)}break;case"DOMRect":case"DOMRectReadOnly":i=a[m];try{s=i.fromRect?i.fromRect(t):new i(t.x,t.y,t.width,t.height)}catch(t){throwUnpolyfillable(m)}break;case"DOMMatrix":case"DOMMatrixReadOnly":i=a[m];try{s=i.fromMatrix?i.fromMatrix(t):new i(t)}catch(t){throwUnpolyfillable(m)}break;case"AudioData":case"VideoFrame":d(t.clone)||throwUnpolyfillable(m);try{s=t.clone()}catch(t){throwUncloneable(m)}break;case"CropTarget":case"CryptoKey":case"FileSystemDirectoryHandle":case"FileSystemFileHandle":case"FileSystemHandle":case"GPUCompilationInfo":case"GPUCompilationMessage":case"ImageBitmap":case"RTCCertificate":case"WebAssembly.Module":throwUnpolyfillable(m);default:throwUncloneable(m)}}K(e,t,s);switch(m){case"Array":case"Object":u=Q(t);for(c=0,h=S(u);c<h;c++){p=u[c];_(s,p,structuredCloneInternal(t[p],e,n))}break;case"Map":t.forEach((function(t,i){K(s,structuredCloneInternal(i,e,n),structuredCloneInternal(t,e,n))}));break;case"Set":t.forEach((function(t){J(s,structuredCloneInternal(t,e,n))}));break;case"Error":A(s,"message",structuredCloneInternal(t.message,e,n));y(t,"cause")&&A(s,"cause",structuredCloneInternal(t.cause,e,n));"AggregateError"===r&&(s.errors=structuredCloneInternal(t.errors,e,n));case"DOMException":T&&A(s,"stack",structuredCloneInternal(t.stack,e,n))}return s},replacePlaceholders=function(t,e){if(!f(t))return t;if($(e,t))return X(e,t);var n,i,r,s,a,o,l,c;if(t instanceof Placeholder){n=t.type;i=t.object;switch(n){case"ArrayBuffer":case"SharedArrayBuffer":c=cloneBuffer(i,e,n);break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float16Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":r=t.metadata;c=cloneView(i,n,r.offset,r.length,e)}}else switch(v(t)){case"Array":case"Object":o=Q(t);for(s=0,a=S(o);s<a;s++)t[l=o[s]]=replacePlaceholders(t[l],e);break;case"Map":c=new V;t.forEach((function(t,n){K(c,replacePlaceholders(n,e),replacePlaceholders(t,e))}));break;case"Set":c=new Y;t.forEach((function(t){J(c,replacePlaceholders(t,e))}));break;case"Error":t.message=replacePlaceholders(t.message,e);y(t,"cause")&&(t.cause=replacePlaceholders(t.cause,e));"AggregateError"===t.name&&(t.errors=replacePlaceholders(t.errors,e));case"DOMException":T&&(t.stack=replacePlaceholders(t.stack,e))}K(e,t,c||t);return c||t};s({global:!0,enumerable:!0,sham:!P,forced:lt},{structuredClone:function structuredClone(t){var e,n,i=E(arguments.length,1)>1&&!p(arguments[1])?b(arguments[1]):void 0,r=i?i.transfer:void 0,s=!1;if(void 0!==r){n=function(t,e){if(!f(t))throw B("Transfer option cannot be converted to a sequence");var n=[];m(t,(function(t){Z(n,b(t))}));for(var i,r,s,o,l,c=0,h=S(n),p=[];c<h;){i=n[c++];if("ArrayBuffer"!==(r=v(i))){if($(e,i))throw new G("Duplicate transferable",st);if(P)o=ot(i,{transfer:[i]});else switch(r){case"ImageBitmap":s=a.OffscreenCanvas;u(s)||throwUnpolyfillable(r,at);try{(l=new s(i.width,i.height)).getContext("bitmaprenderer").transferFromImageBitmap(i);o=l.transferToImageBitmap()}catch(t){}break;case"AudioData":case"VideoFrame":d(i.clone)&&d(i.close)||throwUnpolyfillable(r,at);try{o=i.clone();i.close()}catch(t){}break;case"MediaSourceHandle":case"MessagePort":case"OffscreenCanvas":case"ReadableStream":case"TransformStream":case"WritableStream":throwUnpolyfillable(r,at)}if(void 0===o)throw new G("This object cannot be transferred: "+r,st);K(e,i,o)}else Z(p,i)}return p}(r,e=new V);s=!!S(n)}var o=structuredCloneInternal(t,e,s);if(s){!function(t,e){for(var n,i,r=0,s=S(t);r<s;){n=t[r++];if($(e,n))throw new G("Duplicate transferable",st);if(P)i=ot(n,{transfer:[n]});else{d(n.transfer)||throwUnpolyfillable("ArrayBuffer",at);i=n.transfer()}K(e,n,i)}}(r,e=new V);o=replacePlaceholders(o,e)}return o}})},(t,e,n)=>{var i=n(14),r=n(7),s=n(21),a=n(78),o=n(24),l=n(51),noop=function(){},c=[],h=o("Reflect","construct"),d=/^\s*(?:class|function)\b/,u=i(d.exec),p=!d.exec(noop),f=function isConstructor(t){if(!s(t))return!1;try{h(noop,c,t);return!0}catch(t){return!1}},g=function isConstructor(t){if(!s(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return p||!!u(d,l(t))}catch(t){return!0}};g.sham=!0;t.exports=!h||r((function(){var t;return f(f.call)||!f(Object)||!f((function(){t=!0}))||t}))?g:f},(t,e,n)=>{var i=n(99),r=n(8),s=n(47),a=n(32),o=n(155),l=n(64),c=n(25),h=n(157),d=n(158),u=n(140),p=TypeError,Result=function(t,e){this.stopped=t;this.result=e},f=Result.prototype;t.exports=function(t,e,n){var g,m,b,v,y,_,A,S=n&&n.that,E=!(!n||!n.AS_ENTRIES),x=!(!n||!n.IS_RECORD),w=!(!n||!n.IS_ITERATOR),C=!(!n||!n.INTERRUPTED),T=i(e,S),stop=function(t){g&&u(g,"normal",t);return new Result(!0,t)},callFn=function(t){if(E){s(t);return C?T(t[0],t[1],stop):T(t[0],t[1])}return C?T(t,stop):T(t)};if(x)g=t.iterator;else if(w)g=t;else{if(!(m=d(t)))throw p(a(t)+" is not iterable");if(o(m)){for(b=0,v=l(t);v>b;b++)if((y=callFn(t[b]))&&c(f,y))return y;return new Result(!1)}g=h(t,m)}_=x?t.next:g.next;for(;!(A=r(_,g)).done;){try{y=callFn(A.value)}catch(t){u(g,"throw",t)}if("object"==typeof y&&y&&c(f,y))return y}return new Result(!1)}},(t,e,n)=>{var i=n(34),r=n(156),s=i("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||a[s]===t)}},t=>{t.exports={}},(t,e,n)=>{var i=n(8),r=n(31),s=n(47),a=n(32),o=n(158),l=TypeError;t.exports=function(t,e){var n=arguments.length<2?o(t):e;if(r(n))return s(i(n,t));throw l(a(t)+" is not iterable")}},(t,e,n)=>{var i=n(78),r=n(30),s=n(17),a=n(156),o=n(34)("iterator");t.exports=function(t){if(!s(t))return r(t,o)||r(t,"@@iterator")||a[i(t)]}},(t,e,n)=>{var i=n(18),r=n(45),s=n(11);t.exports=function(t,e,n){var a=i(e);a in t?r.f(t,a,s(0,n)):t[a]=n}},(t,e,n)=>{var i=n(8),r=n(39),s=n(25),a=n(161),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||r(t,"flags")||!s(o,t)?e:i(a,t)}},(t,e,n)=>{var i=n(47);t.exports=function(){var t=i(this),e="";t.hasIndices&&(e+="d");t.global&&(e+="g");t.ignoreCase&&(e+="i");t.multiline&&(e+="m");t.dotAll&&(e+="s");t.unicode&&(e+="u");t.unicodeSets&&(e+="v");t.sticky&&(e+="y");return e}},(t,e,n)=>{var i=n(14),r=Map.prototype;t.exports={Map:Map,set:i(r.set),get:i(r.get),has:i(r.has),remove:i(r.delete),proto:r}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.SerializableEmpty=e.PrintAnnotationStorage=e.AnnotationStorage=void 0;n(89);n(149);n(152);var i=n(1),r=n(164),s=n(170);const a=Object.freeze({map:null,hash:"",transfers:void 0});e.SerializableEmpty=a;class AnnotationStorage{#v=!1;#y=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const n=this.#y.get(t);return void 0===n?e:Object.assign(e,n)}getRawValue(t){return this.#y.get(t)}remove(t){this.#y.delete(t);0===this.#y.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#y.values())if(t instanceof r.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const n=this.#y.get(t);let i=!1;if(void 0!==n){for(const[t,r]of Object.entries(e))if(n[t]!==r){i=!0;n[t]=r}}else{i=!0;this.#y.set(t,e)}i&&this.#_();e instanceof r.AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#y.has(t)}getAll(){return this.#y.size>0?(0,i.objectFromMap)(this.#y):null}setAll(t){for(const[e,n]of Object.entries(t))this.setValue(e,n)}get size(){return this.#y.size}#_(){if(!this.#v){this.#v=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#v){this.#v=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#y.size)return a;const t=new Map,e=new s.MurmurHash3_64,n=[],i=Object.create(null);let o=!1;for(const[n,s]of this.#y){const a=s instanceof r.AnnotationEditor?s.serialize(!1,i):s;if(a){t.set(n,a);e.update(`${n}:${JSON.stringify(a)}`);o||=!!a.bitmap}}if(o)for(const e of t.values())e.bitmap&&n.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfers:n}:a}}e.AnnotationStorage=AnnotationStorage;class PrintAnnotationStorage extends AnnotationStorage{#A;constructor(t){super();const{map:e,hash:n,transfers:i}=t.serializable,r=structuredClone(e,null);this.#A={map:r,hash:n,transfers:i}}get print(){(0,i.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#A}}e.PrintAnnotationStorage=PrintAnnotationStorage},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditor=void 0;n(89);n(2);var i=n(165),r=n(1),s=n(168);class AnnotationEditor{#S="";#E=!1;#x=null;#w=null;#C=null;#T=!1;#P=null;#k=this.focusin.bind(this);#M=this.focusout.bind(this);#F=!1;#R=!1;#D=!1;_initialOptions=Object.create(null);_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#I=!1;#O=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new i.ColorManager;static _zIndex=1;static SMALL_EDITOR_SIZE=0;constructor(t){this.constructor===AnnotationEditor&&(0,r.unreachable)("Cannot initialize AnnotationEditor.");this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:n,pageHeight:i,pageX:s,pageY:a}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[n,i];this.pageTranslation=[s,a];const[o,l]=this.parentDimensions;this.x=t.x/o;this.y=t.y/l;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,r.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;AnnotationEditor._l10nPromise||=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map((e=>[e,t.get(e)])));if(e?.strings)for(const n of e.strings)AnnotationEditor._l10nPromise.set(n,t.get(n));if(-1!==AnnotationEditor._borderLineWidth)return;const n=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(n.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){(0,r.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#I}set _isDraggable(t){this.#I=t;this.div?.classList.toggle("draggable",t)}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#O}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#F?this.#F=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,n,i){const[r,s]=this.parentDimensions;[n,i]=this.screenToPageTranslation(n,i);this.x=(t+n)/r;this.y=(e+i)/s;this.fixAndSetPosition()}#L(t,e,n){let[i,r]=t;[e,n]=this.screenToPageTranslation(e,n);this.x+=e/i;this.y+=n/r;this.fixAndSetPosition()}translate(t,e){this.#L(this.parentDimensions,t,e)}translateInPage(t,e){this.#L(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}drag(t,e){const[n,i]=this.parentDimensions;this.x+=t/n;this.y+=e/i;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:r,y:s}=this;const[a,o]=this.#N();r+=a;s+=o;this.div.style.left=`${(100*r).toFixed(2)}%`;this.div.style.top=`${(100*s).toFixed(2)}%`;this.div.scrollIntoView({block:"nearest"})}#N(){const[t,e]=this.parentDimensions,{_borderLineWidth:n}=AnnotationEditor,i=n/t,r=n/e;switch(this.rotation){case 90:return[-i,r];case 180:return[i,r];case 270:return[i,-r];default:return[-i,-r]}}fixAndSetPosition(){const[t,e]=this.pageDimensions;let{x:n,y:i,width:r,height:s}=this;r*=t;s*=e;n*=t;i*=e;switch(this.rotation){case 0:n=Math.max(0,Math.min(t-r,n));i=Math.max(0,Math.min(e-s,i));break;case 90:n=Math.max(0,Math.min(t-s,n));i=Math.min(e,Math.max(r,i));break;case 180:n=Math.min(t,Math.max(r,n));i=Math.min(e,Math.max(s,i));break;case 270:n=Math.min(t,Math.max(s,n));i=Math.max(0,Math.min(e-r,i))}this.x=n/=t;this.y=i/=e;const[a,o]=this.#N();n+=a;i+=o;const{style:l}=this.div;l.left=`${(100*n).toFixed(2)}%`;l.top=`${(100*i).toFixed(2)}%`;this.moveInDOM()}static#B(t,e,n){switch(n){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#B(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#B(t,e,360-this.parentRotation)}#j(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,n]}=this,i=e*t,s=n*t;return r.FeatureTest.isCSSRoundSupported?[Math.round(i),Math.round(s)]:[i,s]}setDims(t,e){const[n,i]=this.parentDimensions;this.div.style.width=`${(100*t/n).toFixed(2)}%`;this.#T||(this.div.style.height=`${(100*e/i).toFixed(2)}%`);this.#x?.classList.toggle("small",t<AnnotationEditor.SMALL_EDITOR_SIZE||e<AnnotationEditor.SMALL_EDITOR_SIZE)}fixDims(){const{style:t}=this.div,{height:e,width:n}=t,i=n.endsWith("%"),r=!this.#T&&e.endsWith("%");if(i&&r)return;const[s,a]=this.parentDimensions;i||(t.width=`${(100*parseFloat(n)/s).toFixed(2)}%`);this.#T||r||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#U(){if(this.#P)return;this.#P=document.createElement("div");this.#P.classList.add("resizers");const t=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||t.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const e of t){const t=document.createElement("div");this.#P.append(t);t.classList.add("resizer",e);t.addEventListener("pointerdown",this.#z.bind(this,e));t.addEventListener("contextmenu",s.noContextMenu)}this.div.prepend(this.#P)}#z(t,e){e.preventDefault();const{isMac:n}=r.FeatureTest.platform;if(0!==e.button||e.ctrlKey&&n)return;const i=this.#W.bind(this,t),s=this._isDraggable;this._isDraggable=!1;const a={passive:!0,capture:!0};window.addEventListener("pointermove",i,a);const o=this.x,l=this.y,c=this.width,h=this.height,d=this.parent.div.style.cursor,u=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{this._isDraggable=s;window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);window.removeEventListener("pointermove",i,a);this.parent.div.style.cursor=d;this.div.style.cursor=u;const t=this.x,e=this.y,n=this.width,r=this.height;t===o&&e===l&&n===c&&r===h||this.addCommands({cmd:()=>{this.width=n;this.height=r;this.x=t;this.y=e;const[i,s]=this.parentDimensions;this.setDims(i*n,s*r);this.fixAndSetPosition()},undo:()=>{this.width=c;this.height=h;this.x=o;this.y=l;const[t,e]=this.parentDimensions;this.setDims(t*c,e*h);this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",pointerUpCallback);window.addEventListener("blur",pointerUpCallback)}#W(t,e){const[n,i]=this.parentDimensions,r=this.x,s=this.y,a=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/n,c=AnnotationEditor.MIN_SIZE/i,round=t=>Math.round(1e4*t)/1e4,h=this.#j(this.rotation),transf=(t,e)=>[h[0]*t+h[2]*e,h[1]*t+h[3]*e],d=this.#j(360-this.rotation);let u,p,f=!1,g=!1;switch(t){case"topLeft":f=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":f=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":g=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":f=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":f=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":g=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const m=u(a,o),b=p(a,o);let v=transf(...b);const y=round(r+v[0]),_=round(s+v[1]);let A=1,S=1,[E,x]=this.screenToPageTranslation(e.movementX,e.movementY);[E,x]=(w=E/n,C=x/i,[d[0]*w+d[2]*C,d[1]*w+d[3]*C]);var w,C;if(f){const t=Math.hypot(a,o);A=S=Math.max(Math.min(Math.hypot(b[0]-m[0]-E,b[1]-m[1]-x)/t,1/a,1/o),l/a,c/o)}else g?A=Math.max(l,Math.min(1,Math.abs(b[0]-m[0]-E)))/a:S=Math.max(c,Math.min(1,Math.abs(b[1]-m[1]-x)))/o;const T=round(a*A),P=round(o*S);v=transf(...p(T,P));const k=y-v[0],M=_-v[1];this.width=T;this.height=P;this.x=k;this.y=M;this.setDims(n*T,i*P);this.fixAndSetPosition()}async addAltTextButton(){if(this.#x)return;const t=this.#x=document.createElement("button");t.className="altText";const e=await AnnotationEditor._l10nPromise.get("editor_alt_text_button_label");t.textContent=e;t.setAttribute("aria-label",e);t.tabIndex="0";t.addEventListener("contextmenu",s.noContextMenu);t.addEventListener("pointerdown",(t=>t.stopPropagation()));t.addEventListener("click",(t=>{t.preventDefault();this._uiManager.editAltText(this)}),{capture:!0});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){e.preventDefault();this._uiManager.editAltText(this)}}));this.#H();this.div.append(t);if(!AnnotationEditor.SMALL_EDITOR_SIZE){const e=40;AnnotationEditor.SMALL_EDITOR_SIZE=Math.min(128,Math.round(t.getBoundingClientRect().width*(1+e/100)))}}async#H(){const t=this.#x;if(!t)return;if(!this.#S&&!this.#E){t.classList.remove("done");this.#w?.remove();return}AnnotationEditor._l10nPromise.get("editor_alt_text_edit_button_label").then((e=>{t.setAttribute("aria-label",e)}));let e=this.#w;if(!e){this.#w=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");const n=e.id=`alt-text-tooltip-${this.id}`;t.setAttribute("aria-describedby",n);const i=100;t.addEventListener("mouseenter",(()=>{this.#C=setTimeout((()=>{this.#C=null;this.#w.classList.add("show");this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})}),i)}));t.addEventListener("mouseleave",(()=>{clearTimeout(this.#C);this.#C=null;this.#w?.classList.remove("show")}))}t.classList.add("done");e.innerText=this.#E?await AnnotationEditor._l10nPromise.get("editor_alt_text_decorative_tooltip"):this.#S;e.parentNode||t.append(e)}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:this.#S,decorative:this.#E}}set altTextData(t){let{altText:e,decorative:n}=t;if(this.#S!==e||this.#E!==n){this.#S=e;this.#E=n;this.#H()}}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.setAttribute("tabIndex",0);this.setInForeground();this.div.addEventListener("focusin",this.#k);this.div.addEventListener("focusout",this.#M);const[t,e]=this.parentDimensions;if(this.parentRotation%180!=0){this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`;this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`}const[n,r]=this.getInitialTranslation();this.translate(n,r);(0,i.bindEvents)(this,this.div,["pointerdown"]);return this.div}pointerdown(t){const{isMac:e}=r.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#F=!0;this.#q(t)}}#q(t){if(!this._isDraggable)return;const e=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let n,i;if(e){n={passive:!0,capture:!0};i=t=>{const[e,n]=this.screenToPageTranslation(t.movementX,t.movementY);this._uiManager.dragSelectedEditors(e,n)};window.addEventListener("pointermove",i,n)}const pointerUpCallback=()=>{window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);e&&window.removeEventListener("pointermove",i,n);this.#F=!1;if(!this._uiManager.endDragSession()){const{isMac:e}=r.FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",pointerUpCallback);window.addEventListener("blur",pointerUpCallback)}moveInDOM(){this.parent?.moveEditorInDOM(this)}_setParentAndPosition(t,e,n){t.changeParent(this);this.x=e;this.y=n;this.fixAndSetPosition()}getRect(t,e){const n=this.parentScale,[i,r]=this.pageDimensions,[s,a]=this.pageTranslation,o=t/n,l=e/n,c=this.x*i,h=this.y*r,d=this.width*i,u=this.height*r;switch(this.rotation){case 0:return[c+o+s,r-h-l-u+a,c+o+d+s,r-h-l+a];case 90:return[c+l+s,r-h+o+a,c+l+u+s,r-h+o+d+a];case 180:return[c-o-d+s,r-h+l+a,c-o+s,r-h+l+u+a];case 270:return[c-l-u+s,r-h-o-d+a,c-l+s,r-h-o+a];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[n,i,r,s]=t,a=r-n,o=s-i;switch(this.rotation){case 0:return[n,e-s,a,o];case 90:return[n,e-i,o,a];case 180:return[r,e-i,a,o];case 270:return[r,e-s,o,a];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#D=!0}disableEditMode(){this.#D=!1}isInEditMode(){return this.#D}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){this.div?.addEventListener("focusin",this.#k);this.div?.addEventListener("focusout",this.#M)}serialize(){(0,r.unreachable)("An editor must be serializable")}static deserialize(t,e,n){const i=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:n});i.rotation=t.rotation;const[r,s]=i.pageDimensions,[a,o,l,c]=i.getRectInCurrentCoords(t.rect,s);i.x=a/r;i.y=o/s;i.width=l/r;i.height=c/s;return i}remove(){this.div.removeEventListener("focusin",this.#k);this.div.removeEventListener("focusout",this.#M);this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);this.#x?.remove();this.#x=null;this.#w=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#U();this.#P.classList.remove("hidden")}}select(){this.makeResizable();this.div?.classList.add("selectedEditor")}unselect(){this.#P?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(t,e){}disableEditing(){this.#x&&(this.#x.hidden=!0)}enableEditing(){this.#x&&(this.#x.hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return this.#R}set isEditing(t){this.#R=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#T=!0;const n=t/e,{style:i}=this.div;i.aspectRatio=n;i.height="auto"}static get MIN_SIZE(){return 16}}e.AnnotationEditor=AnnotationEditor;class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0;e.bindEvents=function bindEvents(t,e,n){for(const i of n)e.addEventListener(i,t[i].bind(t))};e.opacityToHex=function opacityToHex(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")};n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(2);n(89);n(125);n(136);n(138);n(141);n(143);n(145);n(147);n(166);var i=n(1),r=n(168);class IdManager{#G=0;getId(){return`${i.AnnotationEditorPrefix}${this.#G++}`}}class ImageManager{#V=(0,i.getUuid)();#G=0;#$=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d"),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';const n=e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]}));return(0,i.shadow)(this,"_isSVGFittingCanvas",n)}async#X(t,e){this.#$||=new Map;let n=this.#$.get(t);if(null===n)return null;if(n?.bitmap){n.refCounter+=1;return n}try{n||={bitmap:null,id:`image_${this.#V}_${this.#G++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){n.url=e;const i=await fetch(e);if(!i.ok)throw new Error(i.statusText);t=await i.blob()}else t=n.file=e;if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,i=new FileReader,r=new Image,s=new Promise(((t,s)=>{r.onload=()=>{n.bitmap=r;n.isSvg=!0;t()};i.onload=async()=>{const t=n.svgUrl=i.result;r.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};r.onerror=i.onerror=s}));i.readAsDataURL(t);await s}else n.bitmap=await createImageBitmap(t);n.refCounter=1}catch(t){console.error(t);n=null}this.#$.set(t,n);n&&this.#$.set(n.id,n);return n}async getFromFile(t){const{lastModified:e,name:n,size:i,type:r}=t;return this.#X(`${e}_${n}_${i}_${r}`,t)}async getFromUrl(t){return this.#X(t,t)}async getFromId(t){this.#$||=new Map;const e=this.#$.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}return e.file?this.getFromFile(e.file):this.getFromUrl(e.url)}getSvgUrl(t){const e=this.#$.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#$||=new Map;const e=this.#$.get(t);if(e){e.refCounter-=1;0===e.refCounter&&(e.bitmap=null)}}isValidId(t){return t.startsWith(`image_${this.#V}_`)}}class CommandManager{#K=[];#Y=!1;#J;#Q=-1;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128;this.#J=t}add(t){let{cmd:e,undo:n,mustExec:i,type:r=NaN,overwriteIfSameType:s=!1,keepUndo:a=!1}=t;i&&e();if(this.#Y)return;const o={cmd:e,undo:n,type:r};if(-1===this.#Q){this.#K.length>0&&(this.#K.length=0);this.#Q=0;this.#K.push(o);return}if(s&&this.#K[this.#Q].type===r){a&&(o.undo=this.#K[this.#Q].undo);this.#K[this.#Q]=o;return}const l=this.#Q+1;if(l===this.#J)this.#K.splice(0,1);else{this.#Q=l;l<this.#K.length&&this.#K.splice(l)}this.#K.push(o)}undo(){if(-1!==this.#Q){this.#Y=!0;this.#K[this.#Q].undo();this.#Y=!1;this.#Q-=1}}redo(){if(this.#Q<this.#K.length-1){this.#Q+=1;this.#Y=!0;this.#K[this.#Q].cmd();this.#Y=!1}}hasSomethingToUndo(){return-1!==this.#Q}hasSomethingToRedo(){return this.#Q<this.#K.length-1}destroy(){this.#K=null}}e.CommandManager=CommandManager;class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=i.FeatureTest.platform;for(const[n,i,r={}]of t)for(const t of n){const n=t.startsWith("mac+");if(e&&n){this.callbacks.set(t.slice(4),{callback:i,options:r});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!n){this.callbacks.set(t,{callback:i,options:r});this.allKeys.add(t.split("+").at(-1))}}}#Z(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const n=this.callbacks.get(this.#Z(e));if(!n)return;const{callback:i,options:{bubbles:r=!1,args:s=[],checker:a=null}}=n;if(!a||a(t,e)){i.bind(t,...s)();if(!r){e.stopPropagation();e.preventDefault()}}}}e.KeyboardManager=KeyboardManager;class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);(0,r.getColorValues)(t);return(0,i.shadow)(this,"_colors",t)}convert(t){const e=(0,r.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,n]of this._colors)if(n.every(((t,n)=>t===e[n])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?i.Util.makeHexColor(...e):t}}e.ColorManager=ColorManager;class AnnotationEditorUIManager{#tt=null;#et=new Map;#nt=new Map;#it=null;#rt=null;#st=new CommandManager;#at=0;#ot=new Set;#lt=null;#ct=null;#ht=new Set;#dt=null;#ut=new IdManager;#pt=!1;#ft=!1;#gt=null;#mt=i.AnnotationEditorType.NONE;#bt=new Set;#vt=null;#yt=this.blur.bind(this);#_t=this.focus.bind(this);#At=this.copy.bind(this);#St=this.cut.bind(this);#Et=this.paste.bind(this);#xt=this.keydown.bind(this);#wt=this.onEditingAction.bind(this);#Ct=this.onPageChanging.bind(this);#Tt=this.onScaleChanging.bind(this);#Pt=this.onRotationChanging.bind(this);#kt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1};#Mt=[0,0];#Ft=null;#Rt=null;#Dt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>{const{activeElement:e}=document;return e&&t.#Rt.contains(e)&&t.hasSomethingToControl()},e=this.TRANSLATE_SMALL,n=this.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll],[["ctrl+z","mac+meta+z"],t.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-n,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[n,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-n],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,n],checker:arrowChecker}]]))}constructor(t,e,n,i,s,a){this.#Rt=t;this.#Dt=e;this.#it=n;this._eventBus=i;this._eventBus._on("editingaction",this.#wt);this._eventBus._on("pagechanging",this.#Ct);this._eventBus._on("scalechanging",this.#Tt);this._eventBus._on("rotationchanging",this.#Pt);this.#rt=s.annotationStorage;this.#dt=s.filterFactory;this.#vt=a;this.viewParameters={realScale:r.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}destroy(){this.#It();this.#Ot();this._eventBus._off("editingaction",this.#wt);this._eventBus._off("pagechanging",this.#Ct);this._eventBus._off("scalechanging",this.#Tt);this._eventBus._off("rotationchanging",this.#Pt);for(const t of this.#nt.values())t.destroy();this.#nt.clear();this.#et.clear();this.#ht.clear();this.#tt=null;this.#bt.clear();this.#st.destroy();this.#it.destroy()}get hcmFilter(){return(0,i.shadow)(this,"hcmFilter",this.#vt?this.#dt.addHCMFilter(this.#vt.foreground,this.#vt.background):"none")}get direction(){return(0,i.shadow)(this,"direction",getComputedStyle(this.#Rt).direction)}editAltText(t){this.#it?.editAltText(this,t)}onPageChanging(t){let{pageNumber:e}=t;this.#at=e-1}focusMainContainer(){this.#Rt.focus()}findParent(t,e){for(const n of this.#nt.values()){const{x:i,y:r,width:s,height:a}=n.div.getBoundingClientRect();if(t>=i&&t<=i+s&&e>=r&&e<=r+a)return n}return null}disableUserSelect(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.#Dt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#ht.add(t)}removeShouldRescale(t){this.#ht.delete(t)}onScaleChanging(t){let{scale:e}=t;this.commitOrRemove();this.viewParameters.realScale=e*r.PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#ht)t.onScaleChanging()}onRotationChanging(t){let{pagesRotation:e}=t;this.commitOrRemove();this.viewParameters.rotation=e}addToAnnotationStorage(t){t.isEmpty()||!this.#rt||this.#rt.has(t.id)||this.#rt.setValue(t.id,t)}#Lt(){window.addEventListener("focus",this.#_t);window.addEventListener("blur",this.#yt)}#Ot(){window.removeEventListener("focus",this.#_t);window.removeEventListener("blur",this.#yt)}blur(){if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#bt)if(e.div.contains(t)){this.#gt=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#gt)return;const[t,e]=this.#gt;this.#gt=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0});e.focus()}#Nt(){window.addEventListener("keydown",this.#xt,{capture:!0})}#It(){window.removeEventListener("keydown",this.#xt,{capture:!0})}#Bt(){document.addEventListener("copy",this.#At);document.addEventListener("cut",this.#St);document.addEventListener("paste",this.#Et)}#jt(){document.removeEventListener("copy",this.#At);document.removeEventListener("cut",this.#St);document.removeEventListener("paste",this.#Et)}addEditListeners(){this.#Nt();this.#Bt()}removeEditListeners(){this.#It();this.#jt()}copy(t){t.preventDefault();this.#tt?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#bt){const n=t.serialize(!0);n&&e.push(n)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#ct)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let n=e.getData("application/pdfjs");if(!n)return;try{n=JSON.parse(n)}catch(t){(0,i.warn)(`paste: "${t.message}".`);return}if(!Array.isArray(n))return;this.unselectAll();const r=this.currentLayer;try{const t=[];for(const e of n){const n=r.deserialize(e);if(!n)return;t.push(n)}const cmd=()=>{for(const e of t)this.#Ut(e);this.#zt(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd:cmd,undo:undo,mustExec:!0})}catch(t){(0,i.warn)(`paste: "${t.message}".`)}}keydown(t){this.getActive()?.shouldGetKeyboardEvents()||AnnotationEditorUIManager._keyboardManager.exec(this,t)}onEditingAction(t){["undo","redo","delete","selectAll"].includes(t.name)&&this[t.name]()}#Wt(t){Object.entries(t).some((t=>{let[e,n]=t;return this.#kt[e]!==n}))&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#kt,t)})}#Ht(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#Lt();this.#Nt();this.#Bt();this.#Wt({isEditing:this.#mt!==i.AnnotationEditorType.NONE,isEmpty:this.#qt(),hasSomethingToUndo:this.#st.hasSomethingToUndo(),hasSomethingToRedo:this.#st.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#Ot();this.#It();this.#jt();this.#Wt({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#ct){this.#ct=t;for(const t of this.#ct)this.#Ht(t.defaultPropertiesToUpdate)}}getId(){return this.#ut.getId()}get currentLayer(){return this.#nt.get(this.#at)}getLayer(t){return this.#nt.get(t)}get currentPageIndex(){return this.#at}addLayer(t){this.#nt.set(t.pageIndex,t);this.#pt?t.enable():t.disable()}removeLayer(t){this.#nt.delete(t.pageIndex)}updateMode(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.#mt!==t){this.#mt=t;if(t!==i.AnnotationEditorType.NONE){this.setEditingState(!0);this.#Gt();this.unselectAll();for(const e of this.#nt.values())e.updateMode(t);if(e)for(const t of this.#et.values())if(t.annotationElementId===e){this.setSelected(t);t.enterInEditMode();break}}else{this.setEditingState(!1);this.#Vt()}}}updateToolbar(t){t!==this.#mt&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#ct)if(t!==i.AnnotationEditorParamsType.CREATE){for(const n of this.#bt)n.updateParams(t,e);for(const n of this.#ct)n.updateDefaultParams(t,e)}else this.currentLayer.addNewEditor(t)}enableWaiting(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.#ft!==t){this.#ft=t;for(const e of this.#nt.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}#Gt(){if(!this.#pt){this.#pt=!0;for(const t of this.#nt.values())t.enable()}}#Vt(){this.unselectAll();if(this.#pt){this.#pt=!1;for(const t of this.#nt.values())t.disable()}}getEditors(t){const e=[];for(const n of this.#et.values())n.pageIndex===t&&e.push(n);return e}getEditor(t){return this.#et.get(t)}addEditor(t){this.#et.set(t.id,t)}removeEditor(t){this.#et.delete(t.id);this.unselect(t);t.annotationElementId&&this.#ot.has(t.annotationElementId)||this.#rt?.remove(t.id)}addDeletedAnnotationElement(t){this.#ot.add(t.annotationElementId);t.deleted=!0}isDeletedAnnotationElement(t){return this.#ot.has(t)}removeDeletedAnnotationElement(t){this.#ot.delete(t.annotationElementId);t.deleted=!1}#Ut(t){const e=this.#nt.get(t.pageIndex);e?e.addOrRebuild(t):this.addEditor(t)}setActiveEditor(t){if(this.#tt!==t){this.#tt=t;t&&this.#Ht(t.propertiesToUpdate)}}toggleSelected(t){if(this.#bt.has(t)){this.#bt.delete(t);t.unselect();this.#Wt({hasSelectedEditor:this.hasSelection})}else{this.#bt.add(t);t.select();this.#Ht(t.propertiesToUpdate);this.#Wt({hasSelectedEditor:!0})}}setSelected(t){for(const e of this.#bt)e!==t&&e.unselect();this.#bt.clear();this.#bt.add(t);t.select();this.#Ht(t.propertiesToUpdate);this.#Wt({hasSelectedEditor:!0})}isSelected(t){return this.#bt.has(t)}unselect(t){t.unselect();this.#bt.delete(t);this.#Wt({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#bt.size}undo(){this.#st.undo();this.#Wt({hasSomethingToUndo:this.#st.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#qt()})}redo(){this.#st.redo();this.#Wt({hasSomethingToUndo:!0,hasSomethingToRedo:this.#st.hasSomethingToRedo(),isEmpty:this.#qt()})}addCommands(t){this.#st.add(t);this.#Wt({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#qt()})}#qt(){if(0===this.#et.size)return!0;if(1===this.#et.size)for(const t of this.#et.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();if(!this.hasSelection)return;const t=[...this.#bt];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)this.#Ut(e)},mustExec:!0})}commitOrRemove(){this.#tt?.commitOrRemove()}hasSomethingToControl(){return this.#tt||this.hasSelection}#zt(t){this.#bt.clear();for(const e of t)if(!e.isEmpty()){this.#bt.add(e);e.select()}this.#Wt({hasSelectedEditor:!0})}selectAll(){for(const t of this.#bt)t.commit();this.#zt(this.#et.values())}unselectAll(){if(this.#tt)this.#tt.commitOrRemove();else if(this.hasSelection){for(const t of this.#bt)t.unselect();this.#bt.clear();this.#Wt({hasSelectedEditor:!1})}}translateSelectedEditors(t,e){arguments.length>2&&void 0!==arguments[2]&&arguments[2]||this.commitOrRemove();if(!this.hasSelection)return;this.#Mt[0]+=t;this.#Mt[1]+=e;const[n,i]=this.#Mt,r=[...this.#bt];this.#Ft&&clearTimeout(this.#Ft);this.#Ft=setTimeout((()=>{this.#Ft=null;this.#Mt[0]=this.#Mt[1]=0;this.addCommands({cmd:()=>{for(const t of r)this.#et.has(t.id)&&t.translateInPage(n,i)},undo:()=>{for(const t of r)this.#et.has(t.id)&&t.translateInPage(-n,-i)},mustExec:!1})}),1e3);for(const n of r)n.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#lt=new Map;for(const t of this.#bt)this.#lt.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#lt)return!1;this.disableUserSelect(!1);const t=this.#lt;this.#lt=null;let e=!1;for(const[{x:n,y:i,pageIndex:r},s]of t){s.newX=n;s.newY=i;s.newPageIndex=r;e||=n!==s.savedX||i!==s.savedY||r!==s.savedPageIndex}if(!e)return!1;const move=(t,e,n,i)=>{if(this.#et.has(t.id)){const r=this.#nt.get(i);if(r)t._setParentAndPosition(r,e,n);else{t.pageIndex=i;t.x=e;t.y=n}}};this.addCommands({cmd:()=>{for(const[e,{newX:n,newY:i,newPageIndex:r}]of t)move(e,n,i,r)},undo:()=>{for(const[e,{savedX:n,savedY:i,savedPageIndex:r}]of t)move(e,n,i,r)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#lt)for(const n of this.#lt.keys())n.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}isActive(t){return this.#tt===t}getActive(){return this.#tt}getMode(){return this.#mt}get imageManager(){return(0,i.shadow)(this,"imageManager",new ImageManager)}}e.AnnotationEditorUIManager=AnnotationEditorUIManager},(t,e,n)=>{var i=n(3),r=n(6),s=n(4),a=n(24),o=n(14),l=n(8),c=n(21),h=n(20),d=n(91),u=n(39),p=n(77),f=n(64),g=n(159),m=n(7),b=n(167),v=n(27),y=s.JSON,_=s.Number,A=s.SyntaxError,S=y&&y.parse,E=a("Object","keys"),x=Object.getOwnPropertyDescriptor,w=o("".charAt),C=o("".slice),T=o(/./.exec),P=o([].push),k=/^\d$/,M=/^[1-9]$/,R=/^(?:-|\d)$/,D=/^[\t\n\r ]$/,internalize=function(t,e,n,i){var r,s,a,o,c,p=t[e],g=i&&p===i.value,m=g&&"string"==typeof i.source?{source:i.source}:{};if(h(p)){var b=d(p),v=g?i.nodes:b?[]:{};if(b){r=v.length;a=f(p);for(o=0;o<a;o++)internalizeProperty(p,o,internalize(p,""+o,n,o<r?v[o]:void 0))}else{s=E(p);a=f(s);for(o=0;o<a;o++){c=s[o];internalizeProperty(p,c,internalize(p,c,n,u(v,c)?v[c]:void 0))}}}return l(n,t,e,p,m)},internalizeProperty=function(t,e,n){if(r){var i=x(t,e);if(i&&!i.configurable)return}void 0===n?delete t[e]:g(t,e,n)},Node=function(t,e,n,i){this.value=t;this.end=e;this.source=n;this.nodes=i},Context=function(t,e){this.source=t;this.index=e};Context.prototype={fork:function(t){return new Context(this.source,t)},parse:function(){var t=this.source,e=this.skip(D,this.index),n=this.fork(e),i=w(t,e);if(T(R,i))return n.number();switch(i){case"{":return n.object();case"[":return n.array();case'"':return n.string();case"t":return n.keyword(!0);case"f":return n.keyword(!1);case"n":return n.keyword(null)}throw A('Unexpected character: "'+i+'" at: '+e)},node:function(t,e,n,i,r){return new Node(e,i,t?null:C(this.source,n,i),r)},object:function(){for(var t=this.source,e=this.index+1,n=!1,i={},r={};e<t.length;){e=this.until(['"',"}"],e);if("}"===w(t,e)&&!n){e++;break}var s=this.fork(e).string(),a=s.value;e=s.end;e=this.until([":"],e)+1;e=this.skip(D,e);s=this.fork(e).parse();g(r,a,s);g(i,a,s.value);e=this.until([",","}"],s.end);var o=w(t,e);if(","===o){n=!0;e++}else if("}"===o){e++;break}}return this.node(1,i,this.index,e,r)},array:function(){for(var t=this.source,e=this.index+1,n=!1,i=[],r=[];e<t.length;){e=this.skip(D,e);if("]"===w(t,e)&&!n){e++;break}var s=this.fork(e).parse();P(r,s);P(i,s.value);e=this.until([",","]"],s.end);if(","===w(t,e)){n=!0;e++}else if("]"===w(t,e)){e++;break}}return this.node(1,i,this.index,e,r)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(0,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,n=e;"-"===w(t,n)&&n++;if("0"===w(t,n))n++;else{if(!T(M,w(t,n)))throw A("Failed to parse number at: "+n);n=this.skip(k,++n)}"."===w(t,n)&&(n=this.skip(k,++n));if("e"===w(t,n)||"E"===w(t,n)){n++;"+"!==w(t,n)&&"-"!==w(t,n)||n++;if(n===(n=this.skip(k,n)))throw A("Failed to parse number's exponent value at: "+n)}return this.node(0,_(C(t,e,n)),e,n)},keyword:function(t){var e=""+t,n=this.index,i=n+e.length;if(C(this.source,n,i)!==e)throw A("Failed to parse value at: "+n);return this.node(0,t,n,i)},skip:function(t,e){for(var n=this.source;e<n.length&&T(t,w(n,e));e++);return e},until:function(t,e){e=this.skip(D,e);for(var n=w(this.source,e),i=0;i<t.length;i++)if(t[i]===n)return e;throw A('Unexpected character: "'+n+'" at: '+e)}};var I=m((function(){var t,e="9007199254740993";S(e,(function(e,n,i){t=i.source}));return t!==e})),O=v&&!m((function(){return 1/S("-0 \t")!=-1/0}));i({target:"JSON",stat:!0,forced:I},{parse:function parse(t,e){return O&&!c(e)?S(t):function(t,e){t=p(t);var n=new Context(t,0,""),i=n.parse(),r=i.value,s=n.skip(D,i.end);if(s<t.length)throw A('Unexpected extra character: "'+w(t,s)+'" after the parsed data at: '+s);return c(e)?internalize({"":r},"",e,i):r}(t,e)}})},(t,e,n)=>{var i=n(14),r=n(39),s=SyntaxError,a=parseInt,o=String.fromCharCode,l=i("".charAt),c=i("".slice),h=i(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){for(var n=!0,i="";e<t.length;){var f=l(t,e);if("\\"===f){var g=c(t,e,e+2);if(r(d,g)){i+=d[g];e+=2}else{if("\\u"!==g)throw s('Unknown escape sequence: "'+g+'"');var m=c(t,e+=2,e+4);if(!h(u,m))throw s("Bad Unicode escape at: "+e);i+=o(a(m,16));e+=4}}else{if('"'===f){n=!1;e++;break}if(h(p,f))throw s("Bad control character in string literal at: "+e);i+=f;e++}}if(n)throw s("Unterminated string at: "+e);return{value:i,end:e}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMFilterFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=void 0;e.deprecated=function deprecated(t){console.log("Deprecated API usage: "+t)};e.getColorValues=function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const n of t.keys()){e.style.color=n;const i=window.getComputedStyle(e).color;t.set(n,getRGB(i))}e.remove()};e.getCurrentTransform=function getCurrentTransform(t){const{a:e,b:n,c:i,d:r,e:s,f:a}=t.getTransform();return[e,n,i,r,s,a]};e.getCurrentTransformInverse=function getCurrentTransformInverse(t){const{a:e,b:n,c:i,d:r,e:s,f:a}=t.getTransform().invertSelf();return[e,n,i,r,s,a]};e.getFilenameFromUrl=function getFilenameFromUrl(t){arguments.length>1&&void 0!==arguments[1]&&arguments[1]||([t]=t.split(/[#?]/,1));return t.substring(t.lastIndexOf("/")+1)};e.getPdfFilenameFromUrl=function getPdfFilenameFromUrl(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!=typeof t)return e;if(isDataScheme(t)){(0,r.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const n=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,i=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let s=n.exec(i[1])||n.exec(i[2])||n.exec(i[3]);if(s){s=s[0];if(s.includes("%"))try{s=n.exec(decodeURIComponent(s))[0]}catch{}}return s||e};e.getRGB=getRGB;e.getXfaPageViewport=function getXfaPageViewport(t,e){let{scale:n=1,rotation:i=0}=e;const{width:r,height:s}=t.attributes.style,a=[0,0,parseInt(r),parseInt(s)];return new PageViewport({viewBox:a,scale:n,rotation:i})};e.isDataScheme=isDataScheme;e.isPdfFile=function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)};e.isValidFetchUrl=isValidFetchUrl;e.loadScript=function loadScript(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(((n,i)=>{const r=document.createElement("script");r.src=t;r.onload=function(t){e&&r.remove();n(t)};r.onerror=function(){i(new Error(`Cannot load script at: ${r.src}`))};(document.head||document.documentElement).append(r)}))};e.noContextMenu=function noContextMenu(t){t.preventDefault()};e.setLayerDimensions=function setLayerDimensions(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(e instanceof PageViewport){const{pageWidth:i,pageHeight:s}=e.rawDims,{style:a}=t,o=r.FeatureTest.isCSSRoundSupported,l=`var(--scale-factor) * ${i}px`,c=`var(--scale-factor) * ${s}px`,h=o?`round(${l}, 1px)`:`calc(${l})`,d=o?`round(${c}, 1px)`:`calc(${c})`;if(n&&e.rotation%180!=0){a.width=d;a.height=h}else{a.width=h;a.height=d}}i&&t.setAttribute("data-main-rotation",e.rotation)};n(2);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);n(84);n(86);n(87);var i=n(169),r=n(1);const s="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}e.PixelsPerInch=PixelsPerInch;class DOMFilterFactory extends i.BaseFilterFactory{#$t;#Xt;#e;#Kt;#Yt;#Jt;#Qt;#Zt;#te;#ee;#G=0;constructor(){let{docId:t,ownerDocument:e=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super();this.#e=t;this.#Kt=e}get#$(){return this.#$t||=new Map}get#ne(){if(!this.#Xt){const t=this.#Kt.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const n=this.#Kt.createElementNS(s,"svg");n.setAttribute("width",0);n.setAttribute("height",0);this.#Xt=this.#Kt.createElementNS(s,"defs");t.append(n);n.append(this.#Xt);this.#Kt.body.append(t)}return this.#Xt}addFilter(t){if(!t)return"none";let e,n,i,r,s=this.#$.get(t);if(s)return s;if(1===t.length){const s=t[0],a=new Array(256);for(let t=0;t<256;t++)a[t]=s[t]/255;r=e=n=i=a.join(",")}else{const[s,a,o]=t,l=new Array(256),c=new Array(256),h=new Array(256);for(let t=0;t<256;t++){l[t]=s[t]/255;c[t]=a[t]/255;h[t]=o[t]/255}e=l.join(",");n=c.join(",");i=h.join(",");r=`${e}${n}${i}`}s=this.#$.get(r);if(s){this.#$.set(t,s);return s}const a=`g_${this.#e}_transfer_map_${this.#G++}`,o=`url(#${a})`;this.#$.set(t,o);this.#$.set(r,o);const l=this.#ie(a);this.#re(e,n,i,l);return o}addHCMFilter(t,e){const n=`${t}-${e}`;if(this.#Jt===n)return this.#Qt;this.#Jt=n;this.#Qt="none";this.#Yt?.remove();if(!t||!e)return this.#Qt;const i=this.#se(t);t=r.Util.makeHexColor(...i);const s=this.#se(e);e=r.Util.makeHexColor(...s);this.#ne.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return this.#Qt;const a=new Array(256);for(let t=0;t<=255;t++){const e=t/255;a[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const o=a.join(","),l=`g_${this.#e}_hcm_filter`,c=this.#Zt=this.#ie(l);this.#re(o,o,o,c);this.#ae(c);const getSteps=(t,e)=>{const n=i[t]/255,r=s[t]/255,a=new Array(e+1);for(let t=0;t<=e;t++)a[t]=n+t/e*(r-n);return a.join(",")};this.#re(getSteps(0,5),getSteps(1,5),getSteps(2,5),c);this.#Qt=`url(#${l})`;return this.#Qt}addHighlightHCMFilter(t,e,n,i){const r=`${t}-${e}-${n}-${i}`;if(this.#te===r)return this.#ee;this.#te=r;this.#ee="none";this.#Zt?.remove();if(!t||!e)return this.#ee;const[s,a]=[t,e].map(this.#se.bind(this));let o=Math.round(.2126*s[0]+.7152*s[1]+.0722*s[2]),l=Math.round(.2126*a[0]+.7152*a[1]+.0722*a[2]),[c,h]=[n,i].map(this.#se.bind(this));l<o&&([o,l,c,h]=[l,o,h,c]);this.#ne.style.color="";const getSteps=(t,e,n)=>{const i=new Array(256),r=(l-o)/n,s=t/255,a=(e-t)/(255*n);let c=0;for(let t=0;t<=n;t++){const e=Math.round(o+t*r),n=s+t*a;for(let t=c;t<=e;t++)i[t]=n;c=e+1}for(let t=c;t<256;t++)i[t]=i[c-1];return i.join(",")},d=`g_${this.#e}_hcm_highlight_filter`,u=this.#Zt=this.#ie(d);this.#ae(u);this.#re(getSteps(c[0],h[0],5),getSteps(c[1],h[1],5),getSteps(c[2],h[2],5),u);this.#ee=`url(#${d})`;return this.#ee}destroy(){if(!(arguments.length>0&&void 0!==arguments[0]&&arguments[0])||!this.#Qt&&!this.#ee){if(this.#Xt){this.#Xt.parentNode.parentNode.remove();this.#Xt=null}if(this.#$t){this.#$t.clear();this.#$t=null}this.#G=0}}#ae(t){const e=this.#Kt.createElementNS(s,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#ie(t){const e=this.#Kt.createElementNS(s,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#ne.append(e);return e}#oe(t,e,n){const i=this.#Kt.createElementNS(s,e);i.setAttribute("type","discrete");i.setAttribute("tableValues",n);t.append(i)}#re(t,e,n,i){const r=this.#Kt.createElementNS(s,"feComponentTransfer");i.append(r);this.#oe(r,"feFuncR",t);this.#oe(r,"feFuncG",e);this.#oe(r,"feFuncB",n)}#se(t){this.#ne.style.color=t;return getRGB(getComputedStyle(this.#ne).getPropertyValue("color"))}}e.DOMFilterFactory=DOMFilterFactory;class DOMCanvasFactory extends i.BaseCanvasFactory{constructor(){let{ownerDocument:t=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super();this._document=t}_createCanvas(t,e){const n=this._document.createElement("canvas");n.width=t;n.height=e;return n}}e.DOMCanvasFactory=DOMCanvasFactory;async function fetchData(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(isValidFetchUrl(t,document.baseURI)){const n=await fetch(t);if(!n.ok)throw new Error(n.statusText);return e?new Uint8Array(await n.arrayBuffer()):(0,r.stringToBytes)(await n.text())}return new Promise(((n,i)=>{const s=new XMLHttpRequest;s.open("GET",t,!0);e&&(s.responseType="arraybuffer");s.onreadystatechange=()=>{if(s.readyState===XMLHttpRequest.DONE){if(200===s.status||0===s.status){let t;e&&s.response?t=new Uint8Array(s.response):!e&&s.responseText&&(t=(0,r.stringToBytes)(s.responseText));if(t){n(t);return}}i(new Error(s.statusText))}};s.send(null)}))}class DOMCMapReaderFactory extends i.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t,this.isCompressed).then((t=>({cMapData:t,compressionType:e})))}}e.DOMCMapReaderFactory=DOMCMapReaderFactory;class DOMStandardFontDataFactory extends i.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t,!0)}}e.DOMStandardFontDataFactory=DOMStandardFontDataFactory;class DOMSVGFactory extends i.BaseSVGFactory{_createSVG(t){return document.createElementNS(s,t)}}e.DOMSVGFactory=DOMSVGFactory;class PageViewport{constructor(t){let{viewBox:e,scale:n,rotation:i,offsetX:r=0,offsetY:s=0,dontFlip:a=!1}=t;this.viewBox=e;this.scale=n;this.rotation=i;this.offsetX=r;this.offsetY=s;const o=(e[2]+e[0])/2,l=(e[3]+e[1])/2;let c,h,d,u,p,f,g,m;i%=360;i<0&&(i+=360);switch(i){case 180:c=-1;h=0;d=0;u=1;break;case 90:c=0;h=1;d=1;u=0;break;case 270:c=0;h=-1;d=-1;u=0;break;case 0:c=1;h=0;d=0;u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){d=-d;u=-u}if(0===c){p=Math.abs(l-e[1])*n+r;f=Math.abs(o-e[0])*n+s;g=(e[3]-e[1])*n;m=(e[2]-e[0])*n}else{p=Math.abs(o-e[0])*n+r;f=Math.abs(l-e[1])*n+s;g=(e[2]-e[0])*n;m=(e[3]-e[1])*n}this.transform=[c*n,h*n,d*n,u*n,p-c*n*o-d*n*l,f-h*n*o-u*n*l];this.width=g;this.height=m}get rawDims(){const{viewBox:t}=this;return(0,r.shadow)(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone(){let{scale:t=this.scale,rotation:e=this.rotation,offsetX:n=this.offsetX,offsetY:i=this.offsetY,dontFlip:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new PageViewport({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:n,offsetY:i,dontFlip:r})}convertToViewportPoint(t,e){return r.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=r.Util.applyTransform([t[0],t[1]],this.transform),n=r.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],n[0],n[1]]}convertToPdfPoint(t,e){return r.Util.applyInverseTransform([t,e],this.transform)}}e.PageViewport=PageViewport;class RenderingCancelledException extends r.BaseException{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;super(t,"RenderingCancelledException");this.extraDelay=e}}e.RenderingCancelledException=RenderingCancelledException;function isDataScheme(t){const e=t.length;let n=0;for(;n<e&&""===t[n].trim();)n++;return"data:"===t.substring(n,n+5).toLowerCase()}e.StatTimer=class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&(0,r.warn)(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,r.warn)(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:n,start:i,end:r}of this.times)t.push(`${n.padEnd(e)} ${r-i}ms\n`);return t.join("")}};function isValidFetchUrl(t,e){try{const{protocol:n}=e?new URL(t,e):new URL(t);return"http:"===n||"https:"===n}catch{return!1}}let a;e.PDFDateString=class PDFDateString{static toDateObject(t){if(!t||"string"!=typeof t)return null;a||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=a.exec(t);if(!e)return null;const n=parseInt(e[1],10);let i=parseInt(e[2],10);i=i>=1&&i<=12?i-1:0;let r=parseInt(e[3],10);r=r>=1&&r<=31?r:1;let s=parseInt(e[4],10);s=s>=0&&s<=23?s:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const c=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;d=d>=0&&d<=59?d:0;if("-"===c){s+=h;o+=d}else if("+"===c){s-=h;o-=d}return new Date(Date.UTC(n,i,r,s,o,l))}};function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);(0,r.warn)(`Not a valid color format: "${t}"`);return[0,0,0]}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseFilterFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0;n(2);var i=n(1);class BaseFilterFactory{constructor(){this.constructor===BaseFilterFactory&&(0,i.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addHighlightHCMFilter(t,e,n,i){return"none"}destroy(){}}e.BaseFilterFactory=BaseFilterFactory;class BaseCanvasFactory{constructor(){this.constructor===BaseCanvasFactory&&(0,i.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const n=this._createCanvas(t,e);return{canvas:n,context:n.getContext("2d")}}reset(t,e,n){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||n<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=n}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){(0,i.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=BaseCanvasFactory;class BaseCMapReaderFactory{constructor(t){let{baseUrl:e=null,isCompressed:n=!0}=t;this.constructor===BaseCMapReaderFactory&&(0,i.unreachable)("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=e;this.isCompressed=n}async fetch(t){let{name:e}=t;if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!e)throw new Error("CMap name must be specified.");const n=this.baseUrl+e+(this.isCompressed?".bcmap":""),r=this.isCompressed?i.CMapCompressionType.BINARY:i.CMapCompressionType.NONE;return this._fetchData(n,r).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${n}`)}))}_fetchData(t,e){(0,i.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=BaseCMapReaderFactory;class BaseStandardFontDataFactory{constructor(t){let{baseUrl:e=null}=t;this.constructor===BaseStandardFontDataFactory&&(0,i.unreachable)("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=e}async fetch(t){let{filename:e}=t;if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!e)throw new Error("Font filename must be specified.");const n=`${this.baseUrl}${e}`;return this._fetchData(n).catch((t=>{throw new Error(`Unable to load font data at: ${n}`)}))}_fetchData(t){(0,i.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=BaseStandardFontDataFactory;class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&(0,i.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const i=this._createSVG("svg:svg");i.setAttribute("version","1.1");if(!n){i.setAttribute("width",`${t}px`);i.setAttribute("height",`${e}px`)}i.setAttribute("preserveAspectRatio","none");i.setAttribute("viewBox",`0 0 ${t} ${e}`);return i}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,i.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=BaseSVGFactory},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.MurmurHash3_64=void 0;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(2);var i=n(1);const r=3285377520,s=4294901760,a=65535;e.MurmurHash3_64=class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:r;this.h2=t?4294967295&t:r}update(t){let e,n;if("string"==typeof t){e=new Uint8Array(2*t.length);n=0;for(let i=0,r=t.length;i<r;i++){const r=t.charCodeAt(i);if(r<=255)e[n++]=r;else{e[n++]=r>>>8;e[n++]=255&r}}}else{if(!(0,i.isArrayBuffer)(t))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");e=t.slice();n=e.byteLength}const r=n>>2,o=n-4*r,l=new Uint32Array(e.buffer,0,r);let c=0,h=0,d=this.h1,u=this.h2;const p=3432918353,f=461845907,g=11601,m=13715;for(let t=0;t<r;t++)if(1&t){c=l[t];c=c*p&s|c*g&a;c=c<<15|c>>>17;c=c*f&s|c*m&a;d^=c;d=d<<13|d>>>19;d=5*d+3864292196}else{h=l[t];h=h*p&s|h*g&a;h=h<<15|h>>>17;h=h*f&s|h*m&a;u^=h;u=u<<13|u>>>19;u=5*u+3864292196}c=0;switch(o){case 3:c^=e[4*r+2]<<16;case 2:c^=e[4*r+1]<<8;case 1:c^=e[4*r];c=c*p&s|c*g&a;c=c<<15|c>>>17;c=c*f&s|c*m&a;1&r?d^=c:u^=c}this.h1=d;this.h2=u}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&s|36045*t&a;e=4283543511*e&s|(2950163797*(e<<16|t>>>16)&s)>>>16;t^=e>>>1;t=444984403*t&s|60499*t&a;e=3301882366*e&s|(3120437893*(e<<16|t>>>16)&s)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.FontLoader=e.FontFaceObject=void 0;n(125);n(136);n(138);n(141);n(143);n(145);n(147);n(89);n(149);var i=n(1);e.FontLoader=class FontLoader{#le=new Set;constructor(t){let{ownerDocument:e=globalThis.document,styleElement:n=null}=t;this._document=e;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#le.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont(t){if(t&&!this.#le.has(t.loadedName)){(0,i.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:e,src:n,style:r}=t,s=new FontFace(e,n,r);this.addNativeFontFace(s);try{await s.load();this.#le.add(e)}catch{(0,i.warn)(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(s)}}else(0,i.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t.systemFontInfo);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(n){(0,i.warn)(`Failed to load font '${e.family}': '${n}'.`);t.disableFontFace=!0;throw n}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const n=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,n)}))}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return(0,i.shadow)(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){let t=!1;(i.isNodeJS||"undefined"!=typeof navigator&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0);return(0,i.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,n={done:!1,complete:function completeRequest(){(0,i.assert)(!n.done,"completeRequest() cannot be called twice.");n.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(n);return n}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,i.shadow)(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,n,i){return t.substring(0,e)+i+t.substring(e+n)}let n,r;const s=this._document.createElement("canvas");s.width=1;s.height=1;const a=s.getContext("2d");let o=0;const l=`lt${Date.now()}${this.loadTestFontId++}`;let c=this._loadTestFont;c=spliceString(c,976,l.length,l);const h=1482184792;let d=int32(c,16);for(n=0,r=l.length-3;n<r;n+=4)d=d-h+int32(l,n)|0;n<l.length&&(d=d-h+int32(l+"XXX",n)|0);c=spliceString(c,16,4,(0,i.string32)(d));const u=`@font-face {font-family:"${l}";src:${`url(data:font/opentype;base64,${btoa(c)});`}}`;this.insertRule(u);const p=this._document.createElement("div");p.style.visibility="hidden";p.style.width=p.style.height="10px";p.style.position="absolute";p.style.top=p.style.left="0px";for(const e of[t.loadedName,l]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;p.append(t)}this._document.body.append(p);!function isFontReady(t,e){if(++o>30){(0,i.warn)("Load test font never loaded.");e();return}a.font="30px "+t;a.fillText(".",0,20);a.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(l,(()=>{p.remove();e.complete()}))}};e.FontFaceObject=class FontFaceObject{constructor(t,e){let{isEvalSupported:n=!0,disableFontFace:i=!1,ignoreErrors:r=!1,inspectFont:s=null}=e;this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.isEvalSupported=!1!==n;this.disableFontFace=!0===i;this.ignoreErrors=!0===r;this._inspectFont=s}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=(0,i.bytesToString)(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let n;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);n=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else n=`@font-face {font-family:"${this.loadedName}";src:${e}}`;this._inspectFont?.(this,e);return n}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let n;try{n=t.get(this.loadedName+"_path_"+e)}catch(t){if(!this.ignoreErrors)throw t;(0,i.warn)(`getPathGenerator - ignoring character: "${t}".`);return this.compiledGlyphs[e]=function(t,e){}}if(this.isEvalSupported&&i.FeatureTest.isEvalSupported){const t=[];for(const e of n){const n=void 0!==e.args?e.args.join(","):"";t.push("c.",e.cmd,"(",n,");\n")}return this.compiledGlyphs[e]=new Function("c","size",t.join(""))}return this.compiledGlyphs[e]=function(t,e){for(const i of n){"scale"===i.cmd&&(i.args=[e,-e]);t[i.cmd].apply(t,i.args)}}}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.NodeStandardFontDataFactory=e.NodeFilterFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0;n(2);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var i=n(169),r=n(1);!function checkDOMMatrix(){if(!globalThis.DOMMatrix&&r.isNodeJS)try{globalThis.DOMMatrix=require("canvas").DOMMatrix}catch(t){(0,r.warn)(`Cannot polyfill \`DOMMatrix\`, rendering may be broken: "${t}".`)}}();!function checkPath2D(){if(!globalThis.Path2D&&r.isNodeJS)try{const{CanvasRenderingContext2D:t}=require("canvas"),{polyfillPath2D:e}=require("path2d-polyfill");globalThis.CanvasRenderingContext2D=t;e(globalThis)}catch(t){(0,r.warn)(`Cannot polyfill \`Path2D\`, rendering may be broken: "${t}".`)}}();const fetchData=function(t){return new Promise(((e,n)=>{require("fs").readFile(t,((t,i)=>{!t&&i?e(new Uint8Array(i)):n(new Error(t))}))}))};class NodeFilterFactory extends i.BaseFilterFactory{}e.NodeFilterFactory=NodeFilterFactory;class NodeCanvasFactory extends i.BaseCanvasFactory{_createCanvas(t,e){return require("canvas").createCanvas(t,e)}}e.NodeCanvasFactory=NodeCanvasFactory;class NodeCMapReaderFactory extends i.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t).then((t=>({cMapData:t,compressionType:e})))}}e.NodeCMapReaderFactory=NodeCMapReaderFactory;class NodeStandardFontDataFactory extends i.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t)}}e.NodeStandardFontDataFactory=NodeStandardFontDataFactory},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.CanvasGraphics=void 0;n(2);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);var i=n(1),r=n(168),s=n(174),a=n(175);const o=4096,l=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,n){let i;if(void 0!==this.cache[t]){i=this.cache[t];this.canvasFactory.reset(i,e,n)}else{i=this.canvasFactory.create(e,n);this.cache[t]=i}return i}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,n,i,s,a,o,l,c,h){const[d,u,p,f,g,m]=(0,r.getCurrentTransform)(t);if(0===u&&0===p){const r=o*d+g,b=Math.round(r),v=l*f+m,y=Math.round(v),_=(o+c)*d+g,A=Math.abs(Math.round(_)-b)||1,S=(l+h)*f+m,E=Math.abs(Math.round(S)-y)||1;t.setTransform(Math.sign(d),0,0,Math.sign(f),b,y);t.drawImage(e,n,i,s,a,0,0,A,E);t.setTransform(d,u,p,f,g,m);return[A,E]}if(0===d&&0===f){const r=l*p+g,b=Math.round(r),v=o*u+m,y=Math.round(v),_=(l+h)*p+g,A=Math.abs(Math.round(_)-b)||1,S=(o+c)*u+m,E=Math.abs(Math.round(S)-y)||1;t.setTransform(0,Math.sign(u),Math.sign(p),0,b,y);t.drawImage(e,n,i,s,a,0,0,E,A);t.setTransform(d,u,p,f,g,m);return[E,A]}t.drawImage(e,n,i,s,a,o,l,c,h);return[Math.hypot(d,u)*c,Math.hypot(p,f)*h]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=i.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=i.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=i.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps="none";this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,n){[e,n]=i.Util.applyTransform([e,n],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,n);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,n)}updateRectMinMax(t,e){const n=i.Util.applyTransform(e,t),r=i.Util.applyTransform(e.slice(2),t);this.minX=Math.min(this.minX,n[0],r[0]);this.minY=Math.min(this.minY,n[1],r[1]);this.maxX=Math.max(this.maxX,n[0],r[0]);this.maxY=Math.max(this.maxY,n[1],r[1])}updateScalingPathMinMax(t,e){i.Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.maxX=Math.max(this.maxX,e[1]);this.minY=Math.min(this.minY,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,n,r,s,a,o,l,c,h){const d=i.Util.bezierBoundingBox(e,n,r,s,a,o,l,c);if(h){h[0]=Math.min(h[0],d[0],d[2]);h[1]=Math.max(h[1],d[0],d[2]);h[2]=Math.min(h[2],d[1],d[3]);h[3]=Math.max(h[3],d[1],d[3])}else this.updateRectMinMax(t,d)}getPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=[this.minX,this.minY,this.maxX,this.maxY];if(t===s.PathType.STROKE){e||(0,i.unreachable)("Stroke bounding box must include transform.");const t=i.Util.singularValueDecompose2dScale(e),r=t[0]*this.lineWidth/2,s=t[1]*this.lineWidth/2;n[0]-=r;n[1]-=s;n[2]+=r;n[3]+=s}return n}updateClipFromPath(){const t=i.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:s.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return i.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}const n=e.height,r=e.width,s=n%l,a=(n-s)/l,o=0===s?a:a+1,c=t.createImageData(r,l);let h,d=0;const u=e.data,p=c.data;let f,g,m,b;if(e.kind===i.ImageKind.GRAYSCALE_1BPP){const e=u.byteLength,n=new Uint32Array(p.buffer,0,p.byteLength>>2),b=n.length,v=r+7>>3,y=4294967295,_=i.FeatureTest.isLittleEndian?4278190080:255;for(f=0;f<o;f++){m=f<a?l:s;h=0;for(g=0;g<m;g++){const t=e-d;let i=0;const s=t>v?r:8*t-7,a=-8&s;let o=0,l=0;for(;i<a;i+=8){l=u[d++];n[h++]=128&l?y:_;n[h++]=64&l?y:_;n[h++]=32&l?y:_;n[h++]=16&l?y:_;n[h++]=8&l?y:_;n[h++]=4&l?y:_;n[h++]=2&l?y:_;n[h++]=1&l?y:_}for(;i<s;i++){if(0===o){l=u[d++];o=128}n[h++]=l&o?y:_;o>>=1}}for(;h<b;)n[h++]=0;t.putImageData(c,0,f*l)}}else if(e.kind===i.ImageKind.RGBA_32BPP){g=0;b=r*l*4;for(f=0;f<a;f++){p.set(u.subarray(d,d+b));d+=b;t.putImageData(c,0,g);g+=l}if(f<o){b=r*s*4;p.set(u.subarray(d,d+b));t.putImageData(c,0,g)}}else{if(e.kind!==i.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);m=l;b=r*m;for(f=0;f<o;f++){if(f>=a){m=s;b=r*m}h=0;for(g=b;g--;){p[h++]=u[d++];p[h++]=u[d++];p[h++]=u[d++];p[h++]=255}t.putImageData(c,0,f*l)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const n=e.height,i=e.width,r=n%l,s=(n-r)/l,o=0===r?s:s+1,c=t.createImageData(i,l);let h=0;const d=e.data,u=c.data;for(let e=0;e<o;e++){const n=e<s?l:r;({srcPos:h}=(0,a.convertBlackAndWhiteToRGBA)({src:d,srcPos:h,dest:u,width:i,height:n,nonBlackColor:0}));t.putImageData(c,0,e*l)}}function copyCtxState(t,e){const n=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const i of n)void 0!==t[i]&&(e[i]=t[i]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}if(!i.isNodeJS){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function composeSMaskBackdrop(t,e,n,i){const r=t.length;for(let s=3;s<r;s+=4){const r=t[s];if(0===r){t[s-3]=e;t[s-2]=n;t[s-1]=i}else if(r<255){const a=255-r;t[s-3]=t[s-3]*r+e*a>>8;t[s-2]=t[s-2]*r+n*a>>8;t[s-1]=t[s-1]*r+i*a>>8}}}function composeSMaskAlpha(t,e,n){const i=t.length;for(let r=3;r<i;r+=4){const i=n?n[t[r]]:t[r];e[r]=e[r]*i*.00392156862745098|0}}function composeSMaskLuminosity(t,e,n){const i=t.length;for(let r=3;r<i;r+=4){const i=77*t[r-3]+152*t[r-2]+28*t[r-1];e[r]=n?e[r]*n[i>>8]>>8:e[r]*i>>16}}function composeSMask(t,e,n,i){const r=i[0],s=i[1],a=i[2]-r,o=i[3]-s;if(0!==a&&0!==o){!function genericComposeSMask(t,e,n,i,r,s,a,o,l,c,h){const d=!!s,u=d?s[0]:0,p=d?s[1]:0,f=d?s[2]:0,g="Luminosity"===r?composeSMaskLuminosity:composeSMaskAlpha,m=Math.min(i,Math.ceil(1048576/n));for(let r=0;r<i;r+=m){const s=Math.min(m,i-r),b=t.getImageData(o-c,r+(l-h),n,s),v=e.getImageData(o,r+l,n,s);d&&composeSMaskBackdrop(b.data,u,p,f);g(b.data,v.data,a);e.putImageData(v,o,r+l)}}(e.context,n,a,o,e.subtype,e.backdrop,e.transferMap,r,s,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(n.canvas,0,0);t.restore()}}function getImageSmoothingEnabled(t,e){const n=i.Util.singularValueDecompose2dScale(t);n[0]=Math.fround(n[0]);n[1]=Math.fround(n[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*r.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==e?e:n[0]<=s||n[1]<=s}const c=["butt","round","square"],h=["miter","round","bevel"],d={},u={};class CanvasGraphics{constructor(t,e,n,i,r,s,a,o){let{optionalContentConfig:l,markedContentStack:c=null}=s;this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=n;this.canvasFactory=i;this.filterFactory=r;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=c||[];this.optionalContentConfig=l;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=a;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=o;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing(t){let{transform:e,viewport:n,transparency:i=!1,background:s=null}=t;const a=this.ctx.canvas.width,o=this.ctx.canvas.height,l=this.ctx.fillStyle;this.ctx.fillStyle=s||"#ffffff";this.ctx.fillRect(0,0,a,o);this.ctx.fillStyle=l;if(i){const t=this.cachedCanvases.getCanvas("transparent",a,o);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...(0,r.getCurrentTransform)(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(e){this.ctx.transform(...e);this.outputScaleX=e[0];this.outputScaleY=e[0]}this.ctx.transform(...n.transform);this.viewportScale=n.scale;this.baseTransform=(0,r.getCurrentTransform)(this.ctx)}executeOperatorList(t,e,n,r){const s=t.argsArray,a=t.fnArray;let o=e||0;const l=s.length;if(l===o)return o;const c=l-o>10&&"function"==typeof n,h=c?Date.now()+15:0;let d=0;const u=this.commonObjs,p=this.objs;let f;for(;;){if(void 0!==r&&o===r.nextBreakPoint){r.breakIt(o,n);return o}f=a[o];if(f!==i.OPS.dependency)this[f].apply(this,s[o]);else for(const t of s[o]){const e=t.startsWith("g_")?u:p;if(!e.has(t)){e.get(t,n);return o}}o++;if(o===l)return o;if(c&&++d>10){if(Date.now()>h){n();return o}d=0}}}#ce(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#ce();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#he()}#he(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const n=t.width,i=t.height;let r,s,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=n,c=i,h="prescale1";for(;a>2&&l>1||o>2&&c>1;){let e=l,n=c;if(a>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);a/=l/e}if(o>2&&c>1){n=c>=16384?Math.floor(c/2)-1||1:Math.ceil(c)/2;o/=c/n}r=this.cachedCanvases.getCanvas(h,e,n);s=r.context;s.clearRect(0,0,e,n);s.drawImage(t,0,0,l,c,0,0,e,n);t=r.canvas;l=e;c=n;h="prescale1"===h?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:n,height:a}=t,o=this.current.fillColor,l=this.current.patternFill,c=(0,r.getCurrentTransform)(e);let h,d,u,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;d=JSON.stringify(l?c:[c.slice(0,4),o]);h=this._cachedBitmapsMap.get(e);if(!h){h=new Map;this._cachedBitmapsMap.set(e,h)}const n=h.get(d);if(n&&!l){return{canvas:n,offsetX:Math.round(Math.min(c[0],c[2])+c[4]),offsetY:Math.round(Math.min(c[1],c[3])+c[5])}}u=n}if(!u){p=this.cachedCanvases.getCanvas("maskCanvas",n,a);putBinaryImageMask(p.context,t)}let f=i.Util.transform(c,[1/n,0,0,-1/a,0,0]);f=i.Util.transform(f,[1,0,0,1,0,-a]);const g=i.Util.applyTransform([0,0],f),m=i.Util.applyTransform([n,a],f),b=i.Util.normalizeRect([g[0],g[1],m[0],m[1]]),v=Math.round(b[2]-b[0])||1,y=Math.round(b[3]-b[1])||1,_=this.cachedCanvases.getCanvas("fillCanvas",v,y),A=_.context,S=Math.min(g[0],m[0]),E=Math.min(g[1],m[1]);A.translate(-S,-E);A.transform(...f);if(!u){u=this._scaleImage(p.canvas,(0,r.getCurrentTransformInverse)(A));u=u.img;h&&l&&h.set(d,u)}A.imageSmoothingEnabled=getImageSmoothingEnabled((0,r.getCurrentTransform)(A),t.interpolate);drawImageAtIntegerCoords(A,u,0,0,u.width,u.height,0,0,n,a);A.globalCompositeOperation="source-in";const x=i.Util.transform((0,r.getCurrentTransformInverse)(A),[1,0,0,1,-S,-E]);A.fillStyle=l?o.getPattern(e,this,x,s.PathType.FILL):o;A.fillRect(0,0,n,a);if(h&&!l){this.cachedCanvases.delete("fillCanvas");h.set(d,_.canvas)}return{canvas:_.canvas,offsetX:Math.round(S),offsetY:Math.round(E)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=c[t]}setLineJoin(t){this.ctx.lineJoin=h[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const n=this.ctx;if(void 0!==n.setLineDash){n.setLineDash(t);n.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,n]of t)switch(e){case"LW":this.setLineWidth(n);break;case"LC":this.setLineCap(n);break;case"LJ":this.setLineJoin(n);break;case"ML":this.setMiterLimit(n);break;case"D":this.setDash(n[0],n[1]);break;case"RI":this.setRenderingIntent(n);break;case"FL":this.setFlatness(n);break;case"Font":this.setFont(n[0],n[1]);break;case"CA":this.current.strokeAlpha=n;break;case"ca":this.current.fillAlpha=n;this.ctx.globalAlpha=n;break;case"BM":this.ctx.globalCompositeOperation=n;break;case"SMask":this.current.activeSMask=n?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(n)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,n="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(n,t,e);this.suspendedCtx=this.ctx;this.ctx=i.context;const s=this.ctx;s.setTransform(...(0,r.getCurrentTransform)(this.suspendedCtx));copyCtxState(this.suspendedCtx,s);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,n){e.translate(t,n);this.__originalTranslate(t,n)};t.scale=function ctxScale(t,n){e.scale(t,n);this.__originalScale(t,n)};t.transform=function ctxTransform(t,n,i,r,s,a){e.transform(t,n,i,r,s,a);this.__originalTransform(t,n,i,r,s,a)};t.setTransform=function ctxSetTransform(t,n,i,r,s,a){e.setTransform(t,n,i,r,s,a);this.__originalSetTransform(t,n,i,r,s,a)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,n){e.moveTo(t,n);this.__originalMoveTo(t,n)};t.lineTo=function(t,n){e.lineTo(t,n);this.__originalLineTo(t,n)};t.bezierCurveTo=function(t,n,i,r,s,a){e.bezierCurveTo(t,n,i,r,s,a);this.__originalBezierCurveTo(t,n,i,r,s,a)};t.rect=function(t,n,i,r){e.rect(t,n,i,r);this.__originalRect(t,n,i,r)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(s,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask;composeSMask(this.suspendedCtx,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}}transform(t,e,n,i,r,s){this.ctx.transform(t,e,n,i,r,s);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,n){const s=this.ctx,a=this.current;let o,l,c=a.x,h=a.y;const d=(0,r.getCurrentTransform)(s),u=0===d[0]&&0===d[3]||0===d[1]&&0===d[2],p=u?n.slice(0):null;for(let n=0,r=0,f=t.length;n<f;n++)switch(0|t[n]){case i.OPS.rectangle:c=e[r++];h=e[r++];const t=e[r++],n=e[r++],f=c+t,g=h+n;s.moveTo(c,h);if(0===t||0===n)s.lineTo(f,g);else{s.lineTo(f,h);s.lineTo(f,g);s.lineTo(c,g)}u||a.updateRectMinMax(d,[c,h,f,g]);s.closePath();break;case i.OPS.moveTo:c=e[r++];h=e[r++];s.moveTo(c,h);u||a.updatePathMinMax(d,c,h);break;case i.OPS.lineTo:c=e[r++];h=e[r++];s.lineTo(c,h);u||a.updatePathMinMax(d,c,h);break;case i.OPS.curveTo:o=c;l=h;c=e[r+4];h=e[r+5];s.bezierCurveTo(e[r],e[r+1],e[r+2],e[r+3],c,h);a.updateCurvePathMinMax(d,o,l,e[r],e[r+1],e[r+2],e[r+3],c,h,p);r+=6;break;case i.OPS.curveTo2:o=c;l=h;s.bezierCurveTo(c,h,e[r],e[r+1],e[r+2],e[r+3]);a.updateCurvePathMinMax(d,o,l,c,h,e[r],e[r+1],e[r+2],e[r+3],p);c=e[r+2];h=e[r+3];r+=4;break;case i.OPS.curveTo3:o=c;l=h;c=e[r+2];h=e[r+3];s.bezierCurveTo(e[r],e[r+1],c,h,c,h);a.updateCurvePathMinMax(d,o,l,e[r],e[r+1],c,h,c,h,p);r+=4;break;case i.OPS.closePath:s.closePath()}u&&a.updateScalingPathMinMax(d,p);a.setCurrentPoint(c,h)}closePath(){this.ctx.closePath()}stroke(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,n=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof n&&n?.getPattern){e.save();e.strokeStyle=n.getPattern(e,this,(0,r.getCurrentTransformInverse)(e),s.PathType.STROKE);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,n=this.current.fillColor;let i=!1;if(this.current.patternFill){e.save();e.fillStyle=n.getPattern(e,this,(0,r.getCurrentTransformInverse)(e),s.PathType.FILL);i=!0}const a=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==a)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();i&&e.restore();t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=d}eoClip(){this.pendingClip=u}beginText(){this.current.textMatrix=i.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save();e.beginPath();for(const n of t){e.setTransform(...n.transform);e.translate(n.x,n.y);n.addToPath(e,n.fontSize)}e.restore();e.clip();e.beginPath();delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const n=this.commonObjs.get(t),r=this.current;if(!n)throw new Error(`Can't find font for ${t}`);r.fontMatrix=n.fontMatrix||i.FONT_IDENTITY_MATRIX;0!==r.fontMatrix[0]&&0!==r.fontMatrix[3]||(0,i.warn)("Invalid font matrix for font "+t);if(e<0){e=-e;r.fontDirection=-1}else r.fontDirection=1;this.current.font=n;this.current.fontSize=e;if(n.isType3Font)return;const s=n.loadedName||"sans-serif",a=n.systemFontInfo?.css||`"${s}", ${n.fallbackName}`;let o="normal";n.black?o="900":n.bold&&(o="bold");const l=n.italic?"italic":"normal";let c=e;e<16?c=16:e>100&&(c=100);this.current.fontSizeScale=e/c;this.ctx.font=`${l} ${o} ${c}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,n,i,r,s){this.current.textMatrix=[t,e,n,i,r,s];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,n,s){const a=this.ctx,o=this.current,l=o.font,c=o.textRenderingMode,h=o.fontSize/o.fontSizeScale,d=c&i.TextRenderingMode.FILL_STROKE_MASK,u=!!(c&i.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let f;(l.disableFontFace||u||p)&&(f=l.getPathGenerator(this.commonObjs,t));if(l.disableFontFace||p){a.save();a.translate(e,n);a.beginPath();f(a,h);s&&a.setTransform(...s);d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||a.fill();d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||a.stroke();a.restore()}else{d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||a.fillText(t,e,n);d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||a.strokeText(t,e,n)}if(u){(this.pendingTextPaths||=[]).push({transform:(0,r.getCurrentTransform)(a),x:e,y:n,fontSize:h,addToPath:f})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let n=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){n=!0;break}return(0,i.shadow)(this,"isFontSubpixelAAEnabled",n)}showText(t){const e=this.current,n=e.font;if(n.isType3Font)return this.showType3Text(t);const a=e.fontSize;if(0===a)return;const o=this.ctx,l=e.fontSizeScale,c=e.charSpacing,h=e.wordSpacing,d=e.fontDirection,u=e.textHScale*d,p=t.length,f=n.vertical,g=f?1:-1,m=n.defaultVMetrics,b=a*e.fontMatrix[0],v=e.textRenderingMode===i.TextRenderingMode.FILL&&!n.disableFontFace&&!e.patternFill;o.save();o.transform(...e.textMatrix);o.translate(e.x,e.y+e.textRise);d>0?o.scale(u,-1):o.scale(u,1);let y;if(e.patternFill){o.save();const t=e.fillColor.getPattern(o,this,(0,r.getCurrentTransformInverse)(o),s.PathType.FILL);y=(0,r.getCurrentTransform)(o);o.restore();o.fillStyle=t}let _=e.lineWidth;const A=e.textMatrixScale;if(0===A||0===_){const t=e.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;t!==i.TextRenderingMode.STROKE&&t!==i.TextRenderingMode.FILL_STROKE||(_=this.getSinglePixelWidth())}else _/=A;if(1!==l){o.scale(l,l);_/=l}o.lineWidth=_;if(n.isInvalidPDFjsFont){const n=[];let i=0;for(const e of t){n.push(e.unicode);i+=e.width}o.fillText(n.join(""),0,0);e.x+=i*b*u;o.restore();this.compose();return}let S,E=0;for(S=0;S<p;++S){const e=t[S];if("number"==typeof e){E+=g*e*a/1e3;continue}let i=!1;const r=(e.isSpace?h:0)+c,s=e.fontChar,u=e.accent;let p,_,A=e.width;if(f){const t=e.vmetric||m,n=-(e.vmetric?t[1]:.5*A)*b,i=t[2]*b;A=t?-t[0]:A;p=n/l;_=(E+i)/l}else{p=E/l;_=0}if(n.remeasure&&A>0){const t=1e3*o.measureText(s).width/a*l;if(A<t&&this.isFontSubpixelAAEnabled){const e=A/t;i=!0;o.save();o.scale(e,1);p/=e}else A!==t&&(p+=(A-t)/2e3*a/l)}if(this.contentVisible&&(e.isInFont||n.missingFile))if(v&&!u)o.fillText(s,p,_);else{this.paintChar(s,p,_,y);if(u){const t=p+a*u.offset.x/l,e=_-a*u.offset.y/l;this.paintChar(u.fontChar,t,e,y)}}E+=f?A*b-r*d:A*b+r*d;i&&o.restore()}f?e.y-=E:e.x+=E*u;o.restore();this.compose()}showType3Text(t){const e=this.ctx,n=this.current,r=n.font,s=n.fontSize,a=n.fontDirection,o=r.vertical?1:-1,l=n.charSpacing,c=n.wordSpacing,h=n.textHScale*a,d=n.fontMatrix||i.FONT_IDENTITY_MATRIX,u=t.length;let p,f,g,m;if(!(n.textRenderingMode===i.TextRenderingMode.INVISIBLE)&&0!==s){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...n.textMatrix);e.translate(n.x,n.y);e.scale(h,a);for(p=0;p<u;++p){f=t[p];if("number"==typeof f){m=o*f*s/1e3;this.ctx.translate(m,0);n.x+=m*h;continue}const a=(f.isSpace?c:0)+l,u=r.charProcOperatorList[f.operatorListId];if(!u){(0,i.warn)(`Type3 character "${f.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=f;this.save();e.scale(s,s);e.transform(...d);this.executeOperatorList(u);this.restore()}g=i.Util.applyTransform([f.width,0],d)[0]*s+a;e.translate(g,0);n.x+=g*h}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,n,i,r,s){this.ctx.rect(n,i,r-n,s-i);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const n=t[1],i=this.baseTransform||(0,r.getCurrentTransform)(this.ctx),a={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new s.TilingPattern(t,n,this.ctx,a,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,n){const r=i.Util.makeHexColor(t,e,n);this.ctx.strokeStyle=r;this.current.strokeColor=r}setFillRGBColor(t,e,n){const r=i.Util.makeHexColor(t,e,n);this.ctx.fillStyle=r;this.current.fillColor=r;this.current.patternFill=!1}_getPattern(t){let e,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.cachedPatterns.has(t))e=this.cachedPatterns.get(t);else{e=(0,s.getShadingPattern)(this.getObject(t));this.cachedPatterns.set(t,e)}n&&(e.matrix=n);return e}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const n=this._getPattern(t);e.fillStyle=n.getPattern(e,this,(0,r.getCurrentTransformInverse)(e),s.PathType.SHADING);const a=(0,r.getCurrentTransformInverse)(e);if(a){const{width:t,height:n}=e.canvas,[r,s,o,l]=i.Util.getAxialAlignedBoundingBox([0,0,t,n],a);this.ctx.fillRect(r,s,o-r,l-s)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){(0,i.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,i.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);Array.isArray(t)&&6===t.length&&this.transform(...t);this.baseTransform=(0,r.getCurrentTransform)(this.ctx);if(e){const t=e[2]-e[0],n=e[3]-e[1];this.ctx.rect(e[0],e[1],t,n);this.current.updateRectMinMax((0,r.getCurrentTransform)(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||(0,i.info)("TODO: Support non-isolated groups.");t.knockout&&(0,i.warn)("Knockout groups not supported.");const n=(0,r.getCurrentTransform)(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let s=i.Util.getAxialAlignedBoundingBox(t.bbox,(0,r.getCurrentTransform)(e));const a=[0,0,e.canvas.width,e.canvas.height];s=i.Util.intersect(s,a)||[0,0,0,0];const l=Math.floor(s[0]),c=Math.floor(s[1]);let h=Math.max(Math.ceil(s[2])-l,1),d=Math.max(Math.ceil(s[3])-c,1),u=1,p=1;if(h>o){u=h/o;h=o}if(d>o){p=d/o;d=o}this.current.startNewPathAndClipBox([0,0,h,d]);let f="groupAt"+this.groupLevel;t.smask&&(f+="_smask_"+this.smaskCounter++%2);const g=this.cachedCanvases.getCanvas(f,h,d),m=g.context;m.scale(1/u,1/p);m.translate(-l,-c);m.transform(...n);if(t.smask)this.smaskStack.push({canvas:g.canvas,context:m,offsetX:l,offsetY:c,scaleX:u,scaleY:p,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(l,c);e.scale(u,p);e.save()}copyCtxState(e,m);this.ctx=m;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,n=this.groupStack.pop();this.ctx=n;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=(0,r.getCurrentTransform)(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const n=i.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(n)}}beginAnnotation(t,e,n,s,a){this.#ce();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(Array.isArray(e)&&4===e.length){const s=e[2]-e[0],o=e[3]-e[1];if(a&&this.annotationCanvasMap){(n=n.slice())[4]-=e[0];n[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=s;e[3]=o;const[a,l]=i.Util.singularValueDecompose2dScale((0,r.getCurrentTransform)(this.ctx)),{viewportScale:c}=this,h=Math.ceil(s*this.outputScaleX*c),d=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(h,d);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u);this.annotationCanvas.savedCtx=this.ctx;this.ctx=p;this.ctx.save();this.ctx.setTransform(a,0,0,-l,0,o*l);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.ctx.rect(e[0],e[1],s,o);this.ctx.clip();this.endPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...n);this.transform(...s)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#he();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const n=this.ctx,i=this.processingType3;if(i){void 0===i.compiled&&(i.compiled=function compileType3Glyph(t){const{width:e,height:n}=t;if(e>1e3||n>1e3)return null;const i=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),r=e+1;let s,a,o,l=new Uint8Array(r*(n+1));const c=e+7&-8;let h=new Uint8Array(c*n),d=0;for(const e of t.data){let t=128;for(;t>0;){h[d++]=e&t?0:255;t>>=1}}let u=0;d=0;if(0!==h[d]){l[0]=1;++u}for(a=1;a<e;a++){if(h[d]!==h[d+1]){l[a]=h[d]?2:1;++u}d++}if(0!==h[d]){l[a]=2;++u}for(s=1;s<n;s++){d=s*c;o=s*r;if(h[d-c]!==h[d]){l[o]=h[d]?1:8;++u}let t=(h[d]?4:0)+(h[d-c]?8:0);for(a=1;a<e;a++){t=(t>>2)+(h[d+1]?4:0)+(h[d-c+1]?8:0);if(i[t]){l[o+a]=i[t];++u}d++}if(h[d-c]!==h[d]){l[o+a]=h[d]?2:4;++u}if(u>1e3)return null}d=c*(n-1);o=s*r;if(0!==h[d]){l[o]=8;++u}for(a=1;a<e;a++){if(h[d]!==h[d+1]){l[o+a]=h[d]?4:8;++u}d++}if(0!==h[d]){l[o+a]=4;++u}if(u>1e3)return null;const p=new Int32Array([0,r,-1,0,-r,0,0,0,1]),f=new Path2D;for(s=0;u&&s<=n;s++){let t=s*r;const n=t+e;for(;t<n&&!l[t];)t++;if(t===n)continue;f.moveTo(t%r,s);const i=t;let a=l[t];do{const e=p[a];do{t+=e}while(!l[t]);const n=l[t];if(5!==n&&10!==n){a=n;l[t]=0}else{a=n&51*a>>4;l[t]&=a>>2|a<<2}f.lineTo(t%r,t/r|0);l[t]||--u}while(i!==t);--s}h=null;l=null;return function(t){t.save();t.scale(1/e,-1/n);t.translate(0,-n);t.fill(f);t.beginPath();t.restore()}}(t));if(i.compiled){i.compiled(n);return}}const r=this._createMaskCanvas(t),s=r.canvas;n.save();n.setTransform(1,0,0,1,0,0);n.drawImage(s,r.offsetX,r.offsetY);n.restore();this.compose()}paintImageMaskXObjectRepeat(t,e){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;if(!this.contentVisible)return;t=this.getObject(t.data,t);const l=this.ctx;l.save();const c=(0,r.getCurrentTransform)(l);l.transform(e,n,s,a,0,0);const h=this._createMaskCanvas(t);l.setTransform(1,0,0,1,h.offsetX-c[4],h.offsetY-c[5]);for(let t=0,r=o.length;t<r;t+=2){const r=i.Util.transform(c,[e,n,s,a,o[t],o[t+1]]),[d,u]=i.Util.applyTransform([0,0],r);l.drawImage(h.canvas,d,u)}l.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,n=this.current.fillColor,i=this.current.patternFill;for(const a of t){const{data:t,width:o,height:l,transform:c}=a,h=this.cachedCanvases.getCanvas("maskCanvas",o,l),d=h.context;d.save();putBinaryImageMask(d,this.getObject(t,a));d.globalCompositeOperation="source-in";d.fillStyle=i?n.getPattern(d,this,(0,r.getCurrentTransformInverse)(e),s.PathType.FILL):n;d.fillRect(0,0,o,l);d.restore();e.save();e.transform(...c);e.scale(1,-1);drawImageAtIntegerCoords(e,h.canvas,0,0,o,l,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,i.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,n,r){if(!this.contentVisible)return;const s=this.getObject(t);if(!s){(0,i.warn)("Dependent image isn't ready yet");return}const a=s.width,o=s.height,l=[];for(let t=0,i=r.length;t<i;t+=2)l.push({transform:[e,0,0,n,r[t],r[t+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(s,l)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:n,height:i}=t,r=this.cachedCanvases.getCanvas("inlineImage",n,i),s=r.context;s.filter=this.current.transferMaps;s.drawImage(e,0,0);s.filter="none";return r.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,n=t.height,s=this.ctx;this.save();if(!i.isNodeJS){const{filter:t}=s;"none"!==t&&""!==t&&(s.filter="none")}s.scale(1/e,-1/n);let a;if(t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const i=this.cachedCanvases.getCanvas("inlineImage",e,n).context;putBinaryImageData(i,t);a=this.applyTransferMapsToCanvas(i)}const o=this._scaleImage(a,(0,r.getCurrentTransformInverse)(s));s.imageSmoothingEnabled=getImageSmoothingEnabled((0,r.getCurrentTransform)(s),t.interpolate);drawImageAtIntegerCoords(s,o.img,0,0,o.paintWidth,o.paintHeight,0,-n,e,n);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const n=this.ctx;let i;if(t.bitmap)i=t.bitmap;else{const e=t.width,n=t.height,r=this.cachedCanvases.getCanvas("inlineImage",e,n).context;putBinaryImageData(r,t);i=this.applyTransferMapsToCanvas(r)}for(const t of e){n.save();n.transform(...t.transform);n.scale(1,-1);drawImageAtIntegerCoords(n,i,t.x,t.y,t.w,t.h,0,-1,1,1);n.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const n=this.ctx;if(this.pendingClip){e||(this.pendingClip===u?n.clip("evenodd"):n.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);n.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=(0,r.getCurrentTransform)(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),n=Math.hypot(t[0],t[2]),i=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(n,i)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:n,c:i,d:r}=this.ctx.getTransform();let s,a;if(0===n&&0===i){const n=Math.abs(e),i=Math.abs(r);if(n===i)if(0===t)s=a=1/n;else{const e=n*t;s=a=e<1?1/e:1}else if(0===t){s=1/n;a=1/i}else{const e=n*t,r=i*t;s=e<1?1/e:1;a=r<1?1/r:1}}else{const o=Math.abs(e*r-n*i),l=Math.hypot(e,n),c=Math.hypot(i,r);if(0===t){s=c/o;a=l/o}else{const e=t*o;s=c>e?c/e:1;a=l>e?l/e:1}}this._cachedScaleForStroking[0]=s;this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:n}=this.current,[i,r]=this.getScaleForStroking();e.lineWidth=n||1;if(1===i&&1===r){e.stroke();return}const s=e.getLineDash();t&&e.save();e.scale(i,r);if(s.length>0){const t=Math.max(i,r);e.setLineDash(s.map((e=>e/t)));e.lineDashOffset/=t}e.stroke();t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}e.CanvasGraphics=CanvasGraphics;for(const t in i.OPS)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[i.OPS[t]]=CanvasGraphics.prototype[t])},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.TilingPattern=e.PathType=void 0;e.getShadingPattern=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)};n(2);var i=n(1),r=n(168);const s={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};e.PathType=s;function applyBoundingBox(t,e){if(!e)return;const n=e[2]-e[0],i=e[3]-e[1],r=new Path2D;r.rect(e[0],e[1],n,i);t.clip(r)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&(0,i.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,i.unreachable)("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,n,a){let o;if(a===s.STROKE||a===s.FILL){const s=e.current.getClippedPathBoundingBox(a,(0,r.getCurrentTransform)(t))||[0,0,0,0],l=Math.ceil(s[2]-s[0])||1,c=Math.ceil(s[3]-s[1])||1,h=e.cachedCanvases.getCanvas("pattern",l,c,!0),d=h.context;d.clearRect(0,0,d.canvas.width,d.canvas.height);d.beginPath();d.rect(0,0,d.canvas.width,d.canvas.height);d.translate(-s[0],-s[1]);n=i.Util.transform(n,[1,0,0,1,s[0],s[1]]);d.transform(...e.baseTransform);this.matrix&&d.transform(...this.matrix);applyBoundingBox(d,this._bbox);d.fillStyle=this._createGradient(d);d.fill();o=t.createPattern(h.canvas,"no-repeat");const u=new DOMMatrix(n);o.setTransform(u)}else{applyBoundingBox(t,this._bbox);o=this._createGradient(t)}return o}}function drawTriangle(t,e,n,i,r,s,a,o){const l=e.coords,c=e.colors,h=t.data,d=4*t.width;let u;if(l[n+1]>l[i+1]){u=n;n=i;i=u;u=s;s=a;a=u}if(l[i+1]>l[r+1]){u=i;i=r;r=u;u=a;a=o;o=u}if(l[n+1]>l[i+1]){u=n;n=i;i=u;u=s;s=a;a=u}const p=(l[n]+e.offsetX)*e.scaleX,f=(l[n+1]+e.offsetY)*e.scaleY,g=(l[i]+e.offsetX)*e.scaleX,m=(l[i+1]+e.offsetY)*e.scaleY,b=(l[r]+e.offsetX)*e.scaleX,v=(l[r+1]+e.offsetY)*e.scaleY;if(f>=v)return;const y=c[s],_=c[s+1],A=c[s+2],S=c[a],E=c[a+1],x=c[a+2],w=c[o],C=c[o+1],T=c[o+2],P=Math.round(f),k=Math.round(v);let M,R,D,I,O,L,N,B;for(let t=P;t<=k;t++){if(t<m){const e=t<f?0:(f-t)/(f-m);M=p-(p-g)*e;R=y-(y-S)*e;D=_-(_-E)*e;I=A-(A-x)*e}else{let e;e=t>v?1:m===v?0:(m-t)/(m-v);M=g-(g-b)*e;R=S-(S-w)*e;D=E-(E-C)*e;I=x-(x-T)*e}let e;e=t<f?0:t>v?1:(f-t)/(f-v);O=p-(p-b)*e;L=y-(y-w)*e;N=_-(_-C)*e;B=A-(A-T)*e;const n=Math.round(Math.min(M,O)),i=Math.round(Math.max(M,O));let r=d*t+4*n;for(let t=n;t<=i;t++){e=(M-t)/(M-O);e<0?e=0:e>1&&(e=1);h[r++]=R-(R-L)*e|0;h[r++]=D-(D-N)*e|0;h[r++]=I-(I-B)*e|0;h[r++]=255}}}function drawFigure(t,e,n){const i=e.coords,r=e.colors;let s,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(i.length/o)-1,c=o-1;for(s=0;s<l;s++){let e=s*o;for(let s=0;s<c;s++,e++){drawTriangle(t,n,i[e],i[e+1],i[e+o],r[e],r[e+1],r[e+o]);drawTriangle(t,n,i[e+o+1],i[e+1],i[e+o],r[e+o+1],r[e+1],r[e+o])}}break;case"triangles":for(s=0,a=i.length;s<a;s+=3)drawTriangle(t,n,i[s],i[s+1],i[s+2],r[s],r[s+1],r[s+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,n){const i=Math.floor(this._bounds[0]),r=Math.floor(this._bounds[1]),s=Math.ceil(this._bounds[2])-i,a=Math.ceil(this._bounds[3])-r,o=Math.min(Math.ceil(Math.abs(s*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),c=s/o,h=a/l,d={coords:this._coords,colors:this._colors,offsetX:-i,offsetY:-r,scaleX:1/c,scaleY:1/h},u=o+4,p=l+4,f=n.getCanvas("mesh",u,p,!1),g=f.context,m=g.createImageData(o,l);if(e){const t=m.data;for(let n=0,i=t.length;n<i;n+=4){t[n]=e[0];t[n+1]=e[1];t[n+2]=e[2];t[n+3]=255}}for(const t of this._figures)drawFigure(m,t,d);g.putImageData(m,2,2);return{canvas:f.canvas,offsetX:i-2*c,offsetY:r-2*h,scaleX:c,scaleY:h}}getPattern(t,e,n,a){applyBoundingBox(t,this._bbox);let o;if(a===s.SHADING)o=i.Util.singularValueDecompose2dScale((0,r.getCurrentTransform)(t));else{o=i.Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=i.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*t[0],o[1]*t[1]]}}const l=this._createMeshCanvas(o,a===s.SHADING?null:this._background,e.cachedCanvases);if(a!==s.SHADING){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(l.offsetX,l.offsetY);t.scale(l.scaleX,l.scaleY);return t.createPattern(l.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const a=1,o=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,n,i,r){this.operatorList=t[2];this.matrix=t[3]||[1,0,0,1,0,0];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=n;this.canvasGraphicsFactory=i;this.baseTransform=r}createPatternCanvas(t){const e=this.operatorList,n=this.bbox,s=this.xstep,a=this.ystep,o=this.paintType,l=this.tilingType,c=this.color,h=this.canvasGraphicsFactory;(0,i.info)("TilingType: "+l);const d=n[0],u=n[1],p=n[2],f=n[3],g=i.Util.singularValueDecompose2dScale(this.matrix),m=i.Util.singularValueDecompose2dScale(this.baseTransform),b=[g[0]*m[0],g[1]*m[1]],v=this.getSizeAndScale(s,this.ctx.canvas.width,b[0]),y=this.getSizeAndScale(a,this.ctx.canvas.height,b[1]),_=t.cachedCanvases.getCanvas("pattern",v.size,y.size,!0),A=_.context,S=h.createCanvasGraphics(A);S.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(S,o,c);let E=d,x=u,w=p,C=f;if(d<0){E=0;w+=Math.abs(d)}if(u<0){x=0;C+=Math.abs(u)}A.translate(-v.scale*E,-y.scale*x);S.transform(v.scale,0,0,y.scale,0,0);A.save();this.clipBbox(S,E,x,w,C);S.baseTransform=(0,r.getCurrentTransform)(S.ctx);S.executeOperatorList(e);S.endDrawing();return{canvas:_.canvas,scaleX:v.scale,scaleY:y.scale,offsetX:E,offsetY:x}}getSizeAndScale(t,e,n){t=Math.abs(t);const i=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let r=Math.ceil(t*n);r>=i?r=i:n=r/t;return{scale:n,size:r}}clipBbox(t,e,n,i,s){const a=i-e,o=s-n;t.ctx.rect(e,n,a,o);t.current.updateRectMinMax((0,r.getCurrentTransform)(t.ctx),[e,n,i,s]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,n){const r=t.ctx,s=t.current;switch(e){case a:const t=this.ctx;r.fillStyle=t.fillStyle;r.strokeStyle=t.strokeStyle;s.fillColor=t.fillStyle;s.strokeColor=t.strokeStyle;break;case o:const l=i.Util.makeHexColor(n[0],n[1],n[2]);r.fillStyle=l;r.strokeStyle=l;s.fillColor=l;s.strokeColor=l;break;default:throw new i.FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,n,r){let a=n;if(r!==s.SHADING){a=i.Util.transform(a,e.baseTransform);this.matrix&&(a=i.Util.transform(a,this.matrix))}const o=this.createPatternCanvas(e);let l=new DOMMatrix(a);l=l.translate(o.offsetX,o.offsetY);l=l.scale(1/o.scaleX,1/o.scaleY);const c=t.createPattern(o.canvas,"repeat");c.setTransform(l);return c}}e.TilingPattern=TilingPattern},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.convertBlackAndWhiteToRGBA=convertBlackAndWhiteToRGBA;e.convertToRGBA=function convertToRGBA(t){switch(t.kind){case i.ImageKind.GRAYSCALE_1BPP:return convertBlackAndWhiteToRGBA(t);case i.ImageKind.RGB_24BPP:return function convertRGBToRGBA(t){let{src:e,srcPos:n=0,dest:r,destPos:s=0,width:a,height:o}=t,l=0;const c=e.length>>2,h=new Uint32Array(e.buffer,n,c);if(i.FeatureTest.isLittleEndian){for(;l<c-2;l+=3,s+=4){const t=h[l],e=h[l+1],n=h[l+2];r[s]=4278190080|t;r[s+1]=t>>>24|e<<8|4278190080;r[s+2]=e>>>16|n<<16|4278190080;r[s+3]=n>>>8|4278190080}for(let t=4*l,n=e.length;t<n;t+=3)r[s++]=e[t]|e[t+1]<<8|e[t+2]<<16|4278190080}else{for(;l<c-2;l+=3,s+=4){const t=h[l],e=h[l+1],n=h[l+2];r[s]=255|t;r[s+1]=t<<24|e>>>8|255;r[s+2]=e<<16|n>>>16|255;r[s+3]=n<<8|255}for(let t=4*l,n=e.length;t<n;t+=3)r[s++]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|255}return{srcPos:n,destPos:s}}(t)}return null};e.grayToRGBA=function grayToRGBA(t,e){if(i.FeatureTest.isLittleEndian)for(let n=0,i=t.length;n<i;n++)e[n]=65793*t[n]|4278190080;else for(let n=0,i=t.length;n<i;n++)e[n]=16843008*t[n]|255};n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var i=n(1);function convertBlackAndWhiteToRGBA(t){let{src:e,srcPos:n=0,dest:r,width:s,height:a,nonBlackColor:o=4294967295,inverseDecode:l=!1}=t;const c=i.FeatureTest.isLittleEndian?4278190080:255,[h,d]=l?[o,c]:[c,o],u=s>>3,p=7&s,f=e.length;r=new Uint32Array(r.buffer);let g=0;for(let t=0;t<a;t++){for(const t=n+u;n<t;n++){const t=n<f?e[n]:255;r[g++]=128&t?d:h;r[g++]=64&t?d:h;r[g++]=32&t?d:h;r[g++]=16&t?d:h;r[g++]=8&t?d:h;r[g++]=4&t?d:h;r[g++]=2&t?d:h;r[g++]=1&t?d:h}if(0===p)continue;const t=n<f?e[n++]:255;for(let e=0;e<p;e++)r[g++]=t&1<<7-e?d:h}return{srcPos:n,destPos:g}}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.GlobalWorkerOptions=void 0;const n=Object.create(null);e.GlobalWorkerOptions=n;n.workerPort=null;n.workerSrc=""},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.MessageHandler=void 0;n(2);var i=n(1);const r=1,s=2,a=1,o=2,l=3,c=4,h=5,d=6,u=7,p=8;function wrapReason(t){t instanceof Error||"object"==typeof t&&null!==t||(0,i.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new i.AbortException(t.message);case"MissingPDFException":return new i.MissingPDFException(t.message);case"PasswordException":return new i.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new i.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new i.UnknownErrorException(t.message,t.details);default:return new i.UnknownErrorException(t.message,t.toString())}}e.MessageHandler=class MessageHandler{constructor(t,e,n){this.sourceName=t;this.targetName=e;this.comObj=n;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this.#de(e);return}if(e.callback){const t=e.callbackId,n=this.callbackCapabilities[t];if(!n)throw new Error(`Cannot resolve callback ${t}`);delete this.callbackCapabilities[t];if(e.callback===r)n.resolve(e.data);else{if(e.callback!==s)throw new Error("Unexpected callback case");n.reject(wrapReason(e.reason))}return}const i=this.actionHandler[e.action];if(!i)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,a=e.sourceName;new Promise((function(t){t(i(e.data))})).then((function(i){n.postMessage({sourceName:t,targetName:a,callback:r,callbackId:e.callbackId,data:i})}),(function(i){n.postMessage({sourceName:t,targetName:a,callback:s,callbackId:e.callbackId,reason:wrapReason(i)})}))}else e.streamId?this.#ue(e):i(e.data)};n.addEventListener("message",this._onComObjOnMessage)}on(t,e){const n=this.actionHandler;if(n[t])throw new Error(`There is already an actionName called "${t}"`);n[t]=e}send(t,e,n){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},n)}sendWithPromise(t,e,n){const r=this.callbackId++,s=new i.PromiseCapability;this.callbackCapabilities[r]=s;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:r,data:e},n)}catch(t){s.reject(t)}return s.promise}sendWithStream(t,e,n,r){const s=this.streamId++,o=this.sourceName,l=this.targetName,c=this.comObj;return new ReadableStream({start:n=>{const a=new i.PromiseCapability;this.streamControllers[s]={controller:n,startCall:a,pullCall:null,cancelCall:null,isClosed:!1};c.postMessage({sourceName:o,targetName:l,action:t,streamId:s,data:e,desiredSize:n.desiredSize},r);return a.promise},pull:t=>{const e=new i.PromiseCapability;this.streamControllers[s].pullCall=e;c.postMessage({sourceName:o,targetName:l,stream:d,streamId:s,desiredSize:t.desiredSize});return e.promise},cancel:t=>{(0,i.assert)(t instanceof Error,"cancel must have a valid reason");const e=new i.PromiseCapability;this.streamControllers[s].cancelCall=e;this.streamControllers[s].isClosed=!0;c.postMessage({sourceName:o,targetName:l,stream:a,streamId:s,reason:wrapReason(t)});return e.promise}},n)}#ue(t){const e=t.streamId,n=this.sourceName,r=t.sourceName,s=this.comObj,a=this,o=this.actionHandler[t.action],d={enqueue(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,o=arguments.length>2?arguments[2]:void 0;if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=a;if(l>0&&this.desiredSize<=0){this.sinkCapability=new i.PromiseCapability;this.ready=this.sinkCapability.promise}s.postMessage({sourceName:n,targetName:r,stream:c,streamId:e,chunk:t},o)},close(){if(!this.isCancelled){this.isCancelled=!0;s.postMessage({sourceName:n,targetName:r,stream:l,streamId:e});delete a.streamSinks[e]}},error(t){(0,i.assert)(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;s.postMessage({sourceName:n,targetName:r,stream:h,streamId:e,reason:wrapReason(t)})}},sinkCapability:new i.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};d.sinkCapability.resolve();d.ready=d.sinkCapability.promise;this.streamSinks[e]=d;new Promise((function(e){e(o(t.data,d))})).then((function(){s.postMessage({sourceName:n,targetName:r,stream:p,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:n,targetName:r,stream:p,streamId:e,reason:wrapReason(t)})}))}#de(t){const e=t.streamId,n=this.sourceName,r=t.sourceName,s=this.comObj,f=this.streamControllers[e],g=this.streamSinks[e];switch(t.stream){case p:t.success?f.startCall.resolve():f.startCall.reject(wrapReason(t.reason));break;case u:t.success?f.pullCall.resolve():f.pullCall.reject(wrapReason(t.reason));break;case d:if(!g){s.postMessage({sourceName:n,targetName:r,stream:u,streamId:e,success:!0});break}g.desiredSize<=0&&t.desiredSize>0&&g.sinkCapability.resolve();g.desiredSize=t.desiredSize;new Promise((function(t){t(g.onPull?.())})).then((function(){s.postMessage({sourceName:n,targetName:r,stream:u,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:n,targetName:r,stream:u,streamId:e,reason:wrapReason(t)})}));break;case c:(0,i.assert)(f,"enqueue should have stream controller");if(f.isClosed)break;f.controller.enqueue(t.chunk);break;case l:(0,i.assert)(f,"close should have stream controller");if(f.isClosed)break;f.isClosed=!0;f.controller.close();this.#pe(f,e);break;case h:(0,i.assert)(f,"error should have stream controller");f.controller.error(wrapReason(t.reason));this.#pe(f,e);break;case o:t.success?f.cancelCall.resolve():f.cancelCall.reject(wrapReason(t.reason));this.#pe(f,e);break;case a:if(!g)break;new Promise((function(e){e(g.onCancel?.(wrapReason(t.reason)))})).then((function(){s.postMessage({sourceName:n,targetName:r,stream:o,streamId:e,success:!0})}),(function(t){s.postMessage({sourceName:n,targetName:r,stream:o,streamId:e,reason:wrapReason(t)})}));g.sinkCapability.reject(wrapReason(t.reason));g.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#pe(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.Metadata=void 0;var i=n(1);e.Metadata=class Metadata{#fe;#ge;constructor(t){let{parsedData:e,rawData:n}=t;this.#fe=e;this.#ge=n}getRaw(){return this.#ge}get(t){return this.#fe.get(t)??null}getAll(){return(0,i.objectFromMap)(this.#fe)}has(t){return this.#fe.has(t)}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.OptionalContentConfig=void 0;var i=n(1),r=n(170);const s=Symbol("INTERNAL");class OptionalContentGroup{#me=!0;constructor(t,e){this.name=t;this.intent=e}get visible(){return this.#me}_setVisible(t,e){t!==s&&(0,i.unreachable)("Internal method `_setVisible` called.");this.#me=e}}e.OptionalContentConfig=class OptionalContentConfig{#be=null;#ve=new Map;#ye=null;#_e=null;constructor(t){this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#_e=t.order;for(const e of t.groups)this.#ve.set(e.id,new OptionalContentGroup(e.name,e.intent));if("OFF"===t.baseState)for(const t of this.#ve.values())t._setVisible(s,!1);for(const e of t.on)this.#ve.get(e)._setVisible(s,!0);for(const e of t.off)this.#ve.get(e)._setVisible(s,!1);this.#ye=this.getHash()}}#Ae(t){const e=t.length;if(e<2)return!0;const n=t[0];for(let r=1;r<e;r++){const e=t[r];let s;if(Array.isArray(e))s=this.#Ae(e);else{if(!this.#ve.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}s=this.#ve.get(e).visible}switch(n){case"And":if(!s)return!1;break;case"Or":if(s)return!0;break;case"Not":return!s;default:return!0}}return"And"===n}isVisible(t){if(0===this.#ve.size)return!0;if(!t){(0,i.warn)("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#ve.has(t.id)){(0,i.warn)(`Optional content group not found: ${t.id}`);return!0}return this.#ve.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#Ae(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#ve.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(this.#ve.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#ve.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#ve.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#ve.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#ve.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#ve.has(e)){(0,i.warn)(`Optional content group not found: ${e}`);return!0}if(this.#ve.get(e).visible)return!1}return!0}(0,i.warn)(`Unknown optional content policy ${t.policy}.`);return!0}(0,i.warn)(`Unknown group type ${t.type}.`);return!0}setVisibility(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.#ve.has(t)){this.#ve.get(t)._setVisible(s,!!e);this.#be=null}else(0,i.warn)(`Optional content group not found: ${t}`)}get hasInitialVisibility(){return null===this.#ye||this.getHash()===this.#ye}getOrder(){return this.#ve.size?this.#_e?this.#_e.slice():[...this.#ve.keys()]:null}getGroups(){return this.#ve.size>0?(0,i.objectFromMap)(this.#ve):null}getGroup(t){return this.#ve.get(t)||null}getHash(){if(null!==this.#be)return this.#be;const t=new r.MurmurHash3_64;for(const[e,n]of this.#ve)t.update(`${e}:${n.visible}`);return this.#be=t.hexdigest()}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFDataTransportStream=void 0;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);var i=n(1),r=n(168);e.PDFDataTransportStream=class PDFDataTransportStream{constructor(t,e){let{length:n,initialData:r,progressiveDone:s=!1,contentDispositionFilename:a=null,disableRange:o=!1,disableStream:l=!1}=t;(0,i.assert)(e,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');this._queuedChunks=[];this._progressiveDone=s;this._contentDispositionFilename=a;if(r?.length>0){const t=r instanceof Uint8Array&&r.byteLength===r.buffer.byteLength?r.buffer:new Uint8Array(r).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=e;this._isStreamingSupported=!l;this._isRangeSupported=!o;this._contentLength=n;this._fullRequestReader=null;this._rangeReaders=[];this._pdfDataRangeTransport.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));this._pdfDataRangeTransport.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));this._pdfDataRangeTransport.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));this._pdfDataRangeTransport.transportReady()}_onReceiveData(t){let{begin:e,chunk:n}=t;const r=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;if(void 0===e)this._fullRequestReader?this._fullRequestReader._enqueue(r):this._queuedChunks.push(r);else{const t=this._rangeReaders.some((function(t){if(t._begin!==e)return!1;t._enqueue(r);return!0}));(0,i.assert)(t,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const n=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(n);return n}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}};class PDFDataTransportStreamReader{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2],i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this._stream=t;this._done=n||!1;this._filename=(0,r.isPdfFile)(i)?i:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=new i.PromiseCapability;this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,n){this._stream=t;this._begin=e;this._end=n;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new i.PromiseCapability;this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFFetchStream=void 0;n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(89);var i=n(1),r=n(182);function createFetchOptions(t,e,n){return{method:"GET",headers:t,signal:n.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function createHeaders(t){const e=new Headers;for(const n in t){const i=t[n];void 0!==i&&e.append(n,i)}return e}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;(0,i.warn)(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}e.PDFFetchStream=class PDFFetchStream{constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const n=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(n);return n}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=new i.PromiseCapability;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const n=e.url;fetch(n,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,r.validateResponseStatus)(t.status))throw(0,r.createResponseStatusError)(t.status,n);this._reader=t.body.getReader();this._headersCapability.resolve();const getResponseHeader=e=>t.headers.get(e),{allowRangeRequests:e,suggestedLength:s}=(0,r.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=e;this._contentLength=s||this._contentLength;this._filename=(0,r.extractFilenameFromHeader)(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new i.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,n){this._stream=t;this._reader=null;this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1;this._readCapability=new i.PromiseCapability;this._isStreamingSupported=!s.disableStream;this._abortController=new AbortController;this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${e}-${n-1}`);const a=s.url;fetch(a,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,r.validateResponseStatus)(t.status))throw(0,r.createResponseStatusError)(t.status,a);this._readCapability.resolve();this._reader=t.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.createResponseStatusError=function createResponseStatusError(t,e){if(404===t||0===t&&e.startsWith("file:"))return new i.MissingPDFException('Missing PDF "'+e+'".');return new i.UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)};e.extractFilenameFromHeader=function extractFilenameFromHeader(t){const e=t("Content-Disposition");if(e){let t=(0,r.getFilenameFromContentDispositionHeader)(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if((0,s.isPdfFile)(t))return t}return null};e.validateRangeRequestCapabilities=function validateRangeRequestCapabilities(t){let{getResponseHeader:e,isHttp:n,rangeChunkSize:i,disableRange:r}=t;const s={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(e("Content-Length"),10);if(!Number.isInteger(a))return s;s.suggestedLength=a;if(a<=2*i)return s;if(r||!n)return s;if("bytes"!==e("Accept-Ranges"))return s;if("identity"!==(e("Content-Encoding")||"identity"))return s;s.allowRangeRequests=!0;return s};e.validateResponseStatus=function validateResponseStatus(t){return 200===t||206===t};var i=n(1),r=n(183),s=n(168)},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.getFilenameFromContentDispositionHeader=function getFilenameFromContentDispositionHeader(t){let e=!0,n=toParamRegExp("filename\\*","i").exec(t);if(n){n=n[1];let t=rfc2616unquote(n);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}n=function rfc2231getparam(t){const e=[];let n;const i=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(n=i.exec(t));){let[,t,i,r]=n;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[i,r]}const r=[];for(let t=0;t<e.length&&t in e;++t){let[n,i]=e[t];i=rfc2616unquote(i);if(n){i=unescape(i);0===t&&(i=rfc5987decode(i))}r.push(i)}return r.join("")}(t);if(n){return fixupEncoding(rfc2047decode(n))}n=toParamRegExp("filename","i").exec(t);if(n){n=n[1];let t=rfc2616unquote(n);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,n){if(t){if(!/^[\x00-\xFF]+$/.test(n))return n;try{const r=new TextDecoder(t,{fatal:!0}),s=(0,i.stringToBytes)(n);n=r.decode(s);e=!1}catch{}}return n}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const n=e[t].indexOf('"');if(-1!==n){e[t]=e[t].slice(0,n);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");if(-1===e)return t;return textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,n,i){if("q"===n||"Q"===n)return textdecode(e,i=(i=i.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{i=atob(i)}catch{}return textdecode(e,i)}))}return""};n(89);n(149);var i=n(1)},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFNetworkStream=void 0;n(89);var i=n(1),r=n(182);class NetworkManager{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=t;this.isHttp=/^https?:/i.test(t);this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null);this.withCredentials=e.withCredentials||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(t,e,n){const i={begin:t,end:e};for(const t in n)i[t]=n[t];return this.request(i)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,n=this.currXhrId++,i=this.pendingRequests[n]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const n=this.httpHeaders[t];void 0!==n&&e.setRequestHeader(t,n)}if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);i.expectedStatus=206}else i.expectedStatus=200;e.responseType="arraybuffer";t.onError&&(e.onerror=function(n){t.onError(e.status)});e.onreadystatechange=this.onStateChange.bind(this,n);e.onprogress=this.onProgress.bind(this,n);i.onHeadersReceived=t.onHeadersReceived;i.onDone=t.onDone;i.onError=t.onError;i.onProgress=t.onProgress;e.send(null);return n}onProgress(t,e){const n=this.pendingRequests[t];n&&n.onProgress?.(e)}onStateChange(t,e){const n=this.pendingRequests[t];if(!n)return;const r=n.xhr;if(r.readyState>=2&&n.onHeadersReceived){n.onHeadersReceived();delete n.onHeadersReceived}if(4!==r.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===r.status&&this.isHttp){n.onError?.(r.status);return}const s=r.status||200;if(!(200===s&&206===n.expectedStatus)&&s!==n.expectedStatus){n.onError?.(r.status);return}const a=function getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:(0,i.stringToBytes)(e).buffer}(r);if(206===s){const t=r.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);n.onDone({begin:parseInt(e[1],10),chunk:a})}else a?n.onDone({begin:0,chunk:a}):n.onError?.(r.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}e.PDFNetworkStream=class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const n=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);n.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(n);return n}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;const n={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url;this._fullRequestId=t.requestFull(n);this._headersReceivedCapability=new i.PromiseCapability;this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),getResponseHeader=t=>e.getResponseHeader(t),{allowRangeRequests:n,suggestedLength:i}=(0,r.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0);this._contentLength=i||this._contentLength;this._filename=(0,r.extractFilenameFromHeader)(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(t);this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,r.createResponseStatusError)(t,this._url);this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=new i.PromiseCapability;this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,n){this._manager=t;const i={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._requestId=t.requestRange(e,n,i);this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError=(0,r.createResponseStatusError)(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new i.PromiseCapability;this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFNodeStream=void 0;n(89);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);var i=n(1),r=n(182);const s=/^file:\/\/\/[a-zA-Z]:\//;e.PDFNodeStream=class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrl(t){const e=require("url"),n=e.parse(t);if("file:"===n.protocol||n.host)return n;if(/^[a-z]:[/\\]/i.test(t))return e.parse(`file:///${t}`);n.host||(n.protocol="file:");return n}(t.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,i.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const n=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,t,e):new PDFNodeStreamRangeReader(this,t,e);this._rangeRequestReaders.push(n);return n}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class BaseFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=new i.PromiseCapability;this._headersCapability=new i.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=new i.PromiseCapability;return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new i.AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=new i.PromiseCapability;const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=new i.PromiseCapability;return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(t){super(t);const handleResponse=e=>{if(404===e.statusCode){const t=new i.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t;this._headersCapability.reject(t);return}this._headersCapability.resolve();this._setReadableStream(e);const getResponseHeader=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:n,suggestedLength:s}=(0,r.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=n;this._contentLength=s||this._contentLength;this._filename=(0,r.extractFilenameFromHeader)(getResponseHeader)};this._request=null;if("http:"===this._url.protocol){const e=require("http");this._request=e.request(createRequestOptions(this._url,t.httpHeaders),handleResponse)}else{const e=require("https");this._request=e.request(createRequestOptions(this._url,t.httpHeaders),handleResponse)}this._request.on("error",(t=>{this._storedError=t;this._headersCapability.reject(t)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(t,e,n){super(t);this._httpHeaders={};for(const e in t.httpHeaders){const n=t.httpHeaders[e];void 0!==n&&(this._httpHeaders[e]=n)}this._httpHeaders.Range=`bytes=${e}-${n-1}`;const handleResponse=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new i.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};this._request=null;if("http:"===this._url.protocol){const t=require("http");this._request=t.request(createRequestOptions(this._url,this._httpHeaders),handleResponse)}else{const t=require("https");this._request=t.request(createRequestOptions(this._url,this._httpHeaders),handleResponse)}this._request.on("error",(t=>{this._storedError=t}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(t){super(t);let e=decodeURIComponent(this._url.path);s.test(this._url.href)&&(e=e.replace(/^\//,""));const n=require("fs");n.lstat(e,((t,r)=>{if(t){"ENOENT"===t.code&&(t=new i.MissingPDFException(`Missing PDF "${e}".`));this._storedError=t;this._headersCapability.reject(t)}else{this._contentLength=r.size;this._setReadableStream(n.createReadStream(e));this._headersCapability.resolve()}}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(t,e,n){super(t);let i=decodeURIComponent(this._url.path);s.test(this._url.href)&&(i=i.replace(/^\//,""));const r=require("fs");this._setReadableStream(r.createReadStream(i,{start:e,end:n-1}))}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.SVGGraphics=void 0;n(84);n(86);n(87);n(93);n(101);n(102);n(105);n(107);n(109);n(113);n(116);n(123);n(2);n(89);n(187);var i=n(168),r=n(1);const s="normal",a="normal",o="#000000",l=["butt","round","square"],c=["miter","round","bevel"],createObjectURL=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(URL.createObjectURL&&"undefined"!=typeof Blob&&!n)return URL.createObjectURL(new Blob([t],{type:e}));const i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let r=`data:${e};base64,`;for(let e=0,n=t.length;e<n;e+=3){const s=255&t[e],a=255&t[e+1],o=255&t[e+2];r+=i[s>>2]+i[(3&s)<<4|a>>4]+i[e+1<n?(15&a)<<2|o>>6:64]+i[e+2<n?63&o:64]}return r},h=function(){const t=new Uint8Array([137,80,78,71,13,10,26,10]),e=new Int32Array(256);for(let t=0;t<256;t++){let n=t;for(let t=0;t<8;t++)n=1&n?3988292384^n>>1&2147483647:n>>1&2147483647;e[t]=n}function writePngChunk(t,n,i,r){let s=r;const a=n.length;i[s]=a>>24&255;i[s+1]=a>>16&255;i[s+2]=a>>8&255;i[s+3]=255&a;s+=4;i[s]=255&t.charCodeAt(0);i[s+1]=255&t.charCodeAt(1);i[s+2]=255&t.charCodeAt(2);i[s+3]=255&t.charCodeAt(3);s+=4;i.set(n,s);s+=n.length;const o=function crc32(t,n,i){let r=-1;for(let s=n;s<i;s++){const n=255&(r^t[s]);r=r>>>8^e[n]}return-1^r}(i,r+4,s);i[s]=o>>24&255;i[s+1]=o>>16&255;i[s+2]=o>>8&255;i[s+3]=255&o}function deflateSyncUncompressed(t){let e=t.length;const n=65535,i=Math.ceil(e/n),r=new Uint8Array(2+e+5*i+4);let s=0;r[s++]=120;r[s++]=156;let a=0;for(;e>n;){r[s++]=0;r[s++]=255;r[s++]=255;r[s++]=0;r[s++]=0;r.set(t.subarray(a,a+n),s);s+=n;a+=n;e-=n}r[s++]=1;r[s++]=255&e;r[s++]=e>>8&255;r[s++]=255&~e;r[s++]=(65535&~e)>>8&255;r.set(t.subarray(a),s);s+=t.length-a;const o=function adler32(t,e,n){let i=1,r=0;for(let s=e;s<n;++s){i=(i+(255&t[s]))%65521;r=(r+i)%65521}return r<<16|i}(t,0,t.length);r[s++]=o>>24&255;r[s++]=o>>16&255;r[s++]=o>>8&255;r[s++]=255&o;return r}function encode(e,n,i,s){const a=e.width,o=e.height;let l,c,h;const d=e.data;switch(n){case r.ImageKind.GRAYSCALE_1BPP:c=0;l=1;h=a+7>>3;break;case r.ImageKind.RGB_24BPP:c=2;l=8;h=3*a;break;case r.ImageKind.RGBA_32BPP:c=6;l=8;h=4*a;break;default:throw new Error("invalid format")}const u=new Uint8Array((1+h)*o);let p=0,f=0;for(let t=0;t<o;++t){u[p++]=0;u.set(d.subarray(f,f+h),p);f+=h;p+=h}if(n===r.ImageKind.GRAYSCALE_1BPP&&s){p=0;for(let t=0;t<o;t++){p++;for(let t=0;t<h;t++)u[p++]^=255}}const g=new Uint8Array([a>>24&255,a>>16&255,a>>8&255,255&a,o>>24&255,o>>16&255,o>>8&255,255&o,l,c,0,0,0]),m=function deflateSync(t){if(!r.isNodeJS)return deflateSyncUncompressed(t);try{const e=parseInt(process.versions.node)>=8?t:Buffer.from(t),n=require("zlib").deflateSync(e,{level:9});return n instanceof Uint8Array?n:new Uint8Array(n)}catch(t){(0,r.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+t)}return deflateSyncUncompressed(t)}(u),b=t.length+36+g.length+m.length,v=new Uint8Array(b);let y=0;v.set(t,y);y+=t.length;writePngChunk("IHDR",g,v,y);y+=12+g.length;writePngChunk("IDATA",m,v,y);y+=12+m.length;writePngChunk("IEND",new Uint8Array(0),v,y);return createObjectURL(v,"image/png",i)}return function convertImgDataToPng(t,e,n){return encode(t,void 0===t.kind?r.ImageKind.GRAYSCALE_1BPP:t.kind,e,n)}}();class SVGExtraState{constructor(){this.fontSizeScale=1;this.fontWeight=a;this.fontSize=0;this.textMatrix=r.IDENTITY_MATRIX;this.fontMatrix=r.FONT_IDENTITY_MATRIX;this.leading=0;this.textRenderingMode=r.TextRenderingMode.FILL;this.textMatrixScale=1;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=o;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(t,e){this.x=t;this.y=e}}function pf(t){if(Number.isInteger(t))return t.toString();const e=t.toFixed(10);let n=e.length-1;if("0"!==e[n])return e;do{n--}while("0"===e[n]);return e.substring(0,"."===e[n]?n:n+1)}function pm(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":`scale(${pf(t[0])} ${pf(t[3])})`;if(t[0]===t[3]&&t[1]===-t[2]){return`rotate(${pf(180*Math.acos(t[0])/Math.PI)})`}}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return`translate(${pf(t[4])} ${pf(t[5])})`;return`matrix(${pf(t[0])} ${pf(t[1])} ${pf(t[2])} ${pf(t[3])} ${pf(t[4])} ${pf(t[5])})`}let d=0,u=0,p=0;e.SVGGraphics=class SVGGraphics{constructor(t,e){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(0,i.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future.");this.svgFactory=new i.DOMSVGFactory;this.current=new SVGExtraState;this.transformMatrix=r.IDENTITY_MATRIX;this.transformStack=[];this.extraStack=[];this.commonObjs=t;this.objs=e;this.pendingClip=null;this.pendingEOFill=!1;this.embedFonts=!1;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!n;this._operatorIdMapping=[];for(const t in r.OPS)this._operatorIdMapping[r.OPS[t]]=t}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}save(){this.transformStack.push(this.transformMatrix);const t=this.current;this.extraStack.push(t);this.current=t.clone()}restore(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.pendingClip=null;this.tgrp=null}group(t){this.save();this.executeOpTree(t);this.restore()}loadDependencies(t){const e=t.fnArray,n=t.argsArray;for(let t=0,i=e.length;t<i;t++)if(e[t]===r.OPS.dependency)for(const e of n[t]){const t=e.startsWith("g_")?this.commonObjs:this.objs,n=new Promise((n=>{t.get(e,n)}));this.current.dependencies.push(n)}return Promise.all(this.current.dependencies)}transform(t,e,n,i,s,a){const o=[t,e,n,i,s,a];this.transformMatrix=r.Util.transform(this.transformMatrix,o);this.tgrp=null}getSVG(t,e){this.viewport=e;const n=this._initialize(e);return this.loadDependencies(t).then((()=>{this.transformMatrix=r.IDENTITY_MATRIX;this.executeOpTree(this.convertOpList(t));return n}))}convertOpList(t){const e=this._operatorIdMapping,n=t.argsArray,i=t.fnArray,r=[];for(let t=0,s=i.length;t<s;t++){const s=i[t];r.push({fnId:s,fn:e[s],args:n[t]})}return function opListToTree(t){let e=[];const n=[];for(const i of t)if("save"!==i.fn)"restore"===i.fn?e=n.pop():e.push(i);else{e.push({fnId:92,fn:"group",items:[]});n.push(e);e=e.at(-1).items}return e}(r)}executeOpTree(t){for(const e of t){const t=e.fn,n=e.fnId,i=e.args;switch(0|n){case r.OPS.beginText:this.beginText();break;case r.OPS.dependency:break;case r.OPS.setLeading:this.setLeading(i);break;case r.OPS.setLeadingMoveText:this.setLeadingMoveText(i[0],i[1]);break;case r.OPS.setFont:this.setFont(i);break;case r.OPS.showText:case r.OPS.showSpacedText:this.showText(i[0]);break;case r.OPS.endText:this.endText();break;case r.OPS.moveText:this.moveText(i[0],i[1]);break;case r.OPS.setCharSpacing:this.setCharSpacing(i[0]);break;case r.OPS.setWordSpacing:this.setWordSpacing(i[0]);break;case r.OPS.setHScale:this.setHScale(i[0]);break;case r.OPS.setTextMatrix:this.setTextMatrix(i[0],i[1],i[2],i[3],i[4],i[5]);break;case r.OPS.setTextRise:this.setTextRise(i[0]);break;case r.OPS.setTextRenderingMode:this.setTextRenderingMode(i[0]);break;case r.OPS.setLineWidth:this.setLineWidth(i[0]);break;case r.OPS.setLineJoin:this.setLineJoin(i[0]);break;case r.OPS.setLineCap:this.setLineCap(i[0]);break;case r.OPS.setMiterLimit:this.setMiterLimit(i[0]);break;case r.OPS.setFillRGBColor:this.setFillRGBColor(i[0],i[1],i[2]);break;case r.OPS.setStrokeRGBColor:this.setStrokeRGBColor(i[0],i[1],i[2]);break;case r.OPS.setStrokeColorN:this.setStrokeColorN(i);break;case r.OPS.setFillColorN:this.setFillColorN(i);break;case r.OPS.shadingFill:this.shadingFill(i[0]);break;case r.OPS.setDash:this.setDash(i[0],i[1]);break;case r.OPS.setRenderingIntent:this.setRenderingIntent(i[0]);break;case r.OPS.setFlatness:this.setFlatness(i[0]);break;case r.OPS.setGState:this.setGState(i[0]);break;case r.OPS.fill:this.fill();break;case r.OPS.eoFill:this.eoFill();break;case r.OPS.stroke:this.stroke();break;case r.OPS.fillStroke:this.fillStroke();break;case r.OPS.eoFillStroke:this.eoFillStroke();break;case r.OPS.clip:this.clip("nonzero");break;case r.OPS.eoClip:this.clip("evenodd");break;case r.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case r.OPS.paintImageXObject:this.paintImageXObject(i[0]);break;case r.OPS.paintInlineImageXObject:this.paintInlineImageXObject(i[0]);break;case r.OPS.paintImageMaskXObject:this.paintImageMaskXObject(i[0]);break;case r.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(i[0],i[1]);break;case r.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case r.OPS.closePath:this.closePath();break;case r.OPS.closeStroke:this.closeStroke();break;case r.OPS.closeFillStroke:this.closeFillStroke();break;case r.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case r.OPS.nextLine:this.nextLine();break;case r.OPS.transform:this.transform(i[0],i[1],i[2],i[3],i[4],i[5]);break;case r.OPS.constructPath:this.constructPath(i[0],i[1]);break;case r.OPS.endPath:this.endPath();break;case 92:this.group(e.items);break;default:(0,r.warn)(`Unimplemented operator ${t}`)}}}setWordSpacing(t){this.current.wordSpacing=t}setCharSpacing(t){this.current.charSpacing=t}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(t,e,n,i,r,s){const a=this.current;a.textMatrix=a.lineMatrix=[t,e,n,i,r,s];a.textMatrixScale=Math.hypot(t,e);a.x=a.lineX=0;a.y=a.lineY=0;a.xcoords=[];a.ycoords=[];a.tspan=this.svgFactory.createElement("svg:tspan");a.tspan.setAttributeNS(null,"font-family",a.fontFamily);a.tspan.setAttributeNS(null,"font-size",`${pf(a.fontSize)}px`);a.tspan.setAttributeNS(null,"y",pf(-a.y));a.txtElement=this.svgFactory.createElement("svg:text");a.txtElement.append(a.tspan)}beginText(){const t=this.current;t.x=t.lineX=0;t.y=t.lineY=0;t.textMatrix=r.IDENTITY_MATRIX;t.lineMatrix=r.IDENTITY_MATRIX;t.textMatrixScale=1;t.tspan=this.svgFactory.createElement("svg:tspan");t.txtElement=this.svgFactory.createElement("svg:text");t.txtgrp=this.svgFactory.createElement("svg:g");t.xcoords=[];t.ycoords=[]}moveText(t,e){const n=this.current;n.x=n.lineX+=t;n.y=n.lineY+=e;n.xcoords=[];n.ycoords=[];n.tspan=this.svgFactory.createElement("svg:tspan");n.tspan.setAttributeNS(null,"font-family",n.fontFamily);n.tspan.setAttributeNS(null,"font-size",`${pf(n.fontSize)}px`);n.tspan.setAttributeNS(null,"y",pf(-n.y))}showText(t){const e=this.current,n=e.font,i=e.fontSize;if(0===i)return;const l=e.fontSizeScale,c=e.charSpacing,h=e.wordSpacing,d=e.fontDirection,u=e.textHScale*d,p=n.vertical,f=p?1:-1,g=n.defaultVMetrics,m=i*e.fontMatrix[0];let b=0;for(const r of t){if(null===r){b+=d*h;continue}if("number"==typeof r){b+=f*r*i/1e3;continue}const t=(r.isSpace?h:0)+c,s=r.fontChar;let a,o,u=r.width;if(p){let t;const e=r.vmetric||g;t=r.vmetric?e[1]:.5*u;t=-t*m;const n=e[2]*m;u=e?-e[0]:u;a=t/l;o=(b+n)/l}else{a=b/l;o=0}if(r.isInFont||n.missingFile){e.xcoords.push(e.x+a);p&&e.ycoords.push(-e.y+o);e.tspan.textContent+=s}b+=p?u*m-t*d:u*m+t*d}e.tspan.setAttributeNS(null,"x",e.xcoords.map(pf).join(" "));p?e.tspan.setAttributeNS(null,"y",e.ycoords.map(pf).join(" ")):e.tspan.setAttributeNS(null,"y",pf(-e.y));p?e.y-=b:e.x+=b*u;e.tspan.setAttributeNS(null,"font-family",e.fontFamily);e.tspan.setAttributeNS(null,"font-size",`${pf(e.fontSize)}px`);e.fontStyle!==s&&e.tspan.setAttributeNS(null,"font-style",e.fontStyle);e.fontWeight!==a&&e.tspan.setAttributeNS(null,"font-weight",e.fontWeight);const v=e.textRenderingMode&r.TextRenderingMode.FILL_STROKE_MASK;if(v===r.TextRenderingMode.FILL||v===r.TextRenderingMode.FILL_STROKE){e.fillColor!==o&&e.tspan.setAttributeNS(null,"fill",e.fillColor);e.fillAlpha<1&&e.tspan.setAttributeNS(null,"fill-opacity",e.fillAlpha)}else e.textRenderingMode===r.TextRenderingMode.ADD_TO_PATH?e.tspan.setAttributeNS(null,"fill","transparent"):e.tspan.setAttributeNS(null,"fill","none");if(v===r.TextRenderingMode.STROKE||v===r.TextRenderingMode.FILL_STROKE){const t=1/(e.textMatrixScale||1);this._setStrokeAttributes(e.tspan,t)}let y=e.textMatrix;if(0!==e.textRise){y=y.slice();y[5]+=e.textRise}e.txtElement.setAttributeNS(null,"transform",`${pm(y)} scale(${pf(u)}, -1)`);e.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve");e.txtElement.append(e.tspan);e.txtgrp.append(e.txtElement);this._ensureTransformGroup().append(e.txtElement)}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}addFontStyle(t){if(!t.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');if(!this.cssStyle){this.cssStyle=this.svgFactory.createElement("svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.append(this.cssStyle)}const e=createObjectURL(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${t.loadedName}"; src: url(${e}); }\n`}setFont(t){const e=this.current,n=this.commonObjs.get(t[0]);let i=t[1];e.font=n;if(this.embedFonts&&!n.missingFile&&!this.embeddedFonts[n.loadedName]){this.addFontStyle(n);this.embeddedFonts[n.loadedName]=n}e.fontMatrix=n.fontMatrix||r.FONT_IDENTITY_MATRIX;let s="normal";n.black?s="900":n.bold&&(s="bold");const a=n.italic?"italic":"normal";if(i<0){i=-i;e.fontDirection=-1}else e.fontDirection=1;e.fontSize=i;e.fontFamily=n.loadedName;e.fontWeight=s;e.fontStyle=a;e.tspan=this.svgFactory.createElement("svg:tspan");e.tspan.setAttributeNS(null,"y",pf(-e.y));e.xcoords=[];e.ycoords=[]}endText(){const t=this.current;if(t.textRenderingMode&r.TextRenderingMode.ADD_TO_PATH_FLAG&&t.txtElement?.hasChildNodes()){t.element=t.txtElement;this.clip("nonzero");this.endPath()}}setLineWidth(t){t>0&&(this.current.lineWidth=t)}setLineCap(t){this.current.lineCap=l[t]}setLineJoin(t){this.current.lineJoin=c[t]}setMiterLimit(t){this.current.miterLimit=t}setStrokeAlpha(t){this.current.strokeAlpha=t}setStrokeRGBColor(t,e,n){this.current.strokeColor=r.Util.makeHexColor(t,e,n)}setFillAlpha(t){this.current.fillAlpha=t}setFillRGBColor(t,e,n){this.current.fillColor=r.Util.makeHexColor(t,e,n);this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.xcoords=[];this.current.ycoords=[]}setStrokeColorN(t){this.current.strokeColor=this._makeColorN_Pattern(t)}setFillColorN(t){this.current.fillColor=this._makeColorN_Pattern(t)}shadingFill(t){const{width:e,height:n}=this.viewport,i=r.Util.inverseTransform(this.transformMatrix),[s,a,o,l]=r.Util.getAxialAlignedBoundingBox([0,0,e,n],i),c=this.svgFactory.createElement("svg:rect");c.setAttributeNS(null,"x",s);c.setAttributeNS(null,"y",a);c.setAttributeNS(null,"width",o-s);c.setAttributeNS(null,"height",l-a);c.setAttributeNS(null,"fill",this._makeShadingPattern(t));this.current.fillAlpha<1&&c.setAttributeNS(null,"fill-opacity",this.current.fillAlpha);this._ensureTransformGroup().append(c)}_makeColorN_Pattern(t){return"TilingPattern"===t[0]?this._makeTilingPattern(t):this._makeShadingPattern(t)}_makeTilingPattern(t){const e=t[1],n=t[2],i=t[3]||r.IDENTITY_MATRIX,[s,a,o,l]=t[4],c=t[5],h=t[6],d=t[7],u="shading"+p++,[f,g,m,b]=r.Util.normalizeRect([...r.Util.applyTransform([s,a],i),...r.Util.applyTransform([o,l],i)]),[v,y]=r.Util.singularValueDecompose2dScale(i),_=c*v,A=h*y,S=this.svgFactory.createElement("svg:pattern");S.setAttributeNS(null,"id",u);S.setAttributeNS(null,"patternUnits","userSpaceOnUse");S.setAttributeNS(null,"width",_);S.setAttributeNS(null,"height",A);S.setAttributeNS(null,"x",`${f}`);S.setAttributeNS(null,"y",`${g}`);const E=this.svg,x=this.transformMatrix,w=this.current.fillColor,C=this.current.strokeColor,T=this.svgFactory.create(m-f,b-g);this.svg=T;this.transformMatrix=i;if(2===d){const t=r.Util.makeHexColor(...e);this.current.fillColor=t;this.current.strokeColor=t}this.executeOpTree(this.convertOpList(n));this.svg=E;this.transformMatrix=x;this.current.fillColor=w;this.current.strokeColor=C;S.append(T.childNodes[0]);this.defs.append(S);return`url(#${u})`}_makeShadingPattern(t){"string"==typeof t&&(t=this.objs.get(t));switch(t[0]){case"RadialAxial":const e="shading"+p++,n=t[3];let i;switch(t[1]){case"axial":const n=t[4],r=t[5];i=this.svgFactory.createElement("svg:linearGradient");i.setAttributeNS(null,"id",e);i.setAttributeNS(null,"gradientUnits","userSpaceOnUse");i.setAttributeNS(null,"x1",n[0]);i.setAttributeNS(null,"y1",n[1]);i.setAttributeNS(null,"x2",r[0]);i.setAttributeNS(null,"y2",r[1]);break;case"radial":const s=t[4],a=t[5],o=t[6],l=t[7];i=this.svgFactory.createElement("svg:radialGradient");i.setAttributeNS(null,"id",e);i.setAttributeNS(null,"gradientUnits","userSpaceOnUse");i.setAttributeNS(null,"cx",a[0]);i.setAttributeNS(null,"cy",a[1]);i.setAttributeNS(null,"r",l);i.setAttributeNS(null,"fx",s[0]);i.setAttributeNS(null,"fy",s[1]);i.setAttributeNS(null,"fr",o);break;default:throw new Error(`Unknown RadialAxial type: ${t[1]}`)}for(const t of n){const e=this.svgFactory.createElement("svg:stop");e.setAttributeNS(null,"offset",t[0]);e.setAttributeNS(null,"stop-color",t[1]);i.append(e)}this.defs.append(i);return`url(#${e})`;case"Mesh":(0,r.warn)("Unimplemented pattern Mesh");return null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${t[0]}`)}}setDash(t,e){this.current.dashArray=t;this.current.dashPhase=e}constructPath(t,e){const n=this.current;let i=n.x,s=n.y,a=[],o=0;for(const n of t)switch(0|n){case r.OPS.rectangle:i=e[o++];s=e[o++];const t=i+e[o++],n=s+e[o++];a.push("M",pf(i),pf(s),"L",pf(t),pf(s),"L",pf(t),pf(n),"L",pf(i),pf(n),"Z");break;case r.OPS.moveTo:i=e[o++];s=e[o++];a.push("M",pf(i),pf(s));break;case r.OPS.lineTo:i=e[o++];s=e[o++];a.push("L",pf(i),pf(s));break;case r.OPS.curveTo:i=e[o+4];s=e[o+5];a.push("C",pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]),pf(i),pf(s));o+=6;break;case r.OPS.curveTo2:a.push("C",pf(i),pf(s),pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]));i=e[o+2];s=e[o+3];o+=4;break;case r.OPS.curveTo3:i=e[o+2];s=e[o+3];a.push("C",pf(e[o]),pf(e[o+1]),pf(i),pf(s),pf(i),pf(s));o+=4;break;case r.OPS.closePath:a.push("Z")}a=a.join(" ");if(n.path&&t.length>0&&t[0]!==r.OPS.rectangle&&t[0]!==r.OPS.moveTo)a=n.path.getAttributeNS(null,"d")+a;else{n.path=this.svgFactory.createElement("svg:path");this._ensureTransformGroup().append(n.path)}n.path.setAttributeNS(null,"d",a);n.path.setAttributeNS(null,"fill","none");n.element=n.path;n.setCurrentPoint(i,s)}endPath(){const t=this.current;t.path=null;if(!this.pendingClip)return;if(!t.element){this.pendingClip=null;return}const e="clippath"+d++,n=this.svgFactory.createElement("svg:clipPath");n.setAttributeNS(null,"id",e);n.setAttributeNS(null,"transform",pm(this.transformMatrix));const i=t.element.cloneNode(!0);"evenodd"===this.pendingClip?i.setAttributeNS(null,"clip-rule","evenodd"):i.setAttributeNS(null,"clip-rule","nonzero");this.pendingClip=null;n.append(i);this.defs.append(n);if(t.activeClipUrl){t.clipGroup=null;for(const t of this.extraStack)t.clipGroup=null;n.setAttributeNS(null,"clip-path",t.activeClipUrl)}t.activeClipUrl=`url(#${e})`;this.tgrp=null}clip(t){this.pendingClip=t}closePath(){const t=this.current;if(t.path){const e=`${t.path.getAttributeNS(null,"d")}Z`;t.path.setAttributeNS(null,"d",e)}}setLeading(t){this.current.leading=-t}setTextRise(t){this.current.textRise=t}setTextRenderingMode(t){this.current.textRenderingMode=t}setHScale(t){this.current.textHScale=t/100}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,n]of t)switch(e){case"LW":this.setLineWidth(n);break;case"LC":this.setLineCap(n);break;case"LJ":this.setLineJoin(n);break;case"ML":this.setMiterLimit(n);break;case"D":this.setDash(n[0],n[1]);break;case"RI":this.setRenderingIntent(n);break;case"FL":this.setFlatness(n);break;case"Font":this.setFont(n);break;case"CA":this.setStrokeAlpha(n);break;case"ca":this.setFillAlpha(n);break;default:(0,r.warn)(`Unimplemented graphic state operator ${e}`)}}fill(){const t=this.current;if(t.element){t.element.setAttributeNS(null,"fill",t.fillColor);t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha);this.endPath()}}stroke(){const t=this.current;if(t.element){this._setStrokeAttributes(t.element);t.element.setAttributeNS(null,"fill","none");this.endPath()}}_setStrokeAttributes(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const n=this.current;let i=n.dashArray;1!==e&&i.length>0&&(i=i.map((function(t){return e*t})));t.setAttributeNS(null,"stroke",n.strokeColor);t.setAttributeNS(null,"stroke-opacity",n.strokeAlpha);t.setAttributeNS(null,"stroke-miterlimit",pf(n.miterLimit));t.setAttributeNS(null,"stroke-linecap",n.lineCap);t.setAttributeNS(null,"stroke-linejoin",n.lineJoin);t.setAttributeNS(null,"stroke-width",pf(e*n.lineWidth)+"px");t.setAttributeNS(null,"stroke-dasharray",i.map(pf).join(" "));t.setAttributeNS(null,"stroke-dashoffset",pf(e*n.dashPhase)+"px")}eoFill(){this.current.element?.setAttributeNS(null,"fill-rule","evenodd");this.fill()}fillStroke(){this.stroke();this.fill()}eoFillStroke(){this.current.element?.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()}closeStroke(){this.closePath();this.stroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.closePath();this.eoFillStroke()}paintSolidColorImageMask(){const t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0");t.setAttributeNS(null,"y","0");t.setAttributeNS(null,"width","1px");t.setAttributeNS(null,"height","1px");t.setAttributeNS(null,"fill",this.current.fillColor);this._ensureTransformGroup().append(t)}paintImageXObject(t){const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,r.warn)(`Dependent image with object ID ${t} is not ready yet`)}paintInlineImageXObject(t,e){const n=t.width,i=t.height,r=h(t,this.forceDataSchema,!!e),s=this.svgFactory.createElement("svg:rect");s.setAttributeNS(null,"x","0");s.setAttributeNS(null,"y","0");s.setAttributeNS(null,"width",pf(n));s.setAttributeNS(null,"height",pf(i));this.current.element=s;this.clip("nonzero");const a=this.svgFactory.createElement("svg:image");a.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",r);a.setAttributeNS(null,"x","0");a.setAttributeNS(null,"y",pf(-i));a.setAttributeNS(null,"width",pf(n)+"px");a.setAttributeNS(null,"height",pf(i)+"px");a.setAttributeNS(null,"transform",`scale(${pf(1/n)} ${pf(-1/i)})`);e?e.append(a):this._ensureTransformGroup().append(a)}paintImageMaskXObject(t){const e=this.getObject(t.data,t);if(e.bitmap){(0,r.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");return}const n=this.current,i=e.width,s=e.height,a=n.fillColor;n.maskId="mask"+u++;const o=this.svgFactory.createElement("svg:mask");o.setAttributeNS(null,"id",n.maskId);const l=this.svgFactory.createElement("svg:rect");l.setAttributeNS(null,"x","0");l.setAttributeNS(null,"y","0");l.setAttributeNS(null,"width",pf(i));l.setAttributeNS(null,"height",pf(s));l.setAttributeNS(null,"fill",a);l.setAttributeNS(null,"mask",`url(#${n.maskId})`);this.defs.append(o);this._ensureTransformGroup().append(l);this.paintInlineImageXObject(e,o)}paintFormXObjectBegin(t,e){Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]);if(e){const t=e[2]-e[0],n=e[3]-e[1],i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x",e[0]);i.setAttributeNS(null,"y",e[1]);i.setAttributeNS(null,"width",pf(t));i.setAttributeNS(null,"height",pf(n));this.current.element=i;this.clip("nonzero");this.endPath()}}paintFormXObjectEnd(){}_initialize(t){const e=this.svgFactory.create(t.width,t.height),n=this.svgFactory.createElement("svg:defs");e.append(n);this.defs=n;const i=this.svgFactory.createElement("svg:g");i.setAttributeNS(null,"transform",pm(t.transform));e.append(i);this.svg=i;return e}_ensureClipGroup(){if(!this.current.clipGroup){const t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.append(t);this.current.clipGroup=t}return this.current.clipGroup}_ensureTransformGroup(){if(!this.tgrp){this.tgrp=this.svgFactory.createElement("svg:g");this.tgrp.setAttributeNS(null,"transform",pm(this.transformMatrix));this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)}return this.tgrp}}},(t,e,n)=>{var i=n(3),r=n(188),s=n(193);i({target:"Array",proto:!0},{group:function group(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}});s("group")},(t,e,n)=>{var i=n(99),r=n(14),s=n(13),a=n(40),o=n(18),l=n(64),c=n(189),h=n(108),d=Array,u=r([].push);t.exports=function(t,e,n,r){for(var p,f,g,m=a(t),b=s(m),v=i(e,n),y=c(null),_=l(b),A=0;_>A;A++){g=b[A];(f=o(v(g,A,m)))in y?u(y[f],g):y[f]=[g]}if(r&&(p=r(m))!==d)for(f in y)y[f]=h(p,y[f]);return y}},(t,e,n)=>{var i,r=n(47),s=n(190),a=n(66),o=n(55),l=n(192),c=n(43),h=n(54),d="prototype",u="script",p=h("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<"+u+">"+t+"</"+u+">"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag(""));t.close();var e=t.parentWindow.Object;t=null;return e},NullProtoObject=function(){try{i=new ActiveXObject("htmlfile")}catch(t){}NullProtoObject="undefined"!=typeof document?document.domain&&i?NullProtoObjectViaActiveX(i):function(){var t,e=c("iframe"),n="java"+u+":";e.style.display="none";l.appendChild(e);e.src=String(n);(t=e.contentWindow.document).open();t.write(scriptTag("document.F=Object"));t.close();return t.F}():NullProtoObjectViaActiveX(i);for(var t=a.length;t--;)delete NullProtoObject[d][a[t]];return NullProtoObject()};o[p]=!0;t.exports=Object.create||function create(t,e){var n;if(null!==t){EmptyConstructor[d]=r(t);n=new EmptyConstructor;EmptyConstructor[d]=null;n[p]=t}else n=NullProtoObject();return void 0===e?n:s.f(n,e)}},(t,e,n)=>{var i=n(6),r=n(46),s=n(45),a=n(47),o=n(12),l=n(191);e.f=i&&!r?Object.defineProperties:function defineProperties(t,e){a(t);for(var n,i=o(e),r=l(e),c=r.length,h=0;c>h;)s.f(t,n=r[h++],i[n]);return t}},(t,e,n)=>{var i=n(59),r=n(66);t.exports=Object.keys||function keys(t){return i(t,r)}},(t,e,n)=>{var i=n(24);t.exports=i("document","documentElement")},(t,e,n)=>{var i=n(34),r=n(189),s=n(45).f,a=i("unscopables"),o=Array.prototype;void 0===o[a]&&s(o,a,{configurable:!0,value:r(null)});t.exports=function(t){o[a][t]=!0}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.XfaText=void 0;n(89);class XfaText{static textContent(t){const e=[],n={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let n=null;const i=t.name;if("#text"===i)n=t.value;else{if(!XfaText.shouldBuildText(i))return;t?.attributes?.textContent?n=t.attributes.textContent:t.value&&(n=t.value)}null!==n&&e.push({str:n});if(t.children)for(const e of t.children)walk(e)}(t);return n}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}e.XfaText=XfaText},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.TextLayerRenderTask=void 0;e.renderTextLayer=function renderTextLayer(t){if(!t.textContentSource&&(t.textContent||t.textContentStream)){(0,r.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead.");t.textContentSource=t.textContent||t.textContentStream}const{container:e,viewport:n}=t,i=getComputedStyle(e),s=i.getPropertyValue("visibility"),a=parseFloat(i.getPropertyValue("--scale-factor"));"visible"===s&&(!a||Math.abs(a-n.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const o=new TextLayerRenderTask(t);o._render();return o};e.updateTextLayer=function updateTextLayer(t){let{container:e,viewport:n,textDivs:i,textDivProperties:s,isOffscreenCanvasSupported:a,mustRotate:o=!0,mustRescale:l=!0}=t;o&&(0,r.setLayerDimensions)(e,{rotation:n.rotation});if(l){const t=getCtx(0,a),e={prevFontSize:null,prevFontFamily:null,div:null,scale:n.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:t};for(const t of i){e.properties=s.get(t);e.div=t;layout(e)}}};n(89);n(2);var i=n(1),r=n(168);const s=30,a=.8,o=new Map;function getCtx(t,e){let n;if(e&&i.FeatureTest.isOffscreenCanvasSupported)n=new OffscreenCanvas(t,t).getContext("2d",{alpha:!1});else{const e=document.createElement("canvas");e.width=e.height=t;n=e.getContext("2d",{alpha:!1})}return n}function appendText(t,e,n){const r=document.createElement("span"),l={angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(r);const c=i.Util.transform(t._transform,e.transform);let h=Math.atan2(c[1],c[0]);const d=n[e.fontName];d.vertical&&(h+=Math.PI/2);const u=Math.hypot(c[2],c[3]),p=u*function getAscent(t,e){const n=o.get(t);if(n)return n;const i=getCtx(s,e);i.font=`${s}px ${t}`;const r=i.measureText("");let l=r.fontBoundingBoxAscent,c=Math.abs(r.fontBoundingBoxDescent);if(l){const e=l/(l+c);o.set(t,e);i.canvas.width=i.canvas.height=0;return e}i.strokeStyle="red";i.clearRect(0,0,s,s);i.strokeText("g",0,0);let h=i.getImageData(0,0,s,s).data;c=0;for(let t=h.length-1-3;t>=0;t-=4)if(h[t]>0){c=Math.ceil(t/4/s);break}i.clearRect(0,0,s,s);i.strokeText("A",0,s);h=i.getImageData(0,0,s,s).data;l=0;for(let t=0,e=h.length;t<e;t+=4)if(h[t]>0){l=s-Math.floor(t/4/s);break}i.canvas.width=i.canvas.height=0;if(l){const e=l/(l+c);o.set(t,e);return e}o.set(t,a);return a}(d.fontFamily,t._isOffscreenCanvasSupported);let f,g;if(0===h){f=c[4];g=c[5]-p}else{f=c[4]+p*Math.sin(h);g=c[5]-p*Math.cos(h)}const m="calc(var(--scale-factor)*",b=r.style;if(t._container===t._rootContainer){b.left=`${(100*f/t._pageWidth).toFixed(2)}%`;b.top=`${(100*g/t._pageHeight).toFixed(2)}%`}else{b.left=`${m}${f.toFixed(2)}px)`;b.top=`${m}${g.toFixed(2)}px)`}b.fontSize=`${m}${u.toFixed(2)}px)`;b.fontFamily=d.fontFamily;l.fontSize=u;r.setAttribute("role","presentation");r.textContent=e.str;r.dir=e.dir;t._fontInspectorEnabled&&(r.dataset.fontName=e.fontName);0!==h&&(l.angle=h*(180/Math.PI));let v=!1;if(e.str.length>1)v=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),n=Math.abs(e.transform[3]);t!==n&&Math.max(t,n)/Math.min(t,n)>1.5&&(v=!0)}v&&(l.canvasWidth=d.vertical?e.height:e.width);t._textDivProperties.set(r,l);t._isReadableStream&&t._layoutText(r)}function layout(t){const{div:e,scale:n,properties:i,ctx:r,prevFontSize:s,prevFontFamily:a}=t,{style:o}=e;let l="";if(0!==i.canvasWidth&&i.hasText){const{fontFamily:c}=o,{canvasWidth:h,fontSize:d}=i;if(s!==d||a!==c){r.font=`${d*n}px ${c}`;t.prevFontSize=d;t.prevFontFamily=c}const{width:u}=r.measureText(e.textContent);u>0&&(l=`scaleX(${h*n/u})`)}0!==i.angle&&(l=`rotate(${i.angle}deg) ${l}`);l.length>0&&(o.transform=l)}class TextLayerRenderTask{constructor(t){let{textContentSource:e,container:n,viewport:s,textDivs:a,textDivProperties:o,textContentItemsStr:l,isOffscreenCanvasSupported:c}=t;this._textContentSource=e;this._isReadableStream=e instanceof ReadableStream;this._container=this._rootContainer=n;this._textDivs=a||[];this._textContentItemsStr=l||[];this._isOffscreenCanvasSupported=c;this._fontInspectorEnabled=!!globalThis.FontInspector?.enabled;this._reader=null;this._textDivProperties=o||new WeakMap;this._canceled=!1;this._capability=new i.PromiseCapability;this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:s.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:getCtx(0,c)};const{pageWidth:h,pageHeight:d,pageX:u,pageY:p}=s.rawDims;this._transform=[1,0,0,-1,-u,p+d];this._pageWidth=h;this._pageHeight=d;(0,r.setLayerDimensions)(n,s);this._capability.promise.finally((()=>{this._layoutTextParams=null})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0;if(this._reader){this._reader.cancel(new i.AbortException("TextLayer task cancelled.")).catch((()=>{}));this._reader=null}this._capability.reject(new i.AbortException("TextLayer task cancelled."))}_processItems(t,e){for(const n of t)if(void 0!==n.str){this._textContentItemsStr.push(n.str);appendText(this,n,e)}else if("beginMarkedContentProps"===n.type||"beginMarkedContent"===n.type){const t=this._container;this._container=document.createElement("span");this._container.classList.add("markedContent");null!==n.id&&this._container.setAttribute("id",`${n.id}`);t.append(this._container)}else"endMarkedContent"===n.type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._layoutTextParams.properties=this._textDivProperties.get(t);this._layoutTextParams.div=t;layout(this._layoutTextParams);e.hasText&&this._container.append(t);if(e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this._container.append(t)}}_render(){const t=new i.PromiseCapability;let e=Object.create(null);if(this._isReadableStream){const pump=()=>{this._reader.read().then((n=>{let{value:i,done:r}=n;if(r)t.resolve();else{Object.assign(e,i.styles);this._processItems(i.items,e);pump()}}),t.reject)};this._reader=this._textContentSource.getReader();pump()}else{if(!this._textContentSource)throw new Error('No "textContentSource" parameter specified.');{const{items:e,styles:n}=this._textContentSource;this._processItems(e,n);t.resolve()}}t.promise.then((()=>{e=null;!function render(t){if(t._canceled)return;const e=t._textDivs,n=t._capability;if(e.length>1e5)n.resolve();else{if(!t._isReadableStream)for(const n of e)t._layoutText(n);n.resolve()}}(this)}),this._capability.reject)}}e.TextLayerRenderTask=TextLayerRenderTask},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditorLayer=void 0;n(125);n(136);n(138);n(141);n(143);n(145);n(147);var i=n(1),r=n(164),s=n(197),a=n(202),o=n(168),l=n(203);class AnnotationEditorLayer{#Se;#Ee=!1;#xe=null;#we=this.pointerup.bind(this);#Ce=this.pointerdown.bind(this);#Te=new Map;#Pe=!1;#ke=!1;#Me=!1;#Fe;static _initialized=!1;constructor(t){let{uiManager:e,pageIndex:n,div:i,accessibilityManager:r,annotationLayer:o,viewport:c,l10n:h}=t;const d=[s.FreeTextEditor,a.InkEditor,l.StampEditor];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const t of d)t.initialize(h)}e.registerEditorTypes(d);this.#Fe=e;this.pageIndex=n;this.div=i;this.#Se=r;this.#xe=o;this.viewport=c;this.#Fe.addLayer(this)}get isEmpty(){return 0===this.#Te.size}updateToolbar(t){this.#Fe.updateToolbar(t)}updateMode(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.#Fe.getMode();this.#Re();if(t===i.AnnotationEditorType.INK){this.addInkEditorIfNeeded(!1);this.disableClick()}else this.enableClick();if(t!==i.AnnotationEditorType.NONE){this.div.classList.toggle("freeTextEditing",t===i.AnnotationEditorType.FREETEXT);this.div.classList.toggle("inkEditing",t===i.AnnotationEditorType.INK);this.div.classList.toggle("stampEditing",t===i.AnnotationEditorType.STAMP);this.div.hidden=!1}}addInkEditorIfNeeded(t){if(!t&&this.#Fe.getMode()!==i.AnnotationEditorType.INK)return;if(!t)for(const t of this.#Te.values())if(t.isEmpty()){t.setInBackground();return}this.#De({offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(t){this.#Fe.setEditingState(t)}addCommands(t){this.#Fe.addCommands(t)}enable(){this.div.style.pointerEvents="auto";const t=new Set;for(const e of this.#Te.values()){e.enableEditing();e.annotationElementId&&t.add(e.annotationElementId)}if(!this.#xe)return;const e=this.#xe.getEditableAnnotations();for(const n of e){n.hide();if(this.#Fe.isDeletedAnnotationElement(n.data.id))continue;if(t.has(n.data.id))continue;const e=this.deserialize(n);if(e){this.addOrRebuild(e);e.enableEditing()}}}disable(){this.#Me=!0;this.div.style.pointerEvents="none";const t=new Set;for(const e of this.#Te.values()){e.disableEditing();if(e.annotationElementId&&null===e.serialize()){this.getEditableAnnotation(e.annotationElementId)?.show();e.remove()}else t.add(e.annotationElementId)}if(this.#xe){const e=this.#xe.getEditableAnnotations();for(const n of e){const{id:e}=n.data;t.has(e)||this.#Fe.isDeletedAnnotationElement(e)||n.show()}}this.#Re();this.isEmpty&&(this.div.hidden=!0);this.#Me=!1}getEditableAnnotation(t){return this.#xe?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#Fe.getActive()!==t&&this.#Fe.setActiveEditor(t)}enableClick(){this.div.addEventListener("pointerdown",this.#Ce);this.div.addEventListener("pointerup",this.#we)}disableClick(){this.div.removeEventListener("pointerdown",this.#Ce);this.div.removeEventListener("pointerup",this.#we)}attach(t){this.#Te.set(t.id,t);const{annotationElementId:e}=t;e&&this.#Fe.isDeletedAnnotationElement(e)&&this.#Fe.removeDeletedAnnotationElement(t)}detach(t){this.#Te.delete(t.id);this.#Se?.removePointerInTextLayer(t.contentDiv);!this.#Me&&t.annotationElementId&&this.#Fe.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#Fe.removeEditor(t);t.div.contains(document.activeElement)&&setTimeout((()=>{this.#Fe.focusMainContainer()}),0);t.div.remove();t.isAttachedToDOM=!1;this.#ke||this.addInkEditorIfNeeded(!1)}changeParent(t){if(t.parent!==this){if(t.annotationElementId){this.#Fe.addDeletedAnnotationElement(t.annotationElementId);r.AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){this.changeParent(t);this.#Fe.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded();this.#Fe.addToAnnotationStorage(t)}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)){t._focusEventsAllowed=!1;setTimeout((()=>{if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0});e.focus()}}),0)}t._structTreeParentId=this.#Se?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?t.rebuild():this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#Fe.getId()}#Ie(t){switch(this.#Fe.getMode()){case i.AnnotationEditorType.FREETEXT:return new s.FreeTextEditor(t);case i.AnnotationEditorType.INK:return new a.InkEditor(t);case i.AnnotationEditorType.STAMP:return new l.StampEditor(t)}return null}pasteEditor(t,e){this.#Fe.updateToolbar(t);this.#Fe.updateMode(t);const{offsetX:n,offsetY:i}=this.#Oe(),r=this.getNextId(),s=this.#Ie({parent:this,id:r,x:n,y:i,uiManager:this.#Fe,isCentered:!0,...e});s&&this.add(s)}deserialize(t){switch(t.annotationType??t.annotationEditorType){case i.AnnotationEditorType.FREETEXT:return s.FreeTextEditor.deserialize(t,this,this.#Fe);case i.AnnotationEditorType.INK:return a.InkEditor.deserialize(t,this,this.#Fe);case i.AnnotationEditorType.STAMP:return l.StampEditor.deserialize(t,this,this.#Fe)}return null}#De(t,e){const n=this.getNextId(),i=this.#Ie({parent:this,id:n,x:t.offsetX,y:t.offsetY,uiManager:this.#Fe,isCentered:e});i&&this.add(i);return i}#Oe(){const{x:t,y:e,width:n,height:i}=this.div.getBoundingClientRect(),r=Math.max(0,t),s=Math.max(0,e),a=(r+Math.min(window.innerWidth,t+n))/2-t,o=(s+Math.min(window.innerHeight,e+i))/2-e,[l,c]=this.viewport.rotation%180==0?[a,o]:[o,a];return{offsetX:l,offsetY:c}}addNewEditor(){this.#De(this.#Oe(),!0)}setSelected(t){this.#Fe.setSelected(t)}toggleSelected(t){this.#Fe.toggleSelected(t)}isSelected(t){return this.#Fe.isSelected(t)}unselect(t){this.#Fe.unselect(t)}pointerup(t){const{isMac:e}=i.FeatureTest.platform;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&this.#Pe){this.#Pe=!1;this.#Ee?this.#Fe.getMode()!==i.AnnotationEditorType.STAMP?this.#De(t,!1):this.#Fe.unselectAll():this.#Ee=!0}}pointerdown(t){if(this.#Pe){this.#Pe=!1;return}const{isMac:e}=i.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Pe=!0;const n=this.#Fe.getActive();this.#Ee=!n||n.isEmpty()}findNewParent(t,e,n){const i=this.#Fe.findParent(e,n);if(null===i||i===this)return!1;i.changeParent(t);return!0}destroy(){if(this.#Fe.getActive()?.parent===this){this.#Fe.commitOrRemove();this.#Fe.setActiveEditor(null)}for(const t of this.#Te.values()){this.#Se?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#Te.clear();this.#Fe.removeLayer(this)}#Re(){this.#ke=!0;for(const t of this.#Te.values())t.isEmpty()&&t.remove();this.#ke=!1}render(t){let{viewport:e}=t;this.viewport=e;(0,o.setLayerDimensions)(this.div,e);for(const t of this.#Fe.getEditors(this.pageIndex))this.add(t);this.updateMode()}update(t){let{viewport:e}=t;this.#Fe.commitOrRemove();this.viewport=e;(0,o.setLayerDimensions)(this.div,{rotation:e.rotation});this.updateMode()}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}}e.AnnotationEditorLayer=AnnotationEditorLayer},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.FreeTextEditor=void 0;n(89);var i=n(1),r=n(165),s=n(164),a=n(198);class FreeTextEditor extends s.AnnotationEditor{#Le=this.editorDivBlur.bind(this);#Ne=this.editorDivFocus.bind(this);#Be=this.editorDivInput.bind(this);#je=this.editorDivKeydown.bind(this);#Ue;#ze="";#We=`${this.id}-editor`;#He;#qe=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=r.AnnotationEditorUIManager.TRANSLATE_SMALL,n=r.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,i.shadow)(this,"_keyboardManager",new r.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-n,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[n,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-n],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,n],checker:arrowChecker}]]))}static _type="freetext";constructor(t){super({...t,name:"freeTextEditor"});this.#Ue=t.color||FreeTextEditor._defaultColor||s.AnnotationEditor._defaultLineColor;this.#He=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t){s.AnnotationEditor.initialize(t,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const e=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(e.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case i.AnnotationEditorParamsType.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case i.AnnotationEditorParamsType.FREETEXT_SIZE:this.#Ge(e);break;case i.AnnotationEditorParamsType.FREETEXT_COLOR:this.#Ve(e)}}static get defaultPropertiesToUpdate(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[i.AnnotationEditorParamsType.FREETEXT_COLOR,FreeTextEditor._defaultColor||s.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[i.AnnotationEditorParamsType.FREETEXT_SIZE,this.#He],[i.AnnotationEditorParamsType.FREETEXT_COLOR,this.#Ue]]}#Ge(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-this.#He)*this.parentScale);this.#He=t;this.#$e()},e=this.#He;this.addCommands({cmd:()=>{setFontsize(t)},undo:()=>{setFontsize(e)},mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Ve(t){const e=this.#Ue;this.addCommands({cmd:()=>{this.#Ue=this.editorDiv.style.color=t},undo:()=>{this.#Ue=this.editorDiv.style.color=e},mustExec:!0,type:i.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#He)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(!this.isInEditMode()){this.parent.setEditingState(!1);this.parent.updateToolbar(i.AnnotationEditorType.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");this.editorDiv.addEventListener("keydown",this.#je);this.editorDiv.addEventListener("focus",this.#Ne);this.editorDiv.addEventListener("blur",this.#Le);this.editorDiv.addEventListener("input",this.#Be)}}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#We);this._isDraggable=!0;this.editorDiv.removeEventListener("keydown",this.#je);this.editorDiv.removeEventListener("focus",this.#Ne);this.editorDiv.removeEventListener("blur",this.#Le);this.editorDiv.removeEventListener("input",this.#Be);this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freeTextEditing")}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(){if(this.width)this.#Xe();else{this.enableEditMode();this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freeTextEditing")}super.remove()}#Ke(){const t=this.editorDiv.getElementsByTagName("div");if(0===t.length)return this.editorDiv.innerText;const e=[];for(const n of t)e.push(n.innerText.replace(/\r\n?|\n/,""));return e.join("\n")}#$e(){const[t,e]=this.parentDimensions;let n;if(this.isAttachedToDOM)n=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,i=e.style.display;e.style.display="hidden";t.div.append(this.div);n=e.getBoundingClientRect();e.remove();e.style.display=i}if(this.rotation%180==this.parentRotation%180){this.width=n.width/t;this.height=n.height/e}else{this.width=n.height/t;this.height=n.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#ze,e=this.#ze=this.#Ke().trimEnd();if(t===e)return;const setText=t=>{this.#ze=t;if(t){this.#Ye();this._uiManager.rebuild(this);this.#$e()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#$e()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#We);this.enableEditing();s.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then((t=>this.editorDiv?.setAttribute("aria-label",t)));s.AnnotationEditor._l10nPromise.get("free_text2_default_content").then((t=>this.editorDiv?.setAttribute("default-content",t)));this.editorDiv.contentEditable=!0;const{style:n}=this.editorDiv;n.fontSize=`calc(${this.#He}px * var(--scale-factor))`;n.color=this.#Ue;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);(0,r.bindEvents)(this,this.div,["dblclick","keydown"]);if(this.width){const[n,i]=this.parentDimensions;if(this.annotationElementId){const{position:r}=this.#qe;let[s,a]=this.getInitialTranslation();[s,a]=this.pageTranslationToScreen(s,a);const[o,l]=this.pageDimensions,[c,h]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(r[0]-c)/o;u=e+this.height-(r[1]-h)/l;break;case 90:d=t+(r[0]-c)/o;u=e-(r[1]-h)/l;[s,a]=[a,-s];break;case 180:d=t-this.width+(r[0]-c)/o;u=e-(r[1]-h)/l;[s,a]=[-s,-a];break;case 270:d=t+(r[0]-c-this.height*l)/o;u=e+(r[1]-h-this.width*o)/l;[s,a]=[-a,s]}this.setAt(d*n,u*i,s,a)}else this.setAt(t*n,e*i,this.width*n,this.height*i);this.#Ye();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}#Ye(){this.editorDiv.replaceChildren();if(this.#ze)for(const t of this.#ze.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}get contentDiv(){return this.editorDiv}static deserialize(t,e,n){let r=null;if(t instanceof a.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:n},rect:s,rotation:a,id:o},textContent:l,textPosition:c,parent:{page:{pageNumber:h}}}=t;if(!l||0===l.length)return null;r=t={annotationType:i.AnnotationEditorType.FREETEXT,color:Array.from(n),fontSize:e,value:l.join("\n"),position:c,pageIndex:h-1,rect:s,rotation:a,id:o,deleted:!1}}const s=super.deserialize(t,e,n);s.#He=t.fontSize;s.#Ue=i.Util.makeHexColor(...t.color);s.#ze=t.value;s.annotationElementId=t.id||null;s.#qe=r;return s}serialize(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const e=FreeTextEditor._internalPadding*this.parentScale,n=this.getRect(e,e),r=s.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#Ue),a={annotationType:i.AnnotationEditorType.FREETEXT,color:r,fontSize:this.#He,value:this.#ze,pageIndex:this.pageIndex,rect:n,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t)return a;if(this.annotationElementId&&!this.#Je(a))return null;a.id=this.annotationElementId;return a}#Je(t){const{value:e,fontSize:n,color:i,rect:r,pageIndex:s}=this.#qe;return t.value!==e||t.fontSize!==n||t.rect.some(((t,e)=>Math.abs(t-r[e])>=1))||t.color.some(((t,e)=>t!==i[e]))||t.pageIndex!==s}#Xe(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.annotationElementId)return;this.#$e();if(!t&&(0===this.width||0===this.height)){setTimeout((()=>this.#Xe(!0)),0);return}const e=FreeTextEditor._internalPadding*this.parentScale;this.#qe.rect=this.getRect(e,e)}}e.FreeTextEditor=FreeTextEditor},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.StampAnnotationElement=e.InkAnnotationElement=e.FreeTextAnnotationElement=e.AnnotationLayer=void 0;n(89);n(125);n(136);n(138);n(141);n(143);n(145);n(147);var i=n(1),r=n(168),s=n(163),a=n(199),o=n(200),l=n(201);const c=1e3,h=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case i.AnnotationType.LINK:return new LinkAnnotationElement(t);case i.AnnotationType.TEXT:return new TextAnnotationElement(t);case i.AnnotationType.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case i.AnnotationType.POPUP:return new PopupAnnotationElement(t);case i.AnnotationType.FREETEXT:return new FreeTextAnnotationElement(t);case i.AnnotationType.LINE:return new LineAnnotationElement(t);case i.AnnotationType.SQUARE:return new SquareAnnotationElement(t);case i.AnnotationType.CIRCLE:return new CircleAnnotationElement(t);case i.AnnotationType.POLYLINE:return new PolylineAnnotationElement(t);case i.AnnotationType.CARET:return new CaretAnnotationElement(t);case i.AnnotationType.INK:return new InkAnnotationElement(t);case i.AnnotationType.POLYGON:return new PolygonAnnotationElement(t);case i.AnnotationType.HIGHLIGHT:return new HighlightAnnotationElement(t);case i.AnnotationType.UNDERLINE:return new UnderlineAnnotationElement(t);case i.AnnotationType.SQUIGGLY:return new SquigglyAnnotationElement(t);case i.AnnotationType.STRIKEOUT:return new StrikeOutAnnotationElement(t);case i.AnnotationType.STAMP:return new StampAnnotationElement(t);case i.AnnotationType.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#Qe=!1;constructor(t){let{isRenderable:e=!1,ignoreBorder:n=!1,createQuadrilaterals:i=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(n));i&&this._createQuadrilaterals()}static _hasPopupData(t){let{titleObj:e,contentsObj:n,richText:i}=t;return!!(e?.str||n?.str||i?.str)}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}_createContainer(t){const{data:e,parent:{page:n,viewport:r}}=this,s=document.createElement("section");s.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||(s.tabIndex=c);s.style.zIndex=this.parent.zIndex++;this.data.popupRef&&s.setAttribute("aria-haspopup","dialog");e.noRotate&&s.classList.add("norotate");const{pageWidth:a,pageHeight:o,pageX:l,pageY:h}=r.rawDims;if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,s);return s}const{width:d,height:u}=getRectDims(e.rect),p=i.Util.normalizeRect([e.rect[0],n.view[3]-e.rect[1]+n.view[1],e.rect[2],n.view[3]-e.rect[3]+n.view[1]]);if(!t&&e.borderStyle.width>0){s.style.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,n=e.borderStyle.verticalCornerRadius;if(t>0||n>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${n}px * var(--scale-factor))`;s.style.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${d}px * var(--scale-factor)) / calc(${u}px * var(--scale-factor))`;s.style.borderRadius=t}switch(e.borderStyle.style){case i.AnnotationBorderStyleType.SOLID:s.style.borderStyle="solid";break;case i.AnnotationBorderStyleType.DASHED:s.style.borderStyle="dashed";break;case i.AnnotationBorderStyleType.BEVELED:(0,i.warn)("Unimplemented border style: beveled");break;case i.AnnotationBorderStyleType.INSET:(0,i.warn)("Unimplemented border style: inset");break;case i.AnnotationBorderStyleType.UNDERLINE:s.style.borderBottomStyle="solid"}const r=e.borderColor||null;if(r){this.#Qe=!0;s.style.borderColor=i.Util.makeHexColor(0|r[0],0|r[1],0|r[2])}else s.style.borderWidth=0}s.style.left=100*(p[0]-l)/a+"%";s.style.top=100*(p[1]-h)/o+"%";const{rotation:f}=e;if(e.hasOwnCanvas||0===f){s.style.width=100*d/a+"%";s.style.height=100*u/o+"%"}else this.setRotation(f,s);return s}setRotation(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.container;if(!this.data.rect)return;const{pageWidth:n,pageHeight:i}=this.parent.viewport.rawDims,{width:r,height:s}=getRectDims(this.data.rect);let a,o;if(t%180==0){a=100*r/n;o=100*s/i}else{a=100*s/n;o=100*r/i}e.style.width=`${a}%`;e.style.height=`${o}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,n)=>{const i=n.detail[t],r=i[0],s=i.slice(1);n.target.style[e]=a.ColorConverters[`${r}_HTML`](s);this.annotationStorage.setValue(this.data.id,{[e]:a.ColorConverters[`${r}_rgb`](s)})};return(0,i.shadow)(this,"_commonActions",{display:t=>{const{display:e}=t.detail,n=e%2==1;this.container.style.visibility=n?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:n,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const n=this._commonActions;for(const i of Object.keys(e.detail)){const r=t[i]||n[i];r?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const n=this._commonActions;for(const[i,r]of Object.entries(e)){const s=n[i];if(s){s({detail:{[i]:r},target:t});delete e[i]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,n,i,r]=this.data.rect;if(1===t.length){const[,{x:s,y:a},{x:o,y:l}]=t[0];if(i===s&&r===a&&e===o&&n===l)return}const{style:s}=this.container;let a;if(this.#Qe){const{borderColor:t,borderWidth:e}=s;s.borderWidth=0;a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=i-e,l=r-n,{svgFactory:c}=this,h=c.createElement("svg");h.classList.add("quadrilateralsContainer");h.setAttribute("width",0);h.setAttribute("height",0);const d=c.createElement("defs");h.append(d);const u=c.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");d.append(u);for(const[,{x:n,y:i},{x:s,y:h}]of t){const t=c.createElement("rect"),d=(s-e)/o,p=(r-i)/l,f=(n-s)/o,g=(i-h)/l;t.setAttribute("x",d);t.setAttribute("y",p);t.setAttribute("width",f);t.setAttribute("height",g);u.append(t);a?.push(`<rect vector-effect="non-scaling-stroke" x="${d}" y="${p}" width="${f}" height="${g}"/>`)}if(this.#Qe){a.push("</g></svg>')");s.backgroundImage=a.join("")}this.container.append(h);this.container.style.clipPath=`url(#${p})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const n=new PopupAnnotationElement({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(n.render())}render(){(0,i.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const n=[];if(this._fieldObjects){const r=this._fieldObjects[t];if(r)for(const{page:t,id:s,exportValues:a}of r){if(-1===t)continue;if(s===e)continue;const r="string"==typeof a?a:null,o=document.querySelector(`[data-element-id="${s}"]`);!o||h.has(o)?n.push({id:s,exportValue:r,domElement:o}):(0,i.warn)(`_getElementsByName - element not allowed: ${s}`)}return n}for(const i of document.getElementsByName(t)){const{exportValue:t}=i,r=i.getAttribute("data-element-id");r!==e&&(h.has(i)&&n.push({id:r,exportValue:t,domElement:i}))}return n}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class LinkAnnotationElement extends AnnotationElement{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,n=document.createElement("a");n.setAttribute("data-element-id",t.id);let i=!1;if(t.url){e.addLinkAttributes(n,t.url,t.newWindow);i=!0}else if(t.action){this._bindNamedAction(n,t.action);i=!0}else if(t.attachment){this._bindAttachment(n,t.attachment);i=!0}else if(t.setOCGState){this.#Ze(n,t.setOCGState);i=!0}else if(t.dest){this._bindLink(n,t.dest);i=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(n,t);i=!0}if(t.resetForm){this._bindResetFormAction(n,t.resetForm);i=!0}else if(this.isTooltipOnly&&!i){this._bindLink(n,"");i=!0}}this.container.classList.add("linkAnnotation");i&&this.container.append(n);return this.container}#tn(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#tn()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};this.#tn()}_bindAttachment(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.downloadManager?.openOrDownloadData(this.container,e.content,e.filename);return!1};this.#tn()}#Ze(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};this.#tn()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const n=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const i of Object.keys(e.actions)){const r=n.get(i);r&&(t[r]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:i}});return!1})}t.onclick||(t.onclick=()=>!1);this.#tn()}_bindResetFormAction(t,e){const n=t.onclick;n||(t.href=this.linkService.getAnchorUrl(""));this.#tn();if(this._fieldObjects)t.onclick=()=>{n?.();const{fields:t,refs:r,include:s}=e,a=[];if(0!==t.length||0!==r.length){const e=new Set(r);for(const n of t){const t=this._fieldObjects[n]||[];for(const{id:n}of t)e.add(n)}for(const t of Object.values(this._fieldObjects))for(const n of t)e.has(n.id)===s&&a.push(n)}else for(const t of Object.values(this._fieldObjects))a.push(...t);const o=this.annotationStorage,l=[];for(const t of a){const{id:e}=t;l.push(e);switch(t.type){case"text":{const n=t.defaultValue||"";o.setValue(e,{value:n});break}case"checkbox":case"radiobutton":{const n=t.defaultValue===t.exportValues;o.setValue(e,{value:n});break}case"combobox":case"listbox":{const n=t.defaultValue||"";o.setValue(e,{value:n});break}default:continue}const n=document.querySelector(`[data-element-id="${e}"]`);n&&(h.has(n)?n.dispatchEvent(new Event("resetform")):(0,i.warn)(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}});return!1};else{(0,i.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');n||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.alt="[{{type}} Annotation]";t.dataset.l10nId="text_annotation_type";t.dataset.l10nArgs=JSON.stringify({type:this.data.name});!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){this.data.alternativeText&&(this.container.title=this.data.alternativeText);return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){const{isWin:e,isMac:n}=i.FeatureTest.platform;return e&&t.ctrlKey||n&&t.metaKey}_setEventListener(t,e,n,i,r){n.includes("mouse")?t.addEventListener(n,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(n,(t=>{if("blur"===n){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===n){if(e.focused)return;e.focused=!0}r&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:i,value:r(t)}})}))}_setEventListeners(t,e,n,i){for(const[r,s]of n)if("Action"===s||this.data.actions?.[s]){"Focus"!==s&&"Blur"!==s||(e||={focused:!1});this._setEventListener(t,e,r,s,i);"Focus"!==s||this.data.actions?.Blur?"Blur"!==s||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":i.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:n}=this.data.defaultAppearanceData,r=this.data.defaultAppearanceData.fontSize||9,s=t.style;let a;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(i.LINE_FACTOR*r))||1);a=Math.min(r,roundToOneDecimal(e/i.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);a=Math.min(r,roundToOneDecimal(t/i.LINE_FACTOR))}s.fontSize=`calc(${a}px * var(--scale-factor))`;s.color=i.Util.makeHexColor(n[0],n[1],n[2]);null!==this.data.textAlignment&&(s.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,n,i){const r=this.annotationStorage;for(const s of this._getElementsByName(t.name,t.id)){s.domElement&&(s.domElement[e]=n);r.setValue(s.id,{[i]:n})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let n=null;if(this.renderForms){const i=t.getValue(e,{value:this.data.fieldValue});let r=i.value||"";const s=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;s&&r.length>s&&(r=r.slice(0,s));let a=i.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:r,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){n=document.createElement("textarea");n.textContent=a??r;this.data.doNotScroll&&(n.style.overflowY="hidden")}else{n=document.createElement("input");n.type="text";n.setAttribute("value",a??r);this.data.doNotScroll&&(n.style.overflowX="hidden")}this.data.hasOwnCanvas&&(n.hidden=!0);h.add(n);n.setAttribute("data-element-id",e);n.disabled=this.data.readOnly;n.name=this.data.fieldName;n.tabIndex=c;this._setRequired(n,this.data.required);s&&(n.maxLength=s);n.addEventListener("input",(i=>{t.setValue(e,{value:i.target.value});this.setPropertyOnSiblings(n,"value",i.target.value,"value");o.formattedValue=null}));n.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";n.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){n.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue);o.lastCommittedValue=e.value;o.commitKey=1;o.focused=!0}));n.addEventListener("updatefromsandbox",(n=>{this.showElementAndHideCanvas(n.target);const i={value(n){o.userValue=n.detail.value??"";t.setValue(e,{value:o.userValue.toString()});n.target.value=o.userValue},formattedValue(n){const{formattedValue:i}=n.detail;o.formattedValue=i;null!=i&&n.target!==document.activeElement&&(n.target.value=i);t.setValue(e,{formattedValue:i})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:n=>{const{charLimit:i}=n.detail,{target:r}=n;if(0===i){r.removeAttribute("maxLength");return}r.setAttribute("maxLength",i);let s=o.userValue;if(s&&!(s.length<=i)){s=s.slice(0,i);r.value=o.userValue=s;t.setValue(e,{value:s});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:1,selStart:r.selectionStart,selEnd:r.selectionEnd}})}}};this._dispatchEventFromSandbox(i,n)}));n.addEventListener("keydown",(t=>{o.commitKey=1;let n=-1;"Escape"===t.key?n=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):n=2;if(-1===n)return;const{value:i}=t.target;if(o.lastCommittedValue!==i){o.lastCommittedValue=i;o.userValue=i;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:n,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const i=blurListener;blurListener=null;n.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;o.focused=!1;const{value:n}=t.target;o.userValue=n;o.lastCommittedValue!==n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});i(t)}));this.data.actions?.Keystroke&&n.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:n,target:i}=t,{value:r,selectionStart:s,selectionEnd:a}=i;let l=s,c=a;switch(t.inputType){case"deleteWordBackward":{const t=r.substring(0,s).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=r.substring(s).match(/^[^\w]*\w*/);t&&(c+=t[0].length);break}case"deleteContentBackward":s===a&&(l-=1);break;case"deleteContentForward":s===a&&(c+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,change:n||"",willCommit:!1,selStart:l,selEnd:c}})}));this._setEventListeners(n,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&n.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/s;n.classList.add("comb");n.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{n=document.createElement("div");n.textContent=this.data.fieldValue;n.style.verticalAlign="middle";n.style.display="table-cell"}this._setTextStyle(n);this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,n=e.id;let i=t.getValue(n,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof i){i="Off"!==i;t.setValue(n,{value:i})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const r=document.createElement("input");h.add(r);r.setAttribute("data-element-id",n);r.disabled=e.readOnly;this._setRequired(r,this.data.required);r.type="checkbox";r.name=e.fieldName;i&&r.setAttribute("checked",!0);r.setAttribute("exportValue",e.exportValue);r.tabIndex=c;r.addEventListener("change",(i=>{const{name:r,checked:s}=i.target;for(const i of this._getElementsByName(r,n)){const n=s&&i.exportValue===e.exportValue;i.domElement&&(i.domElement.checked=n);t.setValue(i.id,{value:n})}t.setValue(n,{value:s})}));r.addEventListener("resetform",(t=>{const n=e.defaultFieldValue||"Off";t.target.checked=n===e.exportValue}));if(this.enableScripting&&this.hasJSActions){r.addEventListener("updatefromsandbox",(e=>{const i={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(n,{value:e.target.checked})}};this._dispatchEventFromSandbox(i,e)}));this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(r);this._setDefaultPropertiesFromJS(r);this.container.append(r);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,n=e.id;let i=t.getValue(n,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof i){i=i!==e.buttonValue;t.setValue(n,{value:i})}const r=document.createElement("input");h.add(r);r.setAttribute("data-element-id",n);r.disabled=e.readOnly;this._setRequired(r,this.data.required);r.type="radio";r.name=e.fieldName;i&&r.setAttribute("checked",!0);r.tabIndex=c;r.addEventListener("change",(e=>{const{name:i,checked:r}=e.target;for(const e of this._getElementsByName(i,n))t.setValue(e.id,{value:!1});t.setValue(n,{value:r})}));r.addEventListener("resetform",(t=>{const n=e.defaultFieldValue;t.target.checked=null!=n&&n===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const i=e.buttonValue;r.addEventListener("updatefromsandbox",(e=>{const r={value:e=>{const r=i===e.detail.value;for(const i of this._getElementsByName(e.target.name)){const e=r&&i.id===n;i.domElement&&(i.domElement.checked=e);t.setValue(i.id,{value:e})}}};this._dispatchEventFromSandbox(r,e)}));this._setEventListeners(r,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(r);this._setDefaultPropertiesFromJS(r);this.container.append(r);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");this.data.alternativeText&&(t.title=this.data.alternativeText);const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,n=t.getValue(e,{value:this.data.fieldValue}),i=document.createElement("select");h.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;this._setRequired(i,this.data.required);i.name=this.data.fieldName;i.tabIndex=c;let r=this.data.combo&&this.data.options.length>0;if(!this.data.combo){i.size=this.data.options.length;this.data.multiSelect&&(i.multiple=!0)}i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of i.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(n.value.includes(t.exportValue)){e.setAttribute("selected",!0);r=!1}i.append(e)}let s=null;if(r){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);i.prepend(t);s=()=>{t.remove();i.removeEventListener("input",s);s=null};i.addEventListener("input",s)}const getValue=t=>{const e=t?"value":"textContent",{options:n,multiple:r}=i;return r?Array.prototype.filter.call(n,(t=>t.selected)).map((t=>t[e])):-1===n.selectedIndex?null:n[n.selectedIndex][e]};let a=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){i.addEventListener("updatefromsandbox",(n=>{const r={value(n){s?.();const r=n.detail.value,o=new Set(Array.isArray(r)?r:[r]);for(const t of i.options)t.selected=o.has(t.value);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},multipleSelection(t){i.multiple=!0},remove(n){const r=i.options,s=n.detail.remove;r[s].selected=!1;i.remove(s);if(r.length>0){-1===Array.prototype.findIndex.call(r,(t=>t.selected))&&(r[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(n)});a=getValue(!1)},clear(n){for(;0!==i.length;)i.remove(0);t.setValue(e,{value:null,items:[]});a=getValue(!1)},insert(n){const{index:r,displayValue:s,exportValue:o}=n.detail.insert,l=i.children[r],c=document.createElement("option");c.textContent=s;c.value=o;l?l.before(c):i.append(c);t.setValue(e,{value:getValue(!0),items:getItems(n)});a=getValue(!1)},items(n){const{items:r}=n.detail;for(;0!==i.length;)i.remove(0);for(const t of r){const{displayValue:e,exportValue:n}=t,r=document.createElement("option");r.textContent=e;r.value=n;i.append(r)}i.options.length>0&&(i.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(n)});a=getValue(!1)},indices(n){const i=new Set(n.detail.indices);for(const t of n.target.options)t.selected=i.has(t.index);t.setValue(e,{value:getValue(!0)});a=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(r,n)}));i.addEventListener("input",(n=>{const i=getValue(!0);t.setValue(e,{value:i});n.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,changeEx:i,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(i,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else i.addEventListener("input",(function(n){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:n}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=n}render(){this.container.classList.add("popupAnnotation");const t=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const n of this.elements){n.popup=t;e.push(n.data.id);n.addHighlightArea()}this.container.setAttribute("aria-controls",e.map((t=>`${i.AnnotationPrefix}${t}`)).join(","));return this.container}}class PopupElement{#en=null;#nn=this.#in.bind(this);#rn=this.#sn.bind(this);#an=this.#on.bind(this);#ln=this.#cn.bind(this);#Ue=null;#Rt=null;#hn=null;#dn=null;#un=null;#pn=null;#fn=!1;#gn=null;#mn=null;#bn=null;#vn=null;#yn=!1;constructor(t){let{container:e,color:n,elements:i,titleObj:s,modificationDate:a,contentsObj:o,richText:l,parent:c,rect:h,parentRect:d,open:u}=t;this.#Rt=e;this.#vn=s;this.#hn=o;this.#bn=l;this.#un=c;this.#Ue=n;this.#mn=h;this.#pn=d;this.#dn=i;const p=r.PDFDateString.toDateObject(a);p&&(this.#en=c.l10n.get("annotation_date_string",{date:p.toLocaleDateString(),time:p.toLocaleTimeString()}));this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger){t.addEventListener("click",this.#ln);t.addEventListener("mouseenter",this.#an);t.addEventListener("mouseleave",this.#rn);t.classList.add("popupTriggerArea")}for(const t of i)t.container?.addEventListener("keydown",this.#nn);this.#Rt.hidden=!0;u&&this.#cn()}render(){if(this.#gn)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:n,pageX:r,pageY:s}}}=this.#un,a=this.#gn=document.createElement("div");a.className="popup";if(this.#Ue){const t=a.style.outlineColor=i.Util.makeHexColor(...this.#Ue);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))a.style.backgroundColor=`color-mix(in srgb, ${t} 30%, white)`;else{const t=.7;a.style.backgroundColor=i.Util.makeHexColor(...this.#Ue.map((e=>Math.floor(t*(255-e)+e))))}}const o=document.createElement("span");o.className="header";const c=document.createElement("h1");o.append(c);({dir:c.dir,str:c.textContent}=this.#vn);a.append(o);if(this.#en){const t=document.createElement("span");t.classList.add("popupDate");this.#en.then((e=>{t.textContent=e}));o.append(t)}const h=this.#hn,d=this.#bn;if(!d?.str||h?.str&&h.str!==d.str){const t=this._formatContents(h);a.append(t)}else{l.XfaLayer.render({xfaHtml:d.html,intent:"richText",div:a});a.lastChild.classList.add("richText","popupContent")}let u=!!this.#pn,p=u?this.#pn:this.#mn;for(const t of this.#dn)if(!p||null!==i.Util.intersect(t.data.rect,p)){p=t.data.rect;u=!0;break}const f=i.Util.normalizeRect([p[0],t[3]-p[1]+t[1],p[2],t[3]-p[3]+t[1]]),g=u?p[2]-p[0]+5:0,m=f[0]+g,b=f[1],{style:v}=this.#Rt;v.left=100*(m-r)/e+"%";v.top=100*(b-s)/n+"%";this.#Rt.append(a)}_formatContents(t){let{str:e,dir:n}=t;const i=document.createElement("p");i.classList.add("popupContent");i.dir=n;const r=e.split(/(?:\r\n?|\n)/);for(let t=0,e=r.length;t<e;++t){const n=r[t];i.append(document.createTextNode(n));t<e-1&&i.append(document.createElement("br"))}return i}#in(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#fn)&&this.#cn()}#cn(){this.#fn=!this.#fn;if(this.#fn){this.#on();this.#Rt.addEventListener("click",this.#ln);this.#Rt.addEventListener("keydown",this.#nn)}else{this.#sn();this.#Rt.removeEventListener("click",this.#ln);this.#Rt.removeEventListener("keydown",this.#nn)}}#on(){this.#gn||this.render();if(this.isVisible)this.#fn&&this.#Rt.classList.add("focused");else{this.#Rt.hidden=!1;this.#Rt.style.zIndex=parseInt(this.#Rt.style.zIndex)+1e3}}#sn(){this.#Rt.classList.remove("focused");if(!this.#fn&&this.isVisible){this.#Rt.hidden=!0;this.#Rt.style.zIndex=parseInt(this.#Rt.style.zIndex)-1e3}}forceHide(){this.#yn=this.isVisible;this.#yn&&(this.#Rt.hidden=!0)}maybeShow(){if(this.#yn){this.#yn=!1;this.#Rt.hidden=!1}}get isVisible(){return!1===this.#Rt.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=i.AnnotationEditorType.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const n=document.createElement("span");n.textContent=e;t.append(n)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}}e.FreeTextAnnotationElement=FreeTextAnnotationElement;class LineAnnotationElement extends AnnotationElement{#_n=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:n}=getRectDims(t.rect),i=this.svgFactory.create(e,n,!0),r=this.#_n=this.svgFactory.createElement("svg:line");r.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);r.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);r.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);r.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);r.setAttribute("stroke-width",t.borderStyle.width||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");i.append(r);this.container.append(i);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#_n}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#An=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:n}=getRectDims(t.rect),i=this.svgFactory.create(e,n,!0),r=t.borderStyle.width,s=this.#An=this.svgFactory.createElement("svg:rect");s.setAttribute("x",r/2);s.setAttribute("y",r/2);s.setAttribute("width",e-r);s.setAttribute("height",n-r);s.setAttribute("stroke-width",r||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");i.append(s);this.container.append(i);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#An}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#Sn=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:n}=getRectDims(t.rect),i=this.svgFactory.create(e,n,!0),r=t.borderStyle.width,s=this.#Sn=this.svgFactory.createElement("svg:ellipse");s.setAttribute("cx",e/2);s.setAttribute("cy",n/2);s.setAttribute("rx",e/2-r/2);s.setAttribute("ry",n/2-r/2);s.setAttribute("stroke-width",r||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");i.append(s);this.container.append(i);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Sn}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#En=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:n}=getRectDims(t.rect),i=this.svgFactory.create(e,n,!0);let r=[];for(const e of t.vertices){const n=e.x-t.rect[0],i=t.rect[3]-e.y;r.push(n+","+i)}r=r.join(" ");const s=this.#En=this.svgFactory.createElement(this.svgElementName);s.setAttribute("points",r);s.setAttribute("stroke-width",t.borderStyle.width||1);s.setAttribute("stroke","transparent");s.setAttribute("fill","transparent");i.append(s);this.container.append(i);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#En}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#xn=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType=i.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:n}=getRectDims(t.rect),i=this.svgFactory.create(e,n,!0);for(const e of t.inkLists){let n=[];for(const i of e){const e=i.x-t.rect[0],r=t.rect[3]-i.y;n.push(`${e},${r}`)}n=n.join(" ");const r=this.svgFactory.createElement(this.svgElementName);this.#xn.push(r);r.setAttribute("points",n);r.setAttribute("stroke-width",t.borderStyle.width||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");!t.popupRef&&this.hasPopupData&&this._createPopup();i.append(r)}this.container.append(i);return this.container}getElementsToTriggerPopup(){return this.#xn}addHighlightArea(){this.container.classList.add("highlightArea")}}e.InkAnnotationElement=InkAnnotationElement;class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("stampAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}e.StampAnnotationElement=StampAnnotationElement;class FileAttachmentAnnotationElement extends AnnotationElement{#wn=null;constructor(t){super(t,{isRenderable:!0});const{filename:e,content:n}=this.data.file;this.filename=(0,r.getFilenameFromUrl)(e,!0);this.content=n;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,filename:e,content:n})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let n;if(e.hasAppearance||0===e.fillAlpha)n=document.createElement("div");else{n=document.createElement("img");n.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(n.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}n.addEventListener("dblclick",this.#Cn.bind(this));this.#wn=n;const{isMac:r}=i.FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(r?t.metaKey:t.ctrlKey)&&this.#Cn()}));!e.popupRef&&this.hasPopupData?this._createPopup():n.classList.add("popupTriggerArea");t.append(n);return t}getElementsToTriggerPopup(){return this.#wn}addHighlightArea(){this.container.classList.add("highlightArea")}#Cn(){this.downloadManager?.openOrDownloadData(this.container,this.content,this.filename)}}e.AnnotationLayer=class AnnotationLayer{#Se=null;#Tn=null;#Pn=new Map;constructor(t){let{div:e,accessibilityManager:n,annotationCanvasMap:i,l10n:r,page:s,viewport:a}=t;this.div=e;this.#Se=n;this.#Tn=i;this.l10n=r;this.page=s;this.viewport=a;this.zIndex=0;this.l10n||=o.NullL10n}#kn(t,e){const n=t.firstChild||t;n.id=`${i.AnnotationPrefix}${e}`;this.div.append(t);this.#Se?.moveElementInDOM(this.div,t,n,!1)}async render(t){const{annotations:e}=t,n=this.div;(0,r.setLayerDimensions)(n,this.viewport);const a=new Map,o={data:null,layer:n,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new r.DOMSVGFactory,annotationStorage:t.annotationStorage||new s.AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===i.AnnotationType.POPUP;if(e){const e=a.get(t.id);if(!e)continue;o.elements=e}else{const{width:e,height:n}=getRectDims(t.rect);if(e<=0||n<=0)continue}o.data=t;const n=AnnotationElementFactory.create(o);if(!n.isRenderable)continue;if(!e&&t.popupRef){const e=a.get(t.popupRef);e?e.push(n):a.set(t.popupRef,[n])}n.annotationEditorType>0&&this.#Pn.set(n.data.id,n);const r=n.render();t.hidden&&(r.style.visibility="hidden");this.#kn(r,t.id)}this.#Mn();await this.l10n.translate(n)}update(t){let{viewport:e}=t;const n=this.div;this.viewport=e;(0,r.setLayerDimensions)(n,{rotation:e.rotation});this.#Mn();n.hidden=!1}#Mn(){if(!this.#Tn)return;const t=this.div;for(const[e,n]of this.#Tn){const i=t.querySelector(`[data-annotation-id="${e}"]`);if(!i)continue;const{firstChild:r}=i;r?"CANVAS"===r.nodeName?r.replaceWith(n):r.before(n):i.append(n)}this.#Tn.clear()}getEditableAnnotations(){return Array.from(this.#Pn.values())}getEditableAnnotation(t){return this.#Pn.get(t)}}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.ColorConverters=void 0;function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}e.ColorConverters=class ColorConverters{static CMYK_G(t){let[e,n,i,r]=t;return["G",1-Math.min(1,.3*e+.59*i+.11*n+r)]}static G_CMYK(t){let[e]=t;return["CMYK",0,0,0,1-e]}static G_RGB(t){let[e]=t;return["RGB",e,e,e]}static G_rgb(t){let[e]=t;e=scaleAndClamp(e);return[e,e,e]}static G_HTML(t){let[e]=t;const n=makeColorComp(e);return`#${n}${n}${n}`}static RGB_G(t){let[e,n,i]=t;return["G",.3*e+.59*n+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB(t){let[e,n,i,r]=t;return["RGB",1-Math.min(1,e+r),1-Math.min(1,i+r),1-Math.min(1,n+r)]}static CMYK_rgb(t){let[e,n,i,r]=t;return[scaleAndClamp(1-Math.min(1,e+r)),scaleAndClamp(1-Math.min(1,i+r)),scaleAndClamp(1-Math.min(1,n+r))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK(t){let[e,n,i]=t;const r=1-e,s=1-n,a=1-i;return["CMYK",r,s,a,Math.min(r,s,a)]}}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.NullL10n=void 0;e.getL10nFallback=getL10nFallback;const n={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative",print_progress_percent:"{{progress}}%"};function getL10nFallback(t,e){switch(t){case"find_match_count":t=`find_match_count[${1===e.total?"one":"other"}]`;break;case"find_match_count_limit":t=`find_match_count_limit[${1===e.limit?"one":"other"}]`}return n[t]||""}const i={getLanguage:async()=>"en-us",getDirection:async()=>"ltr",async get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return function formatL10nValue(t,e){return e?t.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,((t,n)=>n in e?e[n]:"{{"+n+"}}")):t}(arguments.length>2&&void 0!==arguments[2]?arguments[2]:getL10nFallback(t,e),e)},async translate(t){}};e.NullL10n=i},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.XfaLayer=void 0;n(89);var i=n(194);e.XfaLayer=class XfaLayer{static setupStorage(t,e,n,i,r){const s=i.getValue(e,{value:null});switch(n.name){case"textarea":null!==s.value&&(t.textContent=s.value);if("print"===r)break;t.addEventListener("input",(t=>{i.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===n.attributes.type||"checkbox"===n.attributes.type){s.value===n.attributes.xfaOn?t.setAttribute("checked",!0):s.value===n.attributes.xfaOff&&t.removeAttribute("checked");if("print"===r)break;t.addEventListener("change",(t=>{i.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==s.value&&t.setAttribute("value",s.value);if("print"===r)break;t.addEventListener("input",(t=>{i.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==s.value){t.setAttribute("value",s.value);for(const t of n.children)t.attributes.value===s.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const n=t.target.options,r=-1===n.selectedIndex?"":n[n.selectedIndex].value;i.setValue(e,{value:r})}))}}static setAttributes(t){let{html:e,element:n,storage:i=null,intent:r,linkService:s}=t;const{attributes:a}=n,o=e instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${r}`);for(const[t,n]of Object.entries(a))if(null!=n)switch(t){case"class":n.length&&e.setAttribute(t,n.join(" "));break;case"dataId":break;case"id":e.setAttribute("data-element-id",n);break;case"style":Object.assign(e.style,n);break;case"textContent":e.textContent=n;break;default:(!o||"href"!==t&&"newWindow"!==t)&&e.setAttribute(t,n)}o&&s.addLinkAttributes(e,a.href,a.newWindow);i&&a.dataId&&this.setupStorage(e,a.dataId,n,i)}static render(t){const e=t.annotationStorage,n=t.linkService,r=t.xfaHtml,s=t.intent||"display",a=document.createElement(r.name);r.attributes&&this.setAttributes({html:a,element:r,intent:s,linkService:n});const o=[[r,-1,a]],l=t.div;l.append(a);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=e}"richText"!==s&&l.setAttribute("class","xfaLayer xfaFont");const c=[];for(;o.length>0;){const[t,r,a]=o.at(-1);if(r+1===t.children.length){o.pop();continue}const l=t.children[++o.at(-1)[1]];if(null===l)continue;const{name:h}=l;if("#text"===h){const t=document.createTextNode(l.value);c.push(t);a.append(t);continue}const d=l?.attributes?.xmlns?document.createElementNS(l.attributes.xmlns,h):document.createElement(h);a.append(d);l.attributes&&this.setAttributes({html:d,element:l,storage:e,intent:s,linkService:n});if(l.children&&l.children.length>0)o.push([l,-1,d]);else if(l.value){const t=document.createTextNode(l.value);i.XfaText.shouldBuildText(h)&&c.push(t);d.append(t)}}for(const t of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.InkEditor=void 0;n(89);n(2);var i=n(1),r=n(164),s=n(198),a=n(168),o=n(165);class InkEditor extends r.AnnotationEditor{#Fn=0;#Rn=0;#Dn=this.canvasPointermove.bind(this);#In=this.canvasPointerleave.bind(this);#On=this.canvasPointerup.bind(this);#Ln=this.canvasPointerdown.bind(this);#Nn=new Path2D;#Bn=!1;#jn=!1;#Un=!1;#zn=null;#Wn=0;#Hn=0;#qn=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";constructor(t){super({...t,name:"inkEditor"});this.color=t.color||null;this.thickness=t.thickness||null;this.opacity=t.opacity||null;this.paths=[];this.bezierPath2D=[];this.allRawPaths=[];this.currentPath=[];this.scaleFactor=1;this.translationX=this.translationY=0;this.x=0;this.y=0;this._willKeepAspectRatio=!0}static initialize(t){r.AnnotationEditor.initialize(t,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(t,e){switch(t){case i.AnnotationEditorParamsType.INK_THICKNESS:InkEditor._defaultThickness=e;break;case i.AnnotationEditorParamsType.INK_COLOR:InkEditor._defaultColor=e;break;case i.AnnotationEditorParamsType.INK_OPACITY:InkEditor._defaultOpacity=e/100}}updateParams(t,e){switch(t){case i.AnnotationEditorParamsType.INK_THICKNESS:this.#Gn(e);break;case i.AnnotationEditorParamsType.INK_COLOR:this.#Ve(e);break;case i.AnnotationEditorParamsType.INK_OPACITY:this.#Vn(e)}}static get defaultPropertiesToUpdate(){return[[i.AnnotationEditorParamsType.INK_THICKNESS,InkEditor._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,InkEditor._defaultColor||r.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*InkEditor._defaultOpacity)]]}get propertiesToUpdate(){return[[i.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||InkEditor._defaultThickness],[i.AnnotationEditorParamsType.INK_COLOR,this.color||InkEditor._defaultColor||r.AnnotationEditor._defaultLineColor],[i.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??InkEditor._defaultOpacity))]]}#Gn(t){const e=this.thickness;this.addCommands({cmd:()=>{this.thickness=t;this.#$n()},undo:()=>{this.thickness=e;this.#$n()},mustExec:!0,type:i.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#Ve(t){const e=this.color;this.addCommands({cmd:()=>{this.color=t;this.#Xn()},undo:()=>{this.color=e;this.#Xn()},mustExec:!0,type:i.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#Vn(t){t/=100;const e=this.opacity;this.addCommands({cmd:()=>{this.opacity=t;this.#Xn()},undo:()=>{this.opacity=e;this.#Xn()},mustExec:!0,type:i.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){if(!this.canvas){this.#Kn();this.#Yn()}if(!this.isAttachedToDOM){this.parent.add(this);this.#Jn()}this.#$n()}}}remove(){if(null!==this.canvas){this.isEmpty()||this.commit();this.canvas.width=this.canvas.height=0;this.canvas.remove();this.canvas=null;this.#zn.disconnect();this.#zn=null;super.remove()}}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this);super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,n=this.width*t,i=this.height*e;this.setDimensions(n,i)}enableEditMode(){if(!this.#Bn&&null!==this.canvas){super.enableEditMode();this._isDraggable=!1;this.canvas.addEventListener("pointerdown",this.#Ln)}}disableEditMode(){if(this.isInEditMode()&&null!==this.canvas){super.disableEditMode();this._isDraggable=!this.isEmpty();this.div.classList.remove("editing");this.canvas.removeEventListener("pointerdown",this.#Ln)}}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#Qn(){const{parentRotation:t,parentDimensions:[e,n]}=this;switch(t){case 90:return[0,n,n,e];case 180:return[e,n,e,n];case 270:return[e,0,n,e];default:return[0,0,e,n]}}#Zn(){const{ctx:t,color:e,opacity:n,thickness:i,parentScale:r,scaleFactor:s}=this;t.lineWidth=i*r/s;t.lineCap="round";t.lineJoin="round";t.miterLimit=10;t.strokeStyle=`${e}${(0,o.opacityToHex)(n)}`}#ti(t,e){this.canvas.addEventListener("contextmenu",a.noContextMenu);this.canvas.addEventListener("pointerleave",this.#In);this.canvas.addEventListener("pointermove",this.#Dn);this.canvas.addEventListener("pointerup",this.#On);this.canvas.removeEventListener("pointerdown",this.#Ln);this.isEditing=!0;if(!this.#Un){this.#Un=!0;this.#Jn();this.thickness||=InkEditor._defaultThickness;this.color||=InkEditor._defaultColor||r.AnnotationEditor._defaultLineColor;this.opacity??=InkEditor._defaultOpacity}this.currentPath.push([t,e]);this.#jn=!1;this.#Zn();this.#qn=()=>{this.#ei();this.#qn&&window.requestAnimationFrame(this.#qn)};window.requestAnimationFrame(this.#qn)}#ni(t,e){const[n,i]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===n&&e===i)return;const r=this.currentPath;let s=this.#Nn;r.push([t,e]);this.#jn=!0;if(r.length<=2){s.moveTo(...r[0]);s.lineTo(t,e)}else{if(3===r.length){this.#Nn=s=new Path2D;s.moveTo(...r[0])}this.#ii(s,...r.at(-3),...r.at(-2),t,e)}}#ri(){if(0===this.currentPath.length)return;const t=this.currentPath.at(-1);this.#Nn.lineTo(...t)}#si(t,e){this.#qn=null;t=Math.min(Math.max(t,0),this.canvas.width);e=Math.min(Math.max(e,0),this.canvas.height);this.#ni(t,e);this.#ri();let n;if(1!==this.currentPath.length)n=this.#ai();else{const i=[t,e];n=[[i,i.slice(),i.slice(),i]]}const i=this.#Nn,r=this.currentPath;this.currentPath=[];this.#Nn=new Path2D;this.addCommands({cmd:()=>{this.allRawPaths.push(r);this.paths.push(n);this.bezierPath2D.push(i);this.rebuild()},undo:()=>{this.allRawPaths.pop();this.paths.pop();this.bezierPath2D.pop();if(0===this.paths.length)this.remove();else{if(!this.canvas){this.#Kn();this.#Yn()}this.#$n()}},mustExec:!0})}#ei(){if(!this.#jn)return;this.#jn=!1;const t=Math.ceil(this.thickness*this.parentScale),e=this.currentPath.slice(-3),n=e.map((t=>t[0])),i=e.map((t=>t[1])),{ctx:r}=(Math.min(...n),Math.max(...n),Math.min(...i),Math.max(...i),this);r.save();r.clearRect(0,0,this.canvas.width,this.canvas.height);for(const t of this.bezierPath2D)r.stroke(t);r.stroke(this.#Nn);r.restore()}#ii(t,e,n,i,r,s,a){const o=(e+i)/2,l=(n+r)/2,c=(i+s)/2,h=(r+a)/2;t.bezierCurveTo(o+2*(i-o)/3,l+2*(r-l)/3,c+2*(i-c)/3,h+2*(r-h)/3,c,h)}#ai(){const t=this.currentPath;if(t.length<=2)return[[t[0],t[0],t.at(-1),t.at(-1)]];const e=[];let n,[i,r]=t[0];for(n=1;n<t.length-2;n++){const[s,a]=t[n],[o,l]=t[n+1],c=(s+o)/2,h=(a+l)/2,d=[i+2*(s-i)/3,r+2*(a-r)/3],u=[c+2*(s-c)/3,h+2*(a-h)/3];e.push([[i,r],d,u,[c,h]]);[i,r]=[c,h]}const[s,a]=t[n],[o,l]=t[n+1],c=[i+2*(s-i)/3,r+2*(a-r)/3],h=[o+2*(s-o)/3,l+2*(a-l)/3];e.push([[i,r],c,h,[o,l]]);return e}#Xn(){if(this.isEmpty()){this.#oi();return}this.#Zn();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,t.width,t.height);this.#oi();for(const t of this.bezierPath2D)e.stroke(t)}commit(){if(!this.#Bn){super.commit();this.isEditing=!1;this.disableEditMode();this.setInForeground();this.#Bn=!0;this.div.classList.add("disabled");this.#$n(!0);this.makeResizable();this.parent.addInkEditorIfNeeded(!0);this.moveInDOM();this.div.focus({preventScroll:!0})}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);this.enableEditMode()}}canvasPointerdown(t){if(0===t.button&&this.isInEditMode()&&!this.#Bn){this.setInForeground();t.preventDefault();"mouse"!==t.type&&this.div.focus();this.#ti(t.offsetX,t.offsetY)}}canvasPointermove(t){t.preventDefault();this.#ni(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault();this.#li(t)}canvasPointerleave(t){this.#li(t)}#li(t){this.canvas.removeEventListener("pointerleave",this.#In);this.canvas.removeEventListener("pointermove",this.#Dn);this.canvas.removeEventListener("pointerup",this.#On);this.canvas.addEventListener("pointerdown",this.#Ln);setTimeout((()=>{this.canvas.removeEventListener("contextmenu",a.noContextMenu)}),10);this.#si(t.offsetX,t.offsetY);this.addToAnnotationStorage();this.setInBackground()}#Kn(){this.canvas=document.createElement("canvas");this.canvas.width=this.canvas.height=0;this.canvas.className="inkEditorCanvas";r.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then((t=>this.canvas?.setAttribute("aria-label",t)));this.div.append(this.canvas);this.ctx=this.canvas.getContext("2d")}#Yn(){this.#zn=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}));this.#zn.observe(this.div)}get isResizable(){return!this.isEmpty()&&this.#Bn}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();r.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then((t=>this.div?.setAttribute("aria-label",t)));const[n,i,s,a]=this.#Qn();this.setAt(n,i,0,0);this.setDims(s,a);this.#Kn();if(this.width){const[n,i]=this.parentDimensions;this.setAspectRatio(this.width*n,this.height*i);this.setAt(t*n,e*i,this.width*n,this.height*i);this.#Un=!0;this.#Jn();this.setDims(this.width*n,this.height*i);this.#Xn();this.div.classList.add("disabled")}else{this.div.classList.add("editing");this.enableEditMode()}this.#Yn();return this.div}#Jn(){if(!this.#Un)return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t);this.canvas.height=Math.ceil(this.height*e);this.#oi()}setDimensions(t,e){const n=Math.round(t),i=Math.round(e);if(this.#Wn===n&&this.#Hn===i)return;this.#Wn=n;this.#Hn=i;this.canvas.style.visibility="hidden";const[r,s]=this.parentDimensions;this.width=t/r;this.height=e/s;this.fixAndSetPosition();this.#Bn&&this.#ci(t,e);this.#Jn();this.#Xn();this.canvas.style.visibility="visible";this.fixDims()}#ci(t,e){const n=this.#hi(),i=(t-n)/this.#Rn,r=(e-n)/this.#Fn;this.scaleFactor=Math.min(i,r)}#oi(){const t=this.#hi()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#di(t){const e=new Path2D;for(let n=0,i=t.length;n<i;n++){const[i,r,s,a]=t[n];0===n&&e.moveTo(...i);e.bezierCurveTo(r[0],r[1],s[0],s[1],a[0],a[1])}return e}static#ui(t,e,n){const[i,r,s,a]=e;switch(n){case 0:for(let e=0,n=t.length;e<n;e+=2){t[e]+=i;t[e+1]=a-t[e+1]}break;case 90:for(let e=0,n=t.length;e<n;e+=2){const n=t[e];t[e]=t[e+1]+i;t[e+1]=n+r}break;case 180:for(let e=0,n=t.length;e<n;e+=2){t[e]=s-t[e];t[e+1]+=r}break;case 270:for(let e=0,n=t.length;e<n;e+=2){const n=t[e];t[e]=s-t[e+1];t[e+1]=a-n}break;default:throw new Error("Invalid rotation")}return t}static#pi(t,e,n){const[i,r,s,a]=e;switch(n){case 0:for(let e=0,n=t.length;e<n;e+=2){t[e]-=i;t[e+1]=a-t[e+1]}break;case 90:for(let e=0,n=t.length;e<n;e+=2){const n=t[e];t[e]=t[e+1]-r;t[e+1]=n-i}break;case 180:for(let e=0,n=t.length;e<n;e+=2){t[e]=s-t[e];t[e+1]-=r}break;case 270:for(let e=0,n=t.length;e<n;e+=2){const n=t[e];t[e]=a-t[e+1];t[e+1]=s-n}break;default:throw new Error("Invalid rotation")}return t}#fi(t,e,n,i){const r=[],s=this.thickness/2,a=t*e+s,o=t*n+s;for(const e of this.paths){const n=[],s=[];for(let i=0,r=e.length;i<r;i++){const[l,c,h,d]=e[i],u=t*l[0]+a,p=t*l[1]+o,f=t*c[0]+a,g=t*c[1]+o,m=t*h[0]+a,b=t*h[1]+o,v=t*d[0]+a,y=t*d[1]+o;if(0===i){n.push(u,p);s.push(u,p)}n.push(f,g,m,b,v,y);s.push(f,g);i===r-1&&s.push(v,y)}r.push({bezier:InkEditor.#ui(n,i,this.rotation),points:InkEditor.#ui(s,i,this.rotation)})}return r}#gi(){let t=1/0,e=-1/0,n=1/0,r=-1/0;for(const s of this.paths)for(const[a,o,l,c]of s){const s=i.Util.bezierBoundingBox(...a,...o,...l,...c);t=Math.min(t,s[0]);n=Math.min(n,s[1]);e=Math.max(e,s[2]);r=Math.max(r,s[3])}return[t,n,e,r]}#hi(){return this.#Bn?Math.ceil(this.thickness*this.parentScale):0}#$n(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return;if(!this.#Bn){this.#Xn();return}const e=this.#gi(),n=this.#hi();this.#Rn=Math.max(r.AnnotationEditor.MIN_SIZE,e[2]-e[0]);this.#Fn=Math.max(r.AnnotationEditor.MIN_SIZE,e[3]-e[1]);const i=Math.ceil(n+this.#Rn*this.scaleFactor),s=Math.ceil(n+this.#Fn*this.scaleFactor),[a,o]=this.parentDimensions;this.width=i/a;this.height=s/o;this.setAspectRatio(i,s);const l=this.translationX,c=this.translationY;this.translationX=-e[0];this.translationY=-e[1];this.#Jn();this.#Xn();this.#Wn=i;this.#Hn=s;this.setDims(i,s);const h=t?n/this.scaleFactor/2:0;this.translate(l-this.translationX-h,c-this.translationY-h)}static deserialize(t,e,n){if(t instanceof s.InkAnnotationElement)return null;const a=super.deserialize(t,e,n);a.thickness=t.thickness;a.color=i.Util.makeHexColor(...t.color);a.opacity=t.opacity;const[o,l]=a.pageDimensions,c=a.width*o,h=a.height*l,d=a.parentScale,u=t.thickness/2;a.#Bn=!0;a.#Wn=Math.round(c);a.#Hn=Math.round(h);const{paths:p,rect:f,rotation:g}=t;for(let{bezier:t}of p){t=InkEditor.#pi(t,f,g);const e=[];a.paths.push(e);let n=d*(t[0]-u),i=d*(t[1]-u);for(let r=2,s=t.length;r<s;r+=6){const s=d*(t[r]-u),a=d*(t[r+1]-u),o=d*(t[r+2]-u),l=d*(t[r+3]-u),c=d*(t[r+4]-u),h=d*(t[r+5]-u);e.push([[n,i],[s,a],[o,l],[c,h]]);n=c;i=h}const r=this.#di(e);a.bezierPath2D.push(r)}const m=a.#gi();a.#Rn=Math.max(r.AnnotationEditor.MIN_SIZE,m[2]-m[0]);a.#Fn=Math.max(r.AnnotationEditor.MIN_SIZE,m[3]-m[1]);a.#ci(c,h);return a}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=r.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:i.AnnotationEditorType.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#fi(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}e.InkEditor=InkEditor},(t,e,n)=>{Object.defineProperty(e,"__esModule",{value:!0});e.StampEditor=void 0;n(149);n(152);var i=n(1),r=n(164),s=n(168),a=n(198);class StampEditor extends r.AnnotationEditor{#mi=null;#bi=null;#vi=null;#yi=null;#_i=null;#Ai=null;#zn=null;#Si=null;#Ei=!1;#xi=!1;static _type="stamp";constructor(t){super({...t,name:"stampEditor"});this.#yi=t.bitmapUrl;this.#_i=t.bitmapFile}static initialize(t){r.AnnotationEditor.initialize(t)}static get supportedTypes(){return(0,i.shadow)(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((t=>`image/${t}`)))}static get supportedTypesStr(){return(0,i.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(i.AnnotationEditorType.STAMP,{bitmapFile:t.getAsFile()})}#wi(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(t){this.#mi=t.bitmap;if(!e){this.#bi=t.id;this.#Ei=t.isSvg}this.#Kn()}else this.remove()}#Ci(){this.#vi=null;this._uiManager.enableWaiting(!1);this.#Ai&&this.div.focus()}#Ti(){if(this.#bi){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#bi).then((t=>this.#wi(t,!0))).finally((()=>this.#Ci()));return}if(this.#yi){const t=this.#yi;this.#yi=null;this._uiManager.enableWaiting(!0);this.#vi=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#wi(t))).finally((()=>this.#Ci()));return}if(this.#_i){const t=this.#_i;this.#_i=null;this._uiManager.enableWaiting(!0);this.#vi=this._uiManager.imageManager.getFromFile(t).then((t=>this.#wi(t))).finally((()=>this.#Ci()));return}const t=document.createElement("input");t.type="file";t.accept=StampEditor.supportedTypesStr;this.#vi=new Promise((e=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#wi(e)}else this.remove();e()}));t.addEventListener("cancel",(()=>{this.remove();e()}))})).finally((()=>this.#Ci()));t.click()}remove(){if(this.#bi){this.#mi=null;this._uiManager.imageManager.deleteId(this.#bi);this.#Ai?.remove();this.#Ai=null;this.#zn?.disconnect();this.#zn=null}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#bi&&this.#Ti();this.isAttachedToDOM||this.parent.add(this)}}else this.#bi&&this.#Ti()}onceAdded(){this._isDraggable=!0;this.div.focus()}isEmpty(){return!(this.#vi||this.#mi||this.#yi||this.#_i)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.#mi?this.#Kn():this.#Ti();if(this.width){const[n,i]=this.parentDimensions;this.setAt(t*n,e*i,this.width*n,this.height*i)}return this.div}#Kn(){const{div:t}=this;let{width:e,height:n}=this.#mi;const[i,r]=this.pageDimensions,s=.75;if(this.width){e=this.width*i;n=this.height*r}else if(e>s*i||n>s*r){const t=Math.min(s*i/e,s*r/n);e*=t;n*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/i,n*o/r);this._uiManager.enableWaiting(!1);const l=this.#Ai=document.createElement("canvas");t.append(l);t.hidden=!1;this.#Pi(e,n);this.#Yn();if(!this.#xi){this.parent.addUndoableEditor(this);this.#xi=!0}this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}});this.addAltTextButton()}#ki(t,e){const[n,i]=this.parentDimensions;this.width=t/n;this.height=e/i;this.setDims(t,e);this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;null!==this.#Si&&clearTimeout(this.#Si);this.#Si=setTimeout((()=>{this.#Si=null;this.#Pi(t,e)}),200)}#Mi(t,e){const{width:n,height:i}=this.#mi;let r=n,s=i,a=this.#mi;for(;r>2*t||s>2*e;){const n=r,i=s;r>2*t&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));s>2*e&&(s=s>=16384?Math.floor(s/2)-1:Math.ceil(s/2));const o=new OffscreenCanvas(r,s);o.getContext("2d").drawImage(a,0,0,n,i,0,0,r,s);a=o.transferToImageBitmap()}return a}#Pi(t,e){t=Math.ceil(t);e=Math.ceil(e);const n=this.#Ai;if(!n||n.width===t&&n.height===e)return;n.width=t;n.height=e;const i=this.#Ei?this.#mi:this.#Mi(t,e),r=n.getContext("2d");r.filter=this._uiManager.hcmFilter;r.drawImage(i,0,0,i.width,i.height,0,0,t,e)}#Fi(t){if(t){if(this.#Ei){const t=this._uiManager.imageManager.getSvgUrl(this.#bi);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#mi);t.getContext("2d").drawImage(this.#mi,0,0);return t.toDataURL()}if(this.#Ei){const[t,e]=this.pageDimensions,n=Math.round(this.width*t*s.PixelsPerInch.PDF_TO_CSS_UNITS),i=Math.round(this.height*e*s.PixelsPerInch.PDF_TO_CSS_UNITS),r=new OffscreenCanvas(n,i);r.getContext("2d").drawImage(this.#mi,0,0,this.#mi.width,this.#mi.height,0,0,n,i);return r.transferToImageBitmap()}return structuredClone(this.#mi)}#Yn(){this.#zn=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.#ki(e.width,e.height)}));this.#zn.observe(this.div)}static deserialize(t,e,n){if(t instanceof a.StampAnnotationElement)return null;const i=super.deserialize(t,e,n),{rect:r,bitmapUrl:s,bitmapId:o,isSvg:l,accessibilityData:c}=t;o&&n.imageManager.isValidId(o)?i.#bi=o:i.#yi=s;i.#Ei=l;const[h,d]=i.pageDimensions;i.width=(r[2]-r[0])/h;i.height=(r[3]-r[1])/d;c&&(i.altTextData=c);return i}serialize(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.isEmpty())return null;const n={annotationType:i.AnnotationEditorType.STAMP,bitmapId:this.#bi,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#Ei,structTreeParentId:this._structTreeParentId};if(t){n.bitmapUrl=this.#Fi(!0);n.accessibilityData=this.altTextData;return n}const{decorative:r,altText:s}=this.altTextData;!r&&s&&(n.accessibilityData={type:"Figure",alt:s});if(null===e)return n;e.stamps||=new Map;const a=this.#Ei?(n.rect[2]-n.rect[0])*(n.rect[3]-n.rect[1]):null;if(e.stamps.has(this.#bi)){if(this.#Ei){const t=e.stamps.get(this.#bi);if(a>t.area){t.area=a;t.serialized.bitmap.close();t.serialized.bitmap=this.#Fi(!1)}}}else{e.stamps.set(this.#bi,{area:a,serialized:n});n.bitmap=this.#Fi(!1)}return n}}e.StampEditor=StampEditor}],__webpack_module_cache__={};function __w_pdfjs_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var n=__webpack_module_cache__[t]={exports:{}};__webpack_modules__[t].call(n.exports,n,n.exports,__w_pdfjs_require__);return n.exports}var __webpack_exports__={};(()=>{var t=__webpack_exports__;Object.defineProperty(t,"__esModule",{value:!0});Object.defineProperty(t,"AbortException",{enumerable:!0,get:function(){return e.AbortException}});Object.defineProperty(t,"AnnotationEditorLayer",{enumerable:!0,get:function(){return s.AnnotationEditorLayer}});Object.defineProperty(t,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}});Object.defineProperty(t,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}});Object.defineProperty(t,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return a.AnnotationEditorUIManager}});Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return o.AnnotationLayer}});Object.defineProperty(t,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}});Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}});Object.defineProperty(t,"DOMSVGFactory",{enumerable:!0,get:function(){return i.DOMSVGFactory}});Object.defineProperty(t,"FeatureTest",{enumerable:!0,get:function(){return e.FeatureTest}});Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return l.GlobalWorkerOptions}});Object.defineProperty(t,"ImageKind",{enumerable:!0,get:function(){return e.ImageKind}});Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}});Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}});Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return e.OPS}});Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return n.PDFDataRangeTransport}});Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return i.PDFDateString}});Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return n.PDFWorker}});Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}});Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}});Object.defineProperty(t,"PixelsPerInch",{enumerable:!0,get:function(){return i.PixelsPerInch}});Object.defineProperty(t,"PromiseCapability",{enumerable:!0,get:function(){return e.PromiseCapability}});Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return i.RenderingCancelledException}});Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return n.SVGGraphics}});Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}});Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return e.Util}});Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}});Object.defineProperty(t,"XfaLayer",{enumerable:!0,get:function(){return c.XfaLayer}});Object.defineProperty(t,"build",{enumerable:!0,get:function(){return n.build}});Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}});Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return n.getDocument}});Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return i.getFilenameFromUrl}});Object.defineProperty(t,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return i.getPdfFilenameFromUrl}});Object.defineProperty(t,"getXfaPageViewport",{enumerable:!0,get:function(){return i.getXfaPageViewport}});Object.defineProperty(t,"isDataScheme",{enumerable:!0,get:function(){return i.isDataScheme}});Object.defineProperty(t,"isPdfFile",{enumerable:!0,get:function(){return i.isPdfFile}});Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return i.loadScript}});Object.defineProperty(t,"noContextMenu",{enumerable:!0,get:function(){return i.noContextMenu}});Object.defineProperty(t,"normalizeUnicode",{enumerable:!0,get:function(){return e.normalizeUnicode}});Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return r.renderTextLayer}});Object.defineProperty(t,"setLayerDimensions",{enumerable:!0,get:function(){return i.setLayerDimensions}});Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return e.shadow}});Object.defineProperty(t,"updateTextLayer",{enumerable:!0,get:function(){return r.updateTextLayer}});Object.defineProperty(t,"version",{enumerable:!0,get:function(){return n.version}});var e=__w_pdfjs_require__(1),n=__w_pdfjs_require__(124),i=__w_pdfjs_require__(168),r=__w_pdfjs_require__(195),s=__w_pdfjs_require__(196),a=__w_pdfjs_require__(165),o=__w_pdfjs_require__(198),l=__w_pdfjs_require__(176),c=__w_pdfjs_require__(201)})();return __webpack_exports__})()));