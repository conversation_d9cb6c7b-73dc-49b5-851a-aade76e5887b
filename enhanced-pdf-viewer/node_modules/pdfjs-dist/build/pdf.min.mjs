/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */var t={d:(e,i)=>{for(var s in i)t.o(i,s)&&!t.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:i[s]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},__webpack_exports__ = globalThis.pdfjsLib = {};t.d(__webpack_exports__,{AbortException:()=>AbortException,AnnotationEditorLayer:()=>AnnotationEditorLayer,AnnotationEditorParamsType:()=>g,AnnotationEditorType:()=>p,AnnotationEditorUIManager:()=>AnnotationEditorUIManager,AnnotationLayer:()=>AnnotationLayer,AnnotationMode:()=>u,CMapCompressionType:()=>q,ColorPicker:()=>ColorPicker,DOMSVGFactory:()=>DOMSVGFactory,DrawLayer:()=>DrawLayer,FeatureTest:()=>util_FeatureTest,GlobalWorkerOptions:()=>GlobalWorkerOptions,ImageKind:()=>x,InvalidPDFException:()=>InvalidPDFException,MissingPDFException:()=>MissingPDFException,OPS:()=>X,Outliner:()=>Outliner,PDFDataRangeTransport:()=>PDFDataRangeTransport,PDFDateString:()=>PDFDateString,PDFWorker:()=>PDFWorker,PasswordResponses:()=>K,PermissionFlag:()=>m,PixelsPerInch:()=>PixelsPerInch,RenderingCancelledException:()=>RenderingCancelledException,TextLayer:()=>TextLayer,UnexpectedResponseException:()=>UnexpectedResponseException,Util:()=>Util,VerbosityLevel:()=>G,XfaLayer:()=>XfaLayer,build:()=>Yt,createValidAbsoluteUrl:()=>createValidAbsoluteUrl,fetchData:()=>fetchData,getDocument:()=>getDocument,getFilenameFromUrl:()=>getFilenameFromUrl,getPdfFilenameFromUrl:()=>getPdfFilenameFromUrl,getXfaPageViewport:()=>getXfaPageViewport,isDataScheme:()=>isDataScheme,isPdfFile:()=>isPdfFile,noContextMenu:()=>noContextMenu,normalizeUnicode:()=>normalizeUnicode,renderTextLayer:()=>renderTextLayer,setLayerDimensions:()=>setLayerDimensions,shadow:()=>shadow,updateTextLayer:()=>updateTextLayer,version:()=>Kt});const e=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type),i=[1,0,0,1,0,0],s=[.001,0,0,.001,0,0],n=1.35,a=1,r=2,o=4,l=16,h=32,d=64,c=256,u={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3},p={DISABLE:-1,NONE:0,FREETEXT:3,HIGHLIGHT:9,STAMP:13,INK:15},g={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23,HIGHLIGHT_COLOR:31,HIGHLIGHT_DEFAULT_COLOR:32,HIGHLIGHT_THICKNESS:33,HIGHLIGHT_FREE:34,HIGHLIGHT_SHOW_ALL:35},m={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048},f=0,b=1,v=2,A=3,y=3,w=4,x={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3},_=1,E=2,C=3,S=4,T=5,M=6,k=7,P=8,D=9,F=10,R=11,I=12,L=13,O=14,N=15,B=16,H=17,z=20,U=1,j=2,$=3,V=4,W=5,G={ERRORS:0,WARNINGS:1,INFOS:5},q={NONE:0,BINARY:1},X={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91},K={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let Y=G.WARNINGS;function setVerbosityLevel(t){Number.isInteger(t)&&(Y=t)}function getVerbosityLevel(){return Y}function info(t){Y>=G.INFOS&&console.log(`Info: ${t}`)}function warn(t){Y>=G.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function assert(t,e){t||unreachable(e)}function createValidAbsoluteUrl(t,e=null,i=null){if(!t)return null;try{if(i&&"string"==typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=function stringToUTF8String(t){return decodeURIComponent(escape(t))}(t)}catch{}}const s=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(s))return s}catch{}return null}function shadow(t,e,i,s=!1){Object.defineProperty(t,e,{value:i,enumerable:!s,configurable:!0,writable:!1});return i}const Q=function BaseExceptionClosure(){function BaseException(t,e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();class PasswordException extends Q{constructor(t,e){super(t,"PasswordException");this.code=e}}class UnknownErrorException extends Q{constructor(t,e){super(t,"UnknownErrorException");this.details=e}}class InvalidPDFException extends Q{constructor(t){super(t,"InvalidPDFException")}}class MissingPDFException extends Q{constructor(t){super(t,"MissingPDFException")}}class UnexpectedResponseException extends Q{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}}class FormatError extends Q{constructor(t){super(t,"FormatError")}}class AbortException extends Q{constructor(t){super(t,"AbortException")}}function bytesToString(t){"object"==typeof t&&void 0!==t?.length||unreachable("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const s=[];for(let n=0;n<e;n+=i){const a=Math.min(n+i,e),r=t.subarray(n,a);s.push(String.fromCharCode.apply(null,r))}return s.join("")}function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let s=0;s<e;++s)i[s]=255&t.charCodeAt(s);return i}function objectFromMap(t){const e=Object.create(null);for(const[i,s]of t)e[i]=s;return e}class util_FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch{return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}static get platform(){return"undefined"!=typeof navigator&&"string"==typeof navigator?.platform?shadow(this,"platform",{isMac:navigator.platform.includes("Mac")}):shadow(this,"platform",{isMac:!1})}static get isCSSRoundSupported(){return shadow(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}const J=Array.from(Array(256).keys(),(t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,i){return`#${J[t]}${J[e]}${J[i]}`}static scaleMinMax(t,e){let i;if(t[0]){if(t[0]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[0];e[2]*=t[0];if(t[3]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[3];e[3]*=t[3]}else{i=e[0];e[0]=e[1];e[1]=i;i=e[2];e[2]=e[3];e[3]=i;if(t[1]<0){i=e[1];e[1]=e[3];e[3]=i}e[1]*=t[1];e[3]*=t[1];if(t[2]<0){i=e[0];e[0]=e[2];e[2]=i}e[0]*=t[2];e[2]*=t[2]}e[0]+=t[4];e[1]+=t[5];e[2]+=t[4];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i]}static getAxialAlignedBoundingBox(t,e){const i=this.applyTransform(t,e),s=this.applyTransform(t.slice(2,4),e),n=this.applyTransform([t[0],t[3]],e),a=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],s[0],n[0],a[0]),Math.min(i[1],s[1],n[1],a[1]),Math.max(i[0],s[0],n[0],a[0]),Math.max(i[1],s[1],n[1],a[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],s=t[0]*e[1]+t[1]*e[3],n=t[2]*e[0]+t[3]*e[2],a=t[2]*e[1]+t[3]*e[3],r=(i+a)/2,o=Math.sqrt((i+a)**2-4*(i*a-n*s))/2,l=r+o||1,h=r-o||1;return[Math.sqrt(l),Math.sqrt(h)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),s=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>s)return null;const n=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),a=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return n>a?null:[i,n,s,a]}static#t(t,e,i,s,n,a,r,o,l,h){if(l<=0||l>=1)return;const d=1-l,c=l*l,u=c*l,p=d*(d*(d*t+3*l*e)+3*c*i)+u*s,g=d*(d*(d*n+3*l*a)+3*c*r)+u*o;h[0]=Math.min(h[0],p);h[1]=Math.min(h[1],g);h[2]=Math.max(h[2],p);h[3]=Math.max(h[3],g)}static#e(t,e,i,s,n,a,r,o,l,h,d,c){if(Math.abs(l)<1e-12){Math.abs(h)>=1e-12&&this.#t(t,e,i,s,n,a,r,o,-d/h,c);return}const u=h**2-4*d*l;if(u<0)return;const p=Math.sqrt(u),g=2*l;this.#t(t,e,i,s,n,a,r,o,(-h+p)/g,c);this.#t(t,e,i,s,n,a,r,o,(-h-p)/g,c)}static bezierBoundingBox(t,e,i,s,n,a,r,o,l){if(l){l[0]=Math.min(l[0],t,r);l[1]=Math.min(l[1],e,o);l[2]=Math.max(l[2],t,r);l[3]=Math.max(l[3],e,o)}else l=[Math.min(t,r),Math.min(e,o),Math.max(t,r),Math.max(e,o)];this.#e(t,i,n,r,e,s,a,o,3*(3*(i-n)-t+r),6*(t-2*i+n),3*(i-t),l);this.#e(t,i,n,r,e,s,a,o,3*(3*(s-a)-e+o),6*(e-2*s+a),3*(s-e),l);return l}}let Z=null,tt=null;function normalizeUnicode(t){if(!Z){Z=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu;tt=new Map([["ﬅ","ſt"]])}return t.replaceAll(Z,((t,e,i)=>e?e.normalize("NFKC"):tt.get(i)))}const et="pdfjs_internal_id_",it=0,st=1,nt=2,at=3,rt=4,ot=5,lt=6,ht=7,dt=8;class BaseFilterFactory{constructor(){this.constructor===BaseFilterFactory&&unreachable("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addAlphaFilter(t){return"none"}addLuminosityFilter(t){return"none"}addHighlightHCMFilter(t,e,i,s,n){return"none"}destroy(t=!1){}}class BaseCanvasFactory{#i=!1;constructor({enableHWA:t=!1}={}){this.constructor===BaseCanvasFactory&&unreachable("Cannot initialize BaseCanvasFactory.");this.#i=t}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d",{willReadFrequently:!this.#i})}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){unreachable("Abstract method `_createCanvas` called.")}}class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!0}){this.constructor===BaseCMapReaderFactory&&unreachable("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":""),i=this.isCompressed?q.BINARY:q.NONE;return this._fetchData(e,i).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}_fetchData(t,e){unreachable("Abstract method `_fetchData` called.")}}class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.constructor===BaseStandardFontDataFactory&&unreachable("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetchData(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}_fetchData(t){unreachable("Abstract method `_fetchData` called.")}}class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&unreachable("Cannot initialize BaseSVGFactory.")}create(t,e,i=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const s=this._createSVG("svg:svg");s.setAttribute("version","1.1");if(!i){s.setAttribute("width",`${t}px`);s.setAttribute("height",`${e}px`)}s.setAttribute("preserveAspectRatio","none");s.setAttribute("viewBox",`0 0 ${t} ${e}`);return s}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){unreachable("Abstract method `_createSVG` called.")}}const ct="http://www.w3.org/2000/svg";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}async function fetchData(t,e="text"){if(isValidFetchUrl(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);switch(e){case"arraybuffer":return i.arrayBuffer();case"blob":return i.blob();case"json":return i.json()}return i.text()}return new Promise(((i,s)=>{const n=new XMLHttpRequest;n.open("GET",t,!0);n.responseType=e;n.onreadystatechange=()=>{if(n.readyState===XMLHttpRequest.DONE)if(200!==n.status&&0!==n.status)s(new Error(n.statusText));else{switch(e){case"arraybuffer":case"blob":case"json":i(n.response);return}i(n.responseText)}};n.send(null)}))}class DOMCMapReaderFactory extends BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t,this.isCompressed?"arraybuffer":"text").then((t=>({cMapData:t instanceof ArrayBuffer?new Uint8Array(t):stringToBytes(t),compressionType:e})))}}class DOMStandardFontDataFactory extends BaseStandardFontDataFactory{_fetchData(t){return fetchData(t,"arraybuffer").then((t=>new Uint8Array(t)))}}class DOMSVGFactory extends BaseSVGFactory{_createSVG(t){return document.createElementNS(ct,t)}}class PageViewport{constructor({viewBox:t,scale:e,rotation:i,offsetX:s=0,offsetY:n=0,dontFlip:a=!1}){this.viewBox=t;this.scale=e;this.rotation=i;this.offsetX=s;this.offsetY=n;const r=(t[2]+t[0])/2,o=(t[3]+t[1])/2;let l,h,d,c,u,p,g,m;(i%=360)<0&&(i+=360);switch(i){case 180:l=-1;h=0;d=0;c=1;break;case 90:l=0;h=1;d=1;c=0;break;case 270:l=0;h=-1;d=-1;c=0;break;case 0:l=1;h=0;d=0;c=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){d=-d;c=-c}if(0===l){u=Math.abs(o-t[1])*e+s;p=Math.abs(r-t[0])*e+n;g=(t[3]-t[1])*e;m=(t[2]-t[0])*e}else{u=Math.abs(r-t[0])*e+s;p=Math.abs(o-t[1])*e+n;g=(t[2]-t[0])*e;m=(t[3]-t[1])*e}this.transform=[l*e,h*e,d*e,c*e,u-l*e*r-d*e*o,p-h*e*r-c*e*o];this.width=g;this.height=m}get rawDims(){const{viewBox:t}=this;return shadow(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}convertToViewportPoint(t,e){return Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=Util.applyTransform([t[0],t[1]],this.transform),i=Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return Util.applyInverseTransform([t,e],this.transform)}}class RenderingCancelledException extends Q{constructor(t,e=0){super(t,"RenderingCancelledException");this.extraDelay=e}}function isDataScheme(t){const e=t.length;let i=0;for(;i<e&&""===t[i].trim();)i++;return"data:"===t.substring(i,i+5).toLowerCase()}function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)}function getFilenameFromUrl(t){[t]=t.split(/[#?]/,1);return t.substring(t.lastIndexOf("/")+1)}function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){warn('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const i=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,s=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let n=i.exec(s[1])||i.exec(s[2])||i.exec(s[3]);if(n){n=n[0];if(n.includes("%"))try{n=i.exec(decodeURIComponent(n))[0]}catch{}}return n||e}class StatTimer{started=Object.create(null);times=[];time(t){t in this.started&&warn(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||warn(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const{name:t}of this.times)e=Math.max(t.length,e);for(const{name:i,start:s,end:n}of this.times)t.push(`${i.padEnd(e)} ${n-s}ms\n`);return t.join("")}}function isValidFetchUrl(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function noContextMenu(t){t.preventDefault()}function deprecated(t){console.log("Deprecated API usage: "+t)}let ut;class PDFDateString{static toDateObject(t){if(!t||"string"!=typeof t)return null;ut||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=ut.exec(t);if(!e)return null;const i=parseInt(e[1],10);let s=parseInt(e[2],10);s=s>=1&&s<=12?s-1:0;let n=parseInt(e[3],10);n=n>=1&&n<=31?n:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let r=parseInt(e[5],10);r=r>=0&&r<=59?r:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;d=d>=0&&d<=59?d:0;if("-"===l){a+=h;r+=d}else if("+"===l){a-=h;r-=d}return new Date(Date.UTC(i,s,n,a,r,o))}}function getXfaPageViewport(t,{scale:e=1,rotation:i=0}){const{width:s,height:n}=t.attributes.style,a=[0,0,parseInt(s),parseInt(n)];return new PageViewport({viewBox:a,scale:e,rotation:i})}function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);warn(`Not a valid color format: "${t}"`);return[0,0,0]}function getCurrentTransform(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform();return[e,i,s,n,a,r]}function getCurrentTransformInverse(t){const{a:e,b:i,c:s,d:n,e:a,f:r}=t.getTransform().invertSelf();return[e,i,s,n,a,r]}function setLayerDimensions(t,e,i=!1,s=!0){if(e instanceof PageViewport){const{pageWidth:s,pageHeight:n}=e.rawDims,{style:a}=t,r=util_FeatureTest.isCSSRoundSupported,o=`var(--scale-factor) * ${s}px`,l=`var(--scale-factor) * ${n}px`,h=r?`round(${o}, 1px)`:`calc(${o})`,d=r?`round(${l}, 1px)`:`calc(${l})`;if(i&&e.rotation%180!=0){a.width=d;a.height=h}else{a.width=h;a.height=d}}s&&t.setAttribute("data-main-rotation",e.rotation)}class EditorToolbar{#s=null;#n=null;#a;#r=null;constructor(t){this.#a=t}render(){const t=this.#s=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");const e=this.#a._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.addEventListener("pointerdown",EditorToolbar.#o,{signal:e});const i=this.#r=document.createElement("div");i.className="buttons";t.append(i);const s=this.#a.toolbarPosition;if(s){const{style:e}=t,i="ltr"===this.#a._uiManager.direction?1-s[0]:s[0];e.insetInlineEnd=100*i+"%";e.top=`calc(${100*s[1]}% + var(--editor-toolbar-vert-offset))`}this.#l();return t}static#o(t){t.stopPropagation()}#h(t){this.#a._focusEventsAllowed=!1;t.preventDefault();t.stopPropagation()}#d(t){this.#a._focusEventsAllowed=!0;t.preventDefault();t.stopPropagation()}#c(t){const e=this.#a._uiManager._signal;t.addEventListener("focusin",this.#h.bind(this),{capture:!0,signal:e});t.addEventListener("focusout",this.#d.bind(this),{capture:!0,signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e})}hide(){this.#s.classList.add("hidden");this.#n?.hideDropdown()}show(){this.#s.classList.remove("hidden")}#l(){const t=document.createElement("button");t.className="delete";t.tabIndex=0;t.setAttribute("data-l10n-id",`pdfjs-editor-remove-${this.#a.editorType}-button`);this.#c(t);t.addEventListener("click",(t=>{this.#a._uiManager.delete()}),{signal:this.#a._uiManager._signal});this.#r.append(t)}get#u(){const t=document.createElement("div");t.className="divider";return t}addAltTextButton(t){this.#c(t);this.#r.prepend(t,this.#u)}addColorPicker(t){this.#n=t;const e=t.renderButton();this.#c(e);this.#r.prepend(e,this.#u)}remove(){this.#s.remove();this.#n?.destroy();this.#n=null}}class HighlightToolbar{#r=null;#s=null;#p;constructor(t){this.#p=t}#g(){const t=this.#s=document.createElement("div");t.className="editToolbar";t.setAttribute("role","toolbar");t.addEventListener("contextmenu",noContextMenu,{signal:this.#p._signal});const e=this.#r=document.createElement("div");e.className="buttons";t.append(e);this.#m();return t}#f(t,e){let i=0,s=0;for(const n of t){const t=n.y+n.height;if(t<i)continue;const a=n.x+(e?n.width:0);if(t>i){s=a;i=t}else e?a>s&&(s=a):a<s&&(s=a)}return[e?1-s:s,i]}show(t,e,i){const[s,n]=this.#f(e,i),{style:a}=this.#s||=this.#g();t.append(this.#s);a.insetInlineEnd=100*s+"%";a.top=`calc(${100*n}% + var(--editor-toolbar-vert-offset))`}hide(){this.#s.remove()}#m(){const t=document.createElement("button");t.className="highlightButton";t.tabIndex=0;t.setAttribute("data-l10n-id","pdfjs-highlight-floating-button1");const e=document.createElement("span");t.append(e);e.className="visuallyHidden";e.setAttribute("data-l10n-id","pdfjs-highlight-floating-button-label");const i=this.#p._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("click",(()=>{this.#p.highlightSelection("floating_button")}),{signal:i});this.#r.append(t)}}function bindEvents(t,e,i){for(const s of i)e.addEventListener(s,t[s].bind(t))}class IdManager{#b=0;get id(){return"pdfjs_internal_editor_"+this.#b++}}class ImageManager{#v=function getUuid(){if("undefined"!=typeof crypto&&"function"==typeof crypto?.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);if("undefined"!=typeof crypto&&"function"==typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return bytesToString(t)}();#b=0;#A=null;static get _isSVGFittingCanvas(){const t=new OffscreenCanvas(1,3).getContext("2d",{willReadFrequently:!0}),e=new Image;e.src='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>';return shadow(this,"_isSVGFittingCanvas",e.decode().then((()=>{t.drawImage(e,0,0,1,1,0,0,1,3);return 0===new Uint32Array(t.getImageData(0,0,1,1).data.buffer)[0]})))}async#y(t,e){this.#A||=new Map;let i=this.#A.get(t);if(null===i)return null;if(i?.bitmap){i.refCounter+=1;return i}try{i||={bitmap:null,id:`image_${this.#v}_${this.#b++}`,refCounter:0,isSvg:!1};let t;if("string"==typeof e){i.url=e;t=await fetchData(e,"blob")}else t=i.file=e;if("image/svg+xml"===t.type){const e=ImageManager._isSVGFittingCanvas,s=new FileReader,n=new Image,a=new Promise(((t,a)=>{n.onload=()=>{i.bitmap=n;i.isSvg=!0;t()};s.onload=async()=>{const t=i.svgUrl=s.result;n.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t};n.onerror=s.onerror=a}));s.readAsDataURL(t);await a}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(t){console.error(t);i=null}this.#A.set(t,i);i&&this.#A.set(i.id,i);return i}async getFromFile(t){const{lastModified:e,name:i,size:s,type:n}=t;return this.#y(`${e}_${i}_${s}_${n}`,t)}async getFromUrl(t){return this.#y(t,t)}async getFromId(t){this.#A||=new Map;const e=this.#A.get(t);if(!e)return null;if(e.bitmap){e.refCounter+=1;return e}return e.file?this.getFromFile(e.file):this.getFromUrl(e.url)}getSvgUrl(t){const e=this.#A.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#A||=new Map;const e=this.#A.get(t);if(e){e.refCounter-=1;0===e.refCounter&&(e.bitmap=null)}}isValidId(t){return t.startsWith(`image_${this.#v}_`)}}class CommandManager{#w=[];#x=!1;#_;#E=-1;constructor(t=128){this.#_=t}add({cmd:t,undo:e,post:i,mustExec:s,type:n=NaN,overwriteIfSameType:a=!1,keepUndo:r=!1}){s&&t();if(this.#x)return;const o={cmd:t,undo:e,post:i,type:n};if(-1===this.#E){this.#w.length>0&&(this.#w.length=0);this.#E=0;this.#w.push(o);return}if(a&&this.#w[this.#E].type===n){r&&(o.undo=this.#w[this.#E].undo);this.#w[this.#E]=o;return}const l=this.#E+1;if(l===this.#_)this.#w.splice(0,1);else{this.#E=l;l<this.#w.length&&this.#w.splice(l)}this.#w.push(o)}undo(){if(-1===this.#E)return;this.#x=!0;const{undo:t,post:e}=this.#w[this.#E];t();e?.();this.#x=!1;this.#E-=1}redo(){if(this.#E<this.#w.length-1){this.#E+=1;this.#x=!0;const{cmd:t,post:e}=this.#w[this.#E];t();e?.();this.#x=!1}}hasSomethingToUndo(){return-1!==this.#E}hasSomethingToRedo(){return this.#E<this.#w.length-1}destroy(){this.#w=null}}class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const{isMac:e}=util_FeatureTest.platform;for(const[i,s,n={}]of t)for(const t of i){const i=t.startsWith("mac+");if(e&&i){this.callbacks.set(t.slice(4),{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}else if(!e&&!i){this.callbacks.set(t,{callback:s,options:n});this.allKeys.add(t.split("+").at(-1))}}}#C(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#C(e));if(!i)return;const{callback:s,options:{bubbles:n=!1,args:a=[],checker:r=null}}=i;if(!r||r(t,e)){s.bind(t,...a,e)();if(!n){e.stopPropagation();e.preventDefault()}}}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);!function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const i of t.keys()){e.style.color=i;const s=window.getComputedStyle(e).color;t.set(i,getRGB(s))}e.remove()}(t);return shadow(this,"_colors",t)}convert(t){const e=getRGB(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,i]of this._colors)if(i.every(((t,i)=>t===e[i])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?Util.makeHexColor(...e):t}}class AnnotationEditorUIManager{#S=new AbortController;#T=null;#M=new Map;#k=new Map;#P=null;#D=null;#F=null;#R=new CommandManager;#I=0;#L=new Set;#O=null;#N=null;#B=new Set;#H=!1;#z=null;#U=null;#j=null;#$=!1;#V=null;#W=new IdManager;#G=!1;#q=!1;#X=null;#K=null;#Y=null;#Q=p.NONE;#J=new Set;#Z=null;#tt=null;#et=null;#it=this.blur.bind(this);#st=this.focus.bind(this);#nt=this.copy.bind(this);#at=this.cut.bind(this);#rt=this.paste.bind(this);#ot=this.keydown.bind(this);#lt=this.keyup.bind(this);#ht=this.onEditingAction.bind(this);#dt=this.onPageChanging.bind(this);#ct=this.onScaleChanging.bind(this);#ut=this.onRotationChanging.bind(this);#pt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1,hasSelectedText:!1};#gt=[0,0];#mt=null;#ft=null;#bt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=AnnotationEditorUIManager.prototype,arrowChecker=t=>t.#ft.contains(document.activeElement)&&"BUTTON"!==document.activeElement.tagName&&t.hasSomethingToControl(),textInputChecker=(t,{target:e})=>{if(e instanceof HTMLInputElement){const{type:t}=e;return"text"!==t&&"number"!==t}return!0},e=this.TRANSLATE_SMALL,i=this.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+a","mac+meta+a"],t.selectAll,{checker:textInputChecker}],[["ctrl+z","mac+meta+z"],t.undo,{checker:textInputChecker}],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo,{checker:textInputChecker}],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete,{checker:textInputChecker}],[["Enter","mac+Enter"],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(e)&&!t.isEnterHandled}],[[" ","mac+ "],t.addNewEditorFromKeyboard,{checker:(t,{target:e})=>!(e instanceof HTMLButtonElement)&&t.#ft.contains(document.activeElement)}],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:arrowChecker}]]))}constructor(t,e,i,s,n,a,r,o,l){this._signal=this.#S.signal;this.#ft=t;this.#bt=e;this.#P=i;this._eventBus=s;this._eventBus._on("editingaction",this.#ht);this._eventBus._on("pagechanging",this.#dt);this._eventBus._on("scalechanging",this.#ct);this._eventBus._on("rotationchanging",this.#ut);this.#vt();this.#At();this.#yt();this.#D=n.annotationStorage;this.#z=n.filterFactory;this.#tt=a;this.#j=r||null;this.#H=o;this.#Y=l||null;this.viewParameters={realScale:PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0};this.isShiftKeyDown=!1}destroy(){this.#S?.abort();this.#S=null;this._signal=null;this._eventBus._off("editingaction",this.#ht);this._eventBus._off("pagechanging",this.#dt);this._eventBus._off("scalechanging",this.#ct);this._eventBus._off("rotationchanging",this.#ut);for(const t of this.#k.values())t.destroy();this.#k.clear();this.#M.clear();this.#B.clear();this.#T=null;this.#J.clear();this.#R.destroy();this.#P?.destroy();this.#V?.hide();this.#V=null;if(this.#U){clearTimeout(this.#U);this.#U=null}if(this.#mt){clearTimeout(this.#mt);this.#mt=null}}async mlGuess(t){return this.#Y?.guess(t)||null}get hasMLManager(){return!!this.#Y}get hcmFilter(){return shadow(this,"hcmFilter",this.#tt?this.#z.addHCMFilter(this.#tt.foreground,this.#tt.background):"none")}get direction(){return shadow(this,"direction",getComputedStyle(this.#ft).direction)}get highlightColors(){return shadow(this,"highlightColors",this.#j?new Map(this.#j.split(",").map((t=>t.split("=").map((t=>t.trim()))))):null)}get highlightColorNames(){return shadow(this,"highlightColorNames",this.highlightColors?new Map(Array.from(this.highlightColors,(t=>t.reverse()))):null)}setMainHighlightColorPicker(t){this.#K=t}editAltText(t){this.#P?.editAltText(this,t)}onPageChanging({pageNumber:t}){this.#I=t-1}focusMainContainer(){this.#ft.focus()}findParent(t,e){for(const i of this.#k.values()){const{x:s,y:n,width:a,height:r}=i.div.getBoundingClientRect();if(t>=s&&t<=s+a&&e>=n&&e<=n+r)return i}return null}disableUserSelect(t=!1){this.#bt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#B.add(t)}removeShouldRescale(t){this.#B.delete(t)}onScaleChanging({scale:t}){this.commitOrRemove();this.viewParameters.realScale=t*PixelsPerInch.PDF_TO_CSS_UNITS;for(const t of this.#B)t.onScaleChanging()}onRotationChanging({pagesRotation:t}){this.commitOrRemove();this.viewParameters.rotation=t}#wt({anchorNode:t}){return t.nodeType===Node.TEXT_NODE?t.parentElement:t}highlightSelection(t=""){const e=document.getSelection();if(!e||e.isCollapsed)return;const{anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a}=e,r=e.toString(),o=this.#wt(e).closest(".textLayer"),l=this.getSelectionBoxes(o);if(l){e.empty();if(this.#Q===p.NONE){this._eventBus.dispatch("showannotationeditorui",{source:this,mode:p.HIGHLIGHT});this.showAllEditors("highlight",!0,!0)}for(const e of this.#k.values())if(e.hasTextLayer(o)){e.createAndAddNewEditor({x:0,y:0},!1,{methodOfCreation:t,boxes:l,anchorNode:i,anchorOffset:s,focusNode:n,focusOffset:a,text:r});break}}}#xt(){const t=document.getSelection();if(!t||t.isCollapsed)return;const e=this.#wt(t).closest(".textLayer"),i=this.getSelectionBoxes(e);if(i){this.#V||=new HighlightToolbar(this);this.#V.show(e,i,"ltr"===this.direction)}}addToAnnotationStorage(t){t.isEmpty()||!this.#D||this.#D.has(t.id)||this.#D.setValue(t.id,t)}#_t(){const t=document.getSelection();if(!t||t.isCollapsed){if(this.#Z){this.#V?.hide();this.#Z=null;this.#Et({hasSelectedText:!1})}return}const{anchorNode:e}=t;if(e===this.#Z)return;if(this.#wt(t).closest(".textLayer")){this.#V?.hide();this.#Z=e;this.#Et({hasSelectedText:!0});if(this.#Q===p.HIGHLIGHT||this.#Q===p.NONE){this.#Q===p.HIGHLIGHT&&this.showAllEditors("highlight",!0,!0);this.#$=this.isShiftKeyDown;if(!this.isShiftKeyDown){const t=this._signal,pointerup=t=>{if("pointerup"!==t.type||0===t.button){window.removeEventListener("pointerup",pointerup);window.removeEventListener("blur",pointerup);"pointerup"===t.type&&this.#Ct("main_toolbar")}};window.addEventListener("pointerup",pointerup,{signal:t});window.addEventListener("blur",pointerup,{signal:t})}}}else if(this.#Z){this.#V?.hide();this.#Z=null;this.#Et({hasSelectedText:!1})}}#Ct(t=""){this.#Q===p.HIGHLIGHT?this.highlightSelection(t):this.#H&&this.#xt()}#vt(){document.addEventListener("selectionchange",this.#_t.bind(this),{signal:this._signal})}#St(){const t=this._signal;window.addEventListener("focus",this.#st,{signal:t});window.addEventListener("blur",this.#it,{signal:t})}#Tt(){window.removeEventListener("focus",this.#st);window.removeEventListener("blur",this.#it)}blur(){this.isShiftKeyDown=!1;if(this.#$){this.#$=!1;this.#Ct("main_toolbar")}if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#J)if(e.div.contains(t)){this.#X=[e,t];e._focusEventsAllowed=!1;break}}focus(){if(!this.#X)return;const[t,e]=this.#X;this.#X=null;e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this._signal});e.focus()}#yt(){const t=this._signal;window.addEventListener("keydown",this.#ot,{signal:t});window.addEventListener("keyup",this.#lt,{signal:t})}#Mt(){window.removeEventListener("keydown",this.#ot);window.removeEventListener("keyup",this.#lt)}#kt(){const t=this._signal;document.addEventListener("copy",this.#nt,{signal:t});document.addEventListener("cut",this.#at,{signal:t});document.addEventListener("paste",this.#rt,{signal:t})}#Pt(){document.removeEventListener("copy",this.#nt);document.removeEventListener("cut",this.#at);document.removeEventListener("paste",this.#rt)}#At(){const t=this._signal;document.addEventListener("dragover",this.dragOver.bind(this),{signal:t});document.addEventListener("drop",this.drop.bind(this),{signal:t})}addEditListeners(){this.#yt();this.#kt()}removeEditListeners(){this.#Mt();this.#Pt()}dragOver(t){for(const{type:e}of t.dataTransfer.items)for(const i of this.#N)if(i.isHandlingMimeForPasting(e)){t.dataTransfer.dropEffect="copy";t.preventDefault();return}}drop(t){for(const e of t.dataTransfer.items)for(const i of this.#N)if(i.isHandlingMimeForPasting(e.type)){i.paste(e,this.currentLayer);t.preventDefault();return}}copy(t){t.preventDefault();this.#T?.commitOrRemove();if(!this.hasSelection)return;const e=[];for(const t of this.#J){const i=t.serialize(!0);i&&e.push(i)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t);this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const t of e.items)for(const e of this.#N)if(e.isHandlingMimeForPasting(t.type)){e.paste(t,this.currentLayer);return}let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(t){warn(`paste: "${t.message}".`);return}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const e of i){const i=s.deserialize(e);if(!i)return;t.push(i)}const cmd=()=>{for(const e of t)this.#Dt(e);this.#Ft(t)},undo=()=>{for(const e of t)e.remove()};this.addCommands({cmd,undo,mustExec:!0})}catch(t){warn(`paste: "${t.message}".`)}}keydown(t){this.isShiftKeyDown||"Shift"!==t.key||(this.isShiftKeyDown=!0);this.#Q===p.NONE||this.isEditorHandlingKeyboard||AnnotationEditorUIManager._keyboardManager.exec(this,t)}keyup(t){if(this.isShiftKeyDown&&"Shift"===t.key){this.isShiftKeyDown=!1;if(this.#$){this.#$=!1;this.#Ct("main_toolbar")}}}onEditingAction({name:t}){switch(t){case"undo":case"redo":case"delete":case"selectAll":this[t]();break;case"highlightSelection":this.highlightSelection("context_menu")}}#Et(t){if(Object.entries(t).some((([t,e])=>this.#pt[t]!==e))){this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#pt,t)});this.#Q===p.HIGHLIGHT&&!1===t.hasSelectedEditor&&this.#Rt([[g.HIGHLIGHT_FREE,!0]])}}#Rt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#St();this.#kt();this.#Et({isEditing:this.#Q!==p.NONE,isEmpty:this.#It(),hasSomethingToUndo:this.#R.hasSomethingToUndo(),hasSomethingToRedo:this.#R.hasSomethingToRedo(),hasSelectedEditor:!1})}else{this.#Tt();this.#Pt();this.#Et({isEditing:!1});this.disableUserSelect(!1)}}registerEditorTypes(t){if(!this.#N){this.#N=t;for(const t of this.#N)this.#Rt(t.defaultPropertiesToUpdate)}}getId(){return this.#W.id}get currentLayer(){return this.#k.get(this.#I)}getLayer(t){return this.#k.get(t)}get currentPageIndex(){return this.#I}addLayer(t){this.#k.set(t.pageIndex,t);this.#G?t.enable():t.disable()}removeLayer(t){this.#k.delete(t.pageIndex)}updateMode(t,e=null,i=!1){if(this.#Q!==t){this.#Q=t;if(t!==p.NONE){this.setEditingState(!0);this.#Lt();this.unselectAll();for(const e of this.#k.values())e.updateMode(t);if(e||!i){if(e)for(const t of this.#M.values())if(t.annotationElementId===e){this.setSelected(t);t.enterInEditMode();break}}else this.addNewEditorFromKeyboard()}else{this.setEditingState(!1);this.#Ot()}}}addNewEditorFromKeyboard(){this.currentLayer.canCreateNewEmptyEditor()&&this.currentLayer.addNewEditor()}updateToolbar(t){t!==this.#Q&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#N){switch(t){case g.CREATE:this.currentLayer.addNewEditor();return;case g.HIGHLIGHT_DEFAULT_COLOR:this.#K?.updateColor(e);break;case g.HIGHLIGHT_SHOW_ALL:this._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:{type:"highlight",action:"toggle_visibility"}}});(this.#et||=new Map).set(t,e);this.showAllEditors("highlight",e)}for(const i of this.#J)i.updateParams(t,e);for(const i of this.#N)i.updateDefaultParams(t,e)}}showAllEditors(t,e,i=!1){for(const i of this.#M.values())i.editorType===t&&i.show(e);(this.#et?.get(g.HIGHLIGHT_SHOW_ALL)??!0)!==e&&this.#Rt([[g.HIGHLIGHT_SHOW_ALL,e]])}enableWaiting(t=!1){if(this.#q!==t){this.#q=t;for(const e of this.#k.values()){t?e.disableClick():e.enableClick();e.div.classList.toggle("waiting",t)}}}#Lt(){if(!this.#G){this.#G=!0;for(const t of this.#k.values())t.enable();for(const t of this.#M.values())t.enable()}}#Ot(){this.unselectAll();if(this.#G){this.#G=!1;for(const t of this.#k.values())t.disable();for(const t of this.#M.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#M.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#M.get(t)}addEditor(t){this.#M.set(t.id,t)}removeEditor(t){if(t.div.contains(document.activeElement)){this.#U&&clearTimeout(this.#U);this.#U=setTimeout((()=>{this.focusMainContainer();this.#U=null}),0)}this.#M.delete(t.id);this.unselect(t);t.annotationElementId&&this.#L.has(t.annotationElementId)||this.#D?.remove(t.id)}addDeletedAnnotationElement(t){this.#L.add(t.annotationElementId);this.addChangedExistingAnnotation(t);t.deleted=!0}isDeletedAnnotationElement(t){return this.#L.has(t)}removeDeletedAnnotationElement(t){this.#L.delete(t.annotationElementId);this.removeChangedExistingAnnotation(t);t.deleted=!1}#Dt(t){const e=this.#k.get(t.pageIndex);if(e)e.addOrRebuild(t);else{this.addEditor(t);this.addToAnnotationStorage(t)}}setActiveEditor(t){if(this.#T!==t){this.#T=t;t&&this.#Rt(t.propertiesToUpdate)}}get#Nt(){let t=null;for(t of this.#J);return t}updateUI(t){this.#Nt===t&&this.#Rt(t.propertiesToUpdate)}toggleSelected(t){if(this.#J.has(t)){this.#J.delete(t);t.unselect();this.#Et({hasSelectedEditor:this.hasSelection})}else{this.#J.add(t);t.select();this.#Rt(t.propertiesToUpdate);this.#Et({hasSelectedEditor:!0})}}setSelected(t){for(const e of this.#J)e!==t&&e.unselect();this.#J.clear();this.#J.add(t);t.select();this.#Rt(t.propertiesToUpdate);this.#Et({hasSelectedEditor:!0})}isSelected(t){return this.#J.has(t)}get firstSelectedEditor(){return this.#J.values().next().value}unselect(t){t.unselect();this.#J.delete(t);this.#Et({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#J.size}get isEnterHandled(){return 1===this.#J.size&&this.firstSelectedEditor.isEnterHandled}undo(){this.#R.undo();this.#Et({hasSomethingToUndo:this.#R.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#It()})}redo(){this.#R.redo();this.#Et({hasSomethingToUndo:!0,hasSomethingToRedo:this.#R.hasSomethingToRedo(),isEmpty:this.#It()})}addCommands(t){this.#R.add(t);this.#Et({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#It()})}#It(){if(0===this.#M.size)return!0;if(1===this.#M.size)for(const t of this.#M.values())return t.isEmpty();return!1}delete(){this.commitOrRemove();if(!this.hasSelection)return;const t=[...this.#J];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)this.#Dt(e)},mustExec:!0})}commitOrRemove(){this.#T?.commitOrRemove()}hasSomethingToControl(){return this.#T||this.hasSelection}#Ft(t){for(const t of this.#J)t.unselect();this.#J.clear();for(const e of t)if(!e.isEmpty()){this.#J.add(e);e.select()}this.#Et({hasSelectedEditor:this.hasSelection})}selectAll(){for(const t of this.#J)t.commit();this.#Ft(this.#M.values())}unselectAll(){if(this.#T){this.#T.commitOrRemove();if(this.#Q!==p.NONE)return}if(this.hasSelection){for(const t of this.#J)t.unselect();this.#J.clear();this.#Et({hasSelectedEditor:!1})}}translateSelectedEditors(t,e,i=!1){i||this.commitOrRemove();if(!this.hasSelection)return;this.#gt[0]+=t;this.#gt[1]+=e;const[s,n]=this.#gt,a=[...this.#J];this.#mt&&clearTimeout(this.#mt);this.#mt=setTimeout((()=>{this.#mt=null;this.#gt[0]=this.#gt[1]=0;this.addCommands({cmd:()=>{for(const t of a)this.#M.has(t.id)&&t.translateInPage(s,n)},undo:()=>{for(const t of a)this.#M.has(t.id)&&t.translateInPage(-s,-n)},mustExec:!1})}),1e3);for(const i of a)i.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0);this.#O=new Map;for(const t of this.#J)this.#O.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#O)return!1;this.disableUserSelect(!1);const t=this.#O;this.#O=null;let e=!1;for(const[{x:i,y:s,pageIndex:n},a]of t){a.newX=i;a.newY=s;a.newPageIndex=n;e||=i!==a.savedX||s!==a.savedY||n!==a.savedPageIndex}if(!e)return!1;const move=(t,e,i,s)=>{if(this.#M.has(t.id)){const n=this.#k.get(s);if(n)t._setParentAndPosition(n,e,i);else{t.pageIndex=s;t.x=e;t.y=i}}};this.addCommands({cmd:()=>{for(const[e,{newX:i,newY:s,newPageIndex:n}]of t)move(e,i,s,n)},undo:()=>{for(const[e,{savedX:i,savedY:s,savedPageIndex:n}]of t)move(e,i,s,n)},mustExec:!0});return!0}dragSelectedEditors(t,e){if(this.#O)for(const i of this.#O.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);if(e){e.changeParent(t);e.addOrRebuild(t)}else{this.addEditor(t);this.addToAnnotationStorage(t);t.rebuild()}}else t.parent.addOrRebuild(t)}get isEditorHandlingKeyboard(){return this.getActive()?.shouldGetKeyboardEvents()||1===this.#J.size&&this.firstSelectedEditor.shouldGetKeyboardEvents()}isActive(t){return this.#T===t}getActive(){return this.#T}getMode(){return this.#Q}get imageManager(){return shadow(this,"imageManager",new ImageManager)}getSelectionBoxes(t){if(!t)return null;const e=document.getSelection();for(let i=0,s=e.rangeCount;i<s;i++)if(!t.contains(e.getRangeAt(i).commonAncestorContainer))return null;const{x:i,y:s,width:n,height:a}=t.getBoundingClientRect();let r;switch(t.getAttribute("data-main-rotation")){case"90":r=(t,e,r,o)=>({x:(e-s)/a,y:1-(t+r-i)/n,width:o/a,height:r/n});break;case"180":r=(t,e,r,o)=>({x:1-(t+r-i)/n,y:1-(e+o-s)/a,width:r/n,height:o/a});break;case"270":r=(t,e,r,o)=>({x:1-(e+o-s)/a,y:(t-i)/n,width:o/a,height:r/n});break;default:r=(t,e,r,o)=>({x:(t-i)/n,y:(e-s)/a,width:r/n,height:o/a})}const o=[];for(let t=0,i=e.rangeCount;t<i;t++){const i=e.getRangeAt(t);if(!i.collapsed)for(const{x:t,y:e,width:s,height:n}of i.getClientRects())0!==s&&0!==n&&o.push(r(t,e,s,n))}return 0===o.length?null:o}addChangedExistingAnnotation({annotationElementId:t,id:e}){(this.#F||=new Map).set(t,e)}removeChangedExistingAnnotation({annotationElementId:t}){this.#F?.delete(t)}renderAnnotationElement(t){const e=this.#F?.get(t.data.id);if(!e)return;const i=this.#D.getRawValue(e);i&&(this.#Q!==p.NONE||i.hasBeenModified)&&i.renderAnnotationElement(t)}}class AltText{#Bt="";#Ht=!1;#zt=null;#Ut=null;#jt=null;#$t=!1;#a=null;static _l10nPromise=null;constructor(t){this.#a=t}static initialize(t){AltText._l10nPromise||=t}async render(){const t=this.#zt=document.createElement("button");t.className="altText";const e=await AltText._l10nPromise.get("pdfjs-editor-alt-text-button-label");t.textContent=e;t.setAttribute("aria-label",e);t.tabIndex="0";const i=this.#a._uiManager._signal;t.addEventListener("contextmenu",noContextMenu,{signal:i});t.addEventListener("pointerdown",(t=>t.stopPropagation()),{signal:i});const onClick=t=>{t.preventDefault();this.#a._uiManager.editAltText(this.#a)};t.addEventListener("click",onClick,{capture:!0,signal:i});t.addEventListener("keydown",(e=>{if(e.target===t&&"Enter"===e.key){this.#$t=!0;onClick(e)}}),{signal:i});await this.#Vt();return t}finish(){if(this.#zt){this.#zt.focus({focusVisible:this.#$t});this.#$t=!1}}isEmpty(){return!this.#Bt&&!this.#Ht}get data(){return{altText:this.#Bt,decorative:this.#Ht}}set data({altText:t,decorative:e}){if(this.#Bt!==t||this.#Ht!==e){this.#Bt=t;this.#Ht=e;this.#Vt()}}toggle(t=!1){if(this.#zt){if(!t&&this.#jt){clearTimeout(this.#jt);this.#jt=null}this.#zt.disabled=!t}}destroy(){this.#zt?.remove();this.#zt=null;this.#Ut=null}async#Vt(){const t=this.#zt;if(!t)return;if(!this.#Bt&&!this.#Ht){t.classList.remove("done");this.#Ut?.remove();return}t.classList.add("done");AltText._l10nPromise.get("pdfjs-editor-alt-text-edit-button-label").then((e=>{t.setAttribute("aria-label",e)}));let e=this.#Ut;if(!e){this.#Ut=e=document.createElement("span");e.className="tooltip";e.setAttribute("role","tooltip");const i=e.id=`alt-text-tooltip-${this.#a.id}`;t.setAttribute("aria-describedby",i);const s=100,n=this.#a._uiManager._signal;n.addEventListener("abort",(()=>{clearTimeout(this.#jt);this.#jt=null}),{once:!0});t.addEventListener("mouseenter",(()=>{this.#jt=setTimeout((()=>{this.#jt=null;this.#Ut.classList.add("show");this.#a._reportTelemetry({action:"alt_text_tooltip"})}),s)}),{signal:n});t.addEventListener("mouseleave",(()=>{if(this.#jt){clearTimeout(this.#jt);this.#jt=null}this.#Ut?.classList.remove("show")}),{signal:n})}e.innerText=this.#Ht?await AltText._l10nPromise.get("pdfjs-editor-alt-text-decorative-tooltip"):this.#Bt;e.parentNode||t.append(e);const i=this.#a.getImageForAltText();i?.setAttribute("aria-describedby",e.id)}}class AnnotationEditor{#Wt=null;#Gt=null;#Bt=null;#qt=!1;#Xt=!1;#Kt=null;#Yt=null;#Qt=this.focusin.bind(this);#Jt=this.focusout.bind(this);#Zt=null;#te="";#ee=!1;#ie=null;#se=!1;#ne=!1;#ae=!1;#re=null;#oe=0;#le=0;#he=null;_initialOptions=Object.create(null);_isVisible=!0;_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#de=!1;#ce=AnnotationEditor._zIndex++;static _borderLineWidth=-1;static _colorManager=new ColorManager;static _zIndex=1;static _telemetryTimeout=1e3;static get _resizerKeyboardManager(){const t=AnnotationEditor.prototype._resizeWithKeyboard,e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_resizerKeyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t,{args:[-e,0]}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t,{args:[-i,0]}],[["ArrowRight","mac+ArrowRight"],t,{args:[e,0]}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t,{args:[i,0]}],[["ArrowUp","mac+ArrowUp"],t,{args:[0,-e]}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t,{args:[0,-i]}],[["ArrowDown","mac+ArrowDown"],t,{args:[0,e]}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t,{args:[0,i]}],[["Escape","mac+Escape"],AnnotationEditor.prototype._stopResizingWithKeyboard]]))}constructor(t){this.constructor===AnnotationEditor&&unreachable("Cannot initialize AnnotationEditor.");this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;this._uiManager=t.uiManager;this.annotationElementId=null;this._willKeepAspectRatio=!1;this._initialOptions.isCentered=t.isCentered;this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:s,pageX:n,pageY:a}}=this.parent.viewport;this.rotation=e;this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360;this.pageDimensions=[i,s];this.pageTranslation=[n,a];const[r,o]=this.parentDimensions;this.x=t.x/r;this.y=t.y/o;this.isAttachedToDOM=!1;this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return shadow(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new FakeEditor({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId;e.deleted=!0;e._uiManager.addToAnnotationStorage(e)}static initialize(t,e,i){AnnotationEditor._l10nPromise||=new Map(["pdfjs-editor-alt-text-button-label","pdfjs-editor-alt-text-edit-button-label","pdfjs-editor-alt-text-decorative-tooltip","pdfjs-editor-resizer-label-topLeft","pdfjs-editor-resizer-label-topMiddle","pdfjs-editor-resizer-label-topRight","pdfjs-editor-resizer-label-middleRight","pdfjs-editor-resizer-label-bottomRight","pdfjs-editor-resizer-label-bottomMiddle","pdfjs-editor-resizer-label-bottomLeft","pdfjs-editor-resizer-label-middleLeft"].map((e=>[e,t.get(e.replaceAll(/([A-Z])/g,(t=>`-${t.toLowerCase()}`)))])));if(i?.strings)for(const e of i.strings)AnnotationEditor._l10nPromise.set(e,t.get(e));if(-1!==AnnotationEditor._borderLineWidth)return;const s=getComputedStyle(document.documentElement);AnnotationEditor._borderLineWidth=parseFloat(s.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){unreachable("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#de}set _isDraggable(t){this.#de=t;this.div?.classList.toggle("draggable",t)}get isEnterHandled(){return!0}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t);this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2;this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t);this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2;this.y-=this.height/2}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#ce}setParent(t){if(null!==t){this.pageIndex=t.pageIndex;this.pageDimensions=t.pageDimensions}else this.#ue();this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#ee?this.#ee=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;if(!e?.closest(`#${this.id}`)){t.preventDefault();this.parent?.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,s){const[n,a]=this.parentDimensions;[i,s]=this.screenToPageTranslation(i,s);this.x=(t+i)/n;this.y=(e+s)/a;this.fixAndSetPosition()}#pe([t,e],i,s){[i,s]=this.screenToPageTranslation(i,s);this.x+=i/t;this.y+=s/e;this.fixAndSetPosition()}translate(t,e){this.#pe(this.parentDimensions,t,e)}translateInPage(t,e){this.#ie||=[this.x,this.y];this.#pe(this.pageDimensions,t,e);this.div.scrollIntoView({block:"nearest"})}drag(t,e){this.#ie||=[this.x,this.y];const[i,s]=this.parentDimensions;this.x+=t/i;this.y+=e/s;if(this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();if(this.parent.findNewParent(this,t,e)){this.x-=Math.floor(this.x);this.y-=Math.floor(this.y)}}let{x:n,y:a}=this;const[r,o]=this.getBaseTranslation();n+=r;a+=o;this.div.style.left=`${(100*n).toFixed(2)}%`;this.div.style.top=`${(100*a).toFixed(2)}%`;this.div.scrollIntoView({block:"nearest"})}get _hasBeenMoved(){return!!this.#ie&&(this.#ie[0]!==this.x||this.#ie[1]!==this.y)}getBaseTranslation(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=AnnotationEditor,s=i/t,n=i/e;switch(this.rotation){case 90:return[-s,n];case 180:return[s,n];case 270:return[s,-n];default:return[-s,-n]}}get _mustFixPosition(){return!0}fixAndSetPosition(t=this.rotation){const[e,i]=this.pageDimensions;let{x:s,y:n,width:a,height:r}=this;a*=e;r*=i;s*=e;n*=i;if(this._mustFixPosition)switch(t){case 0:s=Math.max(0,Math.min(e-a,s));n=Math.max(0,Math.min(i-r,n));break;case 90:s=Math.max(0,Math.min(e-r,s));n=Math.min(i,Math.max(a,n));break;case 180:s=Math.min(e,Math.max(a,s));n=Math.min(i,Math.max(r,n));break;case 270:s=Math.min(e,Math.max(r,s));n=Math.max(0,Math.min(i-a,n))}this.x=s/=e;this.y=n/=i;const[o,l]=this.getBaseTranslation();s+=o;n+=l;const{style:h}=this.div;h.left=`${(100*s).toFixed(2)}%`;h.top=`${(100*n).toFixed(2)}%`;this.moveInDOM()}static#ge(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return AnnotationEditor.#ge(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return AnnotationEditor.#ge(t,e,360-this.parentRotation)}#me(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this,s=e*t,n=i*t;return util_FeatureTest.isCSSRoundSupported?[Math.round(s),Math.round(n)]:[s,n]}setDims(t,e){const[i,s]=this.parentDimensions;this.div.style.width=`${(100*t/i).toFixed(2)}%`;this.#Xt||(this.div.style.height=`${(100*e/s).toFixed(2)}%`)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,s=i.endsWith("%"),n=!this.#Xt&&e.endsWith("%");if(s&&n)return;const[a,r]=this.parentDimensions;s||(t.width=`${(100*parseFloat(i)/a).toFixed(2)}%`);this.#Xt||n||(t.height=`${(100*parseFloat(e)/r).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#fe(){if(this.#Kt)return;this.#Kt=document.createElement("div");this.#Kt.classList.add("resizers");const t=this._willKeepAspectRatio?["topLeft","topRight","bottomRight","bottomLeft"]:["topLeft","topMiddle","topRight","middleRight","bottomRight","bottomMiddle","bottomLeft","middleLeft"],e=this._uiManager._signal;for(const i of t){const t=document.createElement("div");this.#Kt.append(t);t.classList.add("resizer",i);t.setAttribute("data-resizer-name",i);t.addEventListener("pointerdown",this.#be.bind(this,i),{signal:e});t.addEventListener("contextmenu",noContextMenu,{signal:e});t.tabIndex=-1}this.div.prepend(this.#Kt)}#be(t,e){e.preventDefault();const{isMac:i}=util_FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;this.#Bt?.toggle(!1);const s=this.#ve.bind(this,t),n=this._isDraggable;this._isDraggable=!1;const a=this._uiManager._signal,r={passive:!0,capture:!0,signal:a};this.parent.togglePointerEvents(!1);window.addEventListener("pointermove",s,r);window.addEventListener("contextmenu",noContextMenu,{signal:a});const o=this.x,l=this.y,h=this.width,d=this.height,c=this.parent.div.style.cursor,u=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const pointerUpCallback=()=>{this.parent.togglePointerEvents(!0);this.#Bt?.toggle(!0);this._isDraggable=n;window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);window.removeEventListener("pointermove",s,r);window.removeEventListener("contextmenu",noContextMenu);this.parent.div.style.cursor=c;this.div.style.cursor=u;this.#Ae(o,l,h,d)};window.addEventListener("pointerup",pointerUpCallback,{signal:a});window.addEventListener("blur",pointerUpCallback,{signal:a})}#Ae(t,e,i,s){const n=this.x,a=this.y,r=this.width,o=this.height;n===t&&a===e&&r===i&&o===s||this.addCommands({cmd:()=>{this.width=r;this.height=o;this.x=n;this.y=a;const[t,e]=this.parentDimensions;this.setDims(t*r,e*o);this.fixAndSetPosition()},undo:()=>{this.width=i;this.height=s;this.x=t;this.y=e;const[n,a]=this.parentDimensions;this.setDims(n*i,a*s);this.fixAndSetPosition()},mustExec:!0})}#ve(t,e){const[i,s]=this.parentDimensions,n=this.x,a=this.y,r=this.width,o=this.height,l=AnnotationEditor.MIN_SIZE/i,h=AnnotationEditor.MIN_SIZE/s,round=t=>Math.round(1e4*t)/1e4,d=this.#me(this.rotation),transf=(t,e)=>[d[0]*t+d[2]*e,d[1]*t+d[3]*e],c=this.#me(360-this.rotation);let u,p,g=!1,m=!1;switch(t){case"topLeft":g=!0;u=(t,e)=>[0,0];p=(t,e)=>[t,e];break;case"topMiddle":u=(t,e)=>[t/2,0];p=(t,e)=>[t/2,e];break;case"topRight":g=!0;u=(t,e)=>[t,0];p=(t,e)=>[0,e];break;case"middleRight":m=!0;u=(t,e)=>[t,e/2];p=(t,e)=>[0,e/2];break;case"bottomRight":g=!0;u=(t,e)=>[t,e];p=(t,e)=>[0,0];break;case"bottomMiddle":u=(t,e)=>[t/2,e];p=(t,e)=>[t/2,0];break;case"bottomLeft":g=!0;u=(t,e)=>[0,e];p=(t,e)=>[t,0];break;case"middleLeft":m=!0;u=(t,e)=>[0,e/2];p=(t,e)=>[t,e/2]}const f=u(r,o),b=p(r,o);let v=transf(...b);const A=round(n+v[0]),y=round(a+v[1]);let w=1,x=1,[_,E]=this.screenToPageTranslation(e.movementX,e.movementY);[_,E]=(C=_/i,S=E/s,[c[0]*C+c[2]*S,c[1]*C+c[3]*S]);var C,S;if(g){const t=Math.hypot(r,o);w=x=Math.max(Math.min(Math.hypot(b[0]-f[0]-_,b[1]-f[1]-E)/t,1/r,1/o),l/r,h/o)}else m?w=Math.max(l,Math.min(1,Math.abs(b[0]-f[0]-_)))/r:x=Math.max(h,Math.min(1,Math.abs(b[1]-f[1]-E)))/o;const T=round(r*w),M=round(o*x);v=transf(...p(T,M));const k=A-v[0],P=y-v[1];this.width=T;this.height=M;this.x=k;this.y=P;this.setDims(i*T,s*M);this.fixAndSetPosition()}altTextFinish(){this.#Bt?.finish()}async addEditToolbar(){if(this.#Zt||this.#ne)return this.#Zt;this.#Zt=new EditorToolbar(this);this.div.append(this.#Zt.render());this.#Bt&&this.#Zt.addAltTextButton(await this.#Bt.render());return this.#Zt}removeEditToolbar(){if(this.#Zt){this.#Zt.remove();this.#Zt=null;this.#Bt?.destroy()}}getClientDimensions(){return this.div.getBoundingClientRect()}async addAltTextButton(){if(!this.#Bt){AltText.initialize(AnnotationEditor._l10nPromise);this.#Bt=new AltText(this);if(this.#Wt){this.#Bt.data=this.#Wt;this.#Wt=null}await this.addEditToolbar()}}get altTextData(){return this.#Bt?.data}set altTextData(t){this.#Bt&&(this.#Bt.data=t)}hasAltText(){return!this.#Bt?.isEmpty()}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.tabIndex=this.#qt?-1:0;this._isVisible||this.div.classList.add("hidden");this.setInForeground();const t=this._uiManager._signal;this.div.addEventListener("focusin",this.#Qt,{signal:t});this.div.addEventListener("focusout",this.#Jt,{signal:t});const[e,i]=this.parentDimensions;if(this.parentRotation%180!=0){this.div.style.maxWidth=`${(100*i/e).toFixed(2)}%`;this.div.style.maxHeight=`${(100*e/i).toFixed(2)}%`}const[s,n]=this.getInitialTranslation();this.translate(s,n);bindEvents(this,this.div,["pointerdown"]);return this.div}pointerdown(t){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{this.#ee=!0;this._isDraggable?this.#ye(t):this.#we(t)}}#we(t){const{isMac:e}=util_FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}#ye(t){const e=this._uiManager.isSelected(this);this._uiManager.setUpDragSession();let i,s;const n=this._uiManager._signal;if(e){this.div.classList.add("moving");i={passive:!0,capture:!0,signal:n};this.#oe=t.clientX;this.#le=t.clientY;s=t=>{const{clientX:e,clientY:i}=t,[s,n]=this.screenToPageTranslation(e-this.#oe,i-this.#le);this.#oe=e;this.#le=i;this._uiManager.dragSelectedEditors(s,n)};window.addEventListener("pointermove",s,i)}const pointerUpCallback=()=>{window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("blur",pointerUpCallback);if(e){this.div.classList.remove("moving");window.removeEventListener("pointermove",s,i)}this.#ee=!1;this._uiManager.endDragSession()||this.#we(t)};window.addEventListener("pointerup",pointerUpCallback,{signal:n});window.addEventListener("blur",pointerUpCallback,{signal:n})}moveInDOM(){this.#re&&clearTimeout(this.#re);this.#re=setTimeout((()=>{this.#re=null;this.parent?.moveEditorInDOM(this)}),0)}_setParentAndPosition(t,e,i){t.changeParent(this);this.x=e;this.y=i;this.fixAndSetPosition()}getRect(t,e,i=this.rotation){const s=this.parentScale,[n,a]=this.pageDimensions,[r,o]=this.pageTranslation,l=t/s,h=e/s,d=this.x*n,c=this.y*a,u=this.width*n,p=this.height*a;switch(i){case 0:return[d+l+r,a-c-h-p+o,d+l+u+r,a-c-h+o];case 90:return[d+h+r,a-c+l+o,d+h+p+r,a-c+l+u+o];case 180:return[d-l-u+r,a-c+h+o,d-l+r,a-c+h+p+o];case 270:return[d-h-p+r,a-c-l-u+o,d-h+r,a-c-l+o];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,s,n,a]=t,r=n-i,o=a-s;switch(this.rotation){case 0:return[i,e-a,r,o];case 90:return[i,e-s,o,r];case 180:return[n,e-s,r,o];case 270:return[n,e-a,o,r];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#ne=!0}disableEditMode(){this.#ne=!1}isInEditMode(){return this.#ne}shouldGetKeyboardEvents(){return this.#ae}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){const t=this._uiManager._signal;this.div?.addEventListener("focusin",this.#Qt,{signal:t});this.div?.addEventListener("focusout",this.#Jt,{signal:t})}rotate(t){}serialize(t=!1,e=null){unreachable("An editor must be serializable")}static deserialize(t,e,i){const s=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});s.rotation=t.rotation;s.#Wt=t.accessibilityData;const[n,a]=s.pageDimensions,[r,o,l,h]=s.getRectInCurrentCoords(t.rect,a);s.x=r/n;s.y=o/a;s.width=l/n;s.height=h/a;return s}get hasBeenModified(){return!!this.annotationElementId&&(this.deleted||null!==this.serialize())}remove(){this.div.removeEventListener("focusin",this.#Qt);this.div.removeEventListener("focusout",this.#Jt);this.isEmpty()||this.commit();this.parent?this.parent.remove(this):this._uiManager.removeEditor(this);if(this.#re){clearTimeout(this.#re);this.#re=null}this.#ue();this.removeEditToolbar();if(this.#he){for(const t of this.#he.values())clearTimeout(t);this.#he=null}this.parent=null}get isResizable(){return!1}makeResizable(){if(this.isResizable){this.#fe();this.#Kt.classList.remove("hidden");bindEvents(this,this.div,["keydown"])}}get toolbarPosition(){return null}keydown(t){if(!this.isResizable||t.target!==this.div||"Enter"!==t.key)return;this._uiManager.setSelected(this);this.#Yt={savedX:this.x,savedY:this.y,savedWidth:this.width,savedHeight:this.height};const e=this.#Kt.children;if(!this.#Gt){this.#Gt=Array.from(e);const t=this.#xe.bind(this),i=this.#_e.bind(this),s=this._uiManager._signal;for(const e of this.#Gt){const n=e.getAttribute("data-resizer-name");e.setAttribute("role","spinbutton");e.addEventListener("keydown",t,{signal:s});e.addEventListener("blur",i,{signal:s});e.addEventListener("focus",this.#Ee.bind(this,n),{signal:s});AnnotationEditor._l10nPromise.get(`pdfjs-editor-resizer-label-${n}`).then((t=>e.setAttribute("aria-label",t)))}}const i=this.#Gt[0];let s=0;for(const t of e){if(t===i)break;s++}const n=(360-this.rotation+this.parentRotation)%360/90*(this.#Gt.length/4);if(n!==s){if(n<s)for(let t=0;t<s-n;t++)this.#Kt.append(this.#Kt.firstChild);else if(n>s)for(let t=0;t<n-s;t++)this.#Kt.firstChild.before(this.#Kt.lastChild);let t=0;for(const i of e){const e=this.#Gt[t++].getAttribute("data-resizer-name");AnnotationEditor._l10nPromise.get(`pdfjs-editor-resizer-label-${e}`).then((t=>i.setAttribute("aria-label",t)))}}this.#Ce(0);this.#ae=!0;this.#Kt.firstChild.focus({focusVisible:!0});t.preventDefault();t.stopImmediatePropagation()}#xe(t){AnnotationEditor._resizerKeyboardManager.exec(this,t)}#_e(t){this.#ae&&t.relatedTarget?.parentNode!==this.#Kt&&this.#ue()}#Ee(t){this.#te=this.#ae?t:""}#Ce(t){if(this.#Gt)for(const e of this.#Gt)e.tabIndex=t}_resizeWithKeyboard(t,e){this.#ae&&this.#ve(this.#te,{movementX:t,movementY:e})}#ue(){this.#ae=!1;this.#Ce(-1);if(this.#Yt){const{savedX:t,savedY:e,savedWidth:i,savedHeight:s}=this.#Yt;this.#Ae(t,e,i,s);this.#Yt=null}}_stopResizingWithKeyboard(){this.#ue();this.div.focus()}select(){this.makeResizable();this.div?.classList.add("selectedEditor");this.#Zt?this.#Zt?.show():this.addEditToolbar().then((()=>{this.div?.classList.contains("selectedEditor")&&this.#Zt?.show()}))}unselect(){this.#Kt?.classList.add("hidden");this.div?.classList.remove("selectedEditor");this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus({preventScroll:!0});this.#Zt?.hide()}updateParams(t,e){}disableEditing(){}enableEditing(){}enterInEditMode(){}getImageForAltText(){return null}get contentDiv(){return this.div}get isEditing(){return this.#se}set isEditing(t){this.#se=t;if(this.parent)if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}setAspectRatio(t,e){this.#Xt=!0;const i=t/e,{style:s}=this.div;s.aspectRatio=i;s.height="auto"}static get MIN_SIZE(){return 16}static canCreateNewEmptyEditor(){return!0}get telemetryInitialData(){return{action:"added"}}get telemetryFinalData(){return null}_reportTelemetry(t,e=!1){if(e){this.#he||=new Map;const{action:e}=t;let i=this.#he.get(e);i&&clearTimeout(i);i=setTimeout((()=>{this._reportTelemetry(t);this.#he.delete(e);0===this.#he.size&&(this.#he=null)}),AnnotationEditor._telemetryTimeout);this.#he.set(e,i)}else{t.type||=this.editorType;this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",data:t}})}}show(t=this._isVisible){this.div.classList.toggle("hidden",!t);this._isVisible=t}enable(){this.div&&(this.div.tabIndex=0);this.#qt=!1}disable(){this.div&&(this.div.tabIndex=-1);this.#qt=!0}renderAnnotationElement(t){let e=t.container.querySelector(".annotationContent");if(e){if("CANVAS"===e.nodeName){const t=e;e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.before(e)}}else{e=document.createElement("div");e.classList.add("annotationContent",this.editorType);t.container.prepend(e)}return e}resetAnnotationElement(t){const{firstChild:e}=t.container;"DIV"===e.nodeName&&e.classList.contains("annotationContent")&&e.remove()}}class FakeEditor extends AnnotationEditor{constructor(t){super(t);this.annotationElementId=t.annotationElementId;this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}const pt=3285377520,gt=4294901760,mt=65535;class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:pt;this.h2=t?4294967295&t:pt}update(t){let e,i;if("string"==typeof t){e=new Uint8Array(2*t.length);i=0;for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);if(n<=255)e[i++]=n;else{e[i++]=n>>>8;e[i++]=255&n}}}else{if(!ArrayBuffer.isView(t))throw new Error("Invalid data format, must be a string or TypedArray.");e=t.slice();i=e.byteLength}const s=i>>2,n=i-4*s,a=new Uint32Array(e.buffer,0,s);let r=0,o=0,l=this.h1,h=this.h2;const d=3432918353,c=461845907,u=11601,p=13715;for(let t=0;t<s;t++)if(1&t){r=a[t];r=r*d&gt|r*u&mt;r=r<<15|r>>>17;r=r*c&gt|r*p&mt;l^=r;l=l<<13|l>>>19;l=5*l+3864292196}else{o=a[t];o=o*d&gt|o*u&mt;o=o<<15|o>>>17;o=o*c&gt|o*p&mt;h^=o;h=h<<13|h>>>19;h=5*h+3864292196}r=0;switch(n){case 3:r^=e[4*s+2]<<16;case 2:r^=e[4*s+1]<<8;case 1:r^=e[4*s];r=r*d&gt|r*u&mt;r=r<<15|r>>>17;r=r*c&gt|r*p&mt;1&s?l^=r:h^=r}this.h1=l;this.h2=h}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&gt|36045*t&mt;e=4283543511*e&gt|(2950163797*(e<<16|t>>>16)&gt)>>>16;t^=e>>>1;t=444984403*t&gt|60499*t&mt;e=3301882366*e&gt|(3120437893*(e<<16|t>>>16)&gt)>>>16;t^=e>>>1;return(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}const ft=Object.freeze({map:null,hash:"",transfer:void 0});class AnnotationStorage{#Se=!1;#Te=new Map;constructor(){this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const i=this.#Te.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#Te.get(t)}remove(t){this.#Te.delete(t);0===this.#Te.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this.#Te.values())if(t instanceof AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#Te.get(t);let s=!1;if(void 0!==i){for(const[t,n]of Object.entries(e))if(i[t]!==n){s=!0;i[t]=n}}else{s=!0;this.#Te.set(t,e)}s&&this.#Me();e instanceof AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#Te.has(t)}getAll(){return this.#Te.size>0?objectFromMap(this.#Te):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#Te.size}#Me(){if(!this.#Se){this.#Se=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this.#Se){this.#Se=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this.#Te.size)return ft;const t=new Map,e=new MurmurHash3_64,i=[],s=Object.create(null);let n=!1;for(const[i,a]of this.#Te){const r=a instanceof AnnotationEditor?a.serialize(!1,s):a;if(r){t.set(i,r);e.update(`${i}:${JSON.stringify(r)}`);n||=!!r.bitmap}}if(n)for(const e of t.values())e.bitmap&&i.push(e.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfer:i}:ft}get editorStats(){let t=null;const e=new Map;for(const i of this.#Te.values()){if(!(i instanceof AnnotationEditor))continue;const s=i.telemetryFinalData;if(!s)continue;const{type:n}=s;e.has(n)||e.set(n,Object.getPrototypeOf(i).constructor);t||=Object.create(null);const a=t[n]||=new Map;for(const[t,e]of Object.entries(s)){if("type"===t)continue;let i=a.get(t);if(!i){i=new Map;a.set(t,i)}const s=i.get(e)??0;i.set(e,s+1)}}for(const[i,s]of e)t[i]=s.computeTelemetryFinalData(t[i]);return t}}class PrintAnnotationStorage extends AnnotationStorage{#ke;constructor(t){super();const{map:e,hash:i,transfer:s}=t.serializable,n=structuredClone(e,s?{transfer:s}:null);this.#ke={map:n,hash:i,transfer:s}}get print(){unreachable("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#ke}}class FontLoader{#Pe=new Set;constructor({ownerDocument:t=globalThis.document,styleElement:e=null}){this._document=t;this.nativeFontFaces=new Set;this.styleElement=null;this.loadingRequests=[];this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t);this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t);this._document.fonts.delete(t)}insertRule(t){if(!this.styleElement){this.styleElement=this._document.createElement("style");this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement)}const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear();this.#Pe.clear();if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async loadSystemFont({systemFontInfo:t,_inspectFont:e}){if(t&&!this.#Pe.has(t.loadedName)){assert(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set.");if(this.isFontLoadingAPISupported){const{loadedName:i,src:s,style:n}=t,a=new FontFace(i,s,n);this.addNativeFontFace(a);try{await a.load();this.#Pe.add(i);e?.(t)}catch{warn(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`);this.removeNativeFontFace(a)}}else unreachable("Not implemented: loadSystemFont without the Font Loading API.")}}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;t.attached=!0;if(t.systemFontInfo){await this.loadSystemFont(t);return}if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){warn(`Failed to load font '${e.family}': '${i}'.`);t.disableFontFace=!0;throw i}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){return shadow(this,"isFontLoadingAPISupported",!!this._document?.fonts)}get isSyncFontLoadingSupported(){let t=!1;(e||"undefined"!=typeof navigator&&"string"==typeof navigator?.userAgent&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0);return shadow(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const{loadingRequests:e}=this,i={done:!1,complete:function completeRequest(){assert(!i.done,"completeRequest() cannot be called twice.");i.done=!0;for(;e.length>0&&e[0].done;){const t=e.shift();setTimeout(t.callback,0)}},callback:t};e.push(i);return i}get _loadTestFont(){return shadow(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,i,s){return t.substring(0,e)+s+t.substring(e+i)}let i,s;const n=this._document.createElement("canvas");n.width=1;n.height=1;const a=n.getContext("2d");let r=0;const o=`lt${Date.now()}${this.loadTestFontId++}`;let l=this._loadTestFont;l=spliceString(l,976,o.length,o);const h=1482184792;let d=int32(l,16);for(i=0,s=o.length-3;i<s;i+=4)d=d-h+int32(o,i)|0;i<o.length&&(d=d-h+int32(o+"XXX",i)|0);l=spliceString(l,16,4,function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}(d));const c=`@font-face {font-family:"${o}";src:${`url(data:font/opentype;base64,${btoa(l)});`}}`;this.insertRule(c);const u=this._document.createElement("div");u.style.visibility="hidden";u.style.width=u.style.height="10px";u.style.position="absolute";u.style.top=u.style.left="0px";for(const e of[t.loadedName,o]){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;u.append(t)}this._document.body.append(u);!function isFontReady(t,e){if(++r>30){warn("Load test font never loaded.");e();return}a.font="30px "+t;a.fillText(".",0,20);a.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(o,(()=>{u.remove();e.complete()}))}}class FontFaceObject{constructor(t,{disableFontFace:e=!1,inspectFont:i=null}){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.disableFontFace=!0===e;this._inspectFont=i}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this._inspectFont?.(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=bytesToString(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let i;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);i=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else i=`@font-face {font-family:"${this.loadedName}";src:${e}}`;this._inspectFont?.(this,e);return i}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let i;try{i=t.get(this.loadedName+"_path_"+e)}catch(t){warn(`getPathGenerator - ignoring character: "${t}".`)}if(!Array.isArray(i)||0===i.length)return this.compiledGlyphs[e]=function(t,e){};const s=[];for(let t=0,e=i.length;t<e;)switch(i[t++]){case it:{const[e,n,a,r,o,l]=i.slice(t,t+6);s.push((t=>t.bezierCurveTo(e,n,a,r,o,l)));t+=6}break;case st:{const[e,n]=i.slice(t,t+2);s.push((t=>t.moveTo(e,n)));t+=2}break;case nt:{const[e,n]=i.slice(t,t+2);s.push((t=>t.lineTo(e,n)));t+=2}break;case at:{const[e,n,a,r]=i.slice(t,t+4);s.push((t=>t.quadraticCurveTo(e,n,a,r)));t+=4}break;case rt:s.push((t=>t.restore()));break;case ot:s.push((t=>t.save()));break;case lt:assert(2===s.length,"Scale command is only valid at the third position.");break;case ht:{const[e,n,a,r,o,l]=i.slice(t,t+6);s.push((t=>t.transform(e,n,a,r,o,l)));t+=6}break;case dt:{const[e,n]=i.slice(t,t+2);s.push((t=>t.translate(e,n)));t+=2}}return this.compiledGlyphs[e]=function glyphDrawer(t,e){s[0](t);s[1](t);t.scale(e,-e);for(let e=2,i=s.length;e<i;e++)s[e](t)}}}if(e){var bt=Promise.withResolvers(),vt=null;(async()=>{const t=await import("fs"),e=await import("http"),i=await import("https"),s=await import("url");return new Map(Object.entries({fs:t,http:e,https:i,url:s,canvas:undefined,path2d:undefined}))})().then((t=>{vt=t;bt.resolve()}),(t=>{warn(`loadPackages: ${t}`);vt=new Map;bt.resolve()}))}class NodePackages{static get promise(){return bt.promise}static get(t){return vt?.get(t)}}const node_utils_fetchData=function(t){return NodePackages.get("fs").promises.readFile(t).then((t=>new Uint8Array(t)))};const At="Fill",yt="Stroke",wt="Shading";function applyBoundingBox(t,e){if(!e)return;const i=e[2]-e[0],s=e[3]-e[1],n=new Path2D;n.rect(e[0],e[1],i,s);t.clip(n)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&unreachable("Cannot initialize BaseShadingPattern.")}getPattern(){unreachable("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,i,s){let n;if(s===yt||s===At){const a=e.current.getClippedPathBoundingBox(s,getCurrentTransform(t))||[0,0,0,0],r=Math.ceil(a[2]-a[0])||1,o=Math.ceil(a[3]-a[1])||1,l=e.cachedCanvases.getCanvas("pattern",r,o,!0),h=l.context;h.clearRect(0,0,h.canvas.width,h.canvas.height);h.beginPath();h.rect(0,0,h.canvas.width,h.canvas.height);h.translate(-a[0],-a[1]);i=Util.transform(i,[1,0,0,1,a[0],a[1]]);h.transform(...e.baseTransform);this.matrix&&h.transform(...this.matrix);applyBoundingBox(h,this._bbox);h.fillStyle=this._createGradient(h);h.fill();n=t.createPattern(l.canvas,"no-repeat");const d=new DOMMatrix(i);n.setTransform(d)}else{applyBoundingBox(t,this._bbox);n=this._createGradient(t)}return n}}function drawTriangle(t,e,i,s,n,a,r,o){const l=e.coords,h=e.colors,d=t.data,c=4*t.width;let u;if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=a;a=r;r=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=r;r=o;o=u}if(l[i+1]>l[s+1]){u=i;i=s;s=u;u=a;a=r;r=u}const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,m=(l[s]+e.offsetX)*e.scaleX,f=(l[s+1]+e.offsetY)*e.scaleY,b=(l[n]+e.offsetX)*e.scaleX,v=(l[n+1]+e.offsetY)*e.scaleY;if(g>=v)return;const A=h[a],y=h[a+1],w=h[a+2],x=h[r],_=h[r+1],E=h[r+2],C=h[o],S=h[o+1],T=h[o+2],M=Math.round(g),k=Math.round(v);let P,D,F,R,I,L,O,N;for(let t=M;t<=k;t++){if(t<f){const e=t<g?0:(g-t)/(g-f);P=p-(p-m)*e;D=A-(A-x)*e;F=y-(y-_)*e;R=w-(w-E)*e}else{let e;e=t>v?1:f===v?0:(f-t)/(f-v);P=m-(m-b)*e;D=x-(x-C)*e;F=_-(_-S)*e;R=E-(E-T)*e}let e;e=t<g?0:t>v?1:(g-t)/(g-v);I=p-(p-b)*e;L=A-(A-C)*e;O=y-(y-S)*e;N=w-(w-T)*e;const i=Math.round(Math.min(P,I)),s=Math.round(Math.max(P,I));let n=c*t+4*i;for(let t=i;t<=s;t++){e=(P-t)/(P-I);e<0?e=0:e>1&&(e=1);d[n++]=D-(D-L)*e|0;d[n++]=F-(F-O)*e|0;d[n++]=R-(R-N)*e|0;d[n++]=255}}}function drawFigure(t,e,i){const s=e.coords,n=e.colors;let a,r;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(s.length/o)-1,h=o-1;for(a=0;a<l;a++){let e=a*o;for(let a=0;a<h;a++,e++){drawTriangle(t,i,s[e],s[e+1],s[e+o],n[e],n[e+1],n[e+o]);drawTriangle(t,i,s[e+o+1],s[e+1],s[e+o],n[e+o+1],n[e+1],n[e+o])}}break;case"triangles":for(a=0,r=s.length;a<r;a+=3)drawTriangle(t,i,s[a],s[a+1],s[a+2],n[a],n[a+1],n[a+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,i){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-s,r=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(a*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(r*t[1]*1.1)),3e3),h=a/o,d=r/l,c={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/h,scaleY:1/d},u=o+4,p=l+4,g=i.getCanvas("mesh",u,p,!1),m=g.context,f=m.createImageData(o,l);if(e){const t=f.data;for(let i=0,s=t.length;i<s;i+=4){t[i]=e[0];t[i+1]=e[1];t[i+2]=e[2];t[i+3]=255}}for(const t of this._figures)drawFigure(f,t,c);m.putImageData(f,2,2);return{canvas:g.canvas,offsetX:s-2*h,offsetY:n-2*d,scaleX:h,scaleY:d}}getPattern(t,e,i,s){applyBoundingBox(t,this._bbox);let n;if(s===wt)n=Util.singularValueDecompose2dScale(getCurrentTransform(t));else{n=Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=Util.singularValueDecompose2dScale(this.matrix);n=[n[0]*t[0],n[1]*t[1]]}}const a=this._createMeshCanvas(n,s===wt?null:this._background,e.cachedCanvases);if(s!==wt){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(a.offsetX,a.offsetY);t.scale(a.scaleX,a.scaleY);return t.createPattern(a.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const xt=1,_t=2;class TilingPattern{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,s,n){this.operatorList=t[2];this.matrix=t[3];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=i;this.canvasGraphicsFactory=s;this.baseTransform=n}createPatternCanvas(t){const e=this.operatorList,i=this.bbox,s=this.xstep,n=this.ystep,a=this.paintType,r=this.tilingType,o=this.color,l=this.canvasGraphicsFactory;info("TilingType: "+r);const h=i[0],d=i[1],c=i[2],u=i[3],p=Util.singularValueDecompose2dScale(this.matrix),g=Util.singularValueDecompose2dScale(this.baseTransform),m=[p[0]*g[0],p[1]*g[1]],f=this.getSizeAndScale(s,this.ctx.canvas.width,m[0]),b=this.getSizeAndScale(n,this.ctx.canvas.height,m[1]),v=t.cachedCanvases.getCanvas("pattern",f.size,b.size,!0),A=v.context,y=l.createCanvasGraphics(A);y.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(y,a,o);let w=h,x=d,_=c,E=u;if(h<0){w=0;_+=Math.abs(h)}if(d<0){x=0;E+=Math.abs(d)}A.translate(-f.scale*w,-b.scale*x);y.transform(f.scale,0,0,b.scale,0,0);A.save();this.clipBbox(y,w,x,_,E);y.baseTransform=getCurrentTransform(y.ctx);y.executeOperatorList(e);y.endDrawing();return{canvas:v.canvas,scaleX:f.scale,scaleY:b.scale,offsetX:w,offsetY:x}}getSizeAndScale(t,e,i){t=Math.abs(t);const s=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let n=Math.ceil(t*i);n>=s?n=s:i=n/t;return{scale:i,size:n}}clipBbox(t,e,i,s,n){const a=s-e,r=n-i;t.ctx.rect(e,i,a,r);t.current.updateRectMinMax(getCurrentTransform(t.ctx),[e,i,s,n]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,n=t.current;switch(e){case xt:const t=this.ctx;s.fillStyle=t.fillStyle;s.strokeStyle=t.strokeStyle;n.fillColor=t.fillStyle;n.strokeColor=t.strokeStyle;break;case _t:const a=Util.makeHexColor(i[0],i[1],i[2]);s.fillStyle=a;s.strokeStyle=a;n.fillColor=a;n.strokeColor=a;break;default:throw new FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let n=i;if(s!==wt){n=Util.transform(n,e.baseTransform);this.matrix&&(n=Util.transform(n,this.matrix))}const a=this.createPatternCanvas(e);let r=new DOMMatrix(n);r=r.translate(a.offsetX,a.offsetY);r=r.scale(1/a.scaleX,1/a.scaleY);const o=t.createPattern(a.canvas,"repeat");o.setTransform(r);return o}}function convertBlackAndWhiteToRGBA({src:t,srcPos:e=0,dest:i,width:s,height:n,nonBlackColor:a=4294967295,inverseDecode:r=!1}){const o=util_FeatureTest.isLittleEndian?4278190080:255,[l,h]=r?[a,o]:[o,a],d=s>>3,c=7&s,u=t.length;i=new Uint32Array(i.buffer);let p=0;for(let s=0;s<n;s++){for(const s=e+d;e<s;e++){const s=e<u?t[e]:255;i[p++]=128&s?h:l;i[p++]=64&s?h:l;i[p++]=32&s?h:l;i[p++]=16&s?h:l;i[p++]=8&s?h:l;i[p++]=4&s?h:l;i[p++]=2&s?h:l;i[p++]=1&s?h:l}if(0===c)continue;const s=e<u?t[e++]:255;for(let t=0;t<c;t++)i[p++]=s&1<<7-t?h:l}return{srcPos:e,destPos:p}}const Et=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,i){let s;if(void 0!==this.cache[t]){s=this.cache[t];this.canvasFactory.reset(s,e,i)}else{s=this.canvasFactory.create(e,i);this.cache[t]=s}return s}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,i,s,n,a,r,o,l,h){const[d,c,u,p,g,m]=getCurrentTransform(t);if(0===c&&0===u){const f=r*d+g,b=Math.round(f),v=o*p+m,A=Math.round(v),y=(r+l)*d+g,w=Math.abs(Math.round(y)-b)||1,x=(o+h)*p+m,_=Math.abs(Math.round(x)-A)||1;t.setTransform(Math.sign(d),0,0,Math.sign(p),b,A);t.drawImage(e,i,s,n,a,0,0,w,_);t.setTransform(d,c,u,p,g,m);return[w,_]}if(0===d&&0===p){const f=o*u+g,b=Math.round(f),v=r*c+m,A=Math.round(v),y=(o+h)*u+g,w=Math.abs(Math.round(y)-b)||1,x=(r+l)*c+m,_=Math.abs(Math.round(x)-A)||1;t.setTransform(0,Math.sign(c),Math.sign(u),0,b,A);t.drawImage(e,i,s,n,a,0,0,_,w);t.setTransform(d,c,u,p,g,m);return[_,w]}t.drawImage(e,i,s,n,a,r,o,l,h);return[Math.hypot(d,c)*l,Math.hypot(u,p)*h]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=i;this.textMatrixScale=1;this.fontMatrix=s;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=f;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps="none";this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,i){[e,i]=Util.applyTransform([e,i],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,i);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){const i=Util.applyTransform(e,t),s=Util.applyTransform(e.slice(2),t),n=Util.applyTransform([e[0],e[3]],t),a=Util.applyTransform([e[2],e[1]],t);this.minX=Math.min(this.minX,i[0],s[0],n[0],a[0]);this.minY=Math.min(this.minY,i[1],s[1],n[1],a[1]);this.maxX=Math.max(this.maxX,i[0],s[0],n[0],a[0]);this.maxY=Math.max(this.maxY,i[1],s[1],n[1],a[1])}updateScalingPathMinMax(t,e){Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.minY=Math.min(this.minY,e[1]);this.maxX=Math.max(this.maxX,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,n,a,r,o,l,h){const d=Util.bezierBoundingBox(e,i,s,n,a,r,o,l,h);h||this.updateRectMinMax(t,d)}getPathBoundingBox(t=At,e=null){const i=[this.minX,this.minY,this.maxX,this.maxY];if(t===yt){e||unreachable("Stroke bounding box must include transform.");const t=Util.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,n=t[1]*this.lineWidth/2;i[0]-=s;i[1]-=n;i[2]+=s;i[3]+=n}return i}updateClipFromPath(){const t=Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(t=At,e=null){return Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e){if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}const i=e.height,s=e.width,n=i%Et,a=(i-n)/Et,r=0===n?a:a+1,o=t.createImageData(s,Et);let l,h=0;const d=e.data,c=o.data;let u,p,g,m;if(e.kind===x.GRAYSCALE_1BPP){const e=d.byteLength,i=new Uint32Array(c.buffer,0,c.byteLength>>2),m=i.length,f=s+7>>3,b=4294967295,v=util_FeatureTest.isLittleEndian?4278190080:255;for(u=0;u<r;u++){g=u<a?Et:n;l=0;for(p=0;p<g;p++){const t=e-h;let n=0;const a=t>f?s:8*t-7,r=-8&a;let o=0,c=0;for(;n<r;n+=8){c=d[h++];i[l++]=128&c?b:v;i[l++]=64&c?b:v;i[l++]=32&c?b:v;i[l++]=16&c?b:v;i[l++]=8&c?b:v;i[l++]=4&c?b:v;i[l++]=2&c?b:v;i[l++]=1&c?b:v}for(;n<a;n++){if(0===o){c=d[h++];o=128}i[l++]=c&o?b:v;o>>=1}}for(;l<m;)i[l++]=0;t.putImageData(o,0,u*Et)}}else if(e.kind===x.RGBA_32BPP){p=0;m=s*Et*4;for(u=0;u<a;u++){c.set(d.subarray(h,h+m));h+=m;t.putImageData(o,0,p);p+=Et}if(u<r){m=s*n*4;c.set(d.subarray(h,h+m));t.putImageData(o,0,p)}}else{if(e.kind!==x.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);g=Et;m=s*g;for(u=0;u<r;u++){if(u>=a){g=n;m=s*g}l=0;for(p=m;p--;){c[l++]=d[h++];c[l++]=d[h++];c[l++]=d[h++];c[l++]=255}t.putImageData(o,0,u*Et)}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const i=e.height,s=e.width,n=i%Et,a=(i-n)/Et,r=0===n?a:a+1,o=t.createImageData(s,Et);let l=0;const h=e.data,d=o.data;for(let e=0;e<r;e++){const i=e<a?Et:n;({srcPos:l}=convertBlackAndWhiteToRGBA({src:h,srcPos:l,dest:d,width:s,height:i,nonBlackColor:0}));t.putImageData(o,0,e*Et)}}function copyCtxState(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const s of i)void 0!==t[s]&&(e[s]=t[s]);if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t){t.strokeStyle=t.fillStyle="#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}if(!e){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function getImageSmoothingEnabled(t,e){if(e)return!0;const i=Util.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]);i[1]=Math.fround(i[1]);const s=Math.fround((globalThis.devicePixelRatio||1)*PixelsPerInch.PDF_TO_CSS_UNITS);return i[0]<=s&&i[1]<=s}const Ct=["butt","round","square"],St=["miter","round","bevel"],Tt={},Mt={};class CanvasGraphics{constructor(t,e,i,s,n,{optionalContentConfig:a,markedContentStack:r=null},o,l){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=i;this.canvasFactory=s;this.filterFactory=n;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=r||[];this.optionalContentConfig=a;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=o;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.pageColors=l;this._cachedScaleForStroking=[-1,0];this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:i=!1,background:s=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height,r=this.ctx.fillStyle;this.ctx.fillStyle=s||"#ffffff";this.ctx.fillRect(0,0,n,a);this.ctx.fillStyle=r;if(i){const t=this.cachedCanvases.getCanvas("transparent",n,a);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...getCurrentTransform(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=getCurrentTransform(this.ctx)}executeOperatorList(t,e,i,s){const n=t.argsArray,a=t.fnArray;let r=e||0;const o=n.length;if(o===r)return r;const l=o-r>10&&"function"==typeof i,h=l?Date.now()+15:0;let d=0;const c=this.commonObjs,u=this.objs;let p;for(;;){if(void 0!==s&&r===s.nextBreakPoint){s.breakIt(r,i);return r}p=a[r];if(p!==X.dependency)this[p].apply(this,n[r]);else for(const t of n[r]){const e=t.startsWith("g_")?c:u;if(!e.has(t)){e.get(t,i);return r}}r++;if(r===o)return r;if(l&&++d>10){if(Date.now()>h){i();return r}d=0}}}#De(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#De();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.#Fe()}#Fe(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t;this.ctx.drawImage(this.ctx.canvas,0,0);this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width,s=t.height;let n,a,r=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,h=s,d="prescale1";for(;r>2&&l>1||o>2&&h>1;){let e=l,i=h;if(r>2&&l>1){e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2);r/=l/e}if(o>2&&h>1){i=h>=16384?Math.floor(h/2)-1||1:Math.ceil(h)/2;o/=h/i}n=this.cachedCanvases.getCanvas(d,e,i);a=n.context;a.clearRect(0,0,e,i);a.drawImage(t,0,0,l,h,0,0,e,i);t=n.canvas;l=e;h=i;d="prescale1"===d?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:h}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:s}=t,n=this.current.fillColor,a=this.current.patternFill,r=getCurrentTransform(e);let o,l,h,d;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;l=JSON.stringify(a?r:[r.slice(0,4),n]);o=this._cachedBitmapsMap.get(e);if(!o){o=new Map;this._cachedBitmapsMap.set(e,o)}const i=o.get(l);if(i&&!a){return{canvas:i,offsetX:Math.round(Math.min(r[0],r[2])+r[4]),offsetY:Math.round(Math.min(r[1],r[3])+r[5])}}h=i}if(!h){d=this.cachedCanvases.getCanvas("maskCanvas",i,s);putBinaryImageMask(d.context,t)}let c=Util.transform(r,[1/i,0,0,-1/s,0,0]);c=Util.transform(c,[1,0,0,1,0,-s]);const[u,p,g,m]=Util.getAxialAlignedBoundingBox([0,0,i,s],c),f=Math.round(g-u)||1,b=Math.round(m-p)||1,v=this.cachedCanvases.getCanvas("fillCanvas",f,b),A=v.context,y=u,w=p;A.translate(-y,-w);A.transform(...c);if(!h){h=this._scaleImage(d.canvas,getCurrentTransformInverse(A));h=h.img;o&&a&&o.set(l,h)}A.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(A),t.interpolate);drawImageAtIntegerCoords(A,h,0,0,h.width,h.height,0,0,i,s);A.globalCompositeOperation="source-in";const x=Util.transform(getCurrentTransformInverse(A),[1,0,0,1,-y,-w]);A.fillStyle=a?n.getPattern(e,this,x,At):n;A.fillRect(0,0,i,s);if(o&&!a){this.cachedCanvases.delete("fillCanvas");o.set(l,v.canvas)}return{canvas:v.canvas,offsetX:Math.round(y),offsetY:Math.round(w)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=Ct[t]}setLineJoin(t){this.ctx.lineJoin=St[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;if(void 0!==i.setLineDash){i.setLineDash(t);i.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i;this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i)}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,s=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx;this.ctx=s.context;const n=this.ctx;n.setTransform(...getCurrentTransform(this.suspendedCtx));copyCtxState(this.suspendedCtx,n);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,i){e.translate(t,i);this.__originalTranslate(t,i)};t.scale=function ctxScale(t,i){e.scale(t,i);this.__originalScale(t,i)};t.transform=function ctxTransform(t,i,s,n,a,r){e.transform(t,i,s,n,a,r);this.__originalTransform(t,i,s,n,a,r)};t.setTransform=function ctxSetTransform(t,i,s,n,a,r){e.setTransform(t,i,s,n,a,r);this.__originalSetTransform(t,i,s,n,a,r)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,i){e.moveTo(t,i);this.__originalMoveTo(t,i)};t.lineTo=function(t,i){e.lineTo(t,i);this.__originalLineTo(t,i)};t.bezierCurveTo=function(t,i,s,n,a,r){e.bezierCurveTo(t,i,s,n,a,r);this.__originalBezierCurveTo(t,i,s,n,a,r)};t.rect=function(t,i,s,n){e.rect(t,i,s,n);this.__originalRect(t,i,s,n)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(n,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;this.composeSMask(i,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}composeSMask(t,e,i,s){const n=s[0],a=s[1],r=s[2]-n,o=s[3]-a;if(0!==r&&0!==o){this.genericComposeSMask(e.context,i,r,o,e.subtype,e.backdrop,e.transferMap,n,a,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(i.canvas,0,0);t.restore()}}genericComposeSMask(t,e,i,s,n,a,r,o,l,h,d){let c=t.canvas,u=o-h,p=l-d;if(a)if(u<0||p<0||u+i>c.width||p+s>c.height){const t=this.cachedCanvases.getCanvas("maskExtension",i,s),e=t.context;e.drawImage(c,-u,-p);if(a.some((t=>0!==t))){e.globalCompositeOperation="destination-atop";e.fillStyle=Util.makeHexColor(...a);e.fillRect(0,0,i,s);e.globalCompositeOperation="source-over"}c=t.canvas;u=p=0}else if(a.some((t=>0!==t))){t.save();t.globalAlpha=1;t.setTransform(1,0,0,1,0,0);const e=new Path2D;e.rect(u,p,i,s);t.clip(e);t.globalCompositeOperation="destination-atop";t.fillStyle=Util.makeHexColor(...a);t.fillRect(u,p,i,s);t.restore()}e.save();e.globalAlpha=1;e.setTransform(1,0,0,1,0,0);"Alpha"===n&&r?e.filter=this.filterFactory.addAlphaFilter(r):"Luminosity"===n&&(e.filter=this.filterFactory.addLuminosityFilter(r));const g=new Path2D;g.rect(o,l,i,s);e.clip(g);e.globalCompositeOperation="destination-in";e.drawImage(c,u,p,i,s,o,l,i,s);e.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}}transform(t,e,i,s,n,a){this.ctx.transform(t,e,i,s,n,a);this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){const s=this.ctx,n=this.current;let a,r,o=n.x,l=n.y;const h=getCurrentTransform(s),d=0===h[0]&&0===h[3]||0===h[1]&&0===h[2],c=d?i.slice(0):null;for(let i=0,u=0,p=t.length;i<p;i++)switch(0|t[i]){case X.rectangle:o=e[u++];l=e[u++];const t=e[u++],i=e[u++],p=o+t,g=l+i;s.moveTo(o,l);if(0===t||0===i)s.lineTo(p,g);else{s.lineTo(p,l);s.lineTo(p,g);s.lineTo(o,g)}d||n.updateRectMinMax(h,[o,l,p,g]);s.closePath();break;case X.moveTo:o=e[u++];l=e[u++];s.moveTo(o,l);d||n.updatePathMinMax(h,o,l);break;case X.lineTo:o=e[u++];l=e[u++];s.lineTo(o,l);d||n.updatePathMinMax(h,o,l);break;case X.curveTo:a=o;r=l;o=e[u+4];l=e[u+5];s.bezierCurveTo(e[u],e[u+1],e[u+2],e[u+3],o,l);n.updateCurvePathMinMax(h,a,r,e[u],e[u+1],e[u+2],e[u+3],o,l,c);u+=6;break;case X.curveTo2:a=o;r=l;s.bezierCurveTo(o,l,e[u],e[u+1],e[u+2],e[u+3]);n.updateCurvePathMinMax(h,a,r,o,l,e[u],e[u+1],e[u+2],e[u+3],c);o=e[u+2];l=e[u+3];u+=4;break;case X.curveTo3:a=o;r=l;o=e[u+2];l=e[u+3];s.bezierCurveTo(e[u],e[u+1],o,l,o,l);n.updateCurvePathMinMax(h,a,r,e[u],e[u+1],o,l,o,l,c);u+=4;break;case X.closePath:s.closePath()}d&&n.updateScalingPathMinMax(h,c);n.setCurrentPoint(o,l)}closePath(){this.ctx.closePath()}stroke(t=!0){const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof i&&i?.getPattern){e.save();e.strokeStyle=i.getPattern(e,this,getCurrentTransformInverse(e),yt);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(t=!0){const e=this.ctx,i=this.current.fillColor;let s=!1;if(this.current.patternFill){e.save();e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),At);s=!0}const n=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==n)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();s&&e.restore();t&&this.consumePath(n)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=Tt}eoClip(){this.pendingClip=Mt}beginText(){this.current.textMatrix=i;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save();e.beginPath();for(const i of t){e.setTransform(...i.transform);e.translate(i.x,i.y);i.addToPath(e,i.fontSize)}e.restore();e.clip();e.beginPath();delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),n=this.current;if(!i)throw new Error(`Can't find font for ${t}`);n.fontMatrix=i.fontMatrix||s;0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||warn("Invalid font matrix for font "+t);if(e<0){e=-e;n.fontDirection=-1}else n.fontDirection=1;this.current.font=i;this.current.fontSize=e;if(i.isType3Font)return;const a=i.loadedName||"sans-serif",r=i.systemFontInfo?.css||`"${a}", ${i.fallbackName}`;let o="normal";i.black?o="900":i.bold&&(o="bold");const l=i.italic?"italic":"normal";let h=e;e<16?h=16:e>100&&(h=100);this.current.fontSizeScale=e/h;this.ctx.font=`${l} ${o} ${h}px ${r}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,i,s,n,a){this.current.textMatrix=[t,e,i,s,n,a];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,i,s){const n=this.ctx,a=this.current,r=a.font,o=a.textRenderingMode,l=a.fontSize/a.fontSizeScale,h=o&y,d=!!(o&w),c=a.patternFill&&!r.missingFile;let u;(r.disableFontFace||d||c)&&(u=r.getPathGenerator(this.commonObjs,t));if(r.disableFontFace||c){n.save();n.translate(e,i);n.beginPath();u(n,l);s&&n.setTransform(...s);h!==f&&h!==v||n.fill();h!==b&&h!==v||n.stroke();n.restore()}else{h!==f&&h!==v||n.fillText(t,e,i);h!==b&&h!==v||n.strokeText(t,e,i)}if(d){(this.pendingTextPaths||=[]).push({transform:getCurrentTransform(n),x:e,y:i,fontSize:l,addToPath:u})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){i=!0;break}return shadow(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const s=e.fontSize;if(0===s)return;const n=this.ctx,a=e.fontSizeScale,r=e.charSpacing,o=e.wordSpacing,l=e.fontDirection,h=e.textHScale*l,d=t.length,c=i.vertical,u=c?1:-1,p=i.defaultVMetrics,g=s*e.fontMatrix[0],m=e.textRenderingMode===f&&!i.disableFontFace&&!e.patternFill;n.save();n.transform(...e.textMatrix);n.translate(e.x,e.y+e.textRise);l>0?n.scale(h,-1):n.scale(h,1);let A;if(e.patternFill){n.save();const t=e.fillColor.getPattern(n,this,getCurrentTransformInverse(n),At);A=getCurrentTransform(n);n.restore();n.fillStyle=t}let w=e.lineWidth;const x=e.textMatrixScale;if(0===x||0===w){const t=e.textRenderingMode&y;t!==b&&t!==v||(w=this.getSinglePixelWidth())}else w/=x;if(1!==a){n.scale(a,a);w/=a}n.lineWidth=w;if(i.isInvalidPDFjsFont){const i=[];let s=0;for(const e of t){i.push(e.unicode);s+=e.width}n.fillText(i.join(""),0,0);e.x+=s*g*h;n.restore();this.compose();return}let _,E=0;for(_=0;_<d;++_){const e=t[_];if("number"==typeof e){E+=u*e*s/1e3;continue}let h=!1;const d=(e.isSpace?o:0)+r,f=e.fontChar,b=e.accent;let v,y,w=e.width;if(c){const t=e.vmetric||p,i=-(e.vmetric?t[1]:.5*w)*g,s=t[2]*g;w=t?-t[0]:w;v=i/a;y=(E+s)/a}else{v=E/a;y=0}if(i.remeasure&&w>0){const t=1e3*n.measureText(f).width/s*a;if(w<t&&this.isFontSubpixelAAEnabled){const e=w/t;h=!0;n.save();n.scale(e,1);v/=e}else w!==t&&(v+=(w-t)/2e3*s/a)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(m&&!b)n.fillText(f,v,y);else{this.paintChar(f,v,y,A);if(b){const t=v+s*b.offset.x/a,e=y-s*b.offset.y/a;this.paintChar(b.fontChar,t,e,A)}}E+=c?w*g-d*l:w*g+d*l;h&&n.restore()}c?e.y-=E:e.x+=E*h;n.restore();this.compose()}showType3Text(t){const e=this.ctx,i=this.current,n=i.font,a=i.fontSize,r=i.fontDirection,o=n.vertical?1:-1,l=i.charSpacing,h=i.wordSpacing,d=i.textHScale*r,c=i.fontMatrix||s,u=t.length;let p,g,m,f;if(!(i.textRenderingMode===A)&&0!==a){this._cachedScaleForStroking[0]=-1;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...i.textMatrix);e.translate(i.x,i.y);e.scale(d,r);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){f=o*g*a/1e3;this.ctx.translate(f,0);i.x+=f*d;continue}const s=(g.isSpace?h:0)+l,r=n.charProcOperatorList[g.operatorListId];if(!r){warn(`Type3 character "${g.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=g;this.save();e.scale(a,a);e.transform(...c);this.executeOperatorList(r);this.restore()}m=Util.applyTransform([g.width,0],c)[0]*a+s;e.translate(m,0);i.x+=m*d}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,s,n,a){this.ctx.rect(i,s,n-i,a-s);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],s=this.baseTransform||getCurrentTransform(this.ctx),n={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new TilingPattern(t,i,this.ctx,n,s)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,i){const s=Util.makeHexColor(t,e,i);this.ctx.strokeStyle=s;this.current.strokeColor=s}setFillRGBColor(t,e,i){const s=Util.makeHexColor(t,e,i);this.ctx.fillStyle=s;this.current.fillColor=s;this.current.patternFill=!1}_getPattern(t,e=null){let i;if(this.cachedPatterns.has(t))i=this.cachedPatterns.get(t);else{i=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)}(this.getObject(t));this.cachedPatterns.set(t,i)}e&&(i.matrix=e);return i}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,getCurrentTransformInverse(e),wt);const s=getCurrentTransformInverse(e);if(s){const{width:t,height:i}=e.canvas,[n,a,r,o]=Util.getAxialAlignedBoundingBox([0,0,t,i],s);this.ctx.fillRect(n,a,r-n,o-a)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){unreachable("Should not call beginInlineImage")}beginImageData(){unreachable("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);t&&this.transform(...t);this.baseTransform=getCurrentTransform(this.ctx);if(e){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i);this.current.updateRectMinMax(getCurrentTransform(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||info("TODO: Support non-isolated groups.");t.knockout&&warn("Knockout groups not supported.");const i=getCurrentTransform(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let s=Util.getAxialAlignedBoundingBox(t.bbox,getCurrentTransform(e));const n=[0,0,e.canvas.width,e.canvas.height];s=Util.intersect(s,n)||[0,0,0,0];const a=Math.floor(s[0]),r=Math.floor(s[1]),o=Math.max(Math.ceil(s[2])-a,1),l=Math.max(Math.ceil(s[3])-r,1);this.current.startNewPathAndClipBox([0,0,o,l]);let h="groupAt"+this.groupLevel;t.smask&&(h+="_smask_"+this.smaskCounter++%2);const d=this.cachedCanvases.getCanvas(h,o,l),c=d.context;c.translate(-a,-r);c.transform(...i);if(t.smask)this.smaskStack.push({canvas:d.canvas,context:c,offsetX:a,offsetY:r,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(a,r);e.save()}copyCtxState(e,c);this.ctx=c;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();this.ctx=i;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=getCurrentTransform(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const i=Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(i)}}beginAnnotation(t,e,i,s,n){this.#De();resetCtxToDefault(this.ctx);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(e){const s=e[2]-e[0],a=e[3]-e[1];if(n&&this.annotationCanvasMap){(i=i.slice())[4]-=e[0];i[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=s;e[3]=a;const[n,r]=Util.singularValueDecompose2dScale(getCurrentTransform(this.ctx)),{viewportScale:o}=this,l=Math.ceil(s*this.outputScaleX*o),h=Math.ceil(a*this.outputScaleY*o);this.annotationCanvas=this.canvasFactory.create(l,h);const{canvas:d,context:c}=this.annotationCanvas;this.annotationCanvasMap.set(t,d);this.annotationCanvas.savedCtx=this.ctx;this.ctx=c;this.ctx.save();this.ctx.setTransform(n,0,0,-r,0,a*r);resetCtxToDefault(this.ctx)}else{resetCtxToDefault(this.ctx);this.ctx.rect(e[0],e[1],s,a);this.ctx.clip();this.endPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...i);this.transform(...s)}endAnnotation(){if(this.annotationCanvas){this.ctx.restore();this.#Fe();this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const i=this.ctx,s=this.processingType3;if(s){void 0===s.compiled&&(s.compiled=function compileType3Glyph(t){const{width:e,height:i}=t;if(e>1e3||i>1e3)return null;const s=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),n=e+1;let a,r,o,l=new Uint8Array(n*(i+1));const h=e+7&-8;let d=new Uint8Array(h*i),c=0;for(const e of t.data){let t=128;for(;t>0;){d[c++]=e&t?0:255;t>>=1}}let u=0;c=0;if(0!==d[c]){l[0]=1;++u}for(r=1;r<e;r++){if(d[c]!==d[c+1]){l[r]=d[c]?2:1;++u}c++}if(0!==d[c]){l[r]=2;++u}for(a=1;a<i;a++){c=a*h;o=a*n;if(d[c-h]!==d[c]){l[o]=d[c]?1:8;++u}let t=(d[c]?4:0)+(d[c-h]?8:0);for(r=1;r<e;r++){t=(t>>2)+(d[c+1]?4:0)+(d[c-h+1]?8:0);if(s[t]){l[o+r]=s[t];++u}c++}if(d[c-h]!==d[c]){l[o+r]=d[c]?2:4;++u}if(u>1e3)return null}c=h*(i-1);o=a*n;if(0!==d[c]){l[o]=8;++u}for(r=1;r<e;r++){if(d[c]!==d[c+1]){l[o+r]=d[c]?4:8;++u}c++}if(0!==d[c]){l[o+r]=4;++u}if(u>1e3)return null;const p=new Int32Array([0,n,-1,0,-n,0,0,0,1]),g=new Path2D;for(a=0;u&&a<=i;a++){let t=a*n;const i=t+e;for(;t<i&&!l[t];)t++;if(t===i)continue;g.moveTo(t%n,a);const s=t;let r=l[t];do{const e=p[r];do{t+=e}while(!l[t]);const i=l[t];if(5!==i&&10!==i){r=i;l[t]=0}else{r=i&51*r>>4;l[t]&=r>>2|r<<2}g.lineTo(t%n,t/n|0);l[t]||--u}while(s!==t);--a}d=null;l=null;return function(t){t.save();t.scale(1/e,-1/i);t.translate(0,-i);t.fill(g);t.beginPath();t.restore()}}(t));if(s.compiled){s.compiled(i);return}}const n=this._createMaskCanvas(t),a=n.canvas;i.save();i.setTransform(1,0,0,1,0,0);i.drawImage(a,n.offsetX,n.offsetY);i.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,i=0,s=0,n,a){if(!this.contentVisible)return;t=this.getObject(t.data,t);const r=this.ctx;r.save();const o=getCurrentTransform(r);r.transform(e,i,s,n,0,0);const l=this._createMaskCanvas(t);r.setTransform(1,0,0,1,l.offsetX-o[4],l.offsetY-o[5]);for(let t=0,h=a.length;t<h;t+=2){const h=Util.transform(o,[e,i,s,n,a[t],a[t+1]]),[d,c]=Util.applyTransform([0,0],h);r.drawImage(l.canvas,d,c)}r.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,s=this.current.patternFill;for(const n of t){const{data:t,width:a,height:r,transform:o}=n,l=this.cachedCanvases.getCanvas("maskCanvas",a,r),h=l.context;h.save();putBinaryImageMask(h,this.getObject(t,n));h.globalCompositeOperation="source-in";h.fillStyle=s?i.getPattern(h,this,getCurrentTransformInverse(e),At):i;h.fillRect(0,0,a,r);h.restore();e.save();e.transform(...o);e.scale(1,-1);drawImageAtIntegerCoords(e,l.canvas,0,0,a,r,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):warn("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const n=this.getObject(t);if(!n){warn("Dependent image isn't ready yet");return}const a=n.width,r=n.height,o=[];for(let t=0,n=s.length;t<n;t+=2)o.push({transform:[e,0,0,i,s[t],s[t+1]],x:0,y:0,w:a,h:r});this.paintInlineImageXObjectGroup(n,o)}applyTransferMapsToCanvas(t){if("none"!==this.current.transferMaps){t.filter=this.current.transferMaps;t.drawImage(t.canvas,0,0);t.filter="none"}return t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:s}=t,n=this.cachedCanvases.getCanvas("inlineImage",i,s),a=n.context;a.filter=this.current.transferMaps;a.drawImage(e,0,0);a.filter="none";return n.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const i=t.width,s=t.height,n=this.ctx;this.save();if(!e){const{filter:t}=n;"none"!==t&&""!==t&&(n.filter="none")}n.scale(1/i,-1/s);let a;if(t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const e=this.cachedCanvases.getCanvas("inlineImage",i,s).context;putBinaryImageData(e,t);a=this.applyTransferMapsToCanvas(e)}const r=this._scaleImage(a,getCurrentTransformInverse(n));n.imageSmoothingEnabled=getImageSmoothingEnabled(getCurrentTransform(n),t.interpolate);drawImageAtIntegerCoords(n,r.img,0,0,r.paintWidth,r.paintHeight,0,-s,i,s);this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let s;if(t.bitmap)s=t.bitmap;else{const e=t.width,i=t.height,n=this.cachedCanvases.getCanvas("inlineImage",e,i).context;putBinaryImageData(n,t);s=this.applyTransferMapsToCanvas(n)}for(const t of e){i.save();i.transform(...t.transform);i.scale(1,-1);drawImageAtIntegerCoords(i,s,t.x,t.y,t.w,t.h,0,-1,1,1);i.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const i=this.ctx;if(this.pendingClip){e||(this.pendingClip===Mt?i.clip("evenodd"):i.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=getCurrentTransform(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),s=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,s)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:s,d:n}=this.ctx.getTransform();let a,r;if(0===i&&0===s){const i=Math.abs(e),s=Math.abs(n);if(i===s)if(0===t)a=r=1/i;else{const e=i*t;a=r=e<1?1/e:1}else if(0===t){a=1/i;r=1/s}else{const e=i*t,n=s*t;a=e<1?1/e:1;r=n<1?1/n:1}}else{const o=Math.abs(e*n-i*s),l=Math.hypot(e,i),h=Math.hypot(s,n);if(0===t){a=h/o;r=l/o}else{const e=t*o;a=h>e?h/e:1;r=l>e?l/e:1}}this._cachedScaleForStroking[0]=a;this._cachedScaleForStroking[1]=r}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[s,n]=this.getScaleForStroking();e.lineWidth=i||1;if(1===s&&1===n){e.stroke();return}const a=e.getLineDash();t&&e.save();e.scale(s,n);if(a.length>0){const t=Math.max(s,n);e.setLineDash(a.map((e=>e/t)));e.lineDashOffset/=t}e.stroke();t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}for(const t in X)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[X[t]]=CanvasGraphics.prototype[t]);class GlobalWorkerOptions{static#Re=null;static#Ie="";static get workerPort(){return this.#Re}static set workerPort(t){if(!("undefined"!=typeof Worker&&t instanceof Worker)&&null!==t)throw new Error("Invalid `workerPort` type.");this.#Re=t}static get workerSrc(){return this.#Ie}static set workerSrc(t){if("string"!=typeof t)throw new Error("Invalid `workerSrc` type.");this.#Ie=t}}const kt=1,Pt=2,Dt=1,Ft=2,Rt=3,It=4,Lt=5,Ot=6,Nt=7,Bt=8;function wrapReason(t){t instanceof Error||"object"==typeof t&&null!==t||unreachable('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new AbortException(t.message);case"MissingPDFException":return new MissingPDFException(t.message);case"PasswordException":return new PasswordException(t.message,t.code);case"UnexpectedResponseException":return new UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new UnknownErrorException(t.message,t.details);default:return new UnknownErrorException(t.message,t.toString())}}class MessageHandler{constructor(t,e,i){this.sourceName=t;this.targetName=e;this.comObj=i;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this.#Le(e);return}if(e.callback){const t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw new Error(`Cannot resolve callback ${t}`);delete this.callbackCapabilities[t];if(e.callback===kt)i.resolve(e.data);else{if(e.callback!==Pt)throw new Error("Unexpected callback case");i.reject(wrapReason(e.reason))}return}const s=this.actionHandler[e.action];if(!s)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,n=e.sourceName;new Promise((function(t){t(s(e.data))})).then((function(s){i.postMessage({sourceName:t,targetName:n,callback:kt,callbackId:e.callbackId,data:s})}),(function(s){i.postMessage({sourceName:t,targetName:n,callback:Pt,callbackId:e.callbackId,reason:wrapReason(s)})}))}else e.streamId?this.#Oe(e):s(e.data)};i.addEventListener("message",this._onComObjOnMessage)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,n=Promise.withResolvers();this.callbackCapabilities[s]=n;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(t){n.reject(t)}return n.promise}sendWithStream(t,e,i,s){const n=this.streamId++,a=this.sourceName,r=this.targetName,o=this.comObj;return new ReadableStream({start:i=>{const l=Promise.withResolvers();this.streamControllers[n]={controller:i,startCall:l,pullCall:null,cancelCall:null,isClosed:!1};o.postMessage({sourceName:a,targetName:r,action:t,streamId:n,data:e,desiredSize:i.desiredSize},s);return l.promise},pull:t=>{const e=Promise.withResolvers();this.streamControllers[n].pullCall=e;o.postMessage({sourceName:a,targetName:r,stream:Ot,streamId:n,desiredSize:t.desiredSize});return e.promise},cancel:t=>{assert(t instanceof Error,"cancel must have a valid reason");const e=Promise.withResolvers();this.streamControllers[n].cancelCall=e;this.streamControllers[n].isClosed=!0;o.postMessage({sourceName:a,targetName:r,stream:Dt,streamId:n,reason:wrapReason(t)});return e.promise}},i)}#Oe(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this,r=this.actionHandler[t.action],o={enqueue(t,a=1,r){if(this.isCancelled)return;const o=this.desiredSize;this.desiredSize-=a;if(o>0&&this.desiredSize<=0){this.sinkCapability=Promise.withResolvers();this.ready=this.sinkCapability.promise}n.postMessage({sourceName:i,targetName:s,stream:It,streamId:e,chunk:t},r)},close(){if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:Rt,streamId:e});delete a.streamSinks[e]}},error(t){assert(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;n.postMessage({sourceName:i,targetName:s,stream:Lt,streamId:e,reason:wrapReason(t)})}},sinkCapability:Promise.withResolvers(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};o.sinkCapability.resolve();o.ready=o.sinkCapability.promise;this.streamSinks[e]=o;new Promise((function(e){e(r(t.data,o))})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:Bt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:Bt,streamId:e,reason:wrapReason(t)})}))}#Le(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,n=this.comObj,a=this.streamControllers[e],r=this.streamSinks[e];switch(t.stream){case Bt:t.success?a.startCall.resolve():a.startCall.reject(wrapReason(t.reason));break;case Nt:t.success?a.pullCall.resolve():a.pullCall.reject(wrapReason(t.reason));break;case Ot:if(!r){n.postMessage({sourceName:i,targetName:s,stream:Nt,streamId:e,success:!0});break}r.desiredSize<=0&&t.desiredSize>0&&r.sinkCapability.resolve();r.desiredSize=t.desiredSize;new Promise((function(t){t(r.onPull?.())})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:Nt,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:Nt,streamId:e,reason:wrapReason(t)})}));break;case It:assert(a,"enqueue should have stream controller");if(a.isClosed)break;a.controller.enqueue(t.chunk);break;case Rt:assert(a,"close should have stream controller");if(a.isClosed)break;a.isClosed=!0;a.controller.close();this.#Ne(a,e);break;case Lt:assert(a,"error should have stream controller");a.controller.error(wrapReason(t.reason));this.#Ne(a,e);break;case Ft:t.success?a.cancelCall.resolve():a.cancelCall.reject(wrapReason(t.reason));this.#Ne(a,e);break;case Dt:if(!r)break;new Promise((function(e){e(r.onCancel?.(wrapReason(t.reason)))})).then((function(){n.postMessage({sourceName:i,targetName:s,stream:Ft,streamId:e,success:!0})}),(function(t){n.postMessage({sourceName:i,targetName:s,stream:Ft,streamId:e,reason:wrapReason(t)})}));r.sinkCapability.reject(wrapReason(t.reason));r.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#Ne(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]);delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}class Metadata{#Be;#He;constructor({parsedData:t,rawData:e}){this.#Be=t;this.#He=e}getRaw(){return this.#He}get(t){return this.#Be.get(t)??null}getAll(){return objectFromMap(this.#Be)}has(t){return this.#Be.has(t)}}const Ht=Symbol("INTERNAL");class OptionalContentGroup{#ze=!1;#Ue=!1;#je=!1;#$e=!0;constructor(t,{name:e,intent:i,usage:s}){this.#ze=!!(t&r);this.#Ue=!!(t&o);this.name=e;this.intent=i;this.usage=s}get visible(){if(this.#je)return this.#$e;if(!this.#$e)return!1;const{print:t,view:e}=this.usage;return this.#ze?"OFF"!==e?.viewState:!this.#Ue||"OFF"!==t?.printState}_setVisible(t,e,i=!1){t!==Ht&&unreachable("Internal method `_setVisible` called.");this.#je=i;this.#$e=e}}class OptionalContentConfig{#Ve=null;#We=new Map;#Ge=null;#qe=null;constructor(t,e=r){this.renderingIntent=e;this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#qe=t.order;for(const i of t.groups)this.#We.set(i.id,new OptionalContentGroup(e,i));if("OFF"===t.baseState)for(const t of this.#We.values())t._setVisible(Ht,!1);for(const e of t.on)this.#We.get(e)._setVisible(Ht,!0);for(const e of t.off)this.#We.get(e)._setVisible(Ht,!1);this.#Ge=this.getHash()}}#Xe(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let n;if(Array.isArray(e))n=this.#Xe(e);else{if(!this.#We.has(e)){warn(`Optional content group not found: ${e}`);return!0}n=this.#We.get(e).visible}switch(i){case"And":if(!n)return!1;break;case"Or":if(n)return!0;break;case"Not":return!n;default:return!0}}return"And"===i}isVisible(t){if(0===this.#We.size)return!0;if(!t){info("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#We.has(t.id)){warn(`Optional content group not found: ${t.id}`);return!0}return this.#We.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#Xe(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#We.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#We.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#We.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#We.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#We.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(!this.#We.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#We.has(e)){warn(`Optional content group not found: ${e}`);return!0}if(this.#We.get(e).visible)return!1}return!0}warn(`Unknown optional content policy ${t.policy}.`);return!0}warn(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0){const i=this.#We.get(t);if(i){i._setVisible(Ht,!!e,!0);this.#Ve=null}else warn(`Optional content group not found: ${t}`)}setOCGState({state:t,preserveRB:e}){let i;for(const e of t){switch(e){case"ON":case"OFF":case"Toggle":i=e;continue}const t=this.#We.get(e);if(t)switch(i){case"ON":t._setVisible(Ht,!0);break;case"OFF":t._setVisible(Ht,!1);break;case"Toggle":t._setVisible(Ht,!t.visible)}}this.#Ve=null}get hasInitialVisibility(){return null===this.#Ge||this.getHash()===this.#Ge}getOrder(){return this.#We.size?this.#qe?this.#qe.slice():[...this.#We.keys()]:null}getGroups(){return this.#We.size>0?objectFromMap(this.#We):null}getGroup(t){return this.#We.get(t)||null}getHash(){if(null!==this.#Ve)return this.#Ve;const t=new MurmurHash3_64;for(const[e,i]of this.#We)t.update(`${e}:${i.visible}`);return this.#Ve=t.hexdigest()}}class PDFDataTransportStream{constructor(t,{disableRange:e=!1,disableStream:i=!1}){assert(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');const{length:s,initialData:n,progressiveDone:a,contentDispositionFilename:r}=t;this._queuedChunks=[];this._progressiveDone=a;this._contentDispositionFilename=r;if(n?.length>0){const t=n instanceof Uint8Array&&n.byteLength===n.buffer.byteLength?n.buffer:new Uint8Array(n).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!i;this._isRangeSupported=!e;this._contentLength=s;this._fullRequestReader=null;this._rangeReaders=[];t.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));t.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));t.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));t.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));t.transportReady()}_onReceiveData({begin:t,chunk:e}){const i=e instanceof Uint8Array&&e.byteLength===e.buffer.byteLength?e.buffer:new Uint8Array(e).buffer;if(void 0===t)this._fullRequestReader?this._fullRequestReader._enqueue(i):this._queuedChunks.push(i);else{assert(this._rangeReaders.some((function(e){if(e._begin!==t)return!1;e._enqueue(i);return!0})),"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}class PDFDataTransportStreamReader{constructor(t,e,i=!1,s=null){this._stream=t;this._done=i||!1;this._filename=isPdfFile(s)?s:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,i){this._stream=t;this._begin=e;this._end=i;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}function validateRangeRequestCapabilities({getResponseHeader:t,isHttp:e,rangeChunkSize:i,disableRange:s}){const n={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(t("Content-Length"),10);if(!Number.isInteger(a))return n;n.suggestedLength=a;if(a<=2*i)return n;if(s||!e)return n;if("bytes"!==t("Accept-Ranges"))return n;if("identity"!==(t("Content-Encoding")||"identity"))return n;n.allowRangeRequests=!0;return n}function extractFilenameFromHeader(t){const e=t("Content-Disposition");if(e){let t=function getFilenameFromContentDispositionHeader(t){let e=!0,i=toParamRegExp("filename\\*","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}i=function rfc2231getparam(t){const e=[];let i;const s=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(i=s.exec(t));){let[,t,s,n]=i;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[s,n]}const n=[];for(let t=0;t<e.length&&t in e;++t){let[i,s]=e[t];s=rfc2616unquote(s);if(i){s=unescape(s);0===t&&(s=rfc5987decode(s))}n.push(s)}return n.join("")}(t);if(i)return fixupEncoding(rfc2047decode(i));i=toParamRegExp("filename","i").exec(t);if(i){i=i[1];let t=rfc2616unquote(i);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),n=stringToBytes(i);i=s.decode(n);e=!1}catch{}}return i}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');if(-1!==i){e[t]=e[t].slice(0,i);e.length=t+1}e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");return-1===e?t:textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,s){if("q"===i||"Q"===i)return textdecode(e,s=(s=s.replaceAll("_"," ")).replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{s=atob(s)}catch{}return textdecode(e,s)}))}return""}(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if(isPdfFile(t))return t}return null}function createResponseStatusError(t,e){return 404===t||0===t&&e.startsWith("file:")?new MissingPDFException('Missing PDF "'+e+'".'):new UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function validateResponseStatus(t){return 200===t||206===t}function createFetchOptions(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function createHeaders(t){const e=new Headers;for(const i in t){const s=t[i];void 0!==s&&e.append(i,s)}return e}function getArrayBuffer(t){if(t instanceof Uint8Array)return t.buffer;if(t instanceof ArrayBuffer)return t;warn(`getArrayBuffer - unexpected data format: ${t}`);return new Uint8Array(t).buffer}class PDFFetchStream{constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const i=e.url;fetch(i,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!validateResponseStatus(t.status))throw createResponseStatusError(t.status,i);this._reader=t.body.getReader();this._headersCapability.resolve();const getResponseHeader=e=>t.headers.get(e),{allowRangeRequests:e,suggestedLength:s}=validateRangeRequestCapabilities({getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=e;this._contentLength=s||this._contentLength;this._filename=extractFilenameFromHeader(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,i){this._stream=t;this._reader=null;this._loaded=0;const s=t.source;this._withCredentials=s.withCredentials||!1;this._readCapability=Promise.withResolvers();this._isStreamingSupported=!s.disableStream;this._abortController=new AbortController;this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${e}-${i-1}`);const n=s.url;fetch(n,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!validateResponseStatus(t.status))throw createResponseStatusError(t.status,n);this._readCapability.resolve();this._reader=t.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress?.({loaded:this._loaded});return{value:getArrayBuffer(t),done:!1}}cancel(t){this._reader?.cancel(t);this._abortController.abort()}}class NetworkManager{constructor(t,e={}){this.url=t;this.isHttp=/^https?:/i.test(t);this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null);this.withCredentials=e.withCredentials||!1;this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(t,e,i){const s={begin:t,end:e};for(const t in i)s[t]=i[t];return this.request(s)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,s=this.pendingRequests[i]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const i=this.httpHeaders[t];void 0!==i&&e.setRequestHeader(t,i)}if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);s.expectedStatus=206}else s.expectedStatus=200;e.responseType="arraybuffer";t.onError&&(e.onerror=function(i){t.onError(e.status)});e.onreadystatechange=this.onStateChange.bind(this,i);e.onprogress=this.onProgress.bind(this,i);s.onHeadersReceived=t.onHeadersReceived;s.onDone=t.onDone;s.onError=t.onError;s.onProgress=t.onProgress;e.send(null);return i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const s=i.xhr;if(s.readyState>=2&&i.onHeadersReceived){i.onHeadersReceived();delete i.onHeadersReceived}if(4!==s.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===s.status&&this.isHttp){i.onError?.(s.status);return}const n=s.status||200;if(!(200===n&&206===i.expectedStatus)&&n!==i.expectedStatus){i.onError?.(s.status);return}const a=function network_getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:stringToBytes(e).buffer}(s);if(206===n){const t=s.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);i.onDone({begin:parseInt(e[1],10),chunk:a})}else a?i.onDone({begin:0,chunk:a}):i.onError?.(s.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){assert(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const i=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);i.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;const i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url;this._fullRequestId=t.requestFull(i);this._headersReceivedCapability=Promise.withResolvers();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),getResponseHeader=t=>e.getResponseHeader(t),{allowRangeRequests:i,suggestedLength:s}=validateRangeRequestCapabilities({getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});i&&(this._isRangeSupported=!0);this._contentLength=s||this._contentLength;this._filename=extractFilenameFromHeader(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(t);this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=createResponseStatusError(t,this._url);this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,i){this._manager=t;const s={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._requestId=t.requestRange(e,i,s);this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError=createResponseStatusError(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=Promise.withResolvers();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}const zt=/^file:\/\/\/[a-zA-Z]:\//;class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrl(t){const e=NodePackages.get("url"),i=e.parse(t);if("file:"===i.protocol||i.host)return i;if(/^[a-z]:[/\\]/i.test(t))return e.parse(`file:///${t}`);i.host||(i.protocol="file:");return i}(t.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){assert(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,t,e):new PDFNodeStreamRangeReader(this,t,e);this._rangeRequestReaders.push(i);return i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}class BaseFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=Promise.withResolvers();this._headersCapability=Promise.withResolvers()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=Promise.withResolvers();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=Promise.withResolvers();return this.read()}this._loaded+=t.length;this.onProgress?.({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(t){super(t);const handleResponse=e=>{if(404===e.statusCode){const t=new MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t;this._headersCapability.reject(t);return}this._headersCapability.resolve();this._setReadableStream(e);const getResponseHeader=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:i,suggestedLength:s}=validateRangeRequestCapabilities({getResponseHeader,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=i;this._contentLength=s||this._contentLength;this._filename=extractFilenameFromHeader(getResponseHeader)};this._request=null;if("http:"===this._url.protocol){const e=NodePackages.get("http");this._request=e.request(createRequestOptions(this._url,t.httpHeaders),handleResponse)}else{const e=NodePackages.get("https");this._request=e.request(createRequestOptions(this._url,t.httpHeaders),handleResponse)}this._request.on("error",(t=>{this._storedError=t;this._headersCapability.reject(t)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);this._httpHeaders={};for(const e in t.httpHeaders){const i=t.httpHeaders[e];void 0!==i&&(this._httpHeaders[e]=i)}this._httpHeaders.Range=`bytes=${e}-${i-1}`;const handleResponse=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};this._request=null;if("http:"===this._url.protocol){const t=NodePackages.get("http");this._request=t.request(createRequestOptions(this._url,this._httpHeaders),handleResponse)}else{const t=NodePackages.get("https");this._request=t.request(createRequestOptions(this._url,this._httpHeaders),handleResponse)}this._request.on("error",(t=>{this._storedError=t}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(t){super(t);let e=decodeURIComponent(this._url.path);zt.test(this._url.href)&&(e=e.replace(/^\//,""));const i=NodePackages.get("fs");i.promises.lstat(e).then((t=>{this._contentLength=t.size;this._setReadableStream(i.createReadStream(e));this._headersCapability.resolve()}),(t=>{"ENOENT"===t.code&&(t=new MissingPDFException(`Missing PDF "${e}".`));this._storedError=t;this._headersCapability.reject(t)}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(t,e,i){super(t);let s=decodeURIComponent(this._url.path);zt.test(this._url.href)&&(s=s.replace(/^\//,""));const n=NodePackages.get("fs");this._setReadableStream(n.createReadStream(s,{start:e,end:i-1}))}}const Ut=30;class TextLayer{#Ke=Promise.withResolvers();#ft=null;#Ye=!1;#Qe=!!globalThis.FontInspector?.enabled;#Je=null;#Ze=null;#ti=0;#ei=0;#ii=null;#si=null;#ni=0;#ai=0;#ri=Object.create(null);#oi=[];#li=null;#hi=[];#di=new WeakMap;#ci=null;static#ui=new Map;static#pi=new Map;static#gi=null;static#mi=new Set;constructor({textContentSource:t,container:e,viewport:i}){if(t instanceof ReadableStream)this.#li=t;else{if("object"!=typeof t)throw new Error('No "textContentSource" parameter specified.');this.#li=new ReadableStream({start(e){e.enqueue(t);e.close()}})}this.#ft=this.#si=e;this.#ai=i.scale*(globalThis.devicePixelRatio||1);this.#ni=i.rotation;this.#Ze={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:null};const{pageWidth:s,pageHeight:n,pageX:a,pageY:r}=i.rawDims;this.#ci=[1,0,0,-1,-a,r+n];this.#ei=s;this.#ti=n;TextLayer.#fi();setLayerDimensions(e,i);this.#Ke.promise.catch((()=>{})).then((()=>{TextLayer.#mi.delete(this);this.#Ze=null;this.#ri=null}))}render(){const pump=()=>{this.#ii.read().then((({value:t,done:e})=>{if(e)this.#Ke.resolve();else{this.#Je??=t.lang;Object.assign(this.#ri,t.styles);this.#bi(t.items);pump()}}),this.#Ke.reject)};this.#ii=this.#li.getReader();TextLayer.#mi.add(this);pump();return this.#Ke.promise}update({viewport:t,onBefore:e=null}){const i=t.scale*(globalThis.devicePixelRatio||1),s=t.rotation;if(s!==this.#ni){e?.();this.#ni=s;setLayerDimensions(this.#si,{rotation:s})}if(i!==this.#ai){e?.();this.#ai=i;const t={prevFontSize:null,prevFontFamily:null,div:null,properties:null,ctx:TextLayer.#vi(this.#Je)};for(const e of this.#hi){t.properties=this.#di.get(e);t.div=e;this.#Ai(t)}}}cancel(){const t=new AbortException("TextLayer task cancelled.");this.#ii?.cancel(t).catch((()=>{}));this.#ii=null;this.#Ke.reject(t)}get textDivs(){return this.#hi}get textContentItemsStr(){return this.#oi}#bi(t){if(this.#Ye)return;this.#Ze.ctx??=TextLayer.#vi(this.#Je);const e=this.#hi,i=this.#oi;for(const s of t){if(e.length>1e5){warn("Ignoring additional textDivs for performance reasons.");this.#Ye=!0;return}if(void 0!==s.str){i.push(s.str);this.#yi(s)}else if("beginMarkedContentProps"===s.type||"beginMarkedContent"===s.type){const t=this.#ft;this.#ft=document.createElement("span");this.#ft.classList.add("markedContent");null!==s.id&&this.#ft.setAttribute("id",`${s.id}`);t.append(this.#ft)}else"endMarkedContent"===s.type&&(this.#ft=this.#ft.parentNode)}}#yi(t){const e=document.createElement("span"),i={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,fontSize:0};this.#hi.push(e);const s=Util.transform(this.#ci,t.transform);let n=Math.atan2(s[1],s[0]);const a=this.#ri[t.fontName];a.vertical&&(n+=Math.PI/2);const r=this.#Qe&&a.fontSubstitution||a.fontFamily,o=Math.hypot(s[2],s[3]),l=o*TextLayer.#wi(r,this.#Je);let h,d;if(0===n){h=s[4];d=s[5]-l}else{h=s[4]+l*Math.sin(n);d=s[5]-l*Math.cos(n)}const c="calc(var(--scale-factor)*",u=e.style;if(this.#ft===this.#si){u.left=`${(100*h/this.#ei).toFixed(2)}%`;u.top=`${(100*d/this.#ti).toFixed(2)}%`}else{u.left=`${c}${h.toFixed(2)}px)`;u.top=`${c}${d.toFixed(2)}px)`}u.fontSize=`${c}${(TextLayer.#gi*o).toFixed(2)}px)`;u.fontFamily=r;i.fontSize=o;e.setAttribute("role","presentation");e.textContent=t.str;e.dir=t.dir;this.#Qe&&(e.dataset.fontName=a.fontSubstitutionLoadedName||t.fontName);0!==n&&(i.angle=n*(180/Math.PI));let p=!1;if(t.str.length>1)p=!0;else if(" "!==t.str&&t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),i=Math.abs(t.transform[3]);e!==i&&Math.max(e,i)/Math.min(e,i)>1.5&&(p=!0)}p&&(i.canvasWidth=a.vertical?t.height:t.width);this.#di.set(e,i);this.#Ze.div=e;this.#Ze.properties=i;this.#Ai(this.#Ze);i.hasText&&this.#ft.append(e);if(i.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this.#ft.append(t)}}#Ai(t){const{div:e,properties:i,ctx:s,prevFontSize:n,prevFontFamily:a}=t,{style:r}=e;let o="";TextLayer.#gi>1&&(o=`scale(${1/TextLayer.#gi})`);if(0!==i.canvasWidth&&i.hasText){const{fontFamily:l}=r,{canvasWidth:h,fontSize:d}=i;if(n!==d||a!==l){s.font=`${d*this.#ai}px ${l}`;t.prevFontSize=d;t.prevFontFamily=l}const{width:c}=s.measureText(e.textContent);c>0&&(o=`scaleX(${h*this.#ai/c}) ${o}`)}0!==i.angle&&(o=`rotate(${i.angle}deg) ${o}`);o.length>0&&(r.transform=o)}static cleanup(){if(!(this.#mi.size>0)){this.#ui.clear();for(const{canvas:t}of this.#pi.values())t.remove();this.#pi.clear()}}static#vi(t=null){let e=this.#pi.get(t||="");if(!e){const i=document.createElement("canvas");i.className="hiddenCanvasElement";i.lang=t;document.body.append(i);e=i.getContext("2d",{alpha:!1,willReadFrequently:!0});this.#pi.set(t,e)}return e}static#fi(){if(null!==this.#gi)return;const t=document.createElement("div");t.style.opacity=0;t.style.lineHeight=1;t.style.fontSize="1px";t.textContent="X";document.body.append(t);this.#gi=t.getBoundingClientRect().height;t.remove()}static#wi(t,e){const i=this.#ui.get(t);if(i)return i;const s=this.#vi(e),n=s.font;s.canvas.width=s.canvas.height=Ut;s.font=`30px ${t}`;const a=s.measureText("");let r=a.fontBoundingBoxAscent,o=Math.abs(a.fontBoundingBoxDescent);if(r){const e=r/(r+o);this.#ui.set(t,e);s.canvas.width=s.canvas.height=0;s.font=n;return e}s.strokeStyle="red";s.clearRect(0,0,Ut,Ut);s.strokeText("g",0,0);let l=s.getImageData(0,0,Ut,Ut).data;o=0;for(let t=l.length-1-3;t>=0;t-=4)if(l[t]>0){o=Math.ceil(t/4/Ut);break}s.clearRect(0,0,Ut,Ut);s.strokeText("A",0,Ut);l=s.getImageData(0,0,Ut,Ut).data;r=0;for(let t=0,e=l.length;t<e;t+=4)if(l[t]>0){r=Ut-Math.floor(t/4/Ut);break}s.canvas.width=s.canvas.height=0;s.font=n;const h=r?r/(r+o):.8;this.#ui.set(t,h);return h}}function renderTextLayer(){deprecated("`renderTextLayer`, please use `TextLayer` instead.");const{textContentSource:t,container:e,viewport:i,...s}=arguments[0],n=Object.keys(s);n.length>0&&warn("Ignoring `renderTextLayer` parameters: "+n.join(", "));const a=new TextLayer({textContentSource:t,container:e,viewport:i}),{textDivs:r,textContentItemsStr:o}=a;return{promise:a.render(),textDivs:r,textContentItemsStr:o}}function updateTextLayer(){deprecated("`updateTextLayer`, please use `TextLayer` instead.")}class XfaText{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let i=null;const s=t.name;if("#text"===s)i=t.value;else{if(!XfaText.shouldBuildText(s))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}null!==i&&e.push({str:i});if(t.children)for(const e of t.children)walk(e)}(t);return i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}const jt=65536,$t=e?class NodeCanvasFactory extends BaseCanvasFactory{_createCanvas(t,e){return NodePackages.get("canvas").createCanvas(t,e)}}:class DOMCanvasFactory extends BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document,enableHWA:e=!1}={}){super({enableHWA:e});this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");i.width=t;i.height=e;return i}},Vt=e?class NodeCMapReaderFactory extends BaseCMapReaderFactory{_fetchData(t,e){return node_utils_fetchData(t).then((t=>({cMapData:t,compressionType:e})))}}:DOMCMapReaderFactory,Wt=e?class NodeFilterFactory extends BaseFilterFactory{}:class DOMFilterFactory extends BaseFilterFactory{#xi;#_i;#Ei;#Ci;#Si;#b=0;constructor({docId:t,ownerDocument:e=globalThis.document}={}){super();this.#Ei=t;this.#Ci=e}get#A(){return this.#xi||=new Map}get#Ti(){return this.#Si||=new Map}get#Mi(){if(!this.#_i){const t=this.#Ci.createElement("div"),{style:e}=t;e.visibility="hidden";e.contain="strict";e.width=e.height=0;e.position="absolute";e.top=e.left=0;e.zIndex=-1;const i=this.#Ci.createElementNS(ct,"svg");i.setAttribute("width",0);i.setAttribute("height",0);this.#_i=this.#Ci.createElementNS(ct,"defs");t.append(i);i.append(this.#_i);this.#Ci.body.append(t)}return this.#_i}#ki(t){if(1===t.length){const e=t[0],i=new Array(256);for(let t=0;t<256;t++)i[t]=e[t]/255;const s=i.join(",");return[s,s,s]}const[e,i,s]=t,n=new Array(256),a=new Array(256),r=new Array(256);for(let t=0;t<256;t++){n[t]=e[t]/255;a[t]=i[t]/255;r[t]=s[t]/255}return[n.join(","),a.join(","),r.join(",")]}addFilter(t){if(!t)return"none";let e=this.#A.get(t);if(e)return e;const[i,s,n]=this.#ki(t),a=1===t.length?i:`${i}${s}${n}`;e=this.#A.get(a);if(e){this.#A.set(t,e);return e}const r=`g_${this.#Ei}_transfer_map_${this.#b++}`,o=`url(#${r})`;this.#A.set(t,o);this.#A.set(a,o);const l=this.#Pi(r);this.#Di(i,s,n,l);return o}addHCMFilter(t,e){const i=`${t}-${e}`,s="base";let n=this.#Ti.get(s);if(n?.key===i)return n.url;if(n){n.filter?.remove();n.key=i;n.url="none";n.filter=null}else{n={key:i,url:"none",filter:null};this.#Ti.set(s,n)}if(!t||!e)return n.url;const a=this.#Fi(t);t=Util.makeHexColor(...a);const r=this.#Fi(e);e=Util.makeHexColor(...r);this.#Mi.style.color="";if("#000000"===t&&"#ffffff"===e||t===e)return n.url;const o=new Array(256);for(let t=0;t<=255;t++){const e=t/255;o[t]=e<=.03928?e/12.92:((e+.055)/1.055)**2.4}const l=o.join(","),h=`g_${this.#Ei}_hcm_filter`,d=n.filter=this.#Pi(h);this.#Di(l,l,l,d);this.#Ri(d);const getSteps=(t,e)=>{const i=a[t]/255,s=r[t]/255,n=new Array(e+1);for(let t=0;t<=e;t++)n[t]=i+t/e*(s-i);return n.join(",")};this.#Di(getSteps(0,5),getSteps(1,5),getSteps(2,5),d);n.url=`url(#${h})`;return n.url}addAlphaFilter(t){let e=this.#A.get(t);if(e)return e;const[i]=this.#ki([t]),s=`alpha_${i}`;e=this.#A.get(s);if(e){this.#A.set(t,e);return e}const n=`g_${this.#Ei}_alpha_map_${this.#b++}`,a=`url(#${n})`;this.#A.set(t,a);this.#A.set(s,a);const r=this.#Pi(n);this.#Ii(i,r);return a}addLuminosityFilter(t){let e,i,s=this.#A.get(t||"luminosity");if(s)return s;if(t){[e]=this.#ki([t]);i=`luminosity_${e}`}else i="luminosity";s=this.#A.get(i);if(s){this.#A.set(t,s);return s}const n=`g_${this.#Ei}_luminosity_map_${this.#b++}`,a=`url(#${n})`;this.#A.set(t,a);this.#A.set(i,a);const r=this.#Pi(n);this.#Li(r);t&&this.#Ii(e,r);return a}addHighlightHCMFilter(t,e,i,s,n){const a=`${e}-${i}-${s}-${n}`;let r=this.#Ti.get(t);if(r?.key===a)return r.url;if(r){r.filter?.remove();r.key=a;r.url="none";r.filter=null}else{r={key:a,url:"none",filter:null};this.#Ti.set(t,r)}if(!e||!i)return r.url;const[o,l]=[e,i].map(this.#Fi.bind(this));let h=Math.round(.2126*o[0]+.7152*o[1]+.0722*o[2]),d=Math.round(.2126*l[0]+.7152*l[1]+.0722*l[2]),[c,u]=[s,n].map(this.#Fi.bind(this));d<h&&([h,d,c,u]=[d,h,u,c]);this.#Mi.style.color="";const getSteps=(t,e,i)=>{const s=new Array(256),n=(d-h)/i,a=t/255,r=(e-t)/(255*i);let o=0;for(let t=0;t<=i;t++){const e=Math.round(h+t*n),i=a+t*r;for(let t=o;t<=e;t++)s[t]=i;o=e+1}for(let t=o;t<256;t++)s[t]=s[o-1];return s.join(",")},p=`g_${this.#Ei}_hcm_${t}_filter`,g=r.filter=this.#Pi(p);this.#Ri(g);this.#Di(getSteps(c[0],u[0],5),getSteps(c[1],u[1],5),getSteps(c[2],u[2],5),g);r.url=`url(#${p})`;return r.url}destroy(t=!1){if(!t||0===this.#Ti.size){if(this.#_i){this.#_i.parentNode.parentNode.remove();this.#_i=null}if(this.#xi){this.#xi.clear();this.#xi=null}this.#b=0}}#Li(t){const e=this.#Ci.createElementNS(ct,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0.59 0.11 0 0");t.append(e)}#Ri(t){const e=this.#Ci.createElementNS(ct,"feColorMatrix");e.setAttribute("type","matrix");e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0");t.append(e)}#Pi(t){const e=this.#Ci.createElementNS(ct,"filter");e.setAttribute("color-interpolation-filters","sRGB");e.setAttribute("id",t);this.#Mi.append(e);return e}#Oi(t,e,i){const s=this.#Ci.createElementNS(ct,e);s.setAttribute("type","discrete");s.setAttribute("tableValues",i);t.append(s)}#Di(t,e,i,s){const n=this.#Ci.createElementNS(ct,"feComponentTransfer");s.append(n);this.#Oi(n,"feFuncR",t);this.#Oi(n,"feFuncG",e);this.#Oi(n,"feFuncB",i)}#Ii(t,e){const i=this.#Ci.createElementNS(ct,"feComponentTransfer");e.append(i);this.#Oi(i,"feFuncA",t)}#Fi(t){this.#Mi.style.color=t;return getRGB(getComputedStyle(this.#Mi).getPropertyValue("color"))}},Gt=e?class NodeStandardFontDataFactory extends BaseStandardFontDataFactory{_fetchData(t){return node_utils_fetchData(t)}}:DOMStandardFontDataFactory;function getDocument(t={}){"string"==typeof t||t instanceof URL?t={url:t}:(t instanceof ArrayBuffer||ArrayBuffer.isView(t))&&(t={data:t});const i=new PDFDocumentLoadingTask,{docId:s}=i,n=t.url?function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(e&&"string"==typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}(t.url):null,a=t.data?function getDataProp(t){if(e&&"undefined"!=typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"==typeof t)return stringToBytes(t);if(t instanceof ArrayBuffer||ArrayBuffer.isView(t)||"object"==typeof t&&!isNaN(t?.length))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}(t.data):null,r=t.httpHeaders||null,o=!0===t.withCredentials,l=t.password??null,h=t.range instanceof PDFDataRangeTransport?t.range:null,d=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:jt;let c=t.worker instanceof PDFWorker?t.worker:null;const u=t.verbosity,p="string"!=typeof t.docBaseUrl||isDataScheme(t.docBaseUrl)?null:t.docBaseUrl,g="string"==typeof t.cMapUrl?t.cMapUrl:null,m=!1!==t.cMapPacked,f=t.CMapReaderFactory||Vt,b="string"==typeof t.standardFontDataUrl?t.standardFontDataUrl:null,v=t.StandardFontDataFactory||Gt,A=!0!==t.stopAtErrors,y=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,w=!1!==t.isEvalSupported,x="boolean"==typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!e,_=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,E="boolean"==typeof t.disableFontFace?t.disableFontFace:e,C=!0===t.fontExtraProperties,S=!0===t.enableXfa,T=t.ownerDocument||globalThis.document,M=!0===t.disableRange,k=!0===t.disableStream,P=!0===t.disableAutoFetch,D=!0===t.pdfBug,F=!0===t.enableHWA,R=h?h.length:t.length??NaN,I="boolean"==typeof t.useSystemFonts?t.useSystemFonts:!e&&!E,L="boolean"==typeof t.useWorkerFetch?t.useWorkerFetch:f===DOMCMapReaderFactory&&v===DOMStandardFontDataFactory&&g&&b&&isValidFetchUrl(g,document.baseURI)&&isValidFetchUrl(b,document.baseURI),O=t.canvasFactory||new $t({ownerDocument:T,enableHWA:F}),N=t.filterFactory||new Wt({docId:s,ownerDocument:T});setVerbosityLevel(u);const B={canvasFactory:O,filterFactory:N};if(!L){B.cMapReaderFactory=new f({baseUrl:g,isCompressed:m});B.standardFontDataFactory=new v({baseUrl:b})}if(!c){const t={verbosity:u,port:GlobalWorkerOptions.workerPort};c=t.port?PDFWorker.fromPort(t):new PDFWorker(t);i._worker=c}const H={docId:s,apiVersion:"4.4.168",data:a,password:l,disableAutoFetch:P,rangeChunkSize:d,length:R,docBaseUrl:p,enableXfa:S,evaluatorOptions:{maxImageSize:y,disableFontFace:E,ignoreErrors:A,isEvalSupported:w,isOffscreenCanvasSupported:x,canvasMaxAreaInBytes:_,fontExtraProperties:C,useSystemFonts:I,cMapUrl:L?g:null,standardFontDataUrl:L?b:null}},z={disableFontFace:E,fontExtraProperties:C,ownerDocument:T,pdfBug:D,styleElement:null,loadingParams:{disableAutoFetch:P,enableXfa:S}};c.promise.then((function(){if(i.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const t=c.messageHandler.sendWithPromise("GetDocRequest",H,a?[a.buffer]:null);let l;if(h)l=new PDFDataTransportStream(h,{disableRange:M,disableStream:k});else if(!a){if(!n)throw new Error("getDocument - no `url` parameter provided.");l=(t=>{if(e){return function(){return"undefined"!=typeof fetch&&"undefined"!=typeof Response&&"body"in Response.prototype}()&&isValidFetchUrl(t.url)?new PDFFetchStream(t):new PDFNodeStream(t)}return isValidFetchUrl(t.url)?new PDFFetchStream(t):new PDFNetworkStream(t)})({url:n,length:R,httpHeaders:r,withCredentials:o,rangeChunkSize:d,disableRange:M,disableStream:k})}return t.then((t=>{if(i.destroyed)throw new Error("Loading aborted");if(c.destroyed)throw new Error("Worker was destroyed");const e=new MessageHandler(s,t,c.port),n=new WorkerTransport(e,i,l,z,B);i._transport=n;e.send("Ready",null)}))})).catch(i._capability.reject);return i}function isRefProxy(t){return"object"==typeof t&&Number.isInteger(t?.num)&&t.num>=0&&Number.isInteger(t?.gen)&&t.gen>=0}class PDFDocumentLoadingTask{static#Ei=0;constructor(){this._capability=Promise.withResolvers();this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#Ei++;this.destroyed=!1;this.onPassword=null;this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0);await(this._transport?.destroy())}catch(t){this._worker?.port&&delete this._worker._pendingDestroy;throw t}this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}}class PDFDataRangeTransport{constructor(t,e,i=!1,s=null){this.length=t;this.initialData=e;this.progressiveDone=i;this.contentDispositionFilename=s;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=Promise.withResolvers()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){unreachable("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getOptionalContentConfig(e)}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}cachedPageNumber(t){return this._transport.cachedPageNumber(t)}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}class PDFPageProxy{#Ni=null;#Bi=!1;constructor(t,e,i,s=!1){this._pageIndex=t;this._pageInfo=e;this._transport=i;this._stats=s?new StatTimer:null;this._pdfBug=s;this.commonObjs=i.commonObjs;this.objs=new PDFObjects;this._maybeCleanupAfterRender=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:s=0,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:s,dontFlip:n})}getAnnotations({intent:t="display"}={}){const{renderingIntent:e}=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return shadow(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:i="display",annotationMode:s=u.ENABLE,transform:n=null,background:a=null,optionalContentConfigPromise:r=null,annotationCanvasMap:l=null,pageColors:h=null,printAnnotationStorage:d=null}){this._stats?.time("Overall");const c=this._transport.getRenderingIntent(i,s,d),{renderingIntent:p,cacheKey:g}=c;this.#Bi=!1;this.#Hi();r||=this._transport.getOptionalContentConfig(p);let m=this._intentStates.get(g);if(!m){m=Object.create(null);this._intentStates.set(g,m)}if(m.streamReaderCancelTimeout){clearTimeout(m.streamReaderCancelTimeout);m.streamReaderCancelTimeout=null}const f=!!(p&o);if(!m.displayReadyCapability){m.displayReadyCapability=Promise.withResolvers();m.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(c)}const complete=t=>{m.renderTasks.delete(b);(this._maybeCleanupAfterRender||f)&&(this.#Bi=!0);this.#zi(!f);if(t){b.capability.reject(t);this._abortOperatorList({intentState:m,reason:t instanceof Error?t:new Error(t)})}else b.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall");globalThis.Stats?.enabled&&globalThis.Stats.add(this.pageNumber,this._stats)}},b=new InternalRenderTask({callback:complete,params:{canvasContext:t,viewport:e,transform:n,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:m.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!f,pdfBug:this._pdfBug,pageColors:h});(m.renderTasks||=new Set).add(b);const v=b.task;Promise.all([m.displayReadyCapability.promise,r]).then((([t,e])=>{if(this.destroyed)complete();else{this._stats?.time("Rendering");if(!(e.renderingIntent&p))throw new Error("Must use the same `intent`-argument when calling the `PDFPageProxy.render` and `PDFDocumentProxy.getOptionalContentConfig` methods.");b.initializeGraphics({transparency:t,optionalContentConfig:e});b.operatorListChanged()}})).catch(complete);return v}getOperatorList({intent:t="display",annotationMode:e=u.ENABLE,printAnnotationStorage:i=null}={}){const s=this._transport.getRenderingIntent(t,e,i,!0);let n,a=this._intentStates.get(s.cacheKey);if(!a){a=Object.create(null);this._intentStates.set(s.cacheKey,a)}if(!a.opListReadCapability){n=Object.create(null);n.operatorListChanged=function operatorListChanged(){if(a.operatorList.lastChunk){a.opListReadCapability.resolve(a.operatorList);a.renderTasks.delete(n)}};a.opListReadCapability=Promise.withResolvers();(a.renderTasks||=new Set).add(n);a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats?.time("Page Request");this._pumpOperatorList(s)}return a.opListReadCapability.promise}streamTextContent({includeMarkedContent:t=!1,disableNormalization:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){const s=e.getReader(),n={items:[],styles:Object.create(null),lang:null};!function pump(){s.read().then((function({value:e,done:i}){if(i)t(n);else{n.lang??=e.lang;Object.assign(n.styles,e.styles);n.items.push(...e.items);pump()}}),i)}()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const i of e.renderTasks){t.push(i.completed);i.cancel()}}this.objs.clear();this.#Bi=!1;this.#Hi();return Promise.all(t)}cleanup(t=!1){this.#Bi=!0;const e=this.#zi(!1);t&&e&&(this._stats&&=new StatTimer);return e}#zi(t=!1){this.#Hi();if(!this.#Bi||this.destroyed)return!1;if(t){this.#Ni=setTimeout((()=>{this.#Ni=null;this.#zi(!1)}),5e3);return!1}for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this.#Bi=!1;return!0}#Hi(){if(this.#Ni){clearTimeout(this.#Ni);this.#Ni=null}}_startRenderPage(t,e){const i=this._intentStates.get(e);if(i){this._stats?.timeEnd("Page Request");i.displayReadyCapability?.resolve(t)}}_renderPageChunk(t,e){for(let i=0,s=t.length;i<s;i++){e.operatorList.fnArray.push(t.fnArray[i]);e.operatorList.argsArray.push(t.argsArray[i])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this.#zi(!0)}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageSerializable:i}){const{map:s,transfer:n}=i,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s},n).getReader(),r=this._intentStates.get(e);r.streamReader=a;const pump=()=>{a.read().then((({value:t,done:e})=>{if(e)r.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,r);pump()}}),(t=>{r.streamReader=null;if(!this._transport.destroyed){if(r.operatorList){r.operatorList.lastChunk=!0;for(const t of r.renderTasks)t.operatorListChanged();this.#zi(!0)}if(r.displayReadyCapability)r.displayReadyCapability.reject(t);else{if(!r.opListReadCapability)throw t;r.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:i=!1}){if(t.streamReader){if(t.streamReaderCancelTimeout){clearTimeout(t.streamReaderCancelTimeout);t.streamReaderCancelTimeout=null}if(!i){if(t.renderTasks.size>0)return;if(e instanceof RenderingCancelledException){let i=100;e.extraDelay>0&&e.extraDelay<1e3&&(i+=e.extraDelay);t.streamReaderCancelTimeout=setTimeout((()=>{t.streamReaderCancelTimeout=null;this._abortOperatorList({intentState:t,reason:e,force:!0})}),i);return}}t.streamReader.cancel(new AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,i]of this._intentStates)if(i===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}class LoopbackPort{#Ui=new Set;#ji=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,e?{transfer:e}:null)};this.#ji.then((()=>{for(const t of this.#Ui)t.call(this,i)}))}addEventListener(t,e){this.#Ui.add(e)}removeEventListener(t,e){this.#Ui.delete(e)}terminate(){this.#Ui.clear()}}const qt={isWorkerDisabled:!1,fakeWorkerId:0};if(e){qt.isWorkerDisabled=!0;GlobalWorkerOptions.workerSrc||="./pdf.worker.mjs"}qt.isSameOrigin=function(t,e){let i;try{i=new URL(t);if(!i.origin||"null"===i.origin)return!1}catch{return!1}const s=new URL(e,i);return i.origin===s.origin};qt.createCDNWrapper=function(t){const e=`await import("${t}");`;return URL.createObjectURL(new Blob([e],{type:"text/javascript"}))};class PDFWorker{static#$i;constructor({name:t=null,port:e=null,verbosity:i=getVerbosityLevel()}={}){this.name=t;this.destroyed=!1;this.verbosity=i;this._readyCapability=Promise.withResolvers();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){if(PDFWorker.#$i?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");(PDFWorker.#$i||=new WeakMap).set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return e?Promise.all([NodePackages.promise,this._readyCapability.promise]):this._readyCapability.promise}#Vi(){this._readyCapability.resolve();this._messageHandler.send("configure",{verbosity:this.verbosity})}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this.#Vi()}_initialize(){if(qt.isWorkerDisabled||PDFWorker.#Wi){this._setupFakeWorker();return}let{workerSrc:t}=PDFWorker;try{qt.isSameOrigin(window.location.href,t)||(t=qt.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t,{type:"module"}),i=new MessageHandler("main","worker",e),terminateEarly=()=>{s.abort();i.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},s=new AbortController;e.addEventListener("error",(()=>{this._webWorker||terminateEarly()}),{signal:s.signal});i.on("test",(t=>{s.abort();if(!this.destroyed&&t){this._messageHandler=i;this._port=e;this._webWorker=e;this.#Vi()}else terminateEarly()}));i.on("ready",(t=>{s.abort();if(this.destroyed)terminateEarly();else try{sendTest()}catch{this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};sendTest();return}catch{info("The worker has been disabled.")}this._setupFakeWorker()}_setupFakeWorker(){if(!qt.isWorkerDisabled){warn("Setting up fake worker.");qt.isWorkerDisabled=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const i="fake"+qt.fakeWorkerId++,s=new MessageHandler(i+"_worker",i,e);t.setup(s,e);this._messageHandler=new MessageHandler(i,i+"_worker",e);this.#Vi()})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}PDFWorker.#$i?.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#$i?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(GlobalWorkerOptions.workerSrc)return GlobalWorkerOptions.workerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get#Wi(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){return shadow(this,"_setupFakeWorkerGlobal",(async()=>{if(this.#Wi)return this.#Wi;return(await import(this.workerSrc)).WorkerMessageHandler})())}}class WorkerTransport{#Gi=new Map;#qi=new Map;#Xi=new Map;#Ki=new Map;#Yi=null;constructor(t,e,i,s,n){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new FontLoader({ownerDocument:s.ownerDocument,styleElement:s.styleElement});this.loadingParams=s.loadingParams;this._params=s;this.canvasFactory=n.canvasFactory;this.filterFactory=n.filterFactory;this.cMapReaderFactory=n.cMapReaderFactory;this.standardFontDataFactory=n.standardFontDataFactory;this.destroyed=!1;this.destroyCapability=null;this._networkStream=i;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=Promise.withResolvers();this.setupMessageHandler()}#Qi(t,e=null){const i=this.#Gi.get(t);if(i)return i;const s=this.messageHandler.sendWithPromise(t,e);this.#Gi.set(t,s);return s}get annotationStorage(){return shadow(this,"annotationStorage",new AnnotationStorage)}getRenderingIntent(t,e=u.ENABLE,i=null,s=!1){let n=r,p=ft;switch(t){case"any":n=a;break;case"display":break;case"print":n=o;break;default:warn(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case u.DISABLE:n+=d;break;case u.ENABLE:break;case u.ENABLE_FORMS:n+=l;break;case u.ENABLE_STORAGE:n+=h;p=(n&o&&i instanceof PrintAnnotationStorage?i:this.annotationStorage).serializable;break;default:warn(`getRenderingIntent - invalid annotationMode: ${e}`)}s&&(n+=c);return{renderingIntent:n,cacheKey:`${n}_${p.hash}`,annotationStorageSerializable:p}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=Promise.withResolvers();this.#Yi?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#qi.values())t.push(e._destroy());this.#qi.clear();this.#Xi.clear();this.#Ki.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#Gi.clear();this.filterFactory.destroy();TextLayer.cleanup();this._networkStream?.cancelAllRequests(new AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{assert(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(t=>{const i=Promise.withResolvers(),s=this._fullReader;s.headersReady.then((()=>{if(!s.isStreamingSupported||!s.isRangeSupported){this._lastProgress&&e.onProgress?.(this._lastProgress);s.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}i.resolve({isStreamingSupported:s.isStreamingSupported,isRangeSupported:s.isRangeSupported,contentLength:s.contentLength})}),i.reject);return i.promise}));t.on("GetRangeReader",((t,e)=>{assert(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);if(i){e.onPull=()=>{i.read().then((function({value:t,done:i}){if(i)e.close();else{assert(t instanceof ArrayBuffer,"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{i.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(function(t){let i;switch(t.name){case"PasswordException":i=new PasswordException(t.message,t.code);break;case"InvalidPDFException":i=new InvalidPDFException(t.message);break;case"MissingPDFException":i=new MissingPDFException(t.message);break;case"UnexpectedResponseException":i=new UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":i=new UnknownErrorException(t.message,t.details);break;default:unreachable("DocException - expected a valid Error.")}e._capability.reject(i)}));t.on("PasswordRequest",(t=>{this.#Yi=Promise.withResolvers();if(e.onPassword){const updatePassword=t=>{t instanceof Error?this.#Yi.reject(t):this.#Yi.resolve({password:t})};try{e.onPassword(updatePassword,t.code)}catch(t){this.#Yi.reject(t)}}else this.#Yi.reject(new PasswordException(t.message,t.code));return this.#Yi.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#qi.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,i,s])=>{if(this.destroyed)return null;if(this.commonObjs.has(e))return null;switch(i){case"Font":const{disableFontFace:n,fontExtraProperties:a,pdfBug:r}=this._params;if("error"in s){const t=s.error;warn(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}const o=r&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,l=new FontFaceObject(s,{disableFontFace:n,inspectFont:o});this.fontLoader.bind(l).catch((()=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!a&&l.data&&(l.data=null);this.commonObjs.resolve(e,l)}));break;case"CopyLocalImage":const{imageRef:h}=s;assert(h,"The imageRef must be defined.");for(const t of this.#qi.values())for(const[,i]of t.objs)if(i?.ref===h){if(!i.dataLen)return null;this.commonObjs.resolve(e,structuredClone(i));return i.dataLen}break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(e,s);break;default:throw new Error(`Got unknown common object type ${i}`)}return null}));t.on("obj",(([t,e,i,s])=>{if(this.destroyed)return;const n=this.#qi.get(e);if(!n.objs.has(t))if(0!==n._intentStates.size)switch(i){case"Image":n.objs.resolve(t,s);s?.dataLen>1e7&&(n._maybeCleanupAfterRender=!0);break;case"Pattern":n.objs.resolve(t,s);break;default:throw new Error(`Got unknown object type ${i}`)}else s?.bitmap?.close()}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&warn("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfer:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#Xi.get(e);if(i)return i;const s=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((i=>{if(this.destroyed)throw new Error("Transport destroyed");i.refStr&&this.#Ki.set(i.refStr,t);const s=new PDFPageProxy(e,i,this,this._params.pdfBug);this.#qi.set(e,s);return s}));this.#Xi.set(e,s);return s}getPageIndex(t){return isRefProxy(t)?this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen}):Promise.reject(new Error("Invalid pageIndex request."))}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#Qi("GetFieldObjects")}hasJSActions(){return this.#Qi("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#Qi("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(t){return this.#Qi("GetOptionalContentConfig").then((e=>new OptionalContentConfig(e,t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#Gi.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));this.#Gi.set(t,i);return i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#qi.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#Gi.clear();this.filterFactory.destroy(!0);TextLayer.cleanup()}}cachedPageNumber(t){if(!isRefProxy(t))return null;const e=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;return this.#Ki.get(e)??null}}const Xt=Symbol("INITIAL_DATA");class PDFObjects{#Ji=Object.create(null);#Zi(t){return this.#Ji[t]||={...Promise.withResolvers(),data:Xt}}get(t,e=null){if(e){const i=this.#Zi(t);i.promise.then((()=>e(i.data)));return null}const i=this.#Ji[t];if(!i||i.data===Xt)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#Ji[t];return!!e&&e.data!==Xt}resolve(t,e=null){const i=this.#Zi(t);i.data=e;i.resolve()}clear(){for(const t in this.#Ji){const{data:e}=this.#Ji[t];e?.bitmap?.close()}this.#Ji=Object.create(null)}*[Symbol.iterator](){for(const t in this.#Ji){const{data:e}=this.#Ji[t];e!==Xt&&(yield[t,e])}}}class RenderTask{#ts=null;constructor(t){this.#ts=t;this.onContinue=null}get promise(){return this.#ts.capability.promise}cancel(t=0){this.#ts.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#ts.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#ts;return t.form||t.canvas&&e?.size>0}}class InternalRenderTask{#es=null;static#is=new WeakSet;constructor({callback:t,params:e,objs:i,commonObjs:s,annotationCanvasMap:n,operatorList:a,pageIndex:r,canvasFactory:o,filterFactory:l,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:c=null}){this.callback=t;this.params=e;this.objs=i;this.commonObjs=s;this.annotationCanvasMap=n;this.operatorListIdx=null;this.operatorList=a;this._pageIndex=r;this.canvasFactory=o;this.filterFactory=l;this._pdfBug=d;this.pageColors=c;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===h&&"undefined"!=typeof window;this.cancelled=!1;this.capability=Promise.withResolvers();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#is.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#is.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:i,viewport:s,transform:n,background:a}=this.params;this.gfx=new CanvasGraphics(i,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:e},this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:n,viewport:s,transparency:t,background:a});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback?.()}cancel(t=null,e=0){this.running=!1;this.cancelled=!0;this.gfx?.endDrawing();if(this.#es){window.cancelAnimationFrame(this.#es);this.#es=null}InternalRenderTask.#is.delete(this._canvas);this.callback(t||new RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){if(this.graphicsReady){this.stepper?.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?this.#es=window.requestAnimationFrame((()=>{this.#es=null;this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();InternalRenderTask.#is.delete(this._canvas);this.callback()}}}}}const Kt="4.4.168",Yt="19fbc8998";function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function scaleAndClamp(t){return Math.max(0,Math.min(255,255*t))}class ColorConverters{static CMYK_G([t,e,i,s]){return["G",1-Math.min(1,.3*t+.59*i+.11*e+s)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_rgb([t]){return[t=scaleAndClamp(t),t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,i]){return["G",.3*t+.59*e+.11*i]}static RGB_rgb(t){return t.map(scaleAndClamp)}static RGB_HTML(t){return`#${t.map(makeColorComp).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB([t,e,i,s]){return["RGB",1-Math.min(1,t+s),1-Math.min(1,i+s),1-Math.min(1,e+s)]}static CMYK_rgb([t,e,i,s]){return[scaleAndClamp(1-Math.min(1,t+s)),scaleAndClamp(1-Math.min(1,i+s)),scaleAndClamp(1-Math.min(1,e+s))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,i]){const s=1-t,n=1-e,a=1-i;return["CMYK",s,n,a,Math.min(s,n,a)]}}class XfaLayer{static setupStorage(t,e,i,s,n){const a=s.getValue(e,{value:null});switch(i.name){case"textarea":null!==a.value&&(t.textContent=a.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){a.value===i.attributes.xfaOn?t.setAttribute("checked",!0):a.value===i.attributes.xfaOff&&t.removeAttribute("checked");if("print"===n)break;t.addEventListener("change",(t=>{s.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==a.value&&t.setAttribute("value",a.value);if("print"===n)break;t.addEventListener("input",(t=>{s.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==a.value){t.setAttribute("value",a.value);for(const t of i.children)t.attributes.value===a.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,n=-1===i.selectedIndex?"":i[i.selectedIndex].value;s.setValue(e,{value:n})}))}}static setAttributes({html:t,element:e,storage:i=null,intent:s,linkService:n}){const{attributes:a}=e,r=t instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${s}`);for(const[e,i]of Object.entries(a))if(null!=i)switch(e){case"class":i.length&&t.setAttribute(e,i.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",i);break;case"style":Object.assign(t.style,i);break;case"textContent":t.textContent=i;break;default:(!r||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,i)}r&&n.addLinkAttributes(t,a.href,a.newWindow);i&&a.dataId&&this.setupStorage(t,a.dataId,e,i)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,n=t.intent||"display",a=document.createElement(s.name);s.attributes&&this.setAttributes({html:a,element:s,intent:n,linkService:i});const r="richText"!==n,o=t.div;o.append(a);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;o.style.transform=e}r&&o.setAttribute("class","xfaLayer xfaFont");const l=[];if(0===s.children.length){if(s.value){const t=document.createTextNode(s.value);a.append(t);r&&XfaText.shouldBuildText(s.name)&&l.push(t)}return{textDivs:l}}const h=[[s,-1,a]];for(;h.length>0;){const[t,s,a]=h.at(-1);if(s+1===t.children.length){h.pop();continue}const o=t.children[++h.at(-1)[1]];if(null===o)continue;const{name:d}=o;if("#text"===d){const t=document.createTextNode(o.value);l.push(t);a.append(t);continue}const c=o?.attributes?.xmlns?document.createElementNS(o.attributes.xmlns,d):document.createElement(d);a.append(c);o.attributes&&this.setAttributes({html:c,element:o,storage:e,intent:n,linkService:i});if(o.children?.length>0)h.push([o,-1,c]);else if(o.value){const t=document.createTextNode(o.value);r&&XfaText.shouldBuildText(d)&&l.push(t);c.append(t)}}for(const t of o.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:l}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}const Qt=1e3,Jt=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case E:return new LinkAnnotationElement(t);case _:return new TextAnnotationElement(t);case z:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t);case"Sig":return new SignatureWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case B:return new PopupAnnotationElement(t);case C:return new FreeTextAnnotationElement(t);case S:return new LineAnnotationElement(t);case T:return new SquareAnnotationElement(t);case M:return new CircleAnnotationElement(t);case P:return new PolylineAnnotationElement(t);case O:return new CaretAnnotationElement(t);case N:return new InkAnnotationElement(t);case k:return new PolygonAnnotationElement(t);case D:return new HighlightAnnotationElement(t);case F:return new UnderlineAnnotationElement(t);case R:return new SquigglyAnnotationElement(t);case I:return new StrikeOutAnnotationElement(t);case L:return new StampAnnotationElement(t);case H:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{#ss=null;#ns=!1;#as=null;constructor(t,{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this.parent=t.parent;e&&(this.container=this._createContainer(i));s&&this._createQuadrilaterals()}static _hasPopupData({titleObj:t,contentsObj:e,richText:i}){return!!(t?.str||e?.str||i?.str)}get hasPopupData(){return AnnotationElement._hasPopupData(this.data)}updateEdited(t){if(!this.container)return;this.#ss||={rect:this.data.rect.slice(0)};const{rect:e}=t;e&&this.#rs(e);this.#as?.popup.updateEdited(t)}resetEdited(){if(this.#ss){this.#rs(this.#ss.rect);this.#as?.popup.resetEdited();this.#ss=null}}#rs(t){const{container:{style:e},data:{rect:i,rotation:s},parent:{viewport:{rawDims:{pageWidth:n,pageHeight:a,pageX:r,pageY:o}}}}=this;i?.splice(0,4,...t);const{width:l,height:h}=getRectDims(t);e.left=100*(t[0]-r)/n+"%";e.top=100*(a-t[3]+o)/a+"%";if(0===s){e.width=100*l/n+"%";e.height=100*h/a+"%"}else this.setRotation(s)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,n=document.createElement("section");n.setAttribute("data-annotation-id",e.id);this instanceof WidgetAnnotationElement||(n.tabIndex=Qt);const{style:a}=n;a.zIndex=this.parent.zIndex++;e.popupRef&&n.setAttribute("aria-haspopup","dialog");e.alternativeText&&(n.title=e.alternativeText);e.noRotate&&n.classList.add("norotate");if(!e.rect||this instanceof PopupAnnotationElement){const{rotation:t}=e;e.hasOwnCanvas||0===t||this.setRotation(t,n);return n}const{width:r,height:o}=getRectDims(e.rect);if(!t&&e.borderStyle.width>0){a.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;a.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${r}px * var(--scale-factor)) / calc(${o}px * var(--scale-factor))`;a.borderRadius=t}switch(e.borderStyle.style){case U:a.borderStyle="solid";break;case j:a.borderStyle="dashed";break;case $:warn("Unimplemented border style: beveled");break;case V:warn("Unimplemented border style: inset");break;case W:a.borderBottomStyle="solid"}const s=e.borderColor||null;if(s){this.#ns=!0;a.borderColor=Util.makeHexColor(0|s[0],0|s[1],0|s[2])}else a.borderWidth=0}const l=Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]),{pageWidth:h,pageHeight:d,pageX:c,pageY:u}=s.rawDims;a.left=100*(l[0]-c)/h+"%";a.top=100*(l[1]-u)/d+"%";const{rotation:p}=e;if(e.hasOwnCanvas||0===p){a.width=100*r/h+"%";a.height=100*o/d+"%"}else this.setRotation(p,n);return n}setRotation(t,e=this.container){if(!this.data.rect)return;const{pageWidth:i,pageHeight:s}=this.parent.viewport.rawDims,{width:n,height:a}=getRectDims(this.data.rect);let r,o;if(t%180==0){r=100*n/i;o=100*a/s}else{r=100*a/i;o=100*n/s}e.style.width=`${r}%`;e.style.height=`${o}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,i)=>{const s=i.detail[t],n=s[0],a=s.slice(1);i.target.style[e]=ColorConverters[`${n}_HTML`](a);this.annotationStorage.setValue(this.data.id,{[e]:ColorConverters[`${n}_rgb`](a)})};return shadow(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2==1;this.container.style.visibility=i?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const s of Object.keys(e.detail)){const n=t[s]||i[s];n?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[s,n]of Object.entries(e)){const a=i[s];if(a){a({detail:{[s]:n},target:t});delete e[s]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,s,n]=this.data.rect.map((t=>Math.fround(t)));if(8===t.length){const[a,r,o,l]=t.subarray(2,6);if(s===a&&n===r&&e===o&&i===l)return}const{style:a}=this.container;let r;if(this.#ns){const{borderColor:t,borderWidth:e}=a;a.borderWidth=0;r=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`];this.container.classList.add("hasBorder")}const o=s-e,l=n-i,{svgFactory:h}=this,d=h.createElement("svg");d.classList.add("quadrilateralsContainer");d.setAttribute("width",0);d.setAttribute("height",0);const c=h.createElement("defs");d.append(c);const u=h.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p);u.setAttribute("clipPathUnits","objectBoundingBox");c.append(u);for(let i=2,s=t.length;i<s;i+=8){const s=t[i],a=t[i+1],d=t[i+2],c=t[i+3],p=h.createElement("rect"),g=(d-e)/o,m=(n-a)/l,f=(s-d)/o,b=(a-c)/l;p.setAttribute("x",g);p.setAttribute("y",m);p.setAttribute("width",f);p.setAttribute("height",b);u.append(p);r?.push(`<rect vector-effect="non-scaling-stroke" x="${g}" y="${m}" width="${f}" height="${b}"/>`)}if(this.#ns){r.push("</g></svg>')");a.backgroundImage=r.join("")}this.container.append(d);this.container.style.clipPath=`url(#${p})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const i=this.#as=new PopupAnnotationElement({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(i.render())}render(){unreachable("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:n,exportValues:a}of s){if(-1===t)continue;if(n===e)continue;const s="string"==typeof a?a:null,r=document.querySelector(`[data-element-id="${n}"]`);!r||Jt.has(r)?i.push({id:n,exportValue:s,domElement:r}):warn(`_getElementsByName - element not allowed: ${n}`)}return i}for(const s of document.getElementsByName(t)){const{exportValue:t}=s,n=s.getAttribute("data-element-id");n!==e&&(Jt.has(s)&&i.push({id:n,exportValue:t,domElement:s}))}return i}show(){this.container&&(this.container.hidden=!1);this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0);this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}get _isEditable(){return!1}_editOnDoubleClick(){if(!this._isEditable)return;const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let s=!1;if(t.url){e.addLinkAttributes(i,t.url,t.newWindow);s=!0}else if(t.action){this._bindNamedAction(i,t.action);s=!0}else if(t.attachment){this.#os(i,t.attachment,t.attachmentDest);s=!0}else if(t.setOCGState){this.#ls(i,t.setOCGState);s=!0}else if(t.dest){this._bindLink(i,t.dest);s=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(i,t);s=!0}if(t.resetForm){this._bindResetFormAction(i,t.resetForm);s=!0}else if(this.isTooltipOnly&&!s){this._bindLink(i,"");s=!0}}this.container.classList.add("linkAnnotation");s&&this.container.append(i);return this.container}#hs(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&this.#hs()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};this.#hs()}#os(t,e,i=null){t.href=this.linkService.getAnchorUrl("");e.description&&(t.title=e.description);t.onclick=()=>{this.downloadManager?.openOrDownloadData(e.content,e.filename,i);return!1};this.#hs()}#ls(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeSetOCGState(e);return!1};this.#hs()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(e.actions)){const n=i.get(s);n&&(t[n]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:s}});return!1})}t.onclick||(t.onclick=()=>!1);this.#hs()}_bindResetFormAction(t,e){const i=t.onclick;i||(t.href=this.linkService.getAnchorUrl(""));this.#hs();if(this._fieldObjects)t.onclick=()=>{i?.();const{fields:t,refs:s,include:n}=e,a=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===n&&a.push(i)}else for(const t of Object.values(this._fieldObjects))a.push(...t);const r=this.annotationStorage,o=[];for(const t of a){const{id:e}=t;o.push(e);switch(t.type){case"text":{const i=t.defaultValue||"";r.setValue(e,{value:i});break}case"checkbox":case"radiobutton":{const i=t.defaultValue===t.exportValues;r.setValue(e,{value:i});break}case"combobox":case"listbox":{const i=t.defaultValue||"";r.setValue(e,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${e}"]`);i&&(Jt.has(i)?i.dispatchEvent(new Event("resetform")):warn(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:o,name:"ResetForm"}});return!1};else{warn('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');i||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.setAttribute("data-l10n-id","pdfjs-text-annotation-type");t.setAttribute("data-l10n-args",JSON.stringify({type:this.data.name}));!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){return this.container}showElementAndHideCanvas(t){if(this.data.hasOwnCanvas){"CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0);t.hidden=!1}}_getKeyModifier(t){return util_FeatureTest.platform.isMac?t.metaKey:t.ctrlKey}_setEventListener(t,e,i,s,n){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}n&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,i,s){for(const[n,a]of i)if("Action"===a||this.data.actions?.[a]){"Focus"!==a&&"Blur"!==a||(e||={focused:!1});this._setEventListener(t,e,n,a,s);"Focus"!==a||this.data.actions?.Blur?"Blur"!==a||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null)}}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||9,a=t.style;let r;const roundToOneDecimal=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2),e=t/(Math.round(t/(n*s))||1);r=Math.min(s,roundToOneDecimal(e/n))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-2);r=Math.min(s,roundToOneDecimal(t/n))}a.fontSize=`calc(${r}px * var(--scale-factor))`;a.color=Util.makeHexColor(i[0],i[1],i[2]);null!==this.data.textAlignment&&(a.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||t.data.hasOwnCanvas||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,i,s){const n=this.annotationStorage;for(const a of this._getElementsByName(t.name,t.id)){a.domElement&&(a.domElement[e]=i);n.setValue(a.id,{[s]:i})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const s=t.getValue(e,{value:this.data.fieldValue});let n=s.value||"";const a=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;a&&n.length>a&&(n=n.slice(0,a));let r=s.formattedValue||this.data.textContent?.join("\n")||null;r&&this.data.comb&&(r=r.replaceAll(/\s+/g,""));const o={userValue:n,formattedValue:r,lastCommittedValue:null,commitKey:1,focused:!1};if(this.data.multiLine){i=document.createElement("textarea");i.textContent=r??n;this.data.doNotScroll&&(i.style.overflowY="hidden")}else{i=document.createElement("input");i.type="text";i.setAttribute("value",r??n);this.data.doNotScroll&&(i.style.overflowX="hidden")}this.data.hasOwnCanvas&&(i.hidden=!0);Jt.add(i);i.setAttribute("data-element-id",e);i.disabled=this.data.readOnly;i.name=this.data.fieldName;i.tabIndex=Qt;this._setRequired(i,this.data.required);a&&(i.maxLength=a);i.addEventListener("input",(s=>{t.setValue(e,{value:s.target.value});this.setPropertyOnSiblings(i,"value",s.target.value,"value");o.formattedValue=null}));i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e;o.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=o;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue);o.lastCommittedValue=e.value;o.commitKey=1;this.data.actions?.Focus||(o.focused=!0)}));i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const s={value(i){o.userValue=i.detail.value??"";t.setValue(e,{value:o.userValue.toString()});i.target.value=o.userValue},formattedValue(i){const{formattedValue:s}=i.detail;o.formattedValue=s;null!=s&&i.target!==document.activeElement&&(i.target.value=s);t.setValue(e,{formattedValue:s})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:s}=i.detail,{target:n}=i;if(0===s){n.removeAttribute("maxLength");return}n.setAttribute("maxLength",s);let a=o.userValue;if(a&&!(a.length<=s)){a=a.slice(0,s);n.value=o.userValue=a;t.setValue(e,{value:a});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:a,willCommit:!0,commitKey:1,selStart:n.selectionStart,selEnd:n.selectionEnd}})}}};this._dispatchEventFromSandbox(s,i)}));i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;"Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2;if(-1===i)return;const{value:s}=t.target;if(o.lastCommittedValue!==s){o.lastCommittedValue=s;o.userValue=s;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const s=blurListener;blurListener=null;i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;this.data.actions?.Blur||(o.focused=!1);const{value:i}=t.target;o.userValue=i;o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});s(t)}));this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:s}=t,{value:n,selectionStart:a,selectionEnd:r}=s;let l=a,h=r;switch(t.inputType){case"deleteWordBackward":{const t=n.substring(0,a).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=n.substring(a).match(/^[^\w]*\w*/);t&&(h+=t[0].length);break}case"deleteContentBackward":a===r&&(l-=1);break;case"deleteContentForward":a===r&&(h+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,change:i||"",willCommit:!1,selStart:l,selEnd:h}})}));this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&i.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/a;i.classList.add("comb");i.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{i=document.createElement("div");i.textContent=this.data.fieldValue;i.style.verticalAlign="middle";i.style.display="table-cell";this.data.hasOwnCanvas&&(i.hidden=!0)}this._setTextStyle(i);this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class SignatureWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof s){s="Off"!==s;t.setValue(i,{value:s})}this.container.classList.add("buttonWidgetAnnotation","checkBox");const n=document.createElement("input");Jt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="checkbox";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("exportValue",e.exportValue);n.tabIndex=Qt;n.addEventListener("change",(s=>{const{name:n,checked:a}=s.target;for(const s of this._getElementsByName(n,i)){const i=a&&s.exportValue===e.exportValue;s.domElement&&(s.domElement.checked=i);t.setValue(s.id,{value:i})}t.setValue(i,{value:a})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue}));if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(e=>{const s={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(s,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let s=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof s){s=s!==e.buttonValue;t.setValue(i,{value:s})}if(s)for(const s of this._getElementsByName(e.fieldName,i))t.setValue(s.id,{value:!1});const n=document.createElement("input");Jt.add(n);n.setAttribute("data-element-id",i);n.disabled=e.readOnly;this._setRequired(n,this.data.required);n.type="radio";n.name=e.fieldName;s&&n.setAttribute("checked",!0);n.tabIndex=Qt;n.addEventListener("change",(e=>{const{name:s,checked:n}=e.target;for(const e of this._getElementsByName(s,i))t.setValue(e.id,{value:!1});t.setValue(i,{value:n})}));n.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!=i&&i===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const s=e.buttonValue;n.addEventListener("updatefromsandbox",(e=>{const n={value:e=>{const n=s===e.detail.value;for(const s of this._getElementsByName(e.target.name)){const e=n&&s.id===i;s.domElement&&(s.domElement.checked=e);t.setValue(s.id,{value:e})}}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(n,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton");const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),s=document.createElement("select");Jt.add(s);s.setAttribute("data-element-id",e);s.disabled=this.data.readOnly;this._setRequired(s,this.data.required);s.name=this.data.fieldName;s.tabIndex=Qt;let n=this.data.combo&&this.data.options.length>0;if(!this.data.combo){s.size=this.data.options.length;this.data.multiSelect&&(s.multiple=!0)}s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of s.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(i.value.includes(t.exportValue)){e.setAttribute("selected",!0);n=!1}s.append(e)}let a=null;if(n){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);s.prepend(t);a=()=>{t.remove();s.removeEventListener("input",a);a=null};s.addEventListener("input",a)}const getValue=t=>{const e=t?"value":"textContent",{options:i,multiple:n}=s;return n?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let r=getValue(!1);const getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){s.addEventListener("updatefromsandbox",(i=>{const n={value(i){a?.();const n=i.detail.value,o=new Set(Array.isArray(n)?n:[n]);for(const t of s.options)t.selected=o.has(t.value);t.setValue(e,{value:getValue(!0)});r=getValue(!1)},multipleSelection(t){s.multiple=!0},remove(i){const n=s.options,a=i.detail.remove;n[a].selected=!1;s.remove(a);if(n.length>0){-1===Array.prototype.findIndex.call(n,(t=>t.selected))&&(n[0].selected=!0)}t.setValue(e,{value:getValue(!0),items:getItems(i)});r=getValue(!1)},clear(i){for(;0!==s.length;)s.remove(0);t.setValue(e,{value:null,items:[]});r=getValue(!1)},insert(i){const{index:n,displayValue:a,exportValue:o}=i.detail.insert,l=s.children[n],h=document.createElement("option");h.textContent=a;h.value=o;l?l.before(h):s.append(h);t.setValue(e,{value:getValue(!0),items:getItems(i)});r=getValue(!1)},items(i){const{items:n}=i.detail;for(;0!==s.length;)s.remove(0);for(const t of n){const{displayValue:e,exportValue:i}=t,n=document.createElement("option");n.textContent=e;n.value=i;s.append(n)}s.options.length>0&&(s.options[0].selected=!0);t.setValue(e,{value:getValue(!0),items:getItems(i)});r=getValue(!1)},indices(i){const s=new Set(i.detail.indices);for(const t of i.target.options)t.selected=s.has(t.index);t.setValue(e,{value:getValue(!0)});r=getValue(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(n,i)}));s.addEventListener("input",(i=>{const s=getValue(!0),n=getValue(!1);t.setValue(e,{value:s});i.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,change:n,changeEx:s,willCommit:!1,commitKey:1,keyDown:!1}})}));this._setEventListeners(s,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))}else s.addEventListener("input",(function(i){t.setValue(e,{value:getValue(!0)})}));this.data.combo&&this._setTextStyle(s);this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:AnnotationElement._hasPopupData(e)});this.elements=i;this.popup=null}render(){this.container.classList.add("popupAnnotation");const t=this.popup=new PopupElement({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements){i.popup=t;e.push(i.data.id);i.addHighlightArea()}this.container.setAttribute("aria-controls",e.map((t=>`${et}${t}`)).join(","));return this.container}}class PopupElement{#ds=this.#cs.bind(this);#us=this.#ps.bind(this);#gs=this.#ms.bind(this);#fs=this.#bs.bind(this);#vs=null;#ft=null;#As=null;#ys=null;#ws=null;#xs=null;#_s=null;#Es=!1;#Cs=null;#E=null;#Ss=null;#Ts=null;#Ms=null;#ss=null;#ks=!1;constructor({container:t,color:e,elements:i,titleObj:s,modificationDate:n,contentsObj:a,richText:r,parent:o,rect:l,parentRect:h,open:d}){this.#ft=t;this.#Ms=s;this.#As=a;this.#Ts=r;this.#xs=o;this.#vs=e;this.#Ss=l;this.#_s=h;this.#ws=i;this.#ys=PDFDateString.toDateObject(n);this.trigger=i.flatMap((t=>t.getElementsToTriggerPopup()));for(const t of this.trigger){t.addEventListener("click",this.#fs);t.addEventListener("mouseenter",this.#gs);t.addEventListener("mouseleave",this.#us);t.classList.add("popupTriggerArea")}for(const t of i)t.container?.addEventListener("keydown",this.#ds);this.#ft.hidden=!0;d&&this.#bs()}render(){if(this.#Cs)return;const t=this.#Cs=document.createElement("div");t.className="popup";if(this.#vs){const e=t.style.outlineColor=Util.makeHexColor(...this.#vs);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))t.style.backgroundColor=`color-mix(in srgb, ${e} 30%, white)`;else{const e=.7;t.style.backgroundColor=Util.makeHexColor(...this.#vs.map((t=>Math.floor(e*(255-t)+t))))}}const e=document.createElement("span");e.className="header";const i=document.createElement("h1");e.append(i);({dir:i.dir,str:i.textContent}=this.#Ms);t.append(e);if(this.#ys){const t=document.createElement("span");t.classList.add("popupDate");t.setAttribute("data-l10n-id","pdfjs-annotation-date-string");t.setAttribute("data-l10n-args",JSON.stringify({date:this.#ys.toLocaleDateString(),time:this.#ys.toLocaleTimeString()}));e.append(t)}const s=this.#Ps;if(s){XfaLayer.render({xfaHtml:s,intent:"richText",div:t});t.lastChild.classList.add("richText","popupContent")}else{const e=this._formatContents(this.#As);t.append(e)}this.#ft.append(t)}get#Ps(){const t=this.#Ts,e=this.#As;return!t?.str||e?.str&&e.str!==t.str?null:this.#Ts.html||null}get#Ds(){return this.#Ps?.attributes?.style?.fontSize||0}get#Fs(){return this.#Ps?.attributes?.style?.color||null}#Rs(t){const e=[],i={str:t,html:{name:"div",attributes:{dir:"auto"},children:[{name:"p",children:e}]}},s={style:{color:this.#Fs,fontSize:this.#Ds?`calc(${this.#Ds}px * var(--scale-factor))`:""}};for(const i of t.split("\n"))e.push({name:"span",value:i,attributes:s});return i}_formatContents({str:t,dir:e}){const i=document.createElement("p");i.classList.add("popupContent");i.dir=e;const s=t.split(/(?:\r\n?|\n)/);for(let t=0,e=s.length;t<e;++t){const n=s[t];i.append(document.createTextNode(n));t<e-1&&i.append(document.createElement("br"))}return i}#cs(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#Es)&&this.#bs()}updateEdited({rect:t,popupContent:e}){this.#ss||={contentsObj:this.#As,richText:this.#Ts};t&&(this.#E=null);if(e){this.#Ts=this.#Rs(e);this.#As=null}this.#Cs?.remove();this.#Cs=null}resetEdited(){if(this.#ss){({contentsObj:this.#As,richText:this.#Ts}=this.#ss);this.#ss=null;this.#Cs?.remove();this.#Cs=null;this.#E=null}}#Is(){if(null!==this.#E)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:n}}}=this.#xs;let a=!!this.#_s,r=a?this.#_s:this.#Ss;for(const t of this.#ws)if(!r||null!==Util.intersect(t.data.rect,r)){r=t.data.rect;a=!0;break}const o=Util.normalizeRect([r[0],t[3]-r[1]+t[1],r[2],t[3]-r[3]+t[1]]),l=a?r[2]-r[0]+5:0,h=o[0]+l,d=o[1];this.#E=[100*(h-s)/e,100*(d-n)/i];const{style:c}=this.#ft;c.left=`${this.#E[0]}%`;c.top=`${this.#E[1]}%`}#bs(){this.#Es=!this.#Es;if(this.#Es){this.#ms();this.#ft.addEventListener("click",this.#fs);this.#ft.addEventListener("keydown",this.#ds)}else{this.#ps();this.#ft.removeEventListener("click",this.#fs);this.#ft.removeEventListener("keydown",this.#ds)}}#ms(){this.#Cs||this.render();if(this.isVisible)this.#Es&&this.#ft.classList.add("focused");else{this.#Is();this.#ft.hidden=!1;this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)+1e3}}#ps(){this.#ft.classList.remove("focused");if(!this.#Es&&this.isVisible){this.#ft.hidden=!0;this.#ft.style.zIndex=parseInt(this.#ft.style.zIndex)-1e3}}forceHide(){this.#ks=this.isVisible;this.#ks&&(this.#ft.hidden=!0)}maybeShow(){if(this.#ks){this.#Cs||this.#ms();this.#ks=!1;this.#ft.hidden=!1}}get isVisible(){return!1===this.#ft.hidden}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.textContent=t.data.textContent;this.textPosition=t.data.textPosition;this.annotationEditorType=p.FREETEXT}render(){this.container.classList.add("freeTextAnnotation");if(this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent");t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e;t.append(i)}this.container.append(t)}!this.data.popupRef&&this.hasPopupData&&this._createPopup();this._editOnDoubleClick();return this.container}get _isEditable(){return this.data.hasOwnCanvas}}class LineAnnotationElement extends AnnotationElement{#Ls=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=this.#Ls=this.svgFactory.createElement("svg:line");n.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);n.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);n.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);n.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);n.setAttribute("stroke-width",t.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","transparent");s.append(n);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Ls}addHighlightArea(){this.container.classList.add("highlightArea")}}class SquareAnnotationElement extends AnnotationElement{#Os=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#Os=this.svgFactory.createElement("svg:rect");a.setAttribute("x",n/2);a.setAttribute("y",n/2);a.setAttribute("width",e-n);a.setAttribute("height",i-n);a.setAttribute("stroke-width",n||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");s.append(a);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Os}addHighlightArea(){this.container.classList.add("highlightArea")}}class CircleAnnotationElement extends AnnotationElement{#Ns=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:i}=getRectDims(t.rect),s=this.svgFactory.create(e,i,!0),n=t.borderStyle.width,a=this.#Ns=this.svgFactory.createElement("svg:ellipse");a.setAttribute("cx",e/2);a.setAttribute("cy",i/2);a.setAttribute("rx",e/2-n/2);a.setAttribute("ry",i/2-n/2);a.setAttribute("stroke-width",n||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");s.append(a);this.container.append(s);!t.popupRef&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Ns}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolylineAnnotationElement extends AnnotationElement{#Bs=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,vertices:e,borderStyle:i,popupRef:s}}=this;if(!e)return this.container;const{width:n,height:a}=getRectDims(t),r=this.svgFactory.create(n,a,!0);let o=[];for(let i=0,s=e.length;i<s;i+=2){const s=e[i]-t[0],n=t[3]-e[i+1];o.push(`${s},${n}`)}o=o.join(" ");const l=this.#Bs=this.svgFactory.createElement(this.svgElementName);l.setAttribute("points",o);l.setAttribute("stroke-width",i.width||1);l.setAttribute("stroke","transparent");l.setAttribute("fill","transparent");r.append(l);this.container.append(r);!s&&this.hasPopupData&&this._createPopup();return this.container}getElementsToTriggerPopup(){return this.#Bs}addHighlightArea(){this.container.classList.add("highlightArea")}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("caretAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class InkAnnotationElement extends AnnotationElement{#Hs=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline";this.annotationEditorType=p.INK}render(){this.container.classList.add(this.containerClassName);const{data:{rect:t,inkLists:e,borderStyle:i,popupRef:s}}=this,{width:n,height:a}=getRectDims(t),r=this.svgFactory.create(n,a,!0);for(const n of e){let e=[];for(let i=0,s=n.length;i<s;i+=2){const s=n[i]-t[0],a=t[3]-n[i+1];e.push(`${s},${a}`)}e=e.join(" ");const a=this.svgFactory.createElement(this.svgElementName);this.#Hs.push(a);a.setAttribute("points",e);a.setAttribute("stroke-width",i.width||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","transparent");!s&&this.hasPopupData&&this._createPopup();r.append(a)}this.container.append(r);return this.container}getElementsToTriggerPopup(){return this.#Hs}addHighlightArea(){this.container.classList.add("highlightArea")}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("highlightAnnotation");return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("underlineAnnotation");return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("squigglyAnnotation");return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){!this.data.popupRef&&this.hasPopupData&&this._createPopup();this.container.classList.add("strikeoutAnnotation");return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("stampAnnotation");!this.data.popupRef&&this.hasPopupData&&this._createPopup();return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{#zs=null;constructor(t){super(t,{isRenderable:!0});const{file:e}=this.data;this.filename=e.filename;this.content=e.content;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,...e})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;if(e.hasAppearance||0===e.fillAlpha)i=document.createElement("div");else{i=document.createElement("img");i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`;e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)}i.addEventListener("dblclick",this.#Us.bind(this));this.#zs=i;const{isMac:s}=util_FeatureTest.platform;t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Us()}));!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea");t.append(i);return t}getElementsToTriggerPopup(){return this.#zs}addHighlightArea(){this.container.classList.add("highlightArea")}#Us(){this.downloadManager?.openOrDownloadData(this.content,this.filename)}}class AnnotationLayer{#js=null;#$s=null;#Vs=new Map;constructor({div:t,accessibilityManager:e,annotationCanvasMap:i,annotationEditorUIManager:s,page:n,viewport:a}){this.div=t;this.#js=e;this.#$s=i;this.page=n;this.viewport=a;this.zIndex=0;this._annotationEditorUIManager=s}#Ws(t,e){const i=t.firstChild||t;i.id=`${et}${e}`;this.div.append(t);this.#js?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;setLayerDimensions(i,this.viewport);const s=new Map,n={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new DOMSVGFactory,annotationStorage:t.annotationStorage||new AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const t of e){if(t.noHTML)continue;const e=t.annotationType===B;if(e){const e=s.get(t.id);if(!e)continue;n.elements=e}else{const{width:e,height:i}=getRectDims(t.rect);if(e<=0||i<=0)continue}n.data=t;const i=AnnotationElementFactory.create(n);if(!i.isRenderable)continue;if(!e&&t.popupRef){const e=s.get(t.popupRef);e?e.push(i):s.set(t.popupRef,[i])}const a=i.render();t.hidden&&(a.style.visibility="hidden");this.#Ws(a,t.id);if(i.annotationEditorType>0){this.#Vs.set(i.data.id,i);this._annotationEditorUIManager?.renderAnnotationElement(i)}}this.#Gs()}update({viewport:t}){const e=this.div;this.viewport=t;setLayerDimensions(e,{rotation:t.rotation});this.#Gs();e.hidden=!1}#Gs(){if(!this.#$s)return;const t=this.div;for(const[e,i]of this.#$s){const s=t.querySelector(`[data-annotation-id="${e}"]`);if(!s)continue;i.className="annotationContent";const{firstChild:n}=s;n?"CANVAS"===n.nodeName?n.replaceWith(i):n.classList.contains("annotationContent")?n.after(i):n.before(i):s.append(i)}this.#$s.clear()}getEditableAnnotations(){return Array.from(this.#Vs.values())}getEditableAnnotation(t){return this.#Vs.get(t)}}const Zt=/\r\n?|\n/g;class FreeTextEditor extends AnnotationEditor{#qs=this.editorDivBlur.bind(this);#Xs=this.editorDivFocus.bind(this);#Ks=this.editorDivInput.bind(this);#Ys=this.editorDivKeydown.bind(this);#Qs=this.editorDivPaste.bind(this);#vs;#Js="";#Zs=`${this.id}-editor`;#Ds;#tn=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=FreeTextEditor.prototype,arrowChecker=t=>t.isEmpty(),e=AnnotationEditorUIManager.TRANSLATE_SMALL,i=AnnotationEditorUIManager.TRANSLATE_BIG;return shadow(this,"_keyboardManager",new KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-e,0],checker:arrowChecker}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:arrowChecker}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[e,0],checker:arrowChecker}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[i,0],checker:arrowChecker}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-e],checker:arrowChecker}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:arrowChecker}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,e],checker:arrowChecker}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,i],checker:arrowChecker}]]))}static _type="freetext";static _editorType=p.FREETEXT;constructor(t){super({...t,name:"freeTextEditor"});this.#vs=t.color||FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor;this.#Ds=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t,e){AnnotationEditor.initialize(t,e,{strings:["pdfjs-free-text-default-content"]});const i=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(i.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case g.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case g.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case g.FREETEXT_SIZE:this.#en(e);break;case g.FREETEXT_COLOR:this.#in(e)}}static get defaultPropertiesToUpdate(){return[[g.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[g.FREETEXT_COLOR,FreeTextEditor._defaultColor||AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[g.FREETEXT_SIZE,this.#Ds],[g.FREETEXT_COLOR,this.#vs]]}#en(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-this.#Ds)*this.parentScale);this.#Ds=t;this.#sn()},e=this.#Ds;this.addCommands({cmd:setFontsize.bind(this,t),undo:setFontsize.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#in(t){const setColor=t=>{this.#vs=this.editorDiv.style.color=t},e=this.#vs;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-FreeTextEditor._internalPadding*t,-(FreeTextEditor._internalPadding+this.#Ds)*t]}rebuild(){if(this.parent){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}}enableEditMode(){if(this.isInEditMode())return;this.parent.setEditingState(!1);this.parent.updateToolbar(p.FREETEXT);super.enableEditMode();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this._isDraggable=!1;this.div.removeAttribute("aria-activedescendant");const t=this._uiManager._signal;this.editorDiv.addEventListener("keydown",this.#Ys,{signal:t});this.editorDiv.addEventListener("focus",this.#Xs,{signal:t});this.editorDiv.addEventListener("blur",this.#qs,{signal:t});this.editorDiv.addEventListener("input",this.#Ks,{signal:t});this.editorDiv.addEventListener("paste",this.#Qs,{signal:t})}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.setAttribute("aria-activedescendant",this.#Zs);this._isDraggable=!0;this.editorDiv.removeEventListener("keydown",this.#Ys);this.editorDiv.removeEventListener("focus",this.#Xs);this.editorDiv.removeEventListener("blur",this.#qs);this.editorDiv.removeEventListener("input",this.#Ks);this.editorDiv.removeEventListener("paste",this.#Qs);this.div.focus({preventScroll:!0});this.isEditing=!1;this.parent.div.classList.add("freetextEditing")}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}}onceAdded(){if(!this.width){this.enableEditMode();this.editorDiv.focus();this._initialOptions?.isCentered&&this.center();this._initialOptions=null}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;if(this.parent){this.parent.setEditingState(!0);this.parent.div.classList.add("freetextEditing")}super.remove()}#nn(){const t=[];this.editorDiv.normalize();for(const e of this.editorDiv.childNodes)t.push(FreeTextEditor.#an(e));return t.join("\n")}#sn(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,s=e.style.display,n=e.classList.contains("hidden");e.classList.remove("hidden");e.style.display="hidden";t.div.append(this.div);i=e.getBoundingClientRect();e.remove();e.style.display=s;e.classList.toggle("hidden",n)}if(this.rotation%180==this.parentRotation%180){this.width=i.width/t;this.height=i.height/e}else{this.width=i.height/t;this.height=i.width/e}this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit();this.disableEditMode();const t=this.#Js,e=this.#Js=this.#nn().trimEnd();if(t===e)return;const setText=t=>{this.#Js=t;if(t){this.#rn();this._uiManager.rebuild(this);this.#sn()}else this.remove()};this.addCommands({cmd:()=>{setText(e)},undo:()=>{setText(t)},mustExec:!1});this.#sn()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode();this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enterInEditMode();t.preventDefault()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freetextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",this.#Zs);this.editorDiv.setAttribute("data-l10n-id","pdfjs-free-text");this.enableEditing();AnnotationEditor._l10nPromise.get("pdfjs-free-text-default-content").then((t=>this.editorDiv?.setAttribute("default-content",t)));this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;i.fontSize=`calc(${this.#Ds}px * var(--scale-factor))`;i.color=this.#vs;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);bindEvents(this,this.div,["dblclick","keydown"]);if(this.width){const[i,s]=this.parentDimensions;if(this.annotationElementId){const{position:n}=this.#tn;let[a,r]=this.getInitialTranslation();[a,r]=this.pageTranslationToScreen(a,r);const[o,l]=this.pageDimensions,[h,d]=this.pageTranslation;let c,u;switch(this.rotation){case 0:c=t+(n[0]-h)/o;u=e+this.height-(n[1]-d)/l;break;case 90:c=t+(n[0]-h)/o;u=e-(n[1]-d)/l;[a,r]=[r,-a];break;case 180:c=t-this.width+(n[0]-h)/o;u=e-(n[1]-d)/l;[a,r]=[-a,-r];break;case 270:c=t+(n[0]-h-this.height*l)/o;u=e+(n[1]-d-this.width*o)/l;[a,r]=[-r,a]}this.setAt(c*i,u*s,a,r)}else this.setAt(t*i,e*s,this.width*i,this.height*s);this.#rn();this._isDraggable=!0;this.editorDiv.contentEditable=!1}else{this._isDraggable=!1;this.editorDiv.contentEditable=!0}return this.div}static#an(t){return(t.nodeType===Node.TEXT_NODE?t.nodeValue:t.innerText).replaceAll(Zt,"")}editorDivPaste(t){const e=t.clipboardData||window.clipboardData,{types:i}=e;if(1===i.length&&"text/plain"===i[0])return;t.preventDefault();const s=FreeTextEditor.#on(e.getData("text")||"").replaceAll(Zt,"\n");if(!s)return;const n=window.getSelection();if(!n.rangeCount)return;this.editorDiv.normalize();n.deleteFromDocument();const a=n.getRangeAt(0);if(!s.includes("\n")){a.insertNode(document.createTextNode(s));this.editorDiv.normalize();n.collapseToStart();return}const{startContainer:r,startOffset:o}=a,l=[],h=[];if(r.nodeType===Node.TEXT_NODE){const t=r.parentElement;h.push(r.nodeValue.slice(o).replaceAll(Zt,""));if(t!==this.editorDiv){let e=l;for(const i of this.editorDiv.childNodes)i!==t?e.push(FreeTextEditor.#an(i)):e=h}l.push(r.nodeValue.slice(0,o).replaceAll(Zt,""))}else if(r===this.editorDiv){let t=l,e=0;for(const i of this.editorDiv.childNodes){e++===o&&(t=h);t.push(FreeTextEditor.#an(i))}}this.#Js=`${l.join("\n")}${s}${h.join("\n")}`;this.#rn();const d=new Range;let c=l.reduce(((t,e)=>t+e.length),0);for(const{firstChild:t}of this.editorDiv.childNodes)if(t.nodeType===Node.TEXT_NODE){const e=t.nodeValue.length;if(c<=e){d.setStart(t,c);d.setEnd(t,c);break}c-=e}n.removeAllRanges();n.addRange(d)}#rn(){this.editorDiv.replaceChildren();if(this.#Js)for(const t of this.#Js.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}}#ln(){return this.#Js.replaceAll(" "," ")}static#on(t){return t.replaceAll(" "," ")}get contentDiv(){return this.editorDiv}static deserialize(t,e,i){let s=null;if(t instanceof FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:n,rotation:a,id:r},textContent:o,textPosition:l,parent:{page:{pageNumber:h}}}=t;if(!o||0===o.length)return null;s=t={annotationType:p.FREETEXT,color:Array.from(i),fontSize:e,value:o.join("\n"),position:l,pageIndex:h-1,rect:n.slice(0),rotation:a,id:r,deleted:!1}}const n=super.deserialize(t,e,i);n.#Ds=t.fontSize;n.#vs=Util.makeHexColor(...t.color);n.#Js=FreeTextEditor.#on(t.value);n.annotationElementId=t.id||null;n.#tn=s;return n}serialize(t=!1){if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const e=FreeTextEditor._internalPadding*this.parentScale,i=this.getRect(e,e),s=AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#vs),n={annotationType:p.FREETEXT,color:s,fontSize:this.#Ds,value:this.#ln(),pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};if(t)return n;if(this.annotationElementId&&!this.#hn(n))return null;n.id=this.annotationElementId;return n}#hn(t){const{value:e,fontSize:i,color:s,pageIndex:n}=this.#tn;return this._hasBeenMoved||t.value!==e||t.fontSize!==i||t.color.some(((t,e)=>t!==s[e]))||t.pageIndex!==n}renderAnnotationElement(t){const e=super.renderAnnotationElement(t);if(this.deleted)return e;const{style:i}=e;i.fontSize=`calc(${this.#Ds}px * var(--scale-factor))`;i.color=this.#vs;e.replaceChildren();for(const t of this.#Js.split("\n")){const i=document.createElement("div");i.append(t?document.createTextNode(t):document.createElement("br"));e.append(i)}const s=FreeTextEditor._internalPadding*this.parentScale;t.updateEdited({rect:this.getRect(s,s),popupContent:this.#Js});return e}resetAnnotationElement(t){super.resetAnnotationElement(t);t.resetEdited()}}class Outliner{#dn;#cn=[];#un=[];constructor(t,e=0,i=0,s=!0){let n=1/0,a=-1/0,r=1/0,o=-1/0;const l=10**-4;for(const{x:i,y:s,width:h,height:d}of t){const t=Math.floor((i-e)/l)*l,c=Math.ceil((i+h+e)/l)*l,u=Math.floor((s-e)/l)*l,p=Math.ceil((s+d+e)/l)*l,g=[t,u,p,!0],m=[c,u,p,!1];this.#cn.push(g,m);n=Math.min(n,t);a=Math.max(a,c);r=Math.min(r,u);o=Math.max(o,p)}const h=a-n+2*i,d=o-r+2*i,c=n-i,u=r-i,p=this.#cn.at(s?-1:-2),g=[p[0],p[2]];for(const t of this.#cn){const[e,i,s]=t;t[0]=(e-c)/h;t[1]=(i-u)/d;t[2]=(s-u)/d}this.#dn={x:c,y:u,width:h,height:d,lastPoint:g}}getOutlines(){this.#cn.sort(((t,e)=>t[0]-e[0]||t[1]-e[1]||t[2]-e[2]));const t=[];for(const e of this.#cn)if(e[3]){t.push(...this.#pn(e));this.#gn(e)}else{this.#mn(e);t.push(...this.#pn(e))}return this.#fn(t)}#fn(t){const e=[],i=new Set;for(const i of t){const[t,s,n]=i;e.push([t,s,i],[t,n,i])}e.sort(((t,e)=>t[1]-e[1]||t[0]-e[0]));for(let t=0,s=e.length;t<s;t+=2){const s=e[t][2],n=e[t+1][2];s.push(n);n.push(s);i.add(s);i.add(n)}const s=[];let n;for(;i.size>0;){const t=i.values().next().value;let[e,a,r,o,l]=t;i.delete(t);let h=e,d=a;n=[e,r];s.push(n);for(;;){let t;if(i.has(o))t=o;else{if(!i.has(l))break;t=l}i.delete(t);[e,a,r,o,l]=t;if(h!==e){n.push(h,d,e,d===a?a:r);h=e}d=d===a?r:a}n.push(h,d)}return new HighlightOutline(s,this.#dn)}#bn(t){const e=this.#un;let i=0,s=e.length-1;for(;i<=s;){const n=i+s>>1,a=e[n][0];if(a===t)return n;a<t?i=n+1:s=n-1}return s+1}#gn([,t,e]){const i=this.#bn(t);this.#un.splice(i,0,[t,e])}#mn([,t,e]){const i=this.#bn(t);for(let s=i;s<this.#un.length;s++){const[i,n]=this.#un[s];if(i!==t)break;if(i===t&&n===e){this.#un.splice(s,1);return}}for(let s=i-1;s>=0;s--){const[i,n]=this.#un[s];if(i!==t)break;if(i===t&&n===e){this.#un.splice(s,1);return}}}#pn(t){const[e,i,s]=t,n=[[e,i,s]],a=this.#bn(s);for(let t=0;t<a;t++){const[i,s]=this.#un[t];for(let t=0,a=n.length;t<a;t++){const[,r,o]=n[t];if(!(s<=r||o<=i))if(r>=i)if(o>s)n[t][1]=s;else{if(1===a)return[];n.splice(t,1);t--;a--}else{n[t][2]=i;o>s&&n.push([e,s,o])}}}return n}}class Outline{toSVGPath(){throw new Error("Abstract method `toSVGPath` must be implemented.")}get box(){throw new Error("Abstract getter `box` must be implemented.")}serialize(t,e){throw new Error("Abstract method `serialize` must be implemented.")}get free(){return this instanceof FreeHighlightOutline}}class HighlightOutline extends Outline{#dn;#vn;constructor(t,e){super();this.#vn=t;this.#dn=e}toSVGPath(){const t=[];for(const e of this.#vn){let[i,s]=e;t.push(`M${i} ${s}`);for(let n=2;n<e.length;n+=2){const a=e[n],r=e[n+1];if(a===i){t.push(`V${r}`);s=r}else if(r===s){t.push(`H${a}`);i=a}}t.push("Z")}return t.join(" ")}serialize([t,e,i,s],n){const a=[],r=i-t,o=s-e;for(const e of this.#vn){const i=new Array(e.length);for(let n=0;n<e.length;n+=2){i[n]=t+e[n]*r;i[n+1]=s-e[n+1]*o}a.push(i)}return a}get box(){return this.#dn}}class FreeOutliner{#dn;#An=[];#yn;#wn;#xn=[];#_n=new Float64Array(18);#En;#Cn;#Sn;#Tn;#Mn;#kn;#Pn=[];static#Dn=8;static#Fn=2;static#Rn=FreeOutliner.#Dn+FreeOutliner.#Fn;constructor({x:t,y:e},i,s,n,a,r=0){this.#dn=i;this.#kn=n*s;this.#wn=a;this.#_n.set([NaN,NaN,NaN,NaN,t,e],6);this.#yn=r;this.#Tn=FreeOutliner.#Dn*s;this.#Sn=FreeOutliner.#Rn*s;this.#Mn=s;this.#Pn.push(t,e)}get free(){return!0}isEmpty(){return isNaN(this.#_n[8])}#In(){const t=this.#_n.subarray(4,6),e=this.#_n.subarray(16,18),[i,s,n,a]=this.#dn;return[(this.#En+(t[0]-e[0])/2-i)/n,(this.#Cn+(t[1]-e[1])/2-s)/a,(this.#En+(e[0]-t[0])/2-i)/n,(this.#Cn+(e[1]-t[1])/2-s)/a]}add({x:t,y:e}){this.#En=t;this.#Cn=e;const[i,s,n,a]=this.#dn;let[r,o,l,h]=this.#_n.subarray(8,12);const d=t-l,c=e-h,u=Math.hypot(d,c);if(u<this.#Sn)return!1;const p=u-this.#Tn,g=p/u,m=g*d,f=g*c;let b=r,v=o;r=l;o=h;l+=m;h+=f;this.#Pn?.push(t,e);const A=m/p,y=-f/p*this.#kn,w=A*this.#kn;this.#_n.set(this.#_n.subarray(2,8),0);this.#_n.set([l+y,h+w],4);this.#_n.set(this.#_n.subarray(14,18),12);this.#_n.set([l-y,h-w],16);if(isNaN(this.#_n[6])){if(0===this.#xn.length){this.#_n.set([r+y,o+w],2);this.#xn.push(NaN,NaN,NaN,NaN,(r+y-i)/n,(o+w-s)/a);this.#_n.set([r-y,o-w],14);this.#An.push(NaN,NaN,NaN,NaN,(r-y-i)/n,(o-w-s)/a)}this.#_n.set([b,v,r,o,l,h],6);return!this.isEmpty()}this.#_n.set([b,v,r,o,l,h],6);if(Math.abs(Math.atan2(v-o,b-r)-Math.atan2(f,m))<Math.PI/2){[r,o,l,h]=this.#_n.subarray(2,6);this.#xn.push(NaN,NaN,NaN,NaN,((r+l)/2-i)/n,((o+h)/2-s)/a);[r,o,b,v]=this.#_n.subarray(14,18);this.#An.push(NaN,NaN,NaN,NaN,((b+r)/2-i)/n,((v+o)/2-s)/a);return!0}[b,v,r,o,l,h]=this.#_n.subarray(0,6);this.#xn.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a);[l,h,r,o,b,v]=this.#_n.subarray(12,18);this.#An.push(((b+5*r)/6-i)/n,((v+5*o)/6-s)/a,((5*r+l)/6-i)/n,((5*o+h)/6-s)/a,((r+l)/2-i)/n,((o+h)/2-s)/a);return!0}toSVGPath(){if(this.isEmpty())return"";const t=this.#xn,e=this.#An,i=this.#_n.subarray(4,6),s=this.#_n.subarray(16,18),[n,a,r,o]=this.#dn,[l,h,d,c]=this.#In();if(isNaN(this.#_n[6])&&!this.isEmpty())return`M${(this.#_n[2]-n)/r} ${(this.#_n[3]-a)/o} L${(this.#_n[4]-n)/r} ${(this.#_n[5]-a)/o} L${l} ${h} L${d} ${c} L${(this.#_n[16]-n)/r} ${(this.#_n[17]-a)/o} L${(this.#_n[14]-n)/r} ${(this.#_n[15]-a)/o} Z`;const u=[];u.push(`M${t[4]} ${t[5]}`);for(let e=6;e<t.length;e+=6)isNaN(t[e])?u.push(`L${t[e+4]} ${t[e+5]}`):u.push(`C${t[e]} ${t[e+1]} ${t[e+2]} ${t[e+3]} ${t[e+4]} ${t[e+5]}`);u.push(`L${(i[0]-n)/r} ${(i[1]-a)/o} L${l} ${h} L${d} ${c} L${(s[0]-n)/r} ${(s[1]-a)/o}`);for(let t=e.length-6;t>=6;t-=6)isNaN(e[t])?u.push(`L${e[t+4]} ${e[t+5]}`):u.push(`C${e[t]} ${e[t+1]} ${e[t+2]} ${e[t+3]} ${e[t+4]} ${e[t+5]}`);u.push(`L${e[4]} ${e[5]} Z`);return u.join(" ")}getOutlines(){const t=this.#xn,e=this.#An,i=this.#_n,s=i.subarray(4,6),n=i.subarray(16,18),[a,r,o,l]=this.#dn,h=new Float64Array((this.#Pn?.length??0)+2);for(let t=0,e=h.length-2;t<e;t+=2){h[t]=(this.#Pn[t]-a)/o;h[t+1]=(this.#Pn[t+1]-r)/l}h[h.length-2]=(this.#En-a)/o;h[h.length-1]=(this.#Cn-r)/l;const[d,c,u,p]=this.#In();if(isNaN(i[6])&&!this.isEmpty()){const t=new Float64Array(36);t.set([NaN,NaN,NaN,NaN,(i[2]-a)/o,(i[3]-r)/l,NaN,NaN,NaN,NaN,(i[4]-a)/o,(i[5]-r)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(i[16]-a)/o,(i[17]-r)/l,NaN,NaN,NaN,NaN,(i[14]-a)/o,(i[15]-r)/l],0);return new FreeHighlightOutline(t,h,this.#dn,this.#Mn,this.#yn,this.#wn)}const g=new Float64Array(this.#xn.length+24+this.#An.length);let m=t.length;for(let e=0;e<m;e+=2)if(isNaN(t[e]))g[e]=g[e+1]=NaN;else{g[e]=t[e];g[e+1]=t[e+1]}g.set([NaN,NaN,NaN,NaN,(s[0]-a)/o,(s[1]-r)/l,NaN,NaN,NaN,NaN,d,c,NaN,NaN,NaN,NaN,u,p,NaN,NaN,NaN,NaN,(n[0]-a)/o,(n[1]-r)/l],m);m+=24;for(let t=e.length-6;t>=6;t-=6)for(let i=0;i<6;i+=2)if(isNaN(e[t+i])){g[m]=g[m+1]=NaN;m+=2}else{g[m]=e[t+i];g[m+1]=e[t+i+1];m+=2}g.set([NaN,NaN,NaN,NaN,e[4],e[5]],m);return new FreeHighlightOutline(g,h,this.#dn,this.#Mn,this.#yn,this.#wn)}}class FreeHighlightOutline extends Outline{#dn;#Ln=null;#yn;#wn;#Pn;#Mn;#On;constructor(t,e,i,s,n,a){super();this.#On=t;this.#Pn=e;this.#dn=i;this.#Mn=s;this.#yn=n;this.#wn=a;this.#Nn(a);const{x:r,y:o,width:l,height:h}=this.#Ln;for(let e=0,i=t.length;e<i;e+=2){t[e]=(t[e]-r)/l;t[e+1]=(t[e+1]-o)/h}for(let t=0,i=e.length;t<i;t+=2){e[t]=(e[t]-r)/l;e[t+1]=(e[t+1]-o)/h}}toSVGPath(){const t=[`M${this.#On[4]} ${this.#On[5]}`];for(let e=6,i=this.#On.length;e<i;e+=6)isNaN(this.#On[e])?t.push(`L${this.#On[e+4]} ${this.#On[e+5]}`):t.push(`C${this.#On[e]} ${this.#On[e+1]} ${this.#On[e+2]} ${this.#On[e+3]} ${this.#On[e+4]} ${this.#On[e+5]}`);t.push("Z");return t.join(" ")}serialize([t,e,i,s],n){const a=i-t,r=s-e;let o,l;switch(n){case 0:o=this.#Bn(this.#On,t,s,a,-r);l=this.#Bn(this.#Pn,t,s,a,-r);break;case 90:o=this.#Hn(this.#On,t,e,a,r);l=this.#Hn(this.#Pn,t,e,a,r);break;case 180:o=this.#Bn(this.#On,i,e,-a,r);l=this.#Bn(this.#Pn,i,e,-a,r);break;case 270:o=this.#Hn(this.#On,i,s,-a,-r);l=this.#Hn(this.#Pn,i,s,-a,-r)}return{outline:Array.from(o),points:[Array.from(l)]}}#Bn(t,e,i,s,n){const a=new Float64Array(t.length);for(let r=0,o=t.length;r<o;r+=2){a[r]=e+t[r]*s;a[r+1]=i+t[r+1]*n}return a}#Hn(t,e,i,s,n){const a=new Float64Array(t.length);for(let r=0,o=t.length;r<o;r+=2){a[r]=e+t[r+1]*s;a[r+1]=i+t[r]*n}return a}#Nn(t){const e=this.#On;let i=e[4],s=e[5],n=i,a=s,r=i,o=s,l=i,h=s;const d=t?Math.max:Math.min;for(let t=6,c=e.length;t<c;t+=6){if(isNaN(e[t])){n=Math.min(n,e[t+4]);a=Math.min(a,e[t+5]);r=Math.max(r,e[t+4]);o=Math.max(o,e[t+5]);if(h<e[t+5]){l=e[t+4];h=e[t+5]}else h===e[t+5]&&(l=d(l,e[t+4]))}else{const c=Util.bezierBoundingBox(i,s,...e.slice(t,t+6));n=Math.min(n,c[0]);a=Math.min(a,c[1]);r=Math.max(r,c[2]);o=Math.max(o,c[3]);if(h<c[3]){l=c[2];h=c[3]}else h===c[3]&&(l=d(l,c[2]))}i=e[t+4];s=e[t+5]}const c=n-this.#yn,u=a-this.#yn,p=r-n+2*this.#yn,g=o-a+2*this.#yn;this.#Ln={x:c,y:u,width:p,height:g,lastPoint:[l,h]}}get box(){return this.#Ln}getNewOutline(t,e){const{x:i,y:s,width:n,height:a}=this.#Ln,[r,o,l,h]=this.#dn,d=n*l,c=a*h,u=i*l+r,p=s*h+o,g=new FreeOutliner({x:this.#Pn[0]*d+u,y:this.#Pn[1]*c+p},this.#dn,this.#Mn,t,this.#wn,e??this.#yn);for(let t=2;t<this.#Pn.length;t+=2)g.add({x:this.#Pn[t]*d+u,y:this.#Pn[t+1]*c+p});return g.getOutlines()}}class ColorPicker{#ds=this.#cs.bind(this);#zn=this.#o.bind(this);#Un=null;#jn=null;#$n;#Vn=null;#Wn=!1;#Gn=!1;#a=null;#qn;#p=null;#Xn;static get _keyboardManager(){return shadow(this,"_keyboardManager",new KeyboardManager([[["Escape","mac+Escape"],ColorPicker.prototype._hideDropdownFromKeyboard],[[" ","mac+ "],ColorPicker.prototype._colorSelectFromKeyboard],[["ArrowDown","ArrowRight","mac+ArrowDown","mac+ArrowRight"],ColorPicker.prototype._moveToNext],[["ArrowUp","ArrowLeft","mac+ArrowUp","mac+ArrowLeft"],ColorPicker.prototype._moveToPrevious],[["Home","mac+Home"],ColorPicker.prototype._moveToBeginning],[["End","mac+End"],ColorPicker.prototype._moveToEnd]]))}constructor({editor:t=null,uiManager:e=null}){if(t){this.#Gn=!1;this.#Xn=g.HIGHLIGHT_COLOR;this.#a=t}else{this.#Gn=!0;this.#Xn=g.HIGHLIGHT_DEFAULT_COLOR}this.#p=t?._uiManager||e;this.#qn=this.#p._eventBus;this.#$n=t?.color||this.#p?.highlightColors.values().next().value||"#FFFF98"}renderButton(){const t=this.#Un=document.createElement("button");t.className="colorPicker";t.tabIndex="0";t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-button");t.setAttribute("aria-haspopup",!0);const e=this.#p._signal;t.addEventListener("click",this.#Kn.bind(this),{signal:e});t.addEventListener("keydown",this.#ds,{signal:e});const i=this.#jn=document.createElement("span");i.className="swatch";i.setAttribute("aria-hidden",!0);i.style.backgroundColor=this.#$n;t.append(i);return t}renderMainDropdown(){const t=this.#Vn=this.#Yn();t.setAttribute("aria-orientation","horizontal");t.setAttribute("aria-labelledby","highlightColorPickerLabel");return t}#Yn(){const t=document.createElement("div"),e=this.#p._signal;t.addEventListener("contextmenu",noContextMenu,{signal:e});t.className="dropdown";t.role="listbox";t.setAttribute("aria-multiselectable",!1);t.setAttribute("aria-orientation","vertical");t.setAttribute("data-l10n-id","pdfjs-editor-colorpicker-dropdown");for(const[i,s]of this.#p.highlightColors){const n=document.createElement("button");n.tabIndex="0";n.role="option";n.setAttribute("data-color",s);n.title=i;n.setAttribute("data-l10n-id",`pdfjs-editor-colorpicker-${i}`);const a=document.createElement("span");n.append(a);a.className="swatch";a.style.backgroundColor=s;n.setAttribute("aria-selected",s===this.#$n);n.addEventListener("click",this.#Qn.bind(this,s),{signal:e});t.append(n)}t.addEventListener("keydown",this.#ds,{signal:e});return t}#Qn(t,e){e.stopPropagation();this.#qn.dispatch("switchannotationeditorparams",{source:this,type:this.#Xn,value:t})}_colorSelectFromKeyboard(t){if(t.target===this.#Un){this.#Kn(t);return}const e=t.target.getAttribute("data-color");e&&this.#Qn(e,t)}_moveToNext(t){this.#Jn?t.target!==this.#Un?t.target.nextSibling?.focus():this.#Vn.firstChild?.focus():this.#Kn(t)}_moveToPrevious(t){if(t.target!==this.#Vn?.firstChild&&t.target!==this.#Un){this.#Jn||this.#Kn(t);t.target.previousSibling?.focus()}else this.#Jn&&this._hideDropdownFromKeyboard()}_moveToBeginning(t){this.#Jn?this.#Vn.firstChild?.focus():this.#Kn(t)}_moveToEnd(t){this.#Jn?this.#Vn.lastChild?.focus():this.#Kn(t)}#cs(t){ColorPicker._keyboardManager.exec(this,t)}#Kn(t){if(this.#Jn){this.hideDropdown();return}this.#Wn=0===t.detail;window.addEventListener("pointerdown",this.#zn,{signal:this.#p._signal});if(this.#Vn){this.#Vn.classList.remove("hidden");return}const e=this.#Vn=this.#Yn();this.#Un.append(e)}#o(t){this.#Vn?.contains(t.target)||this.hideDropdown()}hideDropdown(){this.#Vn?.classList.add("hidden");window.removeEventListener("pointerdown",this.#zn)}get#Jn(){return this.#Vn&&!this.#Vn.classList.contains("hidden")}_hideDropdownFromKeyboard(){if(!this.#Gn)if(this.#Jn){this.hideDropdown();this.#Un.focus({preventScroll:!0,focusVisible:this.#Wn})}else this.#a?.unselect()}updateColor(t){this.#jn&&(this.#jn.style.backgroundColor=t);if(!this.#Vn)return;const e=this.#p.highlightColors.values();for(const i of this.#Vn.children)i.setAttribute("aria-selected",e.next().value===t)}destroy(){this.#Un?.remove();this.#Un=null;this.#jn=null;this.#Vn?.remove();this.#Vn=null}}class HighlightEditor extends AnnotationEditor{#Zn=null;#ta=0;#ea;#ia=null;#n=null;#sa=null;#na=null;#aa=0;#ra=null;#oa=null;#b=null;#la=!1;#ot=this.#ha.bind(this);#da=null;#ca;#ua=null;#pa="";#kn;#ga="";static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=12;static _l10nPromise;static _type="highlight";static _editorType=p.HIGHLIGHT;static _freeHighlightId=-1;static _freeHighlight=null;static _freeHighlightClipId="";static get _keyboardManager(){const t=HighlightEditor.prototype;return shadow(this,"_keyboardManager",new KeyboardManager([[["ArrowLeft","mac+ArrowLeft"],t._moveCaret,{args:[0]}],[["ArrowRight","mac+ArrowRight"],t._moveCaret,{args:[1]}],[["ArrowUp","mac+ArrowUp"],t._moveCaret,{args:[2]}],[["ArrowDown","mac+ArrowDown"],t._moveCaret,{args:[3]}]]))}constructor(t){super({...t,name:"highlightEditor"});this.color=t.color||HighlightEditor._defaultColor;this.#kn=t.thickness||HighlightEditor._defaultThickness;this.#ca=t.opacity||HighlightEditor._defaultOpacity;this.#ea=t.boxes||null;this.#ga=t.methodOfCreation||"";this.#pa=t.text||"";this._isDraggable=!1;if(t.highlightId>-1){this.#la=!0;this.#ma(t);this.#fa()}else{this.#Zn=t.anchorNode;this.#ta=t.anchorOffset;this.#na=t.focusNode;this.#aa=t.focusOffset;this.#ba();this.#fa();this.rotate(this.rotation)}}get telemetryInitialData(){return{action:"added",type:this.#la?"free_highlight":"highlight",color:this._uiManager.highlightColorNames.get(this.color),thickness:this.#kn,methodOfCreation:this.#ga}}get telemetryFinalData(){return{type:"highlight",color:this._uiManager.highlightColorNames.get(this.color)}}static computeTelemetryFinalData(t){return{numberOfColors:t.get("color").size}}#ba(){const t=new Outliner(this.#ea,.001);this.#oa=t.getOutlines();({x:this.x,y:this.y,width:this.width,height:this.height}=this.#oa.box);const e=new Outliner(this.#ea,.0025,.001,"ltr"===this._uiManager.direction);this.#sa=e.getOutlines();const{lastPoint:i}=this.#sa.box;this.#da=[(i[0]-this.x)/this.width,(i[1]-this.y)/this.height]}#ma({highlightOutlines:t,highlightId:e,clipPathId:i}){this.#oa=t;this.#sa=t.getNewOutline(this.#kn/2****,.0025);if(e>=0){this.#b=e;this.#ia=i;this.parent.drawLayer.finalizeLine(e,t);this.#ua=this.parent.drawLayer.highlightOutline(this.#sa)}else if(this.parent){const e=this.parent.viewport.rotation;this.parent.drawLayer.updateLine(this.#b,t);this.parent.drawLayer.updateBox(this.#b,HighlightEditor.#va(this.#oa.box,(e-this.rotation+360)%360));this.parent.drawLayer.updateLine(this.#ua,this.#sa);this.parent.drawLayer.updateBox(this.#ua,HighlightEditor.#va(this.#sa.box,e))}const{x:s,y:n,width:a,height:r}=t.box;switch(this.rotation){case 0:this.x=s;this.y=n;this.width=a;this.height=r;break;case 90:{const[t,e]=this.parentDimensions;this.x=n;this.y=1-s;this.width=a*e/t;this.height=r*t/e;break}case 180:this.x=1-s;this.y=1-n;this.width=a;this.height=r;break;case 270:{const[t,e]=this.parentDimensions;this.x=1-n;this.y=s;this.width=a*e/t;this.height=r*t/e;break}}const{lastPoint:o}=this.#sa.box;this.#da=[(o[0]-s)/a,(o[1]-n)/r]}static initialize(t,e){AnnotationEditor.initialize(t,e);HighlightEditor._defaultColor||=e.highlightColors?.values().next().value||"#fff066"}static updateDefaultParams(t,e){switch(t){case g.HIGHLIGHT_DEFAULT_COLOR:HighlightEditor._defaultColor=e;break;case g.HIGHLIGHT_THICKNESS:HighlightEditor._defaultThickness=e}}translateInPage(t,e){}get toolbarPosition(){return this.#da}updateParams(t,e){switch(t){case g.HIGHLIGHT_COLOR:this.#in(e);break;case g.HIGHLIGHT_THICKNESS:this.#Aa(e)}}static get defaultPropertiesToUpdate(){return[[g.HIGHLIGHT_DEFAULT_COLOR,HighlightEditor._defaultColor],[g.HIGHLIGHT_THICKNESS,HighlightEditor._defaultThickness]]}get propertiesToUpdate(){return[[g.HIGHLIGHT_COLOR,this.color||HighlightEditor._defaultColor],[g.HIGHLIGHT_THICKNESS,this.#kn||HighlightEditor._defaultThickness],[g.HIGHLIGHT_FREE,this.#la]]}#in(t){const setColor=t=>{this.color=t;this.parent?.drawLayer.changeColor(this.#b,t);this.#n?.updateColor(t)},e=this.color;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.HIGHLIGHT_COLOR,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"color_changed",color:this._uiManager.highlightColorNames.get(t)},!0)}#Aa(t){const e=this.#kn,setThickness=t=>{this.#kn=t;this.#ya(t)};this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0});this._reportTelemetry({action:"thickness_changed",thickness:t},!0)}async addEditToolbar(){const t=await super.addEditToolbar();if(!t)return null;if(this._uiManager.highlightColors){this.#n=new ColorPicker({editor:this});t.addColorPicker(this.#n)}return t}disableEditing(){super.disableEditing();this.div.classList.toggle("disabled",!0)}enableEditing(){super.enableEditing();this.div.classList.toggle("disabled",!1)}fixAndSetPosition(){return super.fixAndSetPosition(this.#wa())}getBaseTranslation(){return[0,0]}getRect(t,e){return super.getRect(t,e,this.#wa())}onceAdded(){this.parent.addUndoableEditor(this);this.div.focus()}remove(){this.#xa();this._reportTelemetry({action:"deleted"});super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#fa();this.isAttachedToDOM||this.parent.add(this)}}}setParent(t){let e=!1;if(this.parent&&!t)this.#xa();else if(t){this.#fa(t);e=!this.parent&&this.div?.classList.contains("selectedEditor")}super.setParent(t);this.show(this._isVisible);e&&this.select()}#ya(t){if(!this.#la)return;this.#ma({highlightOutlines:this.#oa.getNewOutline(t/2)});this.fixAndSetPosition();const[e,i]=this.parentDimensions;this.setDims(this.width*e,this.height*i)}#xa(){if(null!==this.#b&&this.parent){this.parent.drawLayer.remove(this.#b);this.#b=null;this.parent.drawLayer.remove(this.#ua);this.#ua=null}}#fa(t=this.parent){if(null===this.#b){({id:this.#b,clipPathId:this.#ia}=t.drawLayer.highlight(this.#oa,this.color,this.#ca));this.#ua=t.drawLayer.highlightOutline(this.#sa);this.#ra&&(this.#ra.style.clipPath=this.#ia)}}static#va({x:t,y:e,width:i,height:s},n){switch(n){case 90:return{x:1-e-s,y:t,width:s,height:i};case 180:return{x:1-t-i,y:1-e-s,width:i,height:s};case 270:return{x:e,y:1-t-i,width:s,height:i}}return{x:t,y:e,width:i,height:s}}rotate(t){const{drawLayer:e}=this.parent;let i;if(this.#la){t=(t-this.rotation+360)%360;i=HighlightEditor.#va(this.#oa.box,t)}else i=HighlightEditor.#va(this,t);e.rotate(this.#b,t);e.rotate(this.#ua,t);e.updateBox(this.#b,i);e.updateBox(this.#ua,HighlightEditor.#va(this.#sa.box,t))}render(){if(this.div)return this.div;const t=super.render();if(this.#pa){t.setAttribute("aria-label",this.#pa);t.setAttribute("role","mark")}this.#la?t.classList.add("free"):this.div.addEventListener("keydown",this.#ot,{signal:this._uiManager._signal});const e=this.#ra=document.createElement("div");t.append(e);e.setAttribute("aria-hidden","true");e.className="internal";e.style.clipPath=this.#ia;const[i,s]=this.parentDimensions;this.setDims(this.width*i,this.height*s);bindEvents(this,this.#ra,["pointerover","pointerleave"]);this.enableEditing();return t}pointerover(){this.parent.drawLayer.addClass(this.#ua,"hovered")}pointerleave(){this.parent.drawLayer.removeClass(this.#ua,"hovered")}#ha(t){HighlightEditor._keyboardManager.exec(this,t)}_moveCaret(t){this.parent.unselect(this);switch(t){case 0:case 2:this.#_a(!0);break;case 1:case 3:this.#_a(!1)}}#_a(t){if(!this.#Zn)return;const e=window.getSelection();t?e.setPosition(this.#Zn,this.#ta):e.setPosition(this.#na,this.#aa)}select(){super.select();if(this.#ua){this.parent?.drawLayer.removeClass(this.#ua,"hovered");this.parent?.drawLayer.addClass(this.#ua,"selected")}}unselect(){super.unselect();if(this.#ua){this.parent?.drawLayer.removeClass(this.#ua,"selected");this.#la||this.#_a(!1)}}get _mustFixPosition(){return!this.#la}show(t=this._isVisible){super.show(t);if(this.parent){this.parent.drawLayer.show(this.#b,t);this.parent.drawLayer.show(this.#ua,t)}}#wa(){return this.#la?this.rotation:0}#Ea(){if(this.#la)return null;const[t,e]=this.pageDimensions,i=this.#ea,s=new Float32Array(8*i.length);let n=0;for(const{x:a,y:r,width:o,height:l}of i){const i=a*t,h=(1-r-l)*e;s[n]=s[n+4]=i;s[n+1]=s[n+3]=h;s[n+2]=s[n+6]=i+o*t;s[n+5]=s[n+7]=h+l*e;n+=8}return s}#Ca(t){return this.#oa.serialize(t,this.#wa())}static startHighlighting(t,e,{target:i,x:s,y:n}){const{x:a,y:r,width:o,height:l}=i.getBoundingClientRect(),pointerMove=e=>{this.#Sa(t,e)},h=t._signal,d={capture:!0,passive:!1,signal:h},pointerDown=t=>{t.preventDefault();t.stopPropagation()},pointerUpCallback=e=>{i.removeEventListener("pointermove",pointerMove);window.removeEventListener("blur",pointerUpCallback);window.removeEventListener("pointerup",pointerUpCallback);window.removeEventListener("pointerdown",pointerDown,d);window.removeEventListener("contextmenu",noContextMenu);this.#Ta(t,e)};window.addEventListener("blur",pointerUpCallback,{signal:h});window.addEventListener("pointerup",pointerUpCallback,{signal:h});window.addEventListener("pointerdown",pointerDown,d);window.addEventListener("contextmenu",noContextMenu,{signal:h});i.addEventListener("pointermove",pointerMove,{signal:h});this._freeHighlight=new FreeOutliner({x:s,y:n},[a,r,o,l],t.scale,this._defaultThickness/2,e,.001);({id:this._freeHighlightId,clipPathId:this._freeHighlightClipId}=t.drawLayer.highlight(this._freeHighlight,this._defaultColor,this._defaultOpacity,!0))}static#Sa(t,e){this._freeHighlight.add(e)&&t.drawLayer.updatePath(this._freeHighlightId,this._freeHighlight)}static#Ta(t,e){this._freeHighlight.isEmpty()?t.drawLayer.removeFreeHighlight(this._freeHighlightId):t.createAndAddNewEditor(e,!1,{highlightId:this._freeHighlightId,highlightOutlines:this._freeHighlight.getOutlines(),clipPathId:this._freeHighlightClipId,methodOfCreation:"main_toolbar"});this._freeHighlightId=-1;this._freeHighlight=null;this._freeHighlightClipId=""}static deserialize(t,e,i){const s=super.deserialize(t,e,i),{rect:[n,a,r,o],color:l,quadPoints:h}=t;s.color=Util.makeHexColor(...l);s.#ca=t.opacity;const[d,c]=s.pageDimensions;s.width=(r-n)/d;s.height=(o-a)/c;const u=s.#ea=[];for(let t=0;t<h.length;t+=8)u.push({x:(h[4]-r)/d,y:(o-(1-h[t+5]))/c,width:(h[t+2]-h[t])/d,height:(h[t+5]-h[t+1])/c});s.#ba();return s}serialize(t=!1){if(this.isEmpty()||t)return null;const e=this.getRect(0,0),i=AnnotationEditor._colorManager.convert(this.color);return{annotationType:p.HIGHLIGHT,color:i,opacity:this.#ca,thickness:this.#kn,quadPoints:this.#Ea(),outlines:this.#Ca(e),pageIndex:this.pageIndex,rect:e,rotation:this.#wa(),structTreeParentId:this._structTreeParentId}}static canCreateNewEmptyEditor(){return!1}}class InkEditor extends AnnotationEditor{#Ma=0;#ka=0;#Pa=this.canvasPointermove.bind(this);#Da=this.canvasPointerleave.bind(this);#Fa=this.canvasPointerup.bind(this);#Ra=this.canvasPointerdown.bind(this);#Ia=null;#La=new Path2D;#Oa=!1;#Na=!1;#Ba=!1;#Ha=null;#za=0;#Ua=0;#ja=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";static _editorType=p.INK;constructor(t){super({...t,name:"inkEditor"});this.color=t.color||null;this.thickness=t.thickness||null;this.opacity=t.opacity||null;this.paths=[];this.bezierPath2D=[];this.allRawPaths=[];this.currentPath=[];this.scaleFactor=1;this.translationX=this.translationY=0;this.x=0;this.y=0;this._willKeepAspectRatio=!0}static initialize(t,e){AnnotationEditor.initialize(t,e)}static updateDefaultParams(t,e){switch(t){case g.INK_THICKNESS:InkEditor._defaultThickness=e;break;case g.INK_COLOR:InkEditor._defaultColor=e;break;case g.INK_OPACITY:InkEditor._defaultOpacity=e/100}}updateParams(t,e){switch(t){case g.INK_THICKNESS:this.#Aa(e);break;case g.INK_COLOR:this.#in(e);break;case g.INK_OPACITY:this.#$a(e)}}static get defaultPropertiesToUpdate(){return[[g.INK_THICKNESS,InkEditor._defaultThickness],[g.INK_COLOR,InkEditor._defaultColor||AnnotationEditor._defaultLineColor],[g.INK_OPACITY,Math.round(100*InkEditor._defaultOpacity)]]}get propertiesToUpdate(){return[[g.INK_THICKNESS,this.thickness||InkEditor._defaultThickness],[g.INK_COLOR,this.color||InkEditor._defaultColor||AnnotationEditor._defaultLineColor],[g.INK_OPACITY,Math.round(100*(this.opacity??InkEditor._defaultOpacity))]]}#Aa(t){const setThickness=t=>{this.thickness=t;this.#Va()},e=this.thickness;this.addCommands({cmd:setThickness.bind(this,t),undo:setThickness.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#in(t){const setColor=t=>{this.color=t;this.#Wa()},e=this.color;this.addCommands({cmd:setColor.bind(this,t),undo:setColor.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#$a(t){const setOpacity=t=>{this.opacity=t;this.#Wa()};t/=100;const e=this.opacity;this.addCommands({cmd:setOpacity.bind(this,t),undo:setOpacity.bind(this,e),post:this._uiManager.updateUI.bind(this._uiManager,this),mustExec:!0,type:g.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){if(!this.canvas){this.#Ga();this.#qa()}if(!this.isAttachedToDOM){this.parent.add(this);this.#Xa()}this.#Va()}}}remove(){if(null!==this.canvas){this.isEmpty()||this.commit();this.canvas.width=this.canvas.height=0;this.canvas.remove();this.canvas=null;if(this.#Ia){clearTimeout(this.#Ia);this.#Ia=null}this.#Ha?.disconnect();this.#Ha=null;super.remove()}}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this);super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,i=this.width*t,s=this.height*e;this.setDimensions(i,s)}enableEditMode(){if(!this.#Oa&&null!==this.canvas){super.enableEditMode();this._isDraggable=!1;this.canvas.addEventListener("pointerdown",this.#Ra,{signal:this._uiManager._signal})}}disableEditMode(){if(this.isInEditMode()&&null!==this.canvas){super.disableEditMode();this._isDraggable=!this.isEmpty();this.div.classList.remove("editing");this.canvas.removeEventListener("pointerdown",this.#Ra)}}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#Ka(){const{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}#Ya(){const{ctx:t,color:e,opacity:i,thickness:s,parentScale:n,scaleFactor:a}=this;t.lineWidth=s*n/a;t.lineCap="round";t.lineJoin="round";t.miterLimit=10;t.strokeStyle=`${e}${function opacityToHex(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")}(i)}`}#Qa(t,e){const i=this._uiManager._signal;this.canvas.addEventListener("contextmenu",noContextMenu,{signal:i});this.canvas.addEventListener("pointerleave",this.#Da,{signal:i});this.canvas.addEventListener("pointermove",this.#Pa,{signal:i});this.canvas.addEventListener("pointerup",this.#Fa,{signal:i});this.canvas.removeEventListener("pointerdown",this.#Ra);this.isEditing=!0;if(!this.#Ba){this.#Ba=!0;this.#Xa();this.thickness||=InkEditor._defaultThickness;this.color||=InkEditor._defaultColor||AnnotationEditor._defaultLineColor;this.opacity??=InkEditor._defaultOpacity}this.currentPath.push([t,e]);this.#Na=!1;this.#Ya();this.#ja=()=>{this.#Ja();this.#ja&&window.requestAnimationFrame(this.#ja)};window.requestAnimationFrame(this.#ja)}#Za(t,e){const[i,s]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===i&&e===s)return;const n=this.currentPath;let a=this.#La;n.push([t,e]);this.#Na=!0;if(n.length<=2){a.moveTo(...n[0]);a.lineTo(t,e)}else{if(3===n.length){this.#La=a=new Path2D;a.moveTo(...n[0])}this.#tr(a,...n.at(-3),...n.at(-2),t,e)}}#er(){if(0===this.currentPath.length)return;const t=this.currentPath.at(-1);this.#La.lineTo(...t)}#ir(t,e){this.#ja=null;t=Math.min(Math.max(t,0),this.canvas.width);e=Math.min(Math.max(e,0),this.canvas.height);this.#Za(t,e);this.#er();let i;if(1!==this.currentPath.length)i=this.#sr();else{const s=[t,e];i=[[s,s.slice(),s.slice(),s]]}const s=this.#La,n=this.currentPath;this.currentPath=[];this.#La=new Path2D;this.addCommands({cmd:()=>{this.allRawPaths.push(n);this.paths.push(i);this.bezierPath2D.push(s);this._uiManager.rebuild(this)},undo:()=>{this.allRawPaths.pop();this.paths.pop();this.bezierPath2D.pop();if(0===this.paths.length)this.remove();else{if(!this.canvas){this.#Ga();this.#qa()}this.#Va()}},mustExec:!0})}#Ja(){if(!this.#Na)return;this.#Na=!1;const t=Math.ceil(this.thickness*this.parentScale),e=this.currentPath.slice(-3),i=e.map((t=>t[0])),s=e.map((t=>t[1])),{ctx:n}=(Math.min(...i),Math.max(...i),Math.min(...s),Math.max(...s),this);n.save();n.clearRect(0,0,this.canvas.width,this.canvas.height);for(const t of this.bezierPath2D)n.stroke(t);n.stroke(this.#La);n.restore()}#tr(t,e,i,s,n,a,r){const o=(e+s)/2,l=(i+n)/2,h=(s+a)/2,d=(n+r)/2;t.bezierCurveTo(o+2*(s-o)/3,l+2*(n-l)/3,h+2*(s-h)/3,d+2*(n-d)/3,h,d)}#sr(){const t=this.currentPath;if(t.length<=2)return[[t[0],t[0],t.at(-1),t.at(-1)]];const e=[];let i,[s,n]=t[0];for(i=1;i<t.length-2;i++){const[a,r]=t[i],[o,l]=t[i+1],h=(a+o)/2,d=(r+l)/2,c=[s+2*(a-s)/3,n+2*(r-n)/3],u=[h+2*(a-h)/3,d+2*(r-d)/3];e.push([[s,n],c,u,[h,d]]);[s,n]=[h,d]}const[a,r]=t[i],[o,l]=t[i+1],h=[s+2*(a-s)/3,n+2*(r-n)/3],d=[o+2*(a-o)/3,l+2*(r-l)/3];e.push([[s,n],h,d,[o,l]]);return e}#Wa(){if(this.isEmpty()){this.#nr();return}this.#Ya();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,t.width,t.height);this.#nr();for(const t of this.bezierPath2D)e.stroke(t)}commit(){if(!this.#Oa){super.commit();this.isEditing=!1;this.disableEditMode();this.setInForeground();this.#Oa=!0;this.div.classList.add("disabled");this.#Va(!0);this.select();this.parent.addInkEditorIfNeeded(!0);this.moveInDOM();this.div.focus({preventScroll:!0})}}focusin(t){if(this._focusEventsAllowed){super.focusin(t);this.enableEditMode()}}canvasPointerdown(t){if(0===t.button&&this.isInEditMode()&&!this.#Oa){this.setInForeground();t.preventDefault();this.div.contains(document.activeElement)||this.div.focus({preventScroll:!0});this.#Qa(t.offsetX,t.offsetY)}}canvasPointermove(t){t.preventDefault();this.#Za(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault();this.#ar(t)}canvasPointerleave(t){this.#ar(t)}#ar(t){this.canvas.removeEventListener("pointerleave",this.#Da);this.canvas.removeEventListener("pointermove",this.#Pa);this.canvas.removeEventListener("pointerup",this.#Fa);this.canvas.addEventListener("pointerdown",this.#Ra,{signal:this._uiManager._signal});this.#Ia&&clearTimeout(this.#Ia);this.#Ia=setTimeout((()=>{this.#Ia=null;this.canvas.removeEventListener("contextmenu",noContextMenu)}),10);this.#ir(t.offsetX,t.offsetY);this.addToAnnotationStorage();this.setInBackground()}#Ga(){this.canvas=document.createElement("canvas");this.canvas.width=this.canvas.height=0;this.canvas.className="inkEditorCanvas";this.canvas.setAttribute("data-l10n-id","pdfjs-ink-canvas");this.div.append(this.canvas);this.ctx=this.canvas.getContext("2d")}#qa(){this.#Ha=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}));this.#Ha.observe(this.div);this._uiManager._signal.addEventListener("abort",(()=>{this.#Ha?.disconnect();this.#Ha=null}),{once:!0})}get isResizable(){return!this.isEmpty()&&this.#Oa}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.setAttribute("data-l10n-id","pdfjs-ink");const[i,s,n,a]=this.#Ka();this.setAt(i,s,0,0);this.setDims(n,a);this.#Ga();if(this.width){const[i,s]=this.parentDimensions;this.setAspectRatio(this.width*i,this.height*s);this.setAt(t*i,e*s,this.width*i,this.height*s);this.#Ba=!0;this.#Xa();this.setDims(this.width*i,this.height*s);this.#Wa();this.div.classList.add("disabled")}else{this.div.classList.add("editing");this.enableEditMode()}this.#qa();return this.div}#Xa(){if(!this.#Ba)return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t);this.canvas.height=Math.ceil(this.height*e);this.#nr()}setDimensions(t,e){const i=Math.round(t),s=Math.round(e);if(this.#za===i&&this.#Ua===s)return;this.#za=i;this.#Ua=s;this.canvas.style.visibility="hidden";const[n,a]=this.parentDimensions;this.width=t/n;this.height=e/a;this.fixAndSetPosition();this.#Oa&&this.#rr(t,e);this.#Xa();this.#Wa();this.canvas.style.visibility="visible";this.fixDims()}#rr(t,e){const i=this.#or(),s=(t-i)/this.#ka,n=(e-i)/this.#Ma;this.scaleFactor=Math.min(s,n)}#nr(){const t=this.#or()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#lr(t){const e=new Path2D;for(let i=0,s=t.length;i<s;i++){const[s,n,a,r]=t[i];0===i&&e.moveTo(...s);e.bezierCurveTo(n[0],n[1],a[0],a[1],r[0],r[1])}return e}static#hr(t,e,i){const[s,n,a,r]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2){t[e]+=s;t[e+1]=r-t[e+1]}break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]+s;t[e+1]=i+n}break;case 180:for(let e=0,i=t.length;e<i;e+=2){t[e]=a-t[e];t[e+1]+=n}break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=a-t[e+1];t[e+1]=r-i}break;default:throw new Error("Invalid rotation")}return t}static#dr(t,e,i){const[s,n,a,r]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2){t[e]-=s;t[e+1]=r-t[e+1]}break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]-n;t[e+1]=i-s}break;case 180:for(let e=0,i=t.length;e<i;e+=2){t[e]=a-t[e];t[e+1]-=n}break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=r-t[e+1];t[e+1]=a-i}break;default:throw new Error("Invalid rotation")}return t}#cr(t,e,i,s){const n=[],a=this.thickness/2,r=t*e+a,o=t*i+a;for(const e of this.paths){const i=[],a=[];for(let s=0,n=e.length;s<n;s++){const[l,h,d,c]=e[s];if(l[0]===c[0]&&l[1]===c[1]&&1===n){const e=t*l[0]+r,s=t*l[1]+o;i.push(e,s);a.push(e,s);break}const u=t*l[0]+r,p=t*l[1]+o,g=t*h[0]+r,m=t*h[1]+o,f=t*d[0]+r,b=t*d[1]+o,v=t*c[0]+r,A=t*c[1]+o;if(0===s){i.push(u,p);a.push(u,p)}i.push(g,m,f,b,v,A);a.push(g,m);s===n-1&&a.push(v,A)}n.push({bezier:InkEditor.#hr(i,s,this.rotation),points:InkEditor.#hr(a,s,this.rotation)})}return n}#ur(){let t=1/0,e=-1/0,i=1/0,s=-1/0;for(const n of this.paths)for(const[a,r,o,l]of n){const n=Util.bezierBoundingBox(...a,...r,...o,...l);t=Math.min(t,n[0]);i=Math.min(i,n[1]);e=Math.max(e,n[2]);s=Math.max(s,n[3])}return[t,i,e,s]}#or(){return this.#Oa?Math.ceil(this.thickness*this.parentScale):0}#Va(t=!1){if(this.isEmpty())return;if(!this.#Oa){this.#Wa();return}const e=this.#ur(),i=this.#or();this.#ka=Math.max(AnnotationEditor.MIN_SIZE,e[2]-e[0]);this.#Ma=Math.max(AnnotationEditor.MIN_SIZE,e[3]-e[1]);const s=Math.ceil(i+this.#ka*this.scaleFactor),n=Math.ceil(i+this.#Ma*this.scaleFactor),[a,r]=this.parentDimensions;this.width=s/a;this.height=n/r;this.setAspectRatio(s,n);const o=this.translationX,l=this.translationY;this.translationX=-e[0];this.translationY=-e[1];this.#Xa();this.#Wa();this.#za=s;this.#Ua=n;this.setDims(s,n);const h=t?i/this.scaleFactor/2:0;this.translate(o-this.translationX-h,l-this.translationY-h)}static deserialize(t,e,i){if(t instanceof InkAnnotationElement)return null;const s=super.deserialize(t,e,i);s.thickness=t.thickness;s.color=Util.makeHexColor(...t.color);s.opacity=t.opacity;const[n,a]=s.pageDimensions,r=s.width*n,o=s.height*a,l=s.parentScale,h=t.thickness/2;s.#Oa=!0;s.#za=Math.round(r);s.#Ua=Math.round(o);const{paths:d,rect:c,rotation:u}=t;for(let{bezier:t}of d){t=InkEditor.#dr(t,c,u);const e=[];s.paths.push(e);let i=l*(t[0]-h),n=l*(t[1]-h);for(let s=2,a=t.length;s<a;s+=6){const a=l*(t[s]-h),r=l*(t[s+1]-h),o=l*(t[s+2]-h),d=l*(t[s+3]-h),c=l*(t[s+4]-h),u=l*(t[s+5]-h);e.push([[i,n],[a,r],[o,d],[c,u]]);i=c;n=u}const a=this.#lr(e);s.bezierPath2D.push(a)}const p=s.#ur();s.#ka=Math.max(AnnotationEditor.MIN_SIZE,p[2]-p[0]);s.#Ma=Math.max(AnnotationEditor.MIN_SIZE,p[3]-p[1]);s.#rr(r,o);return s}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:p.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#cr(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}class StampEditor extends AnnotationEditor{#pr=null;#gr=null;#mr=null;#fr=null;#br=null;#vr="";#Ar=null;#Ha=null;#yr=null;#wr=!1;#xr=!1;static _type="stamp";static _editorType=p.STAMP;constructor(t){super({...t,name:"stampEditor"});this.#fr=t.bitmapUrl;this.#br=t.bitmapFile}static initialize(t,e){AnnotationEditor.initialize(t,e)}static get supportedTypes(){return shadow(this,"supportedTypes",["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"].map((t=>`image/${t}`)))}static get supportedTypesStr(){return shadow(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(p.STAMP,{bitmapFile:t.getAsFile()})}#_r(t,e=!1){if(t){this.#pr=t.bitmap;if(!e){this.#gr=t.id;this.#wr=t.isSvg}t.file&&(this.#vr=t.file.name);this.#Ga()}else this.remove()}#Er(){this.#mr=null;this._uiManager.enableWaiting(!1);this.#Ar&&this.div.focus()}#Cr(){if(this.#gr){this._uiManager.enableWaiting(!0);this._uiManager.imageManager.getFromId(this.#gr).then((t=>this.#_r(t,!0))).finally((()=>this.#Er()));return}if(this.#fr){const t=this.#fr;this.#fr=null;this._uiManager.enableWaiting(!0);this.#mr=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#_r(t))).finally((()=>this.#Er()));return}if(this.#br){const t=this.#br;this.#br=null;this._uiManager.enableWaiting(!0);this.#mr=this._uiManager.imageManager.getFromFile(t).then((t=>this.#_r(t))).finally((()=>this.#Er()));return}const t=document.createElement("input");t.type="file";t.accept=StampEditor.supportedTypesStr;const e=this._uiManager._signal;this.#mr=new Promise((i=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#_r(e)}else this.remove();i()}),{signal:e});t.addEventListener("cancel",(()=>{this.remove();i()}),{signal:e})})).finally((()=>this.#Er()));t.click()}remove(){if(this.#gr){this.#pr=null;this._uiManager.imageManager.deleteId(this.#gr);this.#Ar?.remove();this.#Ar=null;this.#Ha?.disconnect();this.#Ha=null;if(this.#yr){clearTimeout(this.#yr);this.#yr=null}}super.remove()}rebuild(){if(this.parent){super.rebuild();if(null!==this.div){this.#gr&&null===this.#Ar&&this.#Cr();this.isAttachedToDOM||this.parent.add(this)}}else this.#gr&&this.#Cr()}onceAdded(){this._isDraggable=!0;this.div.focus()}isEmpty(){return!(this.#mr||this.#pr||this.#fr||this.#br||this.#gr)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.div.hidden=!0;this.addAltTextButton();this.#pr?this.#Ga():this.#Cr();if(this.width){const[i,s]=this.parentDimensions;this.setAt(t*i,e*s,this.width*i,this.height*s)}return this.div}#Ga(){const{div:t}=this;let{width:e,height:i}=this.#pr;const[s,n]=this.pageDimensions,a=.75;if(this.width){e=this.width*s;i=this.height*n}else if(e>a*s||i>a*n){const t=Math.min(a*s/e,a*n/i);e*=t;i*=t}const[r,o]=this.parentDimensions;this.setDims(e*r/s,i*o/n);this._uiManager.enableWaiting(!1);const l=this.#Ar=document.createElement("canvas");t.append(l);t.hidden=!1;this.#Sr(e,i);this.#qa();if(!this.#xr){this.parent.addUndoableEditor(this);this.#xr=!0}this._reportTelemetry({action:"inserted_image"});this.#vr&&l.setAttribute("aria-label",this.#vr)}#Tr(t,e){const[i,s]=this.parentDimensions;this.width=t/i;this.height=e/s;this.setDims(t,e);this._initialOptions?.isCentered?this.center():this.fixAndSetPosition();this._initialOptions=null;null!==this.#yr&&clearTimeout(this.#yr);this.#yr=setTimeout((()=>{this.#yr=null;this.#Sr(t,e)}),200)}#Mr(t,e){const{width:i,height:s}=this.#pr;let n=i,a=s,r=this.#pr;for(;n>2*t||a>2*e;){const i=n,s=a;n>2*t&&(n=n>=16384?Math.floor(n/2)-1:Math.ceil(n/2));a>2*e&&(a=a>=16384?Math.floor(a/2)-1:Math.ceil(a/2));const o=new OffscreenCanvas(n,a);o.getContext("2d").drawImage(r,0,0,i,s,0,0,n,a);r=o.transferToImageBitmap()}return r}#Sr(t,e){t=Math.ceil(t);e=Math.ceil(e);const i=this.#Ar;if(!i||i.width===t&&i.height===e)return;i.width=t;i.height=e;const s=this.#wr?this.#pr:this.#Mr(t,e);if(this._uiManager.hasMLManager&&!this.hasAltText()){const i=new OffscreenCanvas(t,e).getContext("2d");i.drawImage(s,0,0,s.width,s.height,0,0,t,e);this._uiManager.mlGuess({service:"image-to-text",request:{data:i.getImageData(0,0,t,e).data,width:t,height:e,channels:4}}).then((t=>{const e=t?.output||"";this.parent&&e&&!this.hasAltText()&&(this.altTextData={altText:e,decorative:!1})}))}const n=i.getContext("2d");n.filter=this._uiManager.hcmFilter;n.drawImage(s,0,0,s.width,s.height,0,0,t,e)}getImageForAltText(){return this.#Ar}#kr(t){if(t){if(this.#wr){const t=this._uiManager.imageManager.getSvgUrl(this.#gr);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#pr);t.getContext("2d").drawImage(this.#pr,0,0);return t.toDataURL()}if(this.#wr){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*PixelsPerInch.PDF_TO_CSS_UNITS),s=Math.round(this.height*e*PixelsPerInch.PDF_TO_CSS_UNITS),n=new OffscreenCanvas(i,s);n.getContext("2d").drawImage(this.#pr,0,0,this.#pr.width,this.#pr.height,0,0,i,s);return n.transferToImageBitmap()}return structuredClone(this.#pr)}#qa(){if(this._uiManager._signal){this.#Ha=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.#Tr(e.width,e.height)}));this.#Ha.observe(this.div);this._uiManager._signal.addEventListener("abort",(()=>{this.#Ha?.disconnect();this.#Ha=null}),{once:!0})}}static deserialize(t,e,i){if(t instanceof StampAnnotationElement)return null;const s=super.deserialize(t,e,i),{rect:n,bitmapUrl:a,bitmapId:r,isSvg:o,accessibilityData:l}=t;r&&i.imageManager.isValidId(r)?s.#gr=r:s.#fr=a;s.#wr=o;const[h,d]=s.pageDimensions;s.width=(n[2]-n[0])/h;s.height=(n[3]-n[1])/d;l&&(s.altTextData=l);return s}serialize(t=!1,e=null){if(this.isEmpty())return null;const i={annotationType:p.STAMP,bitmapId:this.#gr,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#wr,structTreeParentId:this._structTreeParentId};if(t){i.bitmapUrl=this.#kr(!0);i.accessibilityData=this.altTextData;return i}const{decorative:s,altText:n}=this.altTextData;!s&&n&&(i.accessibilityData={type:"Figure",alt:n});if(null===e)return i;e.stamps||=new Map;const a=this.#wr?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#gr)){if(this.#wr){const t=e.stamps.get(this.#gr);if(a>t.area){t.area=a;t.serialized.bitmap.close();t.serialized.bitmap=this.#kr(!1)}}}else{e.stamps.set(this.#gr,{area:a,serialized:i});i.bitmap=this.#kr(!1)}return i}}class AnnotationEditorLayer{#js;#Pr=!1;#Dr=null;#Fr=null;#Rr=null;#Ir=null;#Lr=null;#Or=new Map;#Nr=!1;#Br=!1;#Hr=!1;#zr=null;#p;static _initialized=!1;static#N=new Map([FreeTextEditor,InkEditor,StampEditor,HighlightEditor].map((t=>[t._editorType,t])));constructor({uiManager:t,pageIndex:e,div:i,accessibilityManager:s,annotationLayer:n,drawLayer:a,textLayer:r,viewport:o,l10n:l}){const h=[...AnnotationEditorLayer.#N.values()];if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;for(const e of h)e.initialize(l,t)}t.registerEditorTypes(h);this.#p=t;this.pageIndex=e;this.div=i;this.#js=s;this.#Dr=n;this.viewport=o;this.#zr=r;this.drawLayer=a;this.#p.addLayer(this)}get isEmpty(){return 0===this.#Or.size}get isInvisible(){return this.isEmpty&&this.#p.getMode()===p.NONE}updateToolbar(t){this.#p.updateToolbar(t)}updateMode(t=this.#p.getMode()){this.#Ur();switch(t){case p.NONE:this.disableTextSelection();this.togglePointerEvents(!1);this.toggleAnnotationLayerPointerEvents(!0);this.disableClick();return;case p.INK:this.addInkEditorIfNeeded(!1);this.disableTextSelection();this.togglePointerEvents(!0);this.disableClick();break;case p.HIGHLIGHT:this.enableTextSelection();this.togglePointerEvents(!1);this.disableClick();break;default:this.disableTextSelection();this.togglePointerEvents(!0);this.enableClick()}this.toggleAnnotationLayerPointerEvents(!1);const{classList:e}=this.div;for(const i of AnnotationEditorLayer.#N.values())e.toggle(`${i._type}Editing`,t===i._editorType);this.div.hidden=!1}hasTextLayer(t){return t===this.#zr?.div}addInkEditorIfNeeded(t){if(this.#p.getMode()!==p.INK)return;if(!t)for(const t of this.#Or.values())if(t.isEmpty()){t.setInBackground();return}this.createAndAddNewEditor({offsetX:0,offsetY:0},!1).setInBackground()}setEditingState(t){this.#p.setEditingState(t)}addCommands(t){this.#p.addCommands(t)}togglePointerEvents(t=!1){this.div.classList.toggle("disabled",!t)}toggleAnnotationLayerPointerEvents(t=!1){this.#Dr?.div.classList.toggle("disabled",!t)}enable(){this.div.tabIndex=0;this.togglePointerEvents(!0);const t=new Set;for(const e of this.#Or.values()){e.enableEditing();e.show(!0);if(e.annotationElementId){this.#p.removeChangedExistingAnnotation(e);t.add(e.annotationElementId)}}if(!this.#Dr)return;const e=this.#Dr.getEditableAnnotations();for(const i of e){i.hide();if(this.#p.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=this.deserialize(i);if(e){this.addOrRebuild(e);e.enableEditing()}}}disable(){this.#Hr=!0;this.div.tabIndex=-1;this.togglePointerEvents(!1);const t=new Map,e=new Map;for(const i of this.#Or.values()){i.disableEditing();if(i.annotationElementId)if(null===i.serialize()){e.set(i.annotationElementId,i);this.getEditableAnnotation(i.annotationElementId)?.show();i.remove()}else t.set(i.annotationElementId,i)}if(this.#Dr){const i=this.#Dr.getEditableAnnotations();for(const s of i){const{id:i}=s.data;if(this.#p.isDeletedAnnotationElement(i))continue;let n=e.get(i);if(n){n.resetAnnotationElement(s);n.show(!1);s.show()}else{n=t.get(i);if(n){this.#p.addChangedExistingAnnotation(n);n.renderAnnotationElement(s);n.show(!1)}s.show()}}}this.#Ur();this.isEmpty&&(this.div.hidden=!0);const{classList:i}=this.div;for(const t of AnnotationEditorLayer.#N.values())i.remove(`${t._type}Editing`);this.disableTextSelection();this.toggleAnnotationLayerPointerEvents(!0);this.#Hr=!1}getEditableAnnotation(t){return this.#Dr?.getEditableAnnotation(t)||null}setActiveEditor(t){this.#p.getActive()!==t&&this.#p.setActiveEditor(t)}enableTextSelection(){this.div.tabIndex=-1;if(this.#zr?.div&&!this.#Ir){this.#Ir=this.#jr.bind(this);this.#zr.div.addEventListener("pointerdown",this.#Ir,{signal:this.#p._signal});this.#zr.div.classList.add("highlighting")}}disableTextSelection(){this.div.tabIndex=0;if(this.#zr?.div&&this.#Ir){this.#zr.div.removeEventListener("pointerdown",this.#Ir);this.#Ir=null;this.#zr.div.classList.remove("highlighting")}}#jr(t){this.#p.unselectAll();if(t.target===this.#zr.div){const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;this.#p.showAllEditors("highlight",!0,!0);this.#zr.div.classList.add("free");HighlightEditor.startHighlighting(this,"ltr"===this.#p.direction,t);this.#zr.div.addEventListener("pointerup",(()=>{this.#zr.div.classList.remove("free")}),{once:!0,signal:this.#p._signal});t.preventDefault()}}enableClick(){if(this.#Rr)return;const t=this.#p._signal;this.#Rr=this.pointerdown.bind(this);this.#Fr=this.pointerup.bind(this);this.div.addEventListener("pointerdown",this.#Rr,{signal:t});this.div.addEventListener("pointerup",this.#Fr,{signal:t})}disableClick(){if(this.#Rr){this.div.removeEventListener("pointerdown",this.#Rr);this.div.removeEventListener("pointerup",this.#Fr);this.#Rr=null;this.#Fr=null}}attach(t){this.#Or.set(t.id,t);const{annotationElementId:e}=t;e&&this.#p.isDeletedAnnotationElement(e)&&this.#p.removeDeletedAnnotationElement(t)}detach(t){this.#Or.delete(t.id);this.#js?.removePointerInTextLayer(t.contentDiv);!this.#Hr&&t.annotationElementId&&this.#p.addDeletedAnnotationElement(t)}remove(t){this.detach(t);this.#p.removeEditor(t);t.div.remove();t.isAttachedToDOM=!1;this.#Br||this.addInkEditorIfNeeded(!1)}changeParent(t){if(t.parent!==this){if(t.parent&&t.annotationElementId){this.#p.addDeletedAnnotationElement(t.annotationElementId);AnnotationEditor.deleteAnnotationElement(t);t.annotationElementId=null}this.attach(t);t.parent?.detach(t);t.setParent(this);if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){if(t.parent!==this||!t.isAttachedToDOM){this.changeParent(t);this.#p.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}t.fixAndSetPosition();t.onceAdded();this.#p.addToAnnotationStorage(t);t._reportTelemetry(t.telemetryInitialData)}}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;if(t.div.contains(e)&&!this.#Lr){t._focusEventsAllowed=!1;this.#Lr=setTimeout((()=>{this.#Lr=null;if(t.div.contains(document.activeElement))t._focusEventsAllowed=!0;else{t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0,signal:this.#p._signal});e.focus()}}),0)}t._structTreeParentId=this.#js?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){if(t.needsToBeRebuilt()){t.parent||=this;t.rebuild();t.show()}else this.add(t)}addUndoableEditor(t){this.addCommands({cmd:()=>t._uiManager.rebuild(t),undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#p.getId()}get#$r(){return AnnotationEditorLayer.#N.get(this.#p.getMode())}get _signal(){return this.#p._signal}#Vr(t){const e=this.#$r;return e?new e.prototype.constructor(t):null}canCreateNewEmptyEditor(){return this.#$r?.canCreateNewEmptyEditor()}pasteEditor(t,e){this.#p.updateToolbar(t);this.#p.updateMode(t);const{offsetX:i,offsetY:s}=this.#Wr(),n=this.getNextId(),a=this.#Vr({parent:this,id:n,x:i,y:s,uiManager:this.#p,isCentered:!0,...e});a&&this.add(a)}deserialize(t){return AnnotationEditorLayer.#N.get(t.annotationType??t.annotationEditorType)?.deserialize(t,this,this.#p)||null}createAndAddNewEditor(t,e,i={}){const s=this.getNextId(),n=this.#Vr({parent:this,id:s,x:t.offsetX,y:t.offsetY,uiManager:this.#p,isCentered:e,...i});n&&this.add(n);return n}#Wr(){const{x:t,y:e,width:i,height:s}=this.div.getBoundingClientRect(),n=Math.max(0,t),a=Math.max(0,e),r=(n+Math.min(window.innerWidth,t+i))/2-t,o=(a+Math.min(window.innerHeight,e+s))/2-e,[l,h]=this.viewport.rotation%180==0?[r,o]:[o,r];return{offsetX:l,offsetY:h}}addNewEditor(){this.createAndAddNewEditor(this.#Wr(),!0)}setSelected(t){this.#p.setSelected(t)}toggleSelected(t){this.#p.toggleSelected(t)}isSelected(t){return this.#p.isSelected(t)}unselect(t){this.#p.unselect(t)}pointerup(t){const{isMac:e}=util_FeatureTest.platform;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&this.#Nr){this.#Nr=!1;this.#Pr?this.#p.getMode()!==p.STAMP?this.createAndAddNewEditor(t,!1):this.#p.unselectAll():this.#Pr=!0}}pointerdown(t){this.#p.getMode()===p.HIGHLIGHT&&this.enableTextSelection();if(this.#Nr){this.#Nr=!1;return}const{isMac:e}=util_FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Nr=!0;const i=this.#p.getActive();this.#Pr=!i||i.isEmpty()}findNewParent(t,e,i){const s=this.#p.findParent(e,i);if(null===s||s===this)return!1;s.changeParent(t);return!0}destroy(){if(this.#p.getActive()?.parent===this){this.#p.commitOrRemove();this.#p.setActiveEditor(null)}if(this.#Lr){clearTimeout(this.#Lr);this.#Lr=null}for(const t of this.#Or.values()){this.#js?.removePointerInTextLayer(t.contentDiv);t.setParent(null);t.isAttachedToDOM=!1;t.div.remove()}this.div=null;this.#Or.clear();this.#p.removeLayer(this)}#Ur(){this.#Br=!0;for(const t of this.#Or.values())t.isEmpty()&&t.remove();this.#Br=!1}render({viewport:t}){this.viewport=t;setLayerDimensions(this.div,t);for(const t of this.#p.getEditors(this.pageIndex)){this.add(t);t.rebuild()}this.updateMode()}update({viewport:t}){this.#p.commitOrRemove();this.#Ur();const e=this.viewport.rotation,i=t.rotation;this.viewport=t;setLayerDimensions(this.div,{rotation:i});if(e!==i)for(const t of this.#Or.values())t.rotate(i);this.addInkEditorIfNeeded(!1)}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}get scale(){return this.#p.viewParameters.realScale}}class DrawLayer{#xs=null;#b=0;#Gr=new Map;#qr=new Map;constructor({pageIndex:t}){this.pageIndex=t}setParent(t){if(this.#xs){if(this.#xs!==t){if(this.#Gr.size>0)for(const e of this.#Gr.values()){e.remove();t.append(e)}this.#xs=t}}else this.#xs=t}static get _svgFactory(){return shadow(this,"_svgFactory",new DOMSVGFactory)}static#Xr(t,{x:e=0,y:i=0,width:s=1,height:n=1}={}){const{style:a}=t;a.top=100*i+"%";a.left=100*e+"%";a.width=100*s+"%";a.height=100*n+"%"}#Kr(t){const e=DrawLayer._svgFactory.create(1,1,!0);this.#xs.append(e);e.setAttribute("aria-hidden",!0);DrawLayer.#Xr(e,t);return e}#Yr(t,e){const i=DrawLayer._svgFactory.createElement("clipPath");t.append(i);const s=`clip_${e}`;i.setAttribute("id",s);i.setAttribute("clipPathUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("use");i.append(n);n.setAttribute("href",`#${e}`);n.classList.add("clip");return s}highlight(t,e,i,s=!1){const n=this.#b++,a=this.#Kr(t.box);a.classList.add("highlight");t.free&&a.classList.add("free");const r=DrawLayer._svgFactory.createElement("defs");a.append(r);const o=DrawLayer._svgFactory.createElement("path");r.append(o);const l=`path_p${this.pageIndex}_${n}`;o.setAttribute("id",l);o.setAttribute("d",t.toSVGPath());s&&this.#qr.set(n,o);const h=this.#Yr(r,l),d=DrawLayer._svgFactory.createElement("use");a.append(d);a.setAttribute("fill",e);a.setAttribute("fill-opacity",i);d.setAttribute("href",`#${l}`);this.#Gr.set(n,a);return{id:n,clipPathId:`url(#${h})`}}highlightOutline(t){const e=this.#b++,i=this.#Kr(t.box);i.classList.add("highlightOutline");const s=DrawLayer._svgFactory.createElement("defs");i.append(s);const n=DrawLayer._svgFactory.createElement("path");s.append(n);const a=`path_p${this.pageIndex}_${e}`;n.setAttribute("id",a);n.setAttribute("d",t.toSVGPath());n.setAttribute("vector-effect","non-scaling-stroke");let r;if(t.free){i.classList.add("free");const t=DrawLayer._svgFactory.createElement("mask");s.append(t);r=`mask_p${this.pageIndex}_${e}`;t.setAttribute("id",r);t.setAttribute("maskUnits","objectBoundingBox");const n=DrawLayer._svgFactory.createElement("rect");t.append(n);n.setAttribute("width","1");n.setAttribute("height","1");n.setAttribute("fill","white");const o=DrawLayer._svgFactory.createElement("use");t.append(o);o.setAttribute("href",`#${a}`);o.setAttribute("stroke","none");o.setAttribute("fill","black");o.setAttribute("fill-rule","nonzero");o.classList.add("mask")}const o=DrawLayer._svgFactory.createElement("use");i.append(o);o.setAttribute("href",`#${a}`);r&&o.setAttribute("mask",`url(#${r})`);const l=o.cloneNode();i.append(l);o.classList.add("mainOutline");l.classList.add("secondaryOutline");this.#Gr.set(e,i);return e}finalizeLine(t,e){const i=this.#qr.get(t);this.#qr.delete(t);this.updateBox(t,e.box);i.setAttribute("d",e.toSVGPath())}updateLine(t,e){this.#Gr.get(t).firstChild.firstChild.setAttribute("d",e.toSVGPath())}removeFreeHighlight(t){this.remove(t);this.#qr.delete(t)}updatePath(t,e){this.#qr.get(t).setAttribute("d",e.toSVGPath())}updateBox(t,e){DrawLayer.#Xr(this.#Gr.get(t),e)}show(t,e){this.#Gr.get(t).classList.toggle("hidden",!e)}rotate(t,e){this.#Gr.get(t).setAttribute("data-main-rotation",e)}changeColor(t,e){this.#Gr.get(t).setAttribute("fill",e)}changeOpacity(t,e){this.#Gr.get(t).setAttribute("fill-opacity",e)}addClass(t,e){this.#Gr.get(t).classList.add(e)}removeClass(t,e){this.#Gr.get(t).classList.remove(e)}remove(t){if(null!==this.#xs){this.#Gr.get(t).remove();this.#Gr.delete(t)}}destroy(){this.#xs=null;for(const t of this.#Gr.values())t.remove();this.#Gr.clear()}}var te=__webpack_exports__.AbortException,ee=__webpack_exports__.AnnotationEditorLayer,ie=__webpack_exports__.AnnotationEditorParamsType,se=__webpack_exports__.AnnotationEditorType,ne=__webpack_exports__.AnnotationEditorUIManager,ae=__webpack_exports__.AnnotationLayer,re=__webpack_exports__.AnnotationMode,oe=__webpack_exports__.CMapCompressionType,le=__webpack_exports__.ColorPicker,he=__webpack_exports__.DOMSVGFactory,de=__webpack_exports__.DrawLayer,ce=__webpack_exports__.FeatureTest,ue=__webpack_exports__.GlobalWorkerOptions,pe=__webpack_exports__.ImageKind,ge=__webpack_exports__.InvalidPDFException,me=__webpack_exports__.MissingPDFException,fe=__webpack_exports__.OPS,be=__webpack_exports__.Outliner,ve=__webpack_exports__.PDFDataRangeTransport,Ae=__webpack_exports__.PDFDateString,ye=__webpack_exports__.PDFWorker,we=__webpack_exports__.PasswordResponses,xe=__webpack_exports__.PermissionFlag,_e=__webpack_exports__.PixelsPerInch,Ee=__webpack_exports__.RenderingCancelledException,Ce=__webpack_exports__.TextLayer,Se=__webpack_exports__.UnexpectedResponseException,Te=__webpack_exports__.Util,Me=__webpack_exports__.VerbosityLevel,ke=__webpack_exports__.XfaLayer,Pe=__webpack_exports__.build,De=__webpack_exports__.createValidAbsoluteUrl,Fe=__webpack_exports__.fetchData,Re=__webpack_exports__.getDocument,Ie=__webpack_exports__.getFilenameFromUrl,Le=__webpack_exports__.getPdfFilenameFromUrl,Oe=__webpack_exports__.getXfaPageViewport,Ne=__webpack_exports__.isDataScheme,Be=__webpack_exports__.isPdfFile,He=__webpack_exports__.noContextMenu,ze=__webpack_exports__.normalizeUnicode,Ue=__webpack_exports__.renderTextLayer,je=__webpack_exports__.setLayerDimensions,$e=__webpack_exports__.shadow,Ve=__webpack_exports__.updateTextLayer,We=__webpack_exports__.version;export{te as AbortException,ee as AnnotationEditorLayer,ie as AnnotationEditorParamsType,se as AnnotationEditorType,ne as AnnotationEditorUIManager,ae as AnnotationLayer,re as AnnotationMode,oe as CMapCompressionType,le as ColorPicker,he as DOMSVGFactory,de as DrawLayer,ce as FeatureTest,ue as GlobalWorkerOptions,pe as ImageKind,ge as InvalidPDFException,me as MissingPDFException,fe as OPS,be as Outliner,ve as PDFDataRangeTransport,Ae as PDFDateString,ye as PDFWorker,we as PasswordResponses,xe as PermissionFlag,_e as PixelsPerInch,Ee as RenderingCancelledException,Ce as TextLayer,Se as UnexpectedResponseException,Te as Util,Me as VerbosityLevel,ke as XfaLayer,Pe as build,De as createValidAbsoluteUrl,Fe as fetchData,Re as getDocument,Ie as getFilenameFromUrl,Le as getPdfFilenameFromUrl,Oe as getXfaPageViewport,Ne as isDataScheme,Be as isPdfFile,He as noContextMenu,ze as normalizeUnicode,Ue as renderTextLayer,je as setLayerDimensions,$e as shadow,Ve as updateTextLayer,We as version};