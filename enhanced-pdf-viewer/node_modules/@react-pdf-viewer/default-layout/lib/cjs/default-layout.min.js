"use strict";var e=require("@react-pdf-viewer/core"),t=require("react"),n=require("@react-pdf-viewer/attachment"),a=require("@react-pdf-viewer/bookmark"),r=require("@react-pdf-viewer/thumbnail"),o=require("@react-pdf-viewer/toolbar");function l(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var u=l(t),c=function(){return u.createElement(e.Icon,{size:16},u.createElement("path",{d:"M11.5,1.5h11c0.552,0,1,0.448,1,1v20c0,0.552-0.448,1-1,1h-21c-0.552,0-1-0.448-1-1v-20c0-0.552,0.448-1,1-1h3\n            M11.5,10.5c0,0.55-0.3,0.661-0.659,0.248L8,7.5l-2.844,3.246c-0.363,0.414-0.659,0.3-0.659-0.247v-9c0-0.552,0.448-1,1-1h5\n            c0.552,0,1,0.448,1,1L11.5,10.5z\n            M14.5,6.499h6\n            M14.5,10.499h6\n            M3.5,14.499h17\n            M3.5,18.499h16.497"}))},i=function(){return i=Object.assign||function(e){for(var t,n=1,a=arguments.length;n<a;n++)for(var r in t=arguments[n])Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r]);return e},i.apply(this,arguments)},s=function(){return u.createElement(e.Icon,{size:16},u.createElement("path",{d:"M7.618,15.345l8.666-8.666c0.78-0.812,2.071-0.838,2.883-0.058s0.838,2.071,0.058,2.883\n            c-0.019,0.02-0.038,0.039-0.058,0.058L7.461,21.305c-1.593,1.593-4.175,1.592-5.767,0s-1.592-4.175,0-5.767c0,0,0,0,0,0\n            L13.928,3.305c2.189-2.19,5.739-2.19,7.929-0.001s2.19,5.739,0,7.929l0,0L13.192,19.9"}))},d=function(){return u.createElement(e.Icon,{size:16},u.createElement("path",{d:"M10.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\n            M23.5,9.5c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V9.5z\n            M10.5,22.5 c0,0.552-0.448,1-1,1h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z\n            M23.5,22.5c0,0.552-0.448,1-1,1 h-8c-0.552,0-1-0.448-1-1v-8c0-0.552,0.448-1,1-1h8c0.552,0,1,0.448,1,1V22.5z"}))},b={left:8,top:0},f={left:-8,top:0},m=function(t){var n=t.attachmentTabContent,a=t.bookmarkTabContent,r=t.store,o=t.thumbnailTabContent,l=t.tabs,i=u.useRef(),m=u.useContext(e.LocalizationContext).l10n,h=u.useState(r.get("isCurrentTabOpened")||!1),p=h[0],v=h[1],y=u.useState(Math.max(r.get("currentTab")||0,0)),g=y[0],T=y[1],_=u.useContext(e.ThemeContext).direction===e.TextDirection.RightToLeft,E=[{content:o,icon:u.createElement(d,null),title:m&&m.defaultLayout?m.defaultLayout.thumbnail:"Thumbnail"},{content:a,icon:u.createElement(c,null),title:m&&m.defaultLayout?m.defaultLayout.bookmark:"Bookmark"},{content:n,icon:u.createElement(s,null),title:m&&m.defaultLayout?m.defaultLayout.attachment:"Attachment"}],L=l?l(E):E,k=function(e){e>=0&&e<=L.length-1&&(r.update("isCurrentTabOpened",!0),T(e))},C=function(e){v(e)};return u.useEffect((function(){return r.subscribe("currentTab",k),r.subscribe("isCurrentTabOpened",C),function(){r.unsubscribe("currentTab",k),r.unsubscribe("isCurrentTabOpened",C)}}),[]),0===L.length?u.createElement(u.Fragment,null):u.createElement(u.Fragment,null,u.createElement("div",{"data-testid":"default-layout__sidebar",className:e.classNames({"rpv-default-layout__sidebar":!0,"rpv-default-layout__sidebar--opened":p,"rpv-default-layout__sidebar--ltr":!_,"rpv-default-layout__sidebar--rtl":_}),ref:i},u.createElement("div",{className:"rpv-default-layout__sidebar-tabs"},u.createElement("div",{className:"rpv-default-layout__sidebar-headers",role:"tablist","aria-orientation":"vertical"},L.map((function(t,n){return u.createElement("div",{"aria-controls":"rpv-default-layout__sidebar-content","aria-selected":g===n,key:n,className:"rpv-default-layout__sidebar-header",id:"rpv-default-layout__sidebar-tab-".concat(n),role:"tab"},u.createElement(e.Tooltip,{ariaControlsSuffix:"default-layout-sidebar-tab-".concat(n),position:_?e.Position.LeftCenter:e.Position.RightCenter,target:u.createElement(e.MinimalButton,{ariaLabel:t.title,isSelected:g===n,onClick:function(){return function(e){if(g===e){r.update("isCurrentTabOpened",!r.get("isCurrentTabOpened"));var t=i.current;t&&t.style.width&&t.style.removeProperty("width")}else r.update("currentTab",e)}(n)}},t.icon),content:function(){return t.title},offset:_?f:b}))}))),u.createElement("div",{"aria-labelledby":"rpv-default-layout__sidebar-tab-".concat(g),id:"rpv-default-layout__sidebar-content",className:e.classNames({"rpv-default-layout__sidebar-content":!0,"rpv-default-layout__sidebar-content--opened":p,"rpv-default-layout__sidebar-content--ltr":!_,"rpv-default-layout__sidebar-content--rtl":_}),role:"tabpanel",tabIndex:-1},L[g].content))),p&&u.createElement(e.Splitter,{constrain:function(e){return e.firstHalfPercentage>=20&&e.firstHalfPercentage<=80}}))};exports.BookmarkIcon=c,exports.FileIcon=s,exports.ThumbnailIcon=d,exports.defaultLayoutPlugin=function(t){var l=u.useMemo((function(){return e.createStore({isCurrentTabOpened:!1,currentTab:0})}),[]),c=n.attachmentPlugin(),s=a.bookmarkPlugin(),d=r.thumbnailPlugin(t?t.thumbnailPlugin:{}),b=o.toolbarPlugin(t?t.toolbarPlugin:{}),f=c.Attachments,h=s.Bookmarks,p=d.Thumbnails,v=b.Toolbar,y=t?t.sidebarTabs:function(e){return e},g=[c,s,d,b];return{attachmentPluginInstance:c,bookmarkPluginInstance:s,thumbnailPluginInstance:d,toolbarPluginInstance:b,activateTab:function(e){l.update("currentTab",e)},toggleTab:function(e){var t=l.get("currentTab");l.update("isCurrentTabOpened",!l.get("isCurrentTabOpened")),t!==e&&l.update("currentTab",e)},install:function(e){g.forEach((function(t){t.install&&t.install(e)}))},renderPageLayer:function(e){return u.createElement(u.Fragment,null,g.map((function(t,n){return t.renderPageLayer?u.createElement(u.Fragment,{key:n},t.renderPageLayer(e)):u.createElement(u.Fragment,{key:n},u.createElement(u.Fragment,null))})))},renderViewer:function(n){var a=n.slot;g.forEach((function(e){e.renderViewer&&(a=e.renderViewer(i(i({},n),{slot:a})))}));var r=a.subSlot&&a.subSlot.attrs?{className:a.subSlot.attrs.className,"data-testid":a.subSlot.attrs["data-testid"],ref:a.subSlot.attrs.ref,style:a.subSlot.attrs.style}:{};return a.children=u.createElement("div",{className:"rpv-default-layout__container"},u.createElement("div",{"data-testid":"default-layout__main",className:e.classNames({"rpv-default-layout__main":!0,"rpv-default-layout__main--rtl":n.themeContext.direction===e.TextDirection.RightToLeft})},u.createElement(m,{attachmentTabContent:u.createElement(f,null),bookmarkTabContent:u.createElement(h,null),store:l,thumbnailTabContent:u.createElement(p,null),tabs:y}),u.createElement("div",{className:"rpv-default-layout__body","data-testid":"default-layout__body"},u.createElement("div",{className:"rpv-default-layout__toolbar"},t&&t.renderToolbar?t.renderToolbar(v):u.createElement(v,null)),u.createElement("div",i({},r),a.subSlot.children))),a.children),a.subSlot.attrs={},a.subSlot.children=u.createElement(u.Fragment,null),a},uninstall:function(e){g.forEach((function(t){t.uninstall&&t.uninstall(e)}))},onDocumentLoad:function(e){g.forEach((function(t){t.onDocumentLoad&&t.onDocumentLoad(e)})),t&&t.setInitialTab&&t.setInitialTab(e.doc).then((function(e){l.update("currentTab",e),l.update("isCurrentTabOpened",!0)}))},onAnnotationLayerRender:function(e){g.forEach((function(t){t.onAnnotationLayerRender&&t.onAnnotationLayerRender(e)}))},onTextLayerRender:function(e){g.forEach((function(t){t.onTextLayerRender&&t.onTextLayerRender(e)}))},onViewerStateChange:function(e){var t=e;return g.forEach((function(e){e.onViewerStateChange&&(t=e.onViewerStateChange(t))})),t}}},exports.setInitialTabFromPageMode=function(t){return new Promise((function(n,a){t.getPageMode().then((function(t){if(t)switch(t){case e.PageMode.Attachments:n(2);break;case e.PageMode.Bookmarks:n(1);break;case e.PageMode.Thumbnails:n(0);break;default:n(-1)}else n(-1)}))}))};
