"use strict";var e=require("react"),t=require("pdfjs-dist");function n(e){var t=Object.create(null);return e&&Object.keys(e).forEach((function(n){if("default"!==n){var r=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(t,n,r.get?r:{enumerable:!0,get:function(){return e[n]}})}})),t.default=e,Object.freeze(t)}var r,o=n(e),a=n(t);exports.AnnotationType=void 0,(r=exports.AnnotationType||(exports.AnnotationType={}))[r.Text=1]="Text",r[r.Link=2]="Link",r[r.FreeText=3]="FreeText",r[r.Line=4]="Line",r[r.Square=5]="Square",r[r.Circle=6]="Circle",r[r.Polygon=7]="Polygon",r[r.Polyline=8]="Polyline",r[r.Highlight=9]="Highlight",r[r.Underline=10]="Underline",r[r.Squiggly=11]="Squiggly",r[r.StrikeOut=12]="StrikeOut",r[r.Stamp=13]="Stamp",r[r.Caret=14]="Caret",r[r.Ink=15]="Ink",r[r.Popup=16]="Popup",r[r.FileAttachment=17]="FileAttachment";var i=function(e,t){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},i(e,t)};function c(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function n(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var s,l=function(){return l=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)};exports.TextDirection=void 0,(s=exports.TextDirection||(exports.TextDirection={})).RightToLeft="RTL",s.LeftToRight="LTR";var u,p=o.createContext({currentTheme:"light",direction:exports.TextDirection.LeftToRight,setCurrentTheme:function(){}}),d=function(e){var t=[];return Object.keys(e).forEach((function(n){n&&e[n]&&t.push(n)})),t.join(" ")},f="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,g=function(e){var t=o.useRef(null),n=e.once,r=e.threshold,a=e.onVisibilityChanged;return f((function(){var e=t.current;if(e){var o=new IntersectionObserver((function(t){t.forEach((function(t){var r=t.isIntersecting,i=t.intersectionRatio;a({isVisible:r,ratio:i}),r&&n&&(o.unobserve(e),o.disconnect())}))}),{threshold:r||0});return o.observe(e),function(){o.unobserve(e),o.disconnect()}}}),[]),t},v=function(e){var t=e.children,n=e.ignoreDirection,r=void 0!==n&&n,a=e.size,i=void 0===a?24:a,c=o.useContext(p).direction,s=!r&&c===exports.TextDirection.RightToLeft,l="".concat(i||24,"px");return o.createElement("svg",{"aria-hidden":"true",className:d({"rpv-core__icon":!0,"rpv-core__icon--rtl":s}),focusable:"false",height:l,viewBox:"0 0 24 24",width:l},t)},h=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M23.5,0.499l-16.5,23l-6.5-6.5"}))},m=function(e){var t=e.children,n=e.testId,r=e.onClick,a=o.useContext(p).direction===exports.TextDirection.RightToLeft,i=n?{"data-testid":n}:{};return o.createElement("button",l({className:d({"rpv-core__primary-button":!0,"rpv-core__primary-button--rtl":a}),type:"button",onClick:r},i),t)},x=function(e){var t=e.size,n=void 0===t?"4rem":t,r=e.testId,a=o.useState(!1),i=a[0],c=a[1],s=r?{"data-testid":r}:{},u=g({onVisibilityChanged:function(e){c(e.isVisible)}});return o.createElement("div",l({},s,{className:d({"rpv-core__spinner":!0,"rpv-core__spinner--animating":i}),ref:u,style:{height:n,width:n}}))},w=function(e){var t=e.ariaLabel,n=void 0===t?"":t,r=e.autoFocus,a=void 0!==r&&r,i=e.placeholder,c=void 0===i?"":i,s=e.testId,u=e.type,g=void 0===u?"text":u,v=e.value,h=void 0===v?"":v,m=e.onChange,x=e.onKeyDown,w=void 0===x?function(){}:x,E=o.useContext(p).direction,b=o.useRef(),y=E===exports.TextDirection.RightToLeft,S={ref:b,"data-testid":"","aria-label":n,className:d({"rpv-core__textbox":!0,"rpv-core__textbox--rtl":y}),placeholder:c,value:h,onChange:function(e){return m(e.target.value)},onKeyDown:w};return s&&(S["data-testid"]=s),f((function(){if(a){var e=b.current;if(e){var t=window.scrollX,n=window.scrollY;e.focus(),window.scrollTo(t,n)}}}),[]),"text"===g?o.createElement("input",l({type:"text"},S)):o.createElement("input",l({type:"password"},S))};!function(e){e[e.ExitFullScreen=0]="ExitFullScreen",e[e.FullScreenChange=1]="FullScreenChange",e[e.FullScreenElement=2]="FullScreenElement",e[e.FullScreenEnabled=3]="FullScreenEnabled",e[e.RequestFullScreen=4]="RequestFullScreen"}(u||(u={}));var E,b={ExitFullScreen:"exitFullscreen",FullScreenChange:"fullscreenchange",FullScreenElement:"fullscreenElement",FullScreenEnabled:"fullscreenEnabled",RequestFullScreen:"requestFullscreen"},y={ExitFullScreen:"webkitExitFullscreen",FullScreenChange:"webkitfullscreenchange",FullScreenElement:"webkitFullscreenElement",FullScreenEnabled:"webkitFullscreenEnabled",RequestFullScreen:"webkitRequestFullscreen"},S={ExitFullScreen:"msExitFullscreen",FullScreenChange:"msFullscreenChange",FullScreenElement:"msFullscreenElement",FullScreenEnabled:"msFullscreenEnabled",RequestFullScreen:"msRequestFullscreen"},_="undefined"!=typeof window,P=_&&(u.FullScreenEnabled in document&&b||y.FullScreenEnabled in document&&y||S.FullScreenEnabled in document&&S)||b,R=function(){return _&&P.FullScreenEnabled in document&&!0===document[P.FullScreenEnabled]},M=function(e){return _?e[P.ExitFullScreen]():Promise.resolve({})},k=function(){return _?document[P.FullScreenElement]:null},C=function(e,t){var n=o.useRef(),r=function(){n.current&&clearTimeout(n.current)};return o.useEffect((function(){return function(){return r()}}),[]),o.useCallback((function(){for(var o=[],a=0;a<arguments.length;a++)o[a]=arguments[a];r(),n.current=setTimeout((function(){e.apply(void 0,o)}),t)}),[e,t])},T=function(){var e=o.useRef(!1);return o.useEffect((function(){return e.current=!0,function(){e.current=!1}}),[]),e},L=function(e){var t=o.useRef(e);return o.useEffect((function(){t.current=e}),[e]),t.current};!function(e){e.NotRenderedYet="NotRenderedYet",e.Rendering="Rendering",e.Rendered="Rendered"}(E||(E={}));var O,F=-9999,D=function(e){var t=e.doc,n=t.numPages,r=t.loadingTask.docId,a=o.useMemo((function(){return Array(n).fill(null).map((function(e,t){return{pageIndex:t,renderStatus:E.NotRenderedYet,visibility:F}}))}),[r]),i=o.useRef({currentRenderingPage:-1,startRange:0,endRange:n-1,visibilities:a}),c=function(e,t){i.current.visibilities[e].visibility=t};return{getHighestPriorityPage:function(){var e=i.current.visibilities.slice(i.current.startRange,i.current.endRange+1).filter((function(e){return e.visibility>F}));if(!e.length)return-1;for(var t=e[0].pageIndex,r=e[e.length-1].pageIndex,o=e.length,a=0;a<o;a++){if(e[a].renderStatus===E.Rendering)return-1;if(e[a].renderStatus===E.NotRenderedYet)return e[a].pageIndex}return r+1<n&&i.current.visibilities[r+1].renderStatus!==E.Rendered?r+1:t-1>=0&&i.current.visibilities[t-1].renderStatus!==E.Rendered?t-1:-1},isInRange:function(e){return e>=i.current.startRange&&e<=i.current.endRange},markNotRendered:function(){for(var e=0;e<n;e++)i.current.visibilities[e].renderStatus=E.NotRenderedYet},markRendered:function(e){i.current.visibilities[e].renderStatus=E.Rendered},markRendering:function(e){-1!==i.current.currentRenderingPage&&i.current.currentRenderingPage!==e&&i.current.visibilities[i.current.currentRenderingPage].renderStatus===E.Rendering&&(i.current.visibilities[i.current.currentRenderingPage].renderStatus=E.NotRenderedYet),i.current.visibilities[e].renderStatus=E.Rendering,i.current.currentRenderingPage=e},setOutOfRange:function(e){c(e,F)},setRange:function(e,t){i.current.startRange=e,i.current.endRange=t;for(var r=0;r<n;r++)(r<e||r>t)&&(i.current.visibilities[r].visibility=F,i.current.visibilities[r].renderStatus=E.NotRenderedYet)},setVisibility:c}},I={core:{askingPassword:{requirePasswordToOpen:"This document requires a password to open",submit:"Submit"},wrongPassword:{tryAgain:"The password is wrong. Please try again"},pageLabel:"Page {{pageIndex}}"}},A=o.createContext({l10n:I,setL10n:function(){}}),H=0,N=function(){return H++},V=function(e,t,n){var r=function(e){var r=t.current;if(r){var o=e.target;if(o instanceof Element&&o.shadowRoot){var a=e.composedPath();a.length>0&&!r.contains(a[0])&&n()}else r.contains(o)||n()}};o.useEffect((function(){if(e){var t={capture:!0};return document.addEventListener("click",r,t),function(){document.removeEventListener("click",r,t)}}}),[])},z=function(e){var t=function(t){"Escape"===t.key&&e()};o.useEffect((function(){return document.addEventListener("keyup",t),function(){document.removeEventListener("keyup",t)}}),[])},B=function(e){var t=e.ariaControlsSuffix,n=e.children,r=e.closeOnClickOutside,a=e.closeOnEscape,i=e.onToggle,c=o.useRef(),s=o.useContext(p).direction===exports.TextDirection.RightToLeft;return o.useEffect((function(){var e=window.getComputedStyle(document.body).overflow;return document.body.style.overflow="hidden",function(){document.body.style.overflow=e}}),[]),z((function(){c.current&&a&&i()})),V(r,c,i),f((function(){var e=c.current;if(e){var t=.75*document.body.clientHeight;e.getBoundingClientRect().height>=t&&(e.style.overflow="auto",e.style.maxHeight="".concat(t,"px"))}}),[]),o.createElement("div",{"aria-modal":"true",className:d({"rpv-core__modal-body":!0,"rpv-core__modal-body--rtl":s}),id:"rpv-core__modal-body-".concat(t),ref:c,role:"dialog",tabIndex:-1},n)},W=function(e){var t=e.children;return o.createElement("div",{className:"rpv-core__modal-overlay"},t)};exports.ToggleStatus=void 0,(O=exports.ToggleStatus||(exports.ToggleStatus={})).Close="Close",O.Open="Open",O.Toggle="Toggle";var j,q=function(e){var t=o.useState(e),n=t[0],r=t[1];return{opened:n,toggle:function(e){switch(e){case exports.ToggleStatus.Close:r(!1);break;case exports.ToggleStatus.Open:r(!0);break;case exports.ToggleStatus.Toggle:default:r((function(e){return!e}))}}}},U=function(e){var t=e.content,n=e.isOpened,r=void 0!==n&&n,a=e.target,i=q(r),c=i.opened,s=i.toggle;return o.createElement(o.Fragment,null,a&&a(s,c),c&&t(s))};exports.Position=void 0,(j=exports.Position||(exports.Position={})).TopLeft="TOP_LEFT",j.TopCenter="TOP_CENTER",j.TopRight="TOP_RIGHT",j.RightTop="RIGHT_TOP",j.RightCenter="RIGHT_CENTER",j.RightBottom="RIGHT_BOTTOM",j.BottomLeft="BOTTOM_LEFT",j.BottomCenter="BOTTOM_CENTER",j.BottomRight="BOTTOM_RIGHT",j.LeftTop="LEFT_TOP",j.LeftCenter="LEFT_CENTER",j.LeftBottom="LEFT_BOTTOM";var Z,J,G,Y,K,X,Q,$,ee=function(e,t,n,r,o){f((function(){var a=t.current,i=e.current,c=n.current;if(i&&a&&c){var s=c.getBoundingClientRect(),l=function(e,t,n,r){var o=t.getBoundingClientRect(),a=e.getBoundingClientRect(),i=a.height,c=a.width,s=0,l=0;switch(n){case exports.Position.TopLeft:s=o.top-i,l=o.left;break;case exports.Position.TopCenter:s=o.top-i,l=o.left+o.width/2-c/2;break;case exports.Position.TopRight:s=o.top-i,l=o.left+o.width-c;break;case exports.Position.RightTop:s=o.top,l=o.left+o.width;break;case exports.Position.RightCenter:s=o.top+o.height/2-i/2,l=o.left+o.width;break;case exports.Position.RightBottom:s=o.top+o.height-i,l=o.left+o.width;break;case exports.Position.BottomLeft:s=o.top+o.height,l=o.left;break;case exports.Position.BottomCenter:s=o.top+o.height,l=o.left+o.width/2-c/2;break;case exports.Position.BottomRight:s=o.top+o.height,l=o.left+o.width-c;break;case exports.Position.LeftTop:s=o.top,l=o.left-c;break;case exports.Position.LeftCenter:s=o.top+o.height/2-i/2,l=o.left-c;break;case exports.Position.LeftBottom:s=o.top+o.height-i,l=o.left-c}return{left:l+(r.left||0),top:s+(r.top||0)}}(i,a,r,o),u=l.top,p=l.left;i.style.top="".concat(u-s.top,"px"),i.style.left="".concat(p-s.left,"px")}}),[])},te=function(e){var t,n=e.customClassName,r=e.position;return o.createElement("div",{className:d((t={"rpv-core__arrow":!0,"rpv-core__arrow--tl":r===exports.Position.TopLeft,"rpv-core__arrow--tc":r===exports.Position.TopCenter,"rpv-core__arrow--tr":r===exports.Position.TopRight,"rpv-core__arrow--rt":r===exports.Position.RightTop,"rpv-core__arrow--rc":r===exports.Position.RightCenter,"rpv-core__arrow--rb":r===exports.Position.RightBottom,"rpv-core__arrow--bl":r===exports.Position.BottomLeft,"rpv-core__arrow--bc":r===exports.Position.BottomCenter,"rpv-core__arrow--br":r===exports.Position.BottomRight,"rpv-core__arrow--lt":r===exports.Position.LeftTop,"rpv-core__arrow--lc":r===exports.Position.LeftCenter,"rpv-core__arrow--lb":r===exports.Position.LeftBottom},t["".concat(n)]=""!==n,t))})},ne=function(e){var t=e.ariaControlsSuffix,n=e.children,r=e.closeOnClickOutside,a=e.offset,i=e.position,c=e.targetRef,s=e.onClose,l=o.useRef(),u=o.useRef(),g=o.useRef(),v=o.useContext(p).direction===exports.TextDirection.RightToLeft;V(r,l,s),ee(l,c,g,i,a),f((function(){var e=u.current;if(e){var t=.75*document.body.clientHeight;e.getBoundingClientRect().height>=t&&(e.style.overflow="auto",e.style.maxHeight="".concat(t,"px"))}}),[]);var h="rpv-core__popover-body-inner-".concat(t);return o.createElement(o.Fragment,null,o.createElement("div",{ref:g,style:{left:0,position:"absolute",top:0}}),o.createElement("div",{"aria-describedby":h,className:d({"rpv-core__popover-body":!0,"rpv-core__popover-body--rtl":v}),id:"rpv-core__popover-body-".concat(t),ref:l,role:"dialog",tabIndex:-1},o.createElement(te,{customClassName:"rpv-core__popover-body-arrow",position:i}),o.createElement("div",{id:h,ref:u},n)))},re=function(e){var t=e.closeOnEscape,n=e.onClose,r=o.useRef();return z((function(){r.current&&t&&n()})),o.createElement("div",{className:"rpv-core__popover-overlay",ref:r})},oe=function(e){var t=e.ariaControlsSuffix,n=e.children,r=e.contentRef,a=e.offset,i=e.position,c=e.targetRef,s=o.useRef(),l=o.useContext(p).direction===exports.TextDirection.RightToLeft;return ee(r,c,s,i,a),o.createElement(o.Fragment,null,o.createElement("div",{ref:s,style:{left:0,position:"absolute",top:0}}),o.createElement("div",{className:d({"rpv-core__tooltip-body":!0,"rpv-core__tooltip-body--rtl":l}),id:"rpv-core__tooltip-body-".concat(t),ref:r,role:"tooltip"},o.createElement(te,{customClassName:"rpv-core__tooltip-body-arrow",position:i}),o.createElement("div",{className:"rpv-core__tooltip-body-content"},n)))};exports.FullScreenMode=void 0,(Z=exports.FullScreenMode||(exports.FullScreenMode={})).Normal="Normal",Z.Entering="Entering",Z.Entered="Entered",Z.EnteredCompletely="EnteredCompletely",Z.Exitting="Exitting",Z.Exited="Exited",exports.LayerRenderStatus=void 0,(J=exports.LayerRenderStatus||(exports.LayerRenderStatus={}))[J.PreRender=0]="PreRender",J[J.DidRender=1]="DidRender",exports.PageMode=void 0,(G=exports.PageMode||(exports.PageMode={})).Attachments="UseAttachments",G.Bookmarks="UseOutlines",G.ContentGroup="UseOC",G.Default="UserNone",G.FullScreen="FullScreen",G.Thumbnails="UseThumbs",exports.PasswordStatus=void 0,(Y=exports.PasswordStatus||(exports.PasswordStatus={})).RequiredPassword="RequiredPassword",Y.WrongPassword="WrongPassword",exports.RotateDirection=void 0,(K=exports.RotateDirection||(exports.RotateDirection={})).Backward="Backward",K.Forward="Forward",exports.ScrollMode=void 0,(X=exports.ScrollMode||(exports.ScrollMode={})).Page="Page",X.Horizontal="Horizontal",X.Vertical="Vertical",X.Wrapped="Wrapped",exports.SpecialZoomLevel=void 0,(Q=exports.SpecialZoomLevel||(exports.SpecialZoomLevel={})).ActualSize="ActualSize",Q.PageFit="PageFit",Q.PageWidth="PageWidth",exports.ViewMode=void 0,($=exports.ViewMode||(exports.ViewMode={})).DualPage="DualPage",$.DualPageWithCover="DualPageWithCover",$.SinglePage="SinglePage";var ae,ie=function(e,t){return e.reduce((function(e,n,r){return r%t?e[e.length-1].push(n):e.push([n]),e}),[])},ce=function(e,t){switch(t[1].name){case"XYZ":return{bottomOffset:function(e,n){return null===t[3]?n:t[3]},leftOffset:function(e,n){return null===t[2]?0:t[2]},pageIndex:e,scaleTo:t[4]};case"Fit":case"FitB":return{bottomOffset:0,leftOffset:0,pageIndex:e,scaleTo:exports.SpecialZoomLevel.PageFit};case"FitH":case"FitBH":return{bottomOffset:t[2],leftOffset:0,pageIndex:e,scaleTo:exports.SpecialZoomLevel.PageWidth};default:return{bottomOffset:0,leftOffset:0,pageIndex:e,scaleTo:1}}},se=new Map,le=new Map,ue=function(e,t){return"".concat(e.loadingTask.docId,"___").concat(t.num,"R").concat(0===t.gen?"":t.gen)},pe=function(e,t,n){se.set(ue(e,t),n)},de=function(e,t){if(!e)return Promise.reject("The document is not loaded yet");var n="".concat(e.loadingTask.docId,"___").concat(t),r=le.get(n);return r?Promise.resolve(r):new Promise((function(r,o){e.getPage(t+1).then((function(o){le.set(n,o),o.ref&&pe(e,o.ref,t),r(o)}))}))},fe=function(e,t){return new Promise((function(n){new Promise((function(n){"string"==typeof t?e.getDestination(t).then((function(e){n(e)})):n(t)})).then((function(r){if("object"==typeof r[0]&&null!==r[0]){var o=r[0],a=function(e,t){var n=ue(e,t);return se.has(n)?se.get(n):null}(e,o);null===a?e.getPageIndex(o).then((function(r){pe(e,o,r),fe(e,t).then((function(e){return n(e)}))})):n(ce(a,r))}else{var i=ce(r[0],r);n(i)}}))}))};!function(e){e[e.Solid=1]="Solid",e[e.Dashed=2]="Dashed",e[e.Beveled=3]="Beveled",e[e.Inset=4]="Inset",e[e.Underline=5]="Underline"}(ae||(ae={}));var ge,ve=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"),he=function(e,t,n,r){var o=parseInt(e,10);return o>=t&&o<=n?o:r},me=function(e){return e.contentsObj?e.contentsObj.str:e.contents||""},xe=function(e){return e.titleObj?e.titleObj.str:e.title||""},we=function(e){var t=e.annotation,n=o.useContext(p).direction,r=xe(t),a=me(t),i=n===exports.TextDirection.RightToLeft,c=o.useRef(),s="";if(t.modificationDate){var l=function(e){var t=ve.exec(e);if(!t)return null;var n=parseInt(t[1],10),r=he(t[2],1,12,1)-1,o=he(t[3],1,31,1),a=he(t[4],0,23,0),i=he(t[5],0,59,0),c=he(t[6],0,59,0),s=t[7]||"Z",l=he(t[8],0,23,0),u=he(t[9],0,59,0);switch(s){case"-":a+=l,i+=u;break;case"+":a-=l,i-=u}return new Date(Date.UTC(n,r,o,a,i,c))}(t.modificationDate);s=l?"".concat(l.toLocaleDateString(),", ").concat(l.toLocaleTimeString()):""}return o.useLayoutEffect((function(){if(c.current){var e=document.querySelector('[data-annotation-id="'.concat(t.id,'"]'));if(e){var n=e;return n.style.zIndex+=1,function(){n.style.zIndex="".concat(parseInt(n.style.zIndex,10)-1)}}}}),[]),o.createElement("div",{ref:c,className:d({"rpv-core__annotation-popup-wrapper":!0,"rpv-core__annotation-popup-wrapper--rtl":i}),style:{top:t.annotationType===exports.AnnotationType.Popup?"":"100%"}},r&&o.createElement(o.Fragment,null,o.createElement("div",{className:d({"rpv-core__annotation-popup-title":!0,"rpv-core__annotation-popup-title--ltr":!i,"rpv-core__annotation-popup-title--rtl":i})},r),o.createElement("div",{className:"rpv-core__annotation-popup-date"},s)),a&&o.createElement("div",{className:"rpv-core__annotation-popup-content"},a.split("\n").map((function(e,t){return o.createElement(o.Fragment,{key:t},e,o.createElement("br",null))}))))};!function(e){e.Click="Click",e.Hover="Hover"}(ge||(ge={}));var Ee,be=function(e){var t,n=e.annotation,r=e.children,a=e.ignoreBorder,i=e.hasPopup,c=e.isRenderable,s=e.page,l=e.viewport,u=n.rect,p=function(){var e=q(!1),t=e.opened,n=e.toggle,r=o.useState(ge.Hover),a=r[0],i=r[1];return{opened:t,closeOnHover:function(){a===ge.Hover&&n(exports.ToggleStatus.Close)},openOnHover:function(){a===ge.Hover&&n(exports.ToggleStatus.Open)},toggleOnClick:function(){switch(a){case ge.Click:t&&i(ge.Hover),n(exports.ToggleStatus.Toggle);break;case ge.Hover:i(ge.Click),n(exports.ToggleStatus.Open)}}}}(),d=p.closeOnHover,f=p.opened,g=p.openOnHover,v=p.toggleOnClick,h=(t=[u[0],s.view[3]+s.view[1]-u[1],u[2],s.view[3]+s.view[1]-u[3]],[Math.min(t[0],t[2]),Math.min(t[1],t[3]),Math.max(t[0],t[2]),Math.max(t[1],t[3])]),m=u[2]-u[0],x=u[3]-u[1],w={borderColor:"",borderRadius:"",borderStyle:"",borderWidth:""};if(!a&&n.borderStyle.width>0){switch(n.borderStyle.style){case ae.Dashed:w.borderStyle="dashed";break;case ae.Solid:w.borderStyle="solid";break;case ae.Underline:w=Object.assign({borderBottomStyle:"solid"},w);case ae.Beveled:case ae.Inset:}var E=n.borderStyle.width;w.borderWidth="".concat(E,"px"),n.borderStyle.style!==ae.Underline&&(m-=2*E,x-=2*E);var b=n.borderStyle,y=b.horizontalCornerRadius,S=b.verticalCornerRadius;(y>0||S>0)&&(w.borderRadius="".concat(y,"px / ").concat(S,"px")),n.color?w.borderColor="rgb(".concat(0|n.color[0],", ").concat(0|n.color[1],", ").concat(0|n.color[2],")"):w.borderWidth="0"}return o.createElement(o.Fragment,null,c&&r({popup:{opened:f,closeOnHover:d,openOnHover:g,toggleOnClick:v},slot:{attrs:{style:Object.assign({height:"".concat(x,"px"),left:"".concat(h[0],"px"),top:"".concat(h[1],"px"),transform:"matrix(".concat(l.transform.join(","),")"),transformOrigin:"-".concat(h[0],"px -").concat(h[1],"px"),width:"".concat(m,"px")},w)},children:o.createElement(o.Fragment,null,i&&f&&o.createElement(we,{annotation:n}))}}))},ye=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--caret","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},Se=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--circle","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},o.createElement("circle",{cy:d/2,fill:"none",rx:p/2-f/2,ry:d/2-f/2,stroke:"transparent",strokeWidth:f||1})),e.slot.children)}))},_e=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=xe(t),i=me(t),c=!(!1!==t.hasPopup||!a&&!i),s=function(){var e,n,r,o,a=t.file;a&&(e=a.filename,n=a.content,r="string"==typeof n?"":URL.createObjectURL(new Blob([n],{type:""})),(o=document.createElement("a")).style.display="none",o.href=r||e,o.setAttribute("download",function(e){var t=e.split("/").pop();return t?t.split("#")[0].split("?")[0]:e}(e)),document.body.appendChild(o),o.click(),document.body.removeChild(o),r&&URL.revokeObjectURL(r))};return o.createElement(be,{annotation:t,hasPopup:c,ignoreBorder:!0,isRenderable:!0,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--file-attachment","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onDoubleClick:s,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},Pe=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--free-text","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},Re=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=xe(t),i=me(t),c=!(!a&&!i),s=!t.parentType||-1!==["Circle","Ink","Line","Polygon","PolyLine","Square"].indexOf(t.parentType);return f((function(){if(t.parentId){var e=document.querySelector('[data-annotation-id="'.concat(t.parentId,'"]')),n=document.querySelector('[data-annotation-id="'.concat(t.id,'"]'));if(e&&n){var r=parseFloat(e.style.left),o=parseFloat(e.style.top)+parseFloat(e.style.height);n.style.left="".concat(r,"px"),n.style.top="".concat(o,"px"),n.style.transformOrigin="-".concat(r,"px -").concat(o,"px")}}}),[]),o.createElement(be,{annotation:t,hasPopup:s,ignoreBorder:!1,isRenderable:c,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--popup","data-annotation-id":t.id}),o.createElement(we,{annotation:t}))}))},Me=function(e){var t=e.annotation,n=e.childAnnotation,r=e.page,a=e.viewport,i=!1===t.hasPopup,c=xe(t),s=me(t),u=!!(t.hasPopup||c||s);if(t.quadPoints&&t.quadPoints.length>0){var p=t.quadPoints.map((function(e){return Object.assign({},t,{rect:[e[2].x,e[2].y,e[1].x,e[1].y],quadPoints:[]})}));return o.createElement(o.Fragment,null,p.map((function(e,t){return o.createElement(Me,{key:t,annotation:e,childAnnotation:n,page:r,viewport:a})})))}return o.createElement(be,{annotation:t,hasPopup:i,ignoreBorder:!0,isRenderable:u,page:r,viewport:a},(function(e){return o.createElement(o.Fragment,null,o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--highlight","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children),n&&n.annotationType===exports.AnnotationType.Popup&&e.popup.opened&&o.createElement(Re,{annotation:n,page:r,viewport:a}))}))},ke=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--ink","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),t.inkLists&&t.inkLists.length&&o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},t.inkLists.map((function(e,t){return o.createElement("polyline",{key:t,fill:"none",stroke:"transparent",strokeWidth:f||1,points:e.map((function(e){return"".concat(e.x-u[0],",").concat(u[3]-e.y)})).join(" ")})}))),e.slot.children)}))},Ce=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--line","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},o.createElement("line",{stroke:"transparent",strokeWidth:f||1,x1:u[2]-t.lineCoordinates[0],x2:u[2]-t.lineCoordinates[2],y1:u[3]-t.lineCoordinates[1],y2:u[3]-t.lineCoordinates[3]})),e.slot.children)}))},Te=/^([^\w]*)(javascript|data|vbscript)/im,Le=/&#(\w+)(^\w|;)?/g,Oe=/[\u0000-\u001F\u007F-\u009F\u2000-\u200D\uFEFF]/gim,Fe=/^([^:]+):/gm,De=function(e,t){void 0===t&&(t="about:blank");var n,r=(n=e||"",n.replace(Le,(function(e,t){return String.fromCharCode(t)}))).replace(Oe,"").trim();if(!r)return t;var o=r[0];if("."===o||"/"===o)return r;var a=r.match(Fe);if(!a)return r;var i=a[0];return Te.test(i)?t:r},Ie=function(e){var t,n=e.annotation,r=e.annotationContainerRef,a=e.doc,i=e.outlines,c=e.page,s=e.pageIndex,u=e.scale,p=e.viewport,d=e.onExecuteNamedAction,f=e.onJumpFromLinkAnnotation,g=e.onJumpToDest,v=o.useRef(),h=i&&i.length&&n.dest&&"string"==typeof n.dest?null===(t=i.find((function(e){return e.dest===n.dest})))||void 0===t?void 0:t.title:"",m=!!(n.url||n.dest||n.action||n.unsafeUrl),x={};if(n.url||n.unsafeUrl){var w=De(n.url||n.unsafeUrl,"");w?x={"data-target":"external",href:w,rel:"noopener noreferrer nofollow",target:n.newWindow?"_blank":"",title:w}:m=!1}else x={href:"","data-annotation-link":n.id,onClick:function(e){e.preventDefault(),n.action?d(n.action):fe(a,n.dest).then((function(e){var t=v.current,n=r.current;if(t&&n){var o=t.getBoundingClientRect();n.style.setProperty("height","100%"),n.style.setProperty("width","100%");var a=n.getBoundingClientRect();n.style.removeProperty("height"),n.style.removeProperty("width");var i=(o.left-a.left)/u,c=(a.bottom-o.bottom+o.height)/u;f({bottomOffset:c,label:h,leftOffset:i,pageIndex:s})}g(e)}))}};return h&&(x=Object.assign({},x,{title:h,"aria-label":h})),o.createElement(be,{annotation:n,hasPopup:!1,ignoreBorder:!1,isRenderable:m,page:c,viewport:p},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--link","data-annotation-id":n.id,"data-testid":"core__annotation--link-".concat(n.id)}),o.createElement("a",l({ref:v},x)))}))},Ae=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--polygon","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),t.vertices&&t.vertices.length&&o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},o.createElement("polygon",{fill:"none",stroke:"transparent",strokeWidth:f||1,points:t.vertices.map((function(e){return"".concat(e.x-u[0],",").concat(u[3]-e.y)})).join(" ")})),e.slot.children)}))},He=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--polyline","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),t.vertices&&t.vertices.length&&o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},o.createElement("polyline",{fill:"none",stroke:"transparent",strokeWidth:f||1,points:t.vertices.map((function(e){return"".concat(e.x-u[0],",").concat(u[3]-e.y)})).join(" ")})),e.slot.children)}))},Ne=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c),u=t.rect,p=u[2]-u[0],d=u[3]-u[1],f=t.borderStyle.width;return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--square","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),o.createElement("svg",{height:"".concat(d,"px"),preserveAspectRatio:"none",version:"1.1",viewBox:"0 0 ".concat(p," ").concat(d),width:"".concat(p,"px")},o.createElement("rect",{height:d-f,fill:"none",stroke:"transparent",strokeWidth:f||1,x:f/2,y:f/2,width:p-f})),e.slot.children)}))},Ve=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--squiggly","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},ze=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--stamp","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},Be=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--strike-out","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},We=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M.5,16.5a1,1,0,0,0,1,1h2v4l4-4h15a1,1,0,0,0,1-1V3.5a1,1,0,0,0-1-1H1.5a1,1,0,0,0-1,1Z"}),o.createElement("path",{d:"M7.25,9.75A.25.25,0,1,1,7,10a.25.25,0,0,1,.25-.25"}),o.createElement("path",{d:"M12,9.75a.25.25,0,1,1-.25.25A.25.25,0,0,1,12,9.75"}),o.createElement("path",{d:"M16.75,9.75a.25.25,0,1,1-.25.25.25.25,0,0,1,.25-.25"}))},je=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M0.500 12.001 A11.500 11.500 0 1 0 23.500 12.001 A11.500 11.500 0 1 0 0.500 12.001 Z"}),o.createElement("path",{d:"M6.000 12.001 A6.000 6.000 0 1 0 18.000 12.001 A6.000 6.000 0 1 0 6.000 12.001 Z"}),o.createElement("path",{d:"M21.423 5.406L17.415 9.414"}),o.createElement("path",{d:"M14.587 6.585L18.607 2.565"}),o.createElement("path",{d:"M5.405 21.424L9.413 17.416"}),o.createElement("path",{d:"M6.585 14.588L2.577 18.596"}),o.createElement("path",{d:"M18.602 21.419L14.595 17.412"}),o.createElement("path",{d:"M17.419 14.58L21.428 18.589"}),o.createElement("path",{d:"M2.582 5.399L6.588 9.406"}),o.createElement("path",{d:"M9.421 6.581L5.412 2.572"}))},qe=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M4.000 18.500 A1.500 1.500 0 1 0 7.000 18.500 A1.500 1.500 0 1 0 4.000 18.500 Z"}),o.createElement("path",{d:"M20.5.5l-9.782,9.783a7,7,0,1,0,3,3L17,10h1.5V8.5L19,8h1.5V6.5L21,6h1.5V4.5l1-1V.5Z"}))},Ue=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M2.000 2.500 L22.000 2.500 L22.000 23.500 L2.000 23.500 Z"}),o.createElement("path",{d:"M6 4.5L6 0.5"}),o.createElement("path",{d:"M18 4.5L18 0.5"}),o.createElement("path",{d:"M10 4.5L10 0.5"}),o.createElement("path",{d:"M14 4.5L14 0.5"}))},Ze=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M17.5 0.498L17.5 23.498"}),o.createElement("path",{d:"M10.5 0.498L10.5 23.498"}),o.createElement("path",{d:"M23.5.5H6.5a6,6,0,0,0,0,12h4"}))},Je=function(){return o.createElement(v,{size:16},o.createElement("path",{d:"M2.5 22.995L12 6.005 21.5 22.995 2.5 22.995z"}))},Ge=function(e){var t=e.annotation,n=e.childAnnotation,r=e.page,a=e.viewport,i=!1===t.hasPopup,c=xe(t),s=me(t),u=!!(t.hasPopup||c||s),p=t.name?t.name.toLowerCase():"";return o.createElement(be,{annotation:t,hasPopup:i,ignoreBorder:!1,isRenderable:u,page:r,viewport:a},(function(e){return o.createElement(o.Fragment,null,o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--text","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),p&&o.createElement("div",{className:"rpv-core__annotation-text-icon"},"check"===p&&o.createElement(h,null),"comment"===p&&o.createElement(We,null),"help"===p&&o.createElement(je,null),"insert"===p&&o.createElement(Je,null),"key"===p&&o.createElement(qe,null),"note"===p&&o.createElement(Ue,null),("newparagraph"===p||"paragraph"===p)&&o.createElement(Ze,null)),e.slot.children),n&&n.annotationType===exports.AnnotationType.Popup&&e.popup.opened&&o.createElement(Re,{annotation:n,page:r,viewport:a}))}))},Ye=function(e){var t=e.annotation,n=e.page,r=e.viewport,a=!1===t.hasPopup,i=xe(t),c=me(t),s=!!(t.hasPopup||i||c);return o.createElement(be,{annotation:t,hasPopup:a,ignoreBorder:!0,isRenderable:s,page:n,viewport:r},(function(e){return o.createElement("div",l({},e.slot.attrs,{className:"rpv-core__annotation rpv-core__annotation--underline","data-annotation-id":t.id,onClick:e.popup.toggleOnClick,onMouseEnter:e.popup.openOnHover,onMouseLeave:e.popup.closeOnHover}),e.slot.children)}))},Ke=function(e){var t=e.annotations,n=e.doc,r=e.outlines,a=e.page,i=e.pageIndex,c=e.plugins,s=e.rotation,l=e.scale,u=e.onExecuteNamedAction,p=e.onJumpFromLinkAnnotation,d=e.onJumpToDest,g=o.useRef(),v=a.getViewport({rotation:s,scale:l}).clone({dontFlip:!0}),h=t.filter((function(e){return!e.parentId}));return f((function(){var e=g.current;e&&c.forEach((function(t){t.onAnnotationLayerRender&&t.onAnnotationLayerRender({annotations:h,container:e,pageIndex:i,rotation:s,scale:l})}))}),[]),o.createElement("div",{ref:g,className:"rpv-core__annotation-layer","data-testid":"core__annotation-layer-".concat(i)},h.map((function(e){var c=t.find((function(t){return t.parentId===e.id}));switch(e.annotationType){case exports.AnnotationType.Caret:return o.createElement(ye,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Circle:return o.createElement(Se,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.FileAttachment:return o.createElement(_e,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.FreeText:return o.createElement(Pe,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Highlight:return o.createElement(Me,{key:e.id,annotation:e,childAnnotation:c,page:a,viewport:v});case exports.AnnotationType.Ink:return o.createElement(ke,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Line:return o.createElement(Ce,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Link:return o.createElement(Ie,{key:e.id,annotation:e,annotationContainerRef:g,doc:n,outlines:r,page:a,pageIndex:i,scale:l,viewport:v,onExecuteNamedAction:u,onJumpFromLinkAnnotation:p,onJumpToDest:d});case exports.AnnotationType.Polygon:return o.createElement(Ae,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Polyline:return o.createElement(He,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Popup:return o.createElement(Re,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Square:return o.createElement(Ne,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Squiggly:return o.createElement(Ve,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Stamp:return o.createElement(ze,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.StrikeOut:return o.createElement(Be,{key:e.id,annotation:e,page:a,viewport:v});case exports.AnnotationType.Text:return o.createElement(Ge,{key:e.id,annotation:e,childAnnotation:c,page:a,viewport:v});case exports.AnnotationType.Underline:return o.createElement(Ye,{key:e.id,annotation:e,page:a,viewport:v});default:return o.createElement(o.Fragment,{key:e.id})}})))},Xe=function(e){var t=e.page,n=e.renderAnnotations,r=T(),a=o.useState({loading:!0,annotations:[]}),i=a[0],c=a[1];return o.useEffect((function(){t.getAnnotations({intent:"display"}).then((function(e){r.current&&c({loading:!1,annotations:e})}))}),[]),i.loading?o.createElement(o.Fragment,null):n(i.annotations)},Qe=function(e){var t=e.doc,n=e.outlines,r=e.page,a=e.pageIndex,i=e.plugins,c=e.rotation,s=e.scale,l=e.onExecuteNamedAction,u=e.onJumpFromLinkAnnotation,p=e.onJumpToDest;return o.createElement(Xe,{page:r,renderAnnotations:function(e){return o.createElement(Ke,{annotations:e,doc:t,outlines:n,page:r,pageIndex:a,plugins:i,rotation:c,scale:s,onExecuteNamedAction:l,onJumpFromLinkAnnotation:u,onJumpToDest:p})}})},$e=function(e,t){var n=e%t;return 0===n?e:Math.floor(e-n)},et=function(e){var t=e.canvasLayerRef,n=e.height,r=e.page,a=e.pageIndex,i=e.plugins,c=e.rotation,s=e.scale,l=e.width,u=e.onRenderCanvasCompleted,p=o.useRef();return f((function(){var e=p.current;e&&e.cancel();var n=t.current;n.removeAttribute("data-testid"),i.forEach((function(e){e.onCanvasLayerRender&&e.onCanvasLayerRender({ele:n,pageIndex:a,rotation:c,scale:s,status:exports.LayerRenderStatus.PreRender})}));var o=r.getViewport({rotation:c,scale:s}),l=window.devicePixelRatio||1,d=Math.sqrt(16777216/(o.width*o.height)),f=l>d;f?n.style.transform="scale(1, 1)":n.style.removeProperty("transform");var g=Math.min(d,l),v=function(e,t){var n,r;if(Math.floor(e)===e)return[e,1];var o=1/e;if(o>t)return[1,t];if(Math.floor(o)===o)return[1,o];for(var a=e>1?o:e,i=0,c=1,s=1,l=1;;){var u=i+s,p=c+l;if(p>t)break;a<=u/p?(s=(n=[u,p])[0],l=n[1]):(i=(r=[u,p])[0],c=r[1])}return a<(i/c+s/l)/2?a===e?[i,c]:[c,i]:a===e?[s,l]:[l,s]}(g,8),h=v[0],m=v[1];n.width=$e(o.width*g,h),n.height=$e(o.height*g,h),n.style.width="".concat($e(o.width,m),"px"),n.style.height="".concat($e(o.height,m),"px"),n.hidden=!0;var x=n.getContext("2d",{alpha:!1}),w=f||1!==l?[g,0,0,g,0,0]:null;return p.current=r.render({canvasContext:x,transform:w,viewport:o}),p.current.promise.then((function(){n.hidden=!1,n.setAttribute("data-testid","core__canvas-layer-".concat(a)),i.forEach((function(e){e.onCanvasLayerRender&&e.onCanvasLayerRender({ele:n,pageIndex:a,rotation:c,scale:s,status:exports.LayerRenderStatus.DidRender})})),u()}),(function(){u()})),function(){n&&(n.width=0,n.height=0)}}),[]),o.createElement("div",{className:"rpv-core__canvas-layer",style:{height:"".concat(n,"px"),width:"".concat(l,"px")}},o.createElement("canvas",{ref:t}))},tt=function(e){var t=e.height,n=e.page,r=e.rotation,i=e.scale,c=e.width,s=o.useRef();return f((function(){var e=s.current,o=n.getViewport({rotation:r,scale:i});n.getOperatorList().then((function(r){!function(){var e=s.current;e&&(e.innerHTML="")}(),new a.SVGGraphics(n.commonObjs,n.objs).getSVG(r,o).then((function(n){n.style.height="".concat(t,"px"),n.style.width="".concat(c,"px"),e.appendChild(n)}))}))}),[]),o.createElement("div",{className:"rpv-core__svg-layer",ref:s})},nt=function(e){var t=e.containerRef,n=e.page,r=e.pageIndex,i=e.plugins,c=e.rotation,s=e.scale,l=e.onRenderTextCompleted,u=o.useRef(),p=function(){var e=t.current;e&&([].slice.call(e.querySelectorAll(".rpv-core__text-layer-text")).forEach((function(t){return e.removeChild(t)})),[].slice.call(e.querySelectorAll('br[role="presentation"]')).forEach((function(t){return e.removeChild(t)})))};return f((function(){var e=u.current;e&&e.cancel();var o=t.current;if(o){o.removeAttribute("data-testid");var d=n.getViewport({rotation:c,scale:s});return i.forEach((function(e){e.onTextLayerRender&&e.onTextLayerRender({ele:o,pageIndex:r,scale:s,status:exports.LayerRenderStatus.PreRender})})),n.getTextContent().then((function(e){p(),u.current=a.renderTextLayer({container:o,textContent:e,textContentSource:e,viewport:d}),u.current.promise.then((function(){o.setAttribute("data-testid","core__text-layer-".concat(r)),[].slice.call(o.children).forEach((function(e){e.classList.contains("rpv-core__text-layer-text--not")||e.classList.add("rpv-core__text-layer-text")})),i.forEach((function(e){e.onTextLayerRender&&e.onTextLayerRender({ele:o,pageIndex:r,scale:s,status:exports.LayerRenderStatus.DidRender})})),l()}),(function(){o.removeAttribute("data-testid"),l()}))})),function(){var e;p(),null===(e=u.current)||void 0===e||e.cancel()}}}),[]),o.createElement("div",{className:"rpv-core__text-layer",ref:t})},rt=function(e){var t=e.doc,n=e.measureRef,r=e.outlines,a=e.pageIndex,i=e.pageRotation,c=e.pageSize,s=e.plugins,l=e.renderPage,u=e.renderQueueKey,p=e.rotation,f=e.scale,g=e.shouldRender,v=e.viewMode,h=e.onExecuteNamedAction,m=e.onJumpFromLinkAnnotation,w=e.onJumpToDest,E=e.onRenderCompleted,b=e.onRotatePage,y=T(),S=o.useState(null),_=S[0],P=S[1],R=o.useState(!1),M=R[0],k=R[1],C=o.useState(!1),L=C[0],O=C[1],F=o.useRef(),D=o.useRef(),I=Math.abs(p+i)%180==0,A=c.pageWidth*f,H=c.pageHeight*f,N=I?A:H,V=I?H:A,z=(c.rotation+p+i)%360,B=o.useRef(0),W=l||function(e){return o.createElement(o.Fragment,null,e.canvasLayer.children,e.textLayer.children,e.annotationLayer.children)};return o.useEffect((function(){P(null),k(!1),O(!1)}),[i,p,f]),o.useEffect((function(){g&&y.current&&!_&&de(t,a).then((function(e){y.current&&(B.current=u,P(e))}))}),[g,_]),o.useEffect((function(){M&&L&&(u!==B.current?(P(null),k(!1),O(!1)):E(a))}),[M,L]),o.createElement("div",{className:d({"rpv-core__page-layer":!0,"rpv-core__page-layer--dual":v===exports.ViewMode.DualPage,"rpv-core__page-layer--dual-cover":v===exports.ViewMode.DualPageWithCover,"rpv-core__page-layer--single":v===exports.ViewMode.SinglePage}),"data-testid":"core__page-layer-".concat(a),ref:n,style:{height:"".concat(V,"px"),width:"".concat(N,"px")}},_?o.createElement(o.Fragment,null,W({annotationLayer:{attrs:{},children:o.createElement(Qe,{doc:t,outlines:r,page:_,pageIndex:a,plugins:s,rotation:z,scale:f,onExecuteNamedAction:h,onJumpFromLinkAnnotation:m,onJumpToDest:w})},canvasLayer:{attrs:{},children:o.createElement(et,{canvasLayerRef:F,height:V,page:_,pageIndex:a,plugins:s,rotation:z,scale:f,width:N,onRenderCanvasCompleted:function(){y.current&&k(!0)}})},canvasLayerRendered:M,doc:t,height:V,pageIndex:a,rotation:z,scale:f,svgLayer:{attrs:{},children:o.createElement(tt,{height:V,page:_,rotation:z,scale:f,width:N})},textLayer:{attrs:{},children:o.createElement(nt,{containerRef:D,page:_,pageIndex:a,plugins:s,rotation:z,scale:f,onRenderTextCompleted:function(){y.current&&O(!0)}})},textLayerRendered:L,width:N,markRendered:E,onRotatePage:function(e){return b(a,e)}}),s.map((function(e,n){return e.renderPageLayer?o.createElement(o.Fragment,{key:n},e.renderPageLayer({canvasLayerRef:F,canvasLayerRendered:M,doc:t,height:V,pageIndex:a,rotation:z,scale:f,textLayerRef:D,textLayerRendered:L,width:N})):o.createElement(o.Fragment,{key:n})}))):o.createElement(x,{testId:"core__page-layer-loading-".concat(a)}))},ot=function(e,t){var n=t.rect;return e.height!==n.height||e.width!==n.width?n:e};!function(e){e.Horizontal="Horizontal",e.Vertical="Vertical",e.Both="Both"}(Ee||(Ee={}));var at=function(e){return 1-Math.pow(1-e,4)},it=1e-4,ct={left:0,top:0},st={capture:!1,passive:!0},lt=function(e){var t=e.elementRef,n=e.enableSmoothScroll,r=e.isRtl,a=e.scrollDirection,i=e.onSmoothScroll,c=o.useState(ct),s=c[0],l=c[1],u=o.useState(t.current),p=u[0],d=u[1],g=r?-1:1,v=o.useRef(a);v.current=a;var h=o.useRef(ct),m=o.useRef(!0),x=o.useCallback((function(){m.current=!0,n&&l(h.current),i(!1)}),[]),w=o.useCallback((function(){if(p){switch(v.current){case Ee.Horizontal:h.current={left:g*p.scrollLeft,top:0};break;case Ee.Both:h.current={left:g*p.scrollLeft,top:p.scrollTop};break;case Ee.Vertical:default:h.current={left:0,top:p.scrollTop}}n&&!m.current||l(h.current)}}),[p]);f((function(){d(t.current)})),f((function(){if(p)return p.addEventListener("scroll",w,st),function(){p.removeEventListener("scroll",w,st)}}),[p]);var E=o.useCallback((function(e,n){var r=t.current;if(!r)return Promise.resolve();var o={left:0,top:0};switch(v.current){case Ee.Horizontal:o.left=g*e.left;break;case Ee.Both:o.left=g*e.left,o.top=e.top;break;case Ee.Vertical:default:o.top=e.top}return n?(m.current=!1,i(!0),new Promise((function(e,t){!function(e,t,n,r,o,a){void 0===o&&(o=function(e){return e}),void 0===a&&(a=function(){});var i=0,c=0,s=!1;switch(t){case Ee.Horizontal:c=e.scrollLeft,i=0;case Ee.Both:c=e.scrollLeft,i=e.scrollTop;break;case Ee.Vertical:default:c=0,i=e.scrollTop}var l=function(){s||(s=!0,e.scrollLeft=n.left,e.scrollTop=n.top,a())};if(Math.abs(i-n.top)<=it&&t===Ee.Vertical)l();else if(Math.abs(c-n.left)<=it&&t===Ee.Horizontal)l();else{var u,p=-1,d=c-n.left,f=i-n.top,g=function(a){-1===p&&(p=a);var v=a-p,h=Math.min(v/r,1),m=o(h),x={left:c-d*m,top:i-f*m};switch(t){case Ee.Horizontal:e.scrollLeft=x.left;break;case Ee.Both:e.scrollLeft=x.left,e.scrollTop=x.top;break;case Ee.Vertical:default:e.scrollTop=x.top}Math.abs(x.top-n.top)<=it&&Math.abs(x.left-n.left)<=it&&!s&&(window.cancelAnimationFrame(u),l()),v<r?u=window.requestAnimationFrame(g):window.cancelAnimationFrame(u)};u=window.requestAnimationFrame(g)}}(r,v.current,o,400,at,(function(){x(),e()}))}))):new Promise((function(e,t){switch(v.current){case Ee.Horizontal:r.scrollLeft=o.left;break;case Ee.Both:r.scrollLeft=o.left,r.scrollTop=o.top;break;case Ee.Vertical:default:r.scrollTop=o.top}e()}))}),[t]);return{scrollOffset:s,scrollTo:E}},ut=function(e,t,n){return Math.max(e,Math.min(n,t))},pt=function(e,t){if(t!==exports.ViewMode.DualPageWithCover)return 0;if(!function(e){var t=e.length;if(1===t)return!1;for(var n=1;n<t;n++)if(e[n].height!==e[0].height||e[n].width!==e[0].width)return!0;return!1}(e))return 2*e[0].width;var n=ie(e.slice(1),2).map((function(e){return 2===e.length?e[0].width+e[1].width:e[0].width})),r=[e[0].width].concat(n);return Math.max.apply(Math,r)},dt={left:0,top:0},ft={left:0,top:0},gt={left:0,top:0},vt={left:0,top:0},ht={height:0,width:0},mt={left:0,top:0},xt="data-virtual-index",wt=[0,.1,.2,.3,.4,.5,.6,.7,.8,.9,1],Et=function(e){var t=e.enableSmoothScroll,n=e.isRtl,r=e.numberOfItems,a=e.parentRef,i=e.setRenderRange,c=e.sizes,s=e.scrollMode,u=e.viewMode,p=o.useState(!1),d=p[0],g=p[1],v=o.useCallback((function(e){return g(e)}),[]),h=o.useRef(s);h.current=s;var m=o.useRef(u);m.current=u;var x=s===exports.ScrollMode.Wrapped||u===exports.ViewMode.DualPageWithCover||u===exports.ViewMode.DualPage?Ee.Both:s===exports.ScrollMode.Horizontal?Ee.Horizontal:Ee.Vertical,w=lt({elementRef:a,enableSmoothScroll:t,isRtl:n,scrollDirection:x,onSmoothScroll:v}),E=w.scrollOffset,b=w.scrollTo,y=function(e){var t=e.elementRef,n=o.useState(t.current),r=n[0],a=n[1],i=o.useRef(!1),c=o.useReducer(ot,{height:0,width:0}),s=c[0],l=c[1];return f((function(){t.current!==r&&a(t.current)})),f((function(){if(r&&!i.current){i.current=!0;var e=r.getBoundingClientRect(),t=e.height,n=e.width;l({rect:{height:t,width:n}})}}),[r]),o.useEffect((function(){if(r){var e=new ResizeObserver((function(e,t){e.forEach((function(e){if(e.target===r){var t=e.contentRect,n=t.height,o=t.width;l({rect:{height:n,width:o}})}}))}));return e.observe(r),function(){e.unobserve(r)}}}),[r]),s}({elementRef:a}),S=o.useRef({scrollOffset:mt,measurements:[]});S.current.scrollOffset=E;var _=o.useMemo((function(){return Array(r).fill(-1)}),[]),P=o.useState(_),R=P[0],M=P[1],k=o.useMemo((function(){var e=new IntersectionObserver((function(e){e.forEach((function(e){var t=e.isIntersecting?e.intersectionRatio:-1,n=e.target.getAttribute(xt);if(n){var o=parseInt(n,10);0<=o&&o<r&&M((function(e){return e[o]=t,function(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}([],e,!0)}))}}))}),{threshold:wt});return e}),[]),C=o.useMemo((function(){return s===exports.ScrollMode.Page&&u===exports.ViewMode.SinglePage?function(e,t,n){for(var r=[],o=0;o<e;o++){var a={height:Math.max(t.height,n[o].height),width:Math.max(t.width,n[o].width)},i=0===o?vt:r[o-1].end,c={left:i.left+a.width,top:i.top+a.height};r[o]={index:o,start:i,size:a,end:c,visibility:-1}}return r}(r,y,c):u===exports.ViewMode.DualPageWithCover?function(e,t,n,r){for(var o=[],a=0,i=0,c=gt,s=0;s<e;s++){var l=0===s?{height:r===exports.ScrollMode.Page?Math.max(t.height,n[s].height):n[s].height,width:r===exports.ScrollMode.Page?Math.max(t.width,n[s].width):n[s].width}:{height:r===exports.ScrollMode.Page?Math.max(t.height,n[s].height):n[s].height,width:Math.max(t.width/2,n[s].width)};r===exports.ScrollMode.Page?c=0===s?gt:{left:s%2==0?l.width:0,top:Math.floor((s-1)/2)*l.height+o[0].end.top}:0===s?(c=gt,a=n[0].height,i=0):s%2==1?(c={left:0,top:a+=i},i=s===e-1?n[s].height:Math.max(n[s].height,n[s+1].height)):c={left:o[s-1].end.left,top:a};var u={left:c.left+l.width,top:c.top+l.height};o[s]={index:s,start:c,size:l,end:u,visibility:-1}}return o}(r,y,c,s):u===exports.ViewMode.DualPage?function(e,t,n,r){for(var o=[],a=0,i=0,c=ft,s=0;s<e;s++){var l={height:r===exports.ScrollMode.Page?Math.max(t.height,n[s].height):n[s].height,width:Math.max(t.width/2,n[s].width)};r===exports.ScrollMode.Page?c={left:s%2==0?0:l.width,top:Math.floor(s/2)*l.height}:s%2==0?(c={left:0,top:a+=i},i=s===e-1?n[s].height:Math.max(n[s].height,n[s+1].height)):c={left:o[s-1].end.left,top:a};var u={left:c.left+l.width,top:c.top+l.height};o[s]={index:s,start:c,size:l,end:u,visibility:-1}}return o}(r,y,c,s):function(e,t,n,r){for(var o=[],a=0,i={left:0,top:0},c=0,s=dt,l=0;l<e;l++){var u=n[l];if(0===l)a=u.width,i={left:0,top:0},c=u.height;else switch(r){case exports.ScrollMode.Wrapped:(a+=u.width)<t.width?(s={left:o[l-1].end.left,top:i.top},c=Math.max(c,u.height)):(a=u.width,i={left:(s={left:i.left,top:i.top+c}).left,top:s.top},c=u.height);break;case exports.ScrollMode.Horizontal:case exports.ScrollMode.Vertical:default:s=o[l-1].end}var p={left:s.left+u.width,top:s.top+u.height};o[l]={index:l,start:s,size:u,end:p,visibility:-1}}return o}(r,y,c,s)}),[s,c,u,y]),T=C[r-1]?{height:C[r-1].end.top,width:C[r-1].end.left}:ht;S.current.measurements=C;var L=o.useMemo((function(){var e=function(e,t,n,r){var o=0;switch(e){case Ee.Horizontal:o=r.left;break;case Ee.Vertical:default:o=r.top}var a=t.length-1,i=function(e,t,n,r){for(;e<=t;){var o=(e+t)/2|0,a=r(o);if(a<n)e=o+1;else{if(!(a>n))return o;t=o-1}}return e>0?e-1:0}(0,a,o,(function(n){switch(e){case Ee.Horizontal:return t[n].start.left;case Ee.Both:case Ee.Vertical:default:return t[n].start.top}}));if(e===Ee.Both)for(var c=t[i].start.top;i-1>=0&&t[i-1].start.top===c&&t[i-1].start.left>=r.left;)i--;for(var s=i;s<=a;){var l={top:t[s].start.top-r.top,left:t[s].start.left-r.left},u={height:n.height-l.top,width:n.width-l.left};if(e===Ee.Horizontal&&u.width<0)break;if(e===Ee.Vertical&&u.height<0)break;if(e===Ee.Both&&(u.width<0||u.height<0))break;s++}return{start:i,end:s}}(x,C,y,E),t=e.start,n=e.end,o=R.slice(ut(0,r,t),ut(0,r,n)),a=t+o.reduce((function(e,t,n,r){return t>r[e]?n:e}),0),c=a=ut(0,r-1,a),s=i({endPage:n,numPages:r,startPage:t}),l=s.startPage,p=s.endPage;switch(l=Math.max(l,0),p=Math.min(p,r-1),u){case exports.ViewMode.DualPageWithCover:a>0&&(c=a%2==1?a:a-1),l=0===l?0:l%2==1?l:l-1,r-(p=p%2==1?p-1:p)<=2&&(p=r-1);break;case exports.ViewMode.DualPage:c=a%2==0?a:a-1,l=l%2==0?l:l-1,p=p%2==1?p:p-1;break;case exports.ViewMode.SinglePage:default:c=a}return{startPage:l,endPage:p,maxVisbilityIndex:c}}),[C,y,E,u,R]),O=L.startPage,F=L.endPage,D=L.maxVisbilityIndex,I=o.useMemo((function(){for(var e=[],t=function(t){var n=C[t],r=l(l({},n),{visibility:void 0!==R[t]?R[t]:-1,measureRef:function(e){e&&(e.setAttribute(xt,"".concat(t)),k.observe(e))}});e.push(r)},n=O;n<=F;n++)t(n);return e}),[O,F,R,C]),A=o.useCallback((function(e,n){var o=S.current.measurements[ut(0,r-1,e)],a=h.current===exports.ScrollMode.Page?mt:n;return o?b({left:a.left+o.start.left,top:a.top+o.start.top},t):Promise.resolve()}),[b,t]),H=o.useCallback((function(e,t){var n=S.current.measurements,r=n[e].start,o=n.find((function(e){return e.start.top-r.top>1e-12}));if(!o)return Promise.resolve();var a=o.index;switch(m.current){case exports.ViewMode.DualPage:a=a%2==0?a:a+1;break;case exports.ViewMode.DualPageWithCover:a=a%2==1?a:a+1}return A(a,t)}),[]),N=o.useCallback((function(e,t){for(var n=S.current.measurements,o=n[e].start,a=e,i=!1,c=r-1;c>=0;c--)if(o.top-n[c].start.top>1e-12){i=!0,a=n[c].index;break}if(!i)return Promise.resolve();switch(m.current){case exports.ViewMode.DualPage:a=a%2==0?a:a-1;break;case exports.ViewMode.DualPageWithCover:a=a%2==0?a-1:a}return a===e&&(a=e-1),A(a,t)}),[]),V=o.useCallback((function(e,t){if(m.current===exports.ViewMode.DualPageWithCover||m.current===exports.ViewMode.DualPage)return H(e,t);switch(h.current){case exports.ScrollMode.Wrapped:return H(e,t);case exports.ScrollMode.Horizontal:case exports.ScrollMode.Vertical:default:return A(e+1,t)}}),[]),z=o.useCallback((function(e,t){if(m.current===exports.ViewMode.DualPageWithCover||m.current===exports.ViewMode.DualPage)return N(e,t);switch(h.current){case exports.ScrollMode.Wrapped:return N(e,t);case exports.ScrollMode.Horizontal:case exports.ScrollMode.Vertical:default:return A(e-1,t)}}),[]),B=o.useCallback((function(){return function(e,t){switch(t){case exports.ScrollMode.Horizontal:return{position:"relative",height:"100%",width:"".concat(e.width,"px")};case exports.ScrollMode.Vertical:default:return{position:"relative",height:"".concat(e.height,"px"),width:"100%"}}}(T,h.current)}),[T]),W=o.useCallback((function(e){return function(e,t,n){return n!==exports.ScrollMode.Page?{}:{height:"".concat(t.height,"px"),width:"100%",position:"absolute",top:0,transform:"translateY(".concat(e.start.top,"px)")}}(e,y,h.current)}),[y]),j=o.useCallback((function(e){return function(e,t,n,r,o){var a,i,c,s,l,u,p,d=t?"right":"left",f=t?-1:1,g=n.length,v=e.start.left*f,h=e.size,m=h.height,x=h.width;if(r===exports.ViewMode.DualPageWithCover){var w=o===exports.ScrollMode.Page?0:e.start.top;return 0===e.index||g%2==0&&e.index===g-1?((a={height:"".concat(m,"px"),minWidth:"".concat(pt(n,r),"px"),width:"100%"})[d]=0,a.position="absolute",a.top=0,a.transform="translate(".concat(v,"px, ").concat(w,"px)"),a):((i={height:"".concat(m,"px"),width:"".concat(x,"px")})[d]=0,i.position="absolute",i.top=0,i.transform="translate(".concat(v,"px, ").concat(w,"px)"),i)}if(r===exports.ViewMode.DualPage)return(c={height:"".concat(m,"px"),width:"".concat(x,"px")})[d]=0,c.position="absolute",c.top=0,c.transform="translate(".concat(v,"px, ").concat(o===exports.ScrollMode.Page?0:e.start.top,"px)"),c;switch(o){case exports.ScrollMode.Horizontal:return(s={height:"100%",width:"".concat(x,"px")})[d]=0,s.position="absolute",s.top=0,s.transform="translateX(".concat(v,"px)"),s;case exports.ScrollMode.Page:return(l={height:"".concat(m,"px"),width:"".concat(x,"px")})[d]=0,l.position="absolute",l.top=0,l;case exports.ScrollMode.Wrapped:return(u={height:"".concat(m,"px"),width:"".concat(x,"px")})[d]=0,u.position="absolute",u.top=0,u.transform="translate(".concat(v,"px, ").concat(e.start.top,"px)"),u;case exports.ScrollMode.Vertical:default:return(p={height:"".concat(m,"px"),width:"100%"})[d]=0,p.position="absolute",p.top=0,p.transform="translateY(".concat(e.start.top,"px)"),p}}(e,n,c,m.current,h.current)}),[n,c]),q=o.useCallback((function(e,t){var n=S.current,o=n.measurements,a=n.scrollOffset,i=o[ut(0,r-1,t)];if(i){var c=h.current===exports.ScrollMode.Page?{left:i.start.left,top:i.start.top}:{left:a.left*e,top:a.top*e};return b(c,!1)}return Promise.resolve()}),[]);return o.useEffect((function(){return function(){k.disconnect()}}),[]),{boundingClientRect:y,isSmoothScrolling:d,startPage:O,endPage:F,maxVisbilityIndex:D,virtualItems:I,getContainerStyles:B,getItemContainerStyles:W,getItemStyles:j,scrollToItem:A,scrollToNextItem:V,scrollToPreviousItem:z,zoom:q}},bt=function(e,t,n,r,o,a){var i=n;switch(!0){case o===exports.ViewMode.DualPageWithCover&&a>=3:case o===exports.ViewMode.DualPage&&a>=3:i=2*n;break;default:i=n}switch(r){case exports.SpecialZoomLevel.ActualSize:return 1;case exports.SpecialZoomLevel.PageFit:return Math.min((e.clientWidth-17)/i,(e.clientHeight-16)/t);case exports.SpecialZoomLevel.PageWidth:return(e.clientWidth-17)/i}},yt=function(e){var t,n,r=e.getCurrentPage,a=(t=50,n=o.useRef([]),o.useEffect((function(){return function(){n.current=[]}}),[]),{push:function(e){var r=n.current;r.length+1>t&&r.shift(),r.push(e),n.current=r},map:function(e){return n.current.map((function(t){return e(t)}))},pop:function(){var e=n.current;if(0===e.length)return null;var t=e.pop();return n.current=e,t}}),i=function(e){var t=o.useRef([]);return o.useEffect((function(){return function(){t.current=[]}}),[]),{dequeue:function(){var e=t.current;if(0===e.length)return null;var n=e.shift();return t.current=e,n||null},enqueue:function(n){var r=t.current;r.length+1>e&&r.pop(),t.current=[n].concat(r)},map:function(e){return t.current.map((function(t){return e(t)}))}}}(50),c=function(){var e=i.dequeue();return e&&a.push(e),e&&e.pageIndex===r()?c():e},s=function(){var e=a.pop();return e&&i.enqueue(e),e&&e.pageIndex===r()?s():e},l=o.useCallback((function(e){a.push(e)}),[]);return{getNextDestination:c,getPreviousDestination:s,markVisitedDestination:l}},St=function(e){var t=[];return e.map((function(e){t=t.concat(e).concat(function(e){var t=[];return e.items&&e.items.length>0&&(t=t.concat(St(e.items))),t}(e))})),t},_t={capture:!1,passive:!0},Pt={height:0,width:0},Rt={height:0,width:0},Mt=function(e){var t=e.getCurrentPage,n=e.getCurrentScrollMode,r=e.jumpToPage,a=e.targetRef,i=o.useState(exports.FullScreenMode.Normal),c=i[0],s=i[1],l=function(){var e=o.useState(Pt),t=e[0],n=e[1],r=C((function(){n({height:window.innerHeight,width:window.innerWidth})}),100);return f((function(){return window.addEventListener("resize",r,_t),function(){window.removeEventListener("resize",r,_t)}}),[]),t}(),u=o.useState(Rt),p=u[0],d=u[1],g=o.useRef(Rt),v=o.useRef(t()),h=o.useRef(Rt),m=o.useState(a.current),x=m[0],w=m[1],E=o.useRef();f((function(){a.current!==x&&w(a.current)}),[]),f((function(){if(x){var e=new ResizeObserver((function(e){e.forEach((function(e){var t=e.target.getBoundingClientRect(),n=t.height,r=t.width;d({height:n,width:r})}))}));return e.observe(x),function(){e.unobserve(x),e.disconnect()}}}),[x]);var b=o.useCallback((function(e){var t=k();return t&&t!==e?(s(exports.FullScreenMode.Normal),M(t)):Promise.resolve()}),[]),y=o.useCallback((function(e){e&&R()&&(w(e),b(e).then((function(){E.current=e,s(exports.FullScreenMode.Entering),function(e){_&&e[P.RequestFullScreen]()}(e)})))}),[]),S=o.useCallback((function(){k()&&(s(exports.FullScreenMode.Exitting),M(document))}),[]),T=o.useCallback((function(){x&&(k()!==x&&s(exports.FullScreenMode.Exitting))}),[x]);return o.useEffect((function(){switch(c){case exports.FullScreenMode.Entering:E.current&&(E.current.style.backgroundColor="var(--rpv-core__full-screen-target-background-color)"),v.current=t(),g.current={height:window.innerHeight,width:window.innerWidth};break;case exports.FullScreenMode.Entered:n()===exports.ScrollMode.Page?r(v.current).then((function(){s(exports.FullScreenMode.EnteredCompletely)})):s(exports.FullScreenMode.EnteredCompletely);break;case exports.FullScreenMode.Exitting:E.current&&(E.current.style.backgroundColor="",E.current=null),v.current=t();break;case exports.FullScreenMode.Exited:s(exports.FullScreenMode.Normal),n()===exports.ScrollMode.Page&&r(v.current)}}),[c]),o.useEffect((function(){if(c!==exports.FullScreenMode.Normal)return c===exports.FullScreenMode.Entering&&l.height===p.height&&l.width===p.width&&l.height>0&&l.width>0&&(0===h.current.height||l.height==h.current.height)?(h.current={height:window.innerHeight,width:window.innerWidth},void s(exports.FullScreenMode.Entered)):void(c===exports.FullScreenMode.Exitting&&g.current.height===l.height&&g.current.width===l.width&&l.height>0&&l.width>0&&s(exports.FullScreenMode.Exited))}),[c,l,p]),o.useEffect((function(){var e;return e=T,_&&document.addEventListener(P.FullScreenChange,e),function(){!function(e){_&&document.removeEventListener(P.FullScreenChange,e)}(T)}}),[x]),{enterFullScreenMode:y,exitFullScreenMode:S,fullScreenMode:c}},kt={buildPageStyles:function(){return{}},transformSize:function(e){return e.size}},Ct={left:0,top:0},Tt=function(e){var t=e.currentFile,n=e.defaultScale,r=e.doc,a=e.enableSmoothScroll,i=e.initialPage,c=e.initialRotation,s=e.initialScale,u=e.pageLayout,g=e.pageSizes,v=e.plugins,h=e.renderPage,m=e.scrollMode,x=e.setRenderRange,w=e.viewMode,E=e.viewerState,b=e.onDocumentLoad,y=e.onOpenFile,S=e.onPageChange,_=e.onRotate,P=e.onRotatePage,R=e.onZoom,M=r.numPages,k=r.loadingTask.docId,O=o.useContext(A).l10n,F=o.useContext(p),I=F.direction===exports.TextDirection.RightToLeft,H=o.useRef(),N=o.useRef(),V=o.useState(i),z=V[0],B=V[1],W=o.useRef(null),j=yt({getCurrentPage:function(){return me.current.pageIndex}}),q=o.useState(c),U=q[0],Z=q[1],J=L(U),G=o.useState(!1),Y=G[0],K=G[1],X=o.useState(new Map),Q=X[0],$=X[1],ee=o.useState(m),te=ee[0],ne=ee[1],re=L(te),oe=o.useState(w),ae=oe[0],ce=oe[1],ue=L(ae),pe=function(e){var t=T(),n=o.useState([]),r=n[0],a=n[1];return o.useEffect((function(){e.getOutline().then((function(e){if(t.current&&null!==e){var n=St(e);a(n)}}))}),[]),r}(r),fe=o.useState(s),ge=fe[0],ve=fe[1],he=L(ge),me=o.useRef(E),xe=o.useRef("string"==typeof n?n:null),we=o.useRef(-1),Ee=o.useRef(-1),be=o.useRef(i),ye=Mt({getCurrentPage:function(){return me.current.pageIndex},getCurrentScrollMode:function(){return me.current.scrollMode},jumpToPage:function(e){return je(e)},targetRef:N}),Se=o.useState(-1),_e=Se[0],Pe=Se[1],Re=o.useState(0),Me=Re[0],ke=Re[1],Ce=D({doc:r});o.useEffect((function(){return function(){se.clear(),le.clear()}}),[k]);var Te=o.useMemo((function(){return Object.assign({},kt,u)}),[]),Le=o.useMemo((function(){return Array(M).fill(0).map((function(e,t){var n=[g[t].pageHeight,g[t].pageWidth],r=Math.abs(U)%180==0?{height:n[0],width:n[1]}:{height:n[1],width:n[0]},o={height:r.height*ge,width:r.width*ge};return Te.transformSize({numPages:M,pageIndex:t,size:o})}))}),[U,ge]),Oe=Et({enableSmoothScroll:a,isRtl:I,numberOfItems:M,parentRef:N,scrollMode:te,setRenderRange:x,sizes:Le,viewMode:ae}),Fe=C((function(){!xe.current||me.current.fullScreenMode!==exports.FullScreenMode.Normal||i>0&&be.current===i||Ke(xe.current)}),200);!function(e){var t=e.targetRef,n=e.onResize;f((function(){var e=new ResizeObserver((function(e){e.forEach((function(e){n(e.target)}))})),r=t.current;if(r)return e.observe(r),function(){e.unobserve(r)}}),[])}({targetRef:N,onResize:Fe});var De=function(e){var t=e;v.forEach((function(e){e.onViewerStateChange&&(t=e.onViewerStateChange(t))})),me.current=t},Ie=function(){return N.current},Ae=function(){return me.current},He=o.useCallback((function(e){j.markVisitedDestination(e)}),[]),Ne=o.useCallback((function(e){var t=e.pageIndex,n=e.bottomOffset,o=e.leftOffset,a=e.scaleTo,i=N.current,c=me.current;return i&&c?new Promise((function(e,s){de(r,t).then((function(r){var s=r.getViewport({scale:1}),l=0,u=("function"==typeof n?n(s.width,s.height):n)||0,p=("function"==typeof o?o(s.width,s.height):o)||0,d=c.scale;switch(a){case exports.SpecialZoomLevel.PageFit:l=0,p=0,Ke(exports.SpecialZoomLevel.PageFit);break;case exports.SpecialZoomLevel.PageWidth:d=bt(i,g[t].pageHeight,g[t].pageWidth,exports.SpecialZoomLevel.PageWidth,w,M),l=(s.height-u)*d,p*=d,Ke(d);break;default:l=(s.height-u)*d,p*=d}switch(c.scrollMode){case exports.ScrollMode.Horizontal:Oe.scrollToItem(t,{left:p,top:0}).then((function(){e()}));break;case exports.ScrollMode.Vertical:default:Oe.scrollToItem(t,{left:0,top:l}).then((function(){e()}))}}))})):Promise.resolve()}),[]),Ve=o.useCallback((function(e){return j.markVisitedDestination(e),Ne(e)}),[]),ze=o.useCallback((function(){var e=j.getNextDestination();return e?Ne(e):Promise.resolve()}),[]),Be=o.useCallback((function(){var e=j.getPreviousDestination();return e?Ne(e):Promise.resolve()}),[]),We=o.useCallback((function(){return Oe.scrollToNextItem(me.current.pageIndex,Ct)}),[]),je=o.useCallback((function(e){return 0<=e&&e<M?Oe.scrollToItem(e,Ct):Promise.resolve()}),[]),qe=o.useCallback((function(){return Oe.scrollToPreviousItem(me.current.pageIndex,Ct)}),[]),Ue=o.useCallback((function(e){var t,n;"pdf"===(t=e.name,n=t.split(/\./).pop(),n?n.toLowerCase():"").toLowerCase()&&new Promise((function(t){var n=new FileReader;n.readAsArrayBuffer(e),n.onload=function(){var e=new Uint8Array(n.result);t(e)}})).then((function(t){y(e.name,t)}))}),[y]),Ze=o.useCallback((function(e){var t=e===exports.RotateDirection.Backward?-90:90,n=me.current.rotation,o=360===n||-360===n?t:n+t;Ce.markNotRendered(),Z(o),De(l(l({},me.current),{rotation:o})),_({direction:e,doc:r,rotation:o})}),[]),Je=o.useCallback((function(e,t){var n=t===exports.RotateDirection.Backward?-90:90,o=me.current.pagesRotation,a=(o.has(e)?o.get(e):c)+n,i=o.set(e,a);$(i),K((function(e){return!e})),De(l(l({},me.current),{pagesRotation:i,rotatedPage:e})),P({direction:t,doc:r,pageIndex:e,rotation:a}),Ce.markRendering(e),Pe(e)}),[]),Ge=o.useCallback((function(e){De(l(l({},me.current),{scrollMode:e})),ne(e)}),[]),Ye=o.useCallback((function(e){De(l(l({},me.current),{viewMode:e})),ce(e)}),[]),Ke=o.useCallback((function(e){var t=N.current,n=me.current.pageIndex;if(!(n<0||n>=M)){var o=g[n].pageHeight,a=g[n].pageWidth,i=t?"string"==typeof e?bt(t,o,a,e,me.current.viewMode,M):e:1;xe.current="string"==typeof e?e:null,i!==me.current.scale&&(ke((function(e){return e+1})),Ce.markNotRendered(),ve(i),R({doc:r,scale:i}),De(l(l({},me.current),{scale:i})))}}),[]),Xe=o.useCallback((function(e){ye.enterFullScreenMode(e)}),[]),Qe=o.useCallback((function(){ye.exitFullScreenMode()}),[]);o.useEffect((function(){De(l(l({},me.current),{fullScreenMode:ye.fullScreenMode}))}),[ye.fullScreenMode]),o.useEffect((function(){var e={enterFullScreenMode:Xe,exitFullScreenMode:Qe,getPagesContainer:Ie,getViewerState:Ae,jumpToDestination:Ve,jumpToNextDestination:ze,jumpToPreviousDestination:Be,jumpToNextPage:We,jumpToPreviousPage:qe,jumpToPage:je,openFile:Ue,rotate:Ze,rotatePage:Je,setViewerState:De,switchScrollMode:Ge,switchViewMode:Ye,zoom:Ke};return v.forEach((function(t){t.install&&t.install(e)})),function(){v.forEach((function(t){t.uninstall&&t.uninstall(e)}))}}),[k]),o.useEffect((function(){b({doc:r,file:t}),v.forEach((function(e){e.onDocumentLoad&&e.onDocumentLoad({doc:r,file:t})}))}),[k]);var $e,et,tt,nt=Oe.boundingClientRect;$e=function(){i&&je(i)},et=nt.height>0&&nt.width>0,tt=o.useRef(!1),f((function(){et&&!tt.current&&(tt.current=!0,$e())}),[$e,et]),f((function(){var e=me.current.pageIndex;e>-1&&re!==te&&Oe.scrollToItem(e,Ct).then((function(){ye.fullScreenMode===exports.FullScreenMode.EnteredCompletely&&(a||Ce.markNotRendered(),we.current=-1)}))}),[te]),f((function(){var e=me.current.pageIndex;e>-1&&J!==U&&Oe.scrollToItem(e,Ct)}),[U]),f((function(){0!=he&&he!=me.current.scale&&Oe.zoom(me.current.scale/he,me.current.pageIndex).then((function(){ye.fullScreenMode===exports.FullScreenMode.EnteredCompletely&&(Ee.current=-1)}))}),[ge]),f((function(){if(ue!==me.current.viewMode){var e=Oe.startPage,t=Oe.endPage,n=Oe.virtualItems;Ce.markNotRendered(),Ce.setRange(e,t);for(var r=function(e){var t=n.find((function(t){return t.index===e}));t&&Ce.setVisibility(e,t.visibility)},o=e;o<=t;o++)r(o);at()}}),[ae]),f((function(){var e=me.current.pageIndex;e>-1&&ue!==ae&&Oe.scrollToItem(e,Ct)}),[ae]),f((function(){var e=me.current.pageIndex;e>0&&e===i&&be.current===i&&xe.current&&(be.current=-1,Ke(xe.current))}),[z]),o.useEffect((function(){Oe.isSmoothScrolling||null!==W.current&&W.current===z||(W.current=z,S({currentPage:z,doc:r}))}),[z,Oe.isSmoothScrolling]),o.useEffect((function(){ye.fullScreenMode===exports.FullScreenMode.Entering&&me.current.scrollMode===exports.ScrollMode.Page&&(we.current=me.current.pageIndex),ye.fullScreenMode===exports.FullScreenMode.EnteredCompletely&&me.current.scrollMode===exports.ScrollMode.Page&&a&&(we.current=-1),ye.fullScreenMode===exports.FullScreenMode.EnteredCompletely&&xe.current&&(Ee.current=me.current.pageIndex,Ke(xe.current))}),[ye.fullScreenMode]),o.useEffect((function(){if(ye.fullScreenMode!==exports.FullScreenMode.Entering&&ye.fullScreenMode!==exports.FullScreenMode.Exitting&&!Oe.isSmoothScrolling){var e=Oe.startPage,t=Oe.endPage,n=Oe.maxVisbilityIndex,r=Oe.virtualItems,o=n,a=ye.fullScreenMode===exports.FullScreenMode.Entered||ye.fullScreenMode===exports.FullScreenMode.EnteredCompletely;if(!(a&&o!==we.current&&we.current>-1||a&&o!==Ee.current&&Ee.current>-1)){B(o),De(l(l({},me.current),{pageIndex:o})),Ce.setRange(e,t);for(var i=function(e){var t=r.find((function(t){return t.index===e}));t&&Ce.setVisibility(e,t.visibility)},c=e;c<=t;c++)i(c);at()}}}),[Oe.startPage,Oe.endPage,Oe.isSmoothScrolling,Oe.maxVisbilityIndex,ye.fullScreenMode,Y,U,ge]);var ot=o.useCallback((function(e){Ce.markRendered(e),at()}),[Me]),at=function(){var e=Ce.getHighestPriorityPage();e>-1&&Ce.isInRange(e)&&(Ce.markRendering(e),Pe(e))},it=function(e){var t=z-1,n=z+1;switch(e){case"FirstPage":je(0);break;case"LastPage":je(M-1);break;case"NextPage":n<M&&je(n);break;case"PrevPage":t>=0&&je(t)}},ct=o.useCallback((function(){var e=Oe.virtualItems,t=[];switch(ae){case exports.ViewMode.DualPage:t=ie(e,2);break;case exports.ViewMode.DualPageWithCover:e.length&&(t=0===e[0].index?[[e[0]]].concat(ie(e.slice(1),2)):ie(e,2));break;case exports.ViewMode.SinglePage:default:t=ie(e,1)}var n=O&&O.core?O.core.pageLabel:"Page {{pageIndex}}",a={attrs:{className:"rpv-core__inner-container","data-testid":"core__inner-container",ref:H,style:{height:"100%"}},children:o.createElement(o.Fragment,null),subSlot:{attrs:{"data-testid":"core__inner-pages",className:d({"rpv-core__inner-pages":!0,"rpv-core__inner-pages--horizontal":te===exports.ScrollMode.Horizontal,"rpv-core__inner-pages--rtl":I,"rpv-core__inner-pages--single":te===exports.ScrollMode.Page,"rpv-core__inner-pages--vertical":te===exports.ScrollMode.Vertical,"rpv-core__inner-pages--wrapped":te===exports.ScrollMode.Wrapped}),ref:N,style:{height:"100%",position:"relative"}},children:o.createElement("div",{"data-testid":"core__inner-current-page-".concat(z),style:Object.assign({"--scale-factor":ge},Oe.getContainerStyles())},t.map((function(e){return o.createElement("div",{className:d({"rpv-core__inner-page-container":!0,"rpv-core__inner-page-container--single":te===exports.ScrollMode.Page}),style:Oe.getItemContainerStyles(e[0]),key:"".concat(e[0].index,"-").concat(ae)},e.map((function(e){var t=ae===exports.ViewMode.DualPageWithCover&&(0===e.index||M%2==0&&e.index===M-1);return o.createElement("div",{"aria-label":n.replace("{{pageIndex}}","".concat(e.index+1)),className:d({"rpv-core__inner-page":!0,"rpv-core__inner-page--dual-even":ae===exports.ViewMode.DualPage&&e.index%2==0,"rpv-core__inner-page--dual-odd":ae===exports.ViewMode.DualPage&&e.index%2==1,"rpv-core__inner-page--dual-cover":t,"rpv-core__inner-page--dual-cover-even":ae===exports.ViewMode.DualPageWithCover&&!t&&e.index%2==0,"rpv-core__inner-page--dual-cover-odd":ae===exports.ViewMode.DualPageWithCover&&!t&&e.index%2==1,"rpv-core__inner-page--single":ae===exports.ViewMode.SinglePage&&te===exports.ScrollMode.Page}),role:"region",key:"".concat(e.index,"-").concat(ae),style:Object.assign({},Oe.getItemStyles(e),Te.buildPageStyles({numPages:M,pageIndex:e.index,scrollMode:te,viewMode:ae}))},o.createElement(rt,{doc:r,measureRef:e.measureRef,outlines:pe,pageIndex:e.index,pageRotation:Q.has(e.index)?Q.get(e.index):0,pageSize:g[e.index],plugins:v,renderPage:h,renderQueueKey:Me,rotation:U,scale:ge,shouldRender:_e===e.index,viewMode:ae,onExecuteNamedAction:it,onJumpFromLinkAnnotation:He,onJumpToDest:Ve,onRenderCompleted:ot,onRotatePage:Je}))})))})))}};return v.forEach((function(e){e.renderViewer&&(a=e.renderViewer({containerRef:H,doc:r,pagesContainerRef:N,pagesRotation:Q,pageSizes:g,rotation:U,slot:a,themeContext:F,jumpToPage:je,openFile:Ue,rotate:Ze,rotatePage:Je,switchScrollMode:Ge,switchViewMode:Ye,zoom:Ke}))})),a}),[v,Oe]),st=o.useCallback((function(e){return o.createElement("div",l({},e.attrs,{style:e.attrs&&e.attrs.style?e.attrs.style:{}}),e.children,e.subSlot&&st(e.subSlot))}),[]);return st(ct())},Lt=[.1,.2,.3,.4,.5,.6,.7,.8,.9,1,1.1,1.3,1.5,1.7,1.9,2.1,2.4,2.7,3,3.3,3.7,4.1,4.6,5.1,5.7,6.3,7,7.7,8.5,9.4,10],Ot=function(e){var t=e.defaultScale,n=e.doc,r=e.render,a=e.scrollMode,i=e.viewMode,c=o.useRef(),s=o.useState({pageSizes:[],scale:0}),l=s[0],u=s[1];return o.useLayoutEffect((function(){var e=Array(n.numPages).fill(0).map((function(e,t){return new Promise((function(e,r){de(n,t).then((function(t){var n=t.getViewport({scale:1});e({pageHeight:n.height,pageWidth:n.width,rotation:n.rotation})}))}))}));Promise.all(e).then((function(e){var r=c.current;if(r&&0!==e.length){var o=e[0].pageWidth,s=e[0].pageHeight,l=r.parentElement,p=(l.clientWidth-45)/o,d=(l.clientHeight-45)/s,f=p;switch(a){case exports.ScrollMode.Horizontal:f=Math.min(p,d);break;case exports.ScrollMode.Vertical:default:f=p}var g,v,h=t?"string"==typeof t?bt(l,s,o,t,i,n.numPages):t:(g=f,-1===(v=Lt.findIndex((function(e){return e>=g})))||0===v?g:Lt[v-1]);u({pageSizes:e,scale:h})}}))}),[n.loadingTask.docId]),0===l.pageSizes.length||0===l.scale?o.createElement("div",{className:"rpv-core__page-size-calculator","data-testid":"core__page-size-calculating",ref:c},o.createElement(x,null)):r(l.pageSizes,l.scale)},Ft=function(){},Dt=function(e){function t(t,n){var r=e.call(this)||this;return r.verifyPassword=t,r.passwordStatus=n,r}return c(t,e),t}(Ft),It=function(e){var t=e.passwordStatus,n=e.renderProtectedView,r=e.verifyPassword,a=e.onDocumentAskPassword,i=o.useContext(A).l10n,c=o.useState(""),s=c[0],l=c[1],u=o.useContext(p).direction===exports.TextDirection.RightToLeft,f=function(){return r(s)};return o.useEffect((function(){a&&a({verifyPassword:r})}),[]),n?n({passwordStatus:t,verifyPassword:r}):o.createElement("div",{className:"rpv-core__asking-password-wrapper"},o.createElement("div",{className:d({"rpv-core__asking-password":!0,"rpv-core__asking-password--rtl":u})},o.createElement("div",{className:"rpv-core__asking-password-message"},t===exports.PasswordStatus.RequiredPassword&&i.core.askingPassword.requirePasswordToOpen,t===exports.PasswordStatus.WrongPassword&&i.core.wrongPassword.tryAgain),o.createElement("div",{className:"rpv-core__asking-password-body"},o.createElement("div",{className:d({"rpv-core__asking-password-input":!0,"rpv-core__asking-password-input--ltr":!u,"rpv-core__asking-password-input--rtl":u})},o.createElement(w,{testId:"core__asking-password-input",type:"password",value:s,onChange:l,onKeyDown:function(e){"Enter"===e.key&&f()}})),o.createElement(m,{onClick:f},i.core.askingPassword.submit))))},At=function(e){function t(t){var n=e.call(this)||this;return n.doc=t,n}return c(t,e),t}(Ft),Ht=function(e){function t(t){var n=e.call(this)||this;return n.error=t,n}return c(t,e),t}(Ft),Nt=function(e){function t(t){var n=e.call(this)||this;return n.percentages=t,n}return c(t,e),t}(Ft),Vt=function(e){var t=e.characterMap,n=e.file,r=e.httpHeaders,i=e.render,c=e.renderError,s=e.renderLoader,l=e.renderProtectedView,u=e.transformGetDocumentParams,f=e.withCredentials,g=e.onDocumentAskPassword,v=o.useContext(p).direction===exports.TextDirection.RightToLeft,h=o.useState(new Nt(0)),m=h[0],w=h[1],E=o.useRef(""),b=T();return o.useEffect((function(){E.current="",w(new Nt(0));var e=new a.PDFWorker({name:"PDFWorker_".concat(Date.now())}),o=Object.assign({httpHeaders:r,withCredentials:f,worker:e},"string"==typeof n?{url:n}:{data:n},t?{cMapUrl:t.url,cMapPacked:t.isCompressed}:{}),i=u?u(o):o,c=a.getDocument(i);return c.onPassword=function(e,t){switch(t){case a.PasswordResponses.NEED_PASSWORD:b.current&&w(new Dt(e,exports.PasswordStatus.RequiredPassword));break;case a.PasswordResponses.INCORRECT_PASSWORD:b.current&&w(new Dt(e,exports.PasswordStatus.WrongPassword))}},c.onProgress=function(e){var t=e.total>0?Math.min(100,100*e.loaded/e.total):100;b.current&&""===E.current&&w(new Nt(t))},c.promise.then((function(e){E.current=e.loadingTask.docId,b.current&&w(new At(e))}),(function(t){return b.current&&!e.destroyed&&w(new Ht({message:t.message||"Cannot load document",name:t.name}))})),function(){c.destroy(),e.destroy()}}),[n]),m instanceof Dt?o.createElement(It,{passwordStatus:m.passwordStatus,renderProtectedView:l,verifyPassword:m.verifyPassword,onDocumentAskPassword:g}):m instanceof At?i(m.doc):m instanceof Ht?c?c(m.error):o.createElement("div",{className:d({"rpv-core__doc-error":!0,"rpv-core__doc-error--rtl":v})},o.createElement("div",{className:"rpv-core__doc-error-text"},m.error.message)):o.createElement("div",{"data-testid":"core__doc-loading",className:d({"rpv-core__doc-loading":!0,"rpv-core__doc-loading--rtl":v})},s?s(m.percentages):o.createElement(x,null))},zt=function(e,t){var n=o.useMemo((function(){return"auto"===e?"undefined"!=typeof window&&window.matchMedia&&window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e}),[]),r=o.useState(n),a=r[0],i=r[1],c=L(a);return o.useEffect((function(){if("auto"===e){var t=window.matchMedia("(prefers-color-scheme: dark)"),n=function(e){i(e.matches?"dark":"light")};return t.addEventListener("change",n),function(){return t.removeEventListener("change",n)}}}),[]),o.useEffect((function(){a!==c&&t&&t(a)}),[a]),o.useEffect((function(){e!==a&&i(e)}),[e]),{currentTheme:a,setCurrentTheme:i}},Bt=function(e){return{startPage:e.startPage-3,endPage:e.endPage+3}};exports.Button=function(e){var t=e.children,n=e.testId,r=e.onClick,a=o.useContext(p).direction===exports.TextDirection.RightToLeft,i=n?{"data-testid":n}:{};return o.createElement("button",l({className:d({"rpv-core__button":!0,"rpv-core__button--rtl":a}),type:"button",onClick:r},i),t)},exports.Icon=v,exports.LazyRender=function(e){var t=e.attrs,n=e.children,r=e.testId,a=o.useState(!1),i=a[0],c=a[1],s=r?l(l({},t),{"data-testid":r}):t,u=g({once:!0,onVisibilityChanged:function(e){e.isVisible&&c(!0)}});return o.createElement("div",l({ref:u},s),i&&n)},exports.LocalizationContext=A,exports.Menu=function(e){var t=e.children,n=o.useRef(),r=o.useRef([]),a=o.useContext(p).direction===exports.TextDirection.RightToLeft,i=function(e){if(n.current)switch(e.key){case"Tab":e.preventDefault();break;case"ArrowDown":e.preventDefault(),c((function(e,t){return t+1}));break;case"ArrowUp":e.preventDefault(),c((function(e,t){return t-1}));break;case"End":e.preventDefault(),c((function(e,t){return e.length-1}));break;case"Home":e.preventDefault(),c((function(e,t){return 0}))}},c=function(e){if(n.current){var t=r.current,o=t.findIndex((function(e){return"0"===e.getAttribute("tabindex")})),a=Math.min(t.length-1,Math.max(0,e(t,o)));o>=0&&o<=t.length-1&&t[o].setAttribute("tabindex","-1"),t[a].setAttribute("tabindex","0"),t[a].focus()}};return f((function(){var e=n.current;if(e){var t=function(e){var t=[];return e.querySelectorAll('.rpv-core__menu-item[role="menuitem"]').forEach((function(n){if(n instanceof HTMLElement){var r=n.parentElement;(r===e||"none"!==window.getComputedStyle(r).display)&&t.push(n)}})),t}(e);r.current=t}}),[]),f((function(){return document.addEventListener("keydown",i),function(){document.removeEventListener("keydown",i)}}),[]),o.createElement("div",{ref:n,"aria-orientation":"vertical",className:d({"rpv-core__menu":!0,"rpv-core__menu--rtl":a}),role:"menu",tabIndex:0},t)},exports.MenuDivider=function(){return o.createElement("div",{"aria-orientation":"horizontal",className:"rpv-core__menu-divider",role:"separator"})},exports.MenuItem=function(e){var t=e.checked,n=void 0!==t&&t,r=e.children,a=e.icon,i=void 0===a?null:a,c=e.isDisabled,s=void 0!==c&&c,u=e.testId,f=e.onClick,g=o.useContext(p).direction===exports.TextDirection.RightToLeft,v=u?{"data-testid":u}:{};return o.createElement("button",l({className:d({"rpv-core__menu-item":!0,"rpv-core__menu-item--disabled":s,"rpv-core__menu-item--ltr":!g,"rpv-core__menu-item--rtl":g}),role:"menuitem",tabIndex:-1,type:"button",onClick:f},v),o.createElement("div",{className:d({"rpv-core__menu-item-icon":!0,"rpv-core__menu-item-icon--ltr":!g,"rpv-core__menu-item-icon--rtl":g})},i),o.createElement("div",{className:d({"rpv-core__menu-item-label":!0,"rpv-core__menu-item-label--ltr":!g,"rpv-core__menu-item-label--rtl":g})},r),o.createElement("div",{className:d({"rpv-core__menu-item-check":!0,"rpv-core__menu-item-check--ltr":!g,"rpv-core__menu-item-check--rtl":g})},n&&o.createElement(h,null)))},exports.MinimalButton=function(e){var t=e.ariaLabel,n=void 0===t?"":t,r=e.ariaKeyShortcuts,a=void 0===r?"":r,i=e.children,c=e.isDisabled,s=void 0!==c&&c,u=e.isSelected,f=void 0!==u&&u,g=e.testId,v=e.onClick,h=o.useContext(p).direction===exports.TextDirection.RightToLeft,m=g?{"data-testid":g}:{};return o.createElement("button",l({"aria-label":n},a&&{"aria-keyshortcuts":a},s&&{"aria-disabled":!0},{className:d({"rpv-core__minimal-button":!0,"rpv-core__minimal-button--disabled":s,"rpv-core__minimal-button--rtl":h,"rpv-core__minimal-button--selected":f}),type:"button",onClick:v},m),i)},exports.Modal=function(e){var t=e.ariaControlsSuffix,n=e.closeOnClickOutside,r=e.closeOnEscape,a=e.content,i=e.isOpened,c=void 0!==i&&i,s=e.target,l=t||"".concat(N());return o.createElement(U,{target:s?function(e,t){return o.createElement("div",{"aria-expanded":t?"true":"false","aria-haspopup":"dialog","aria-controls":"rpv-core__modal-body-".concat(l)},s(e,t))}:null,content:function(e){return o.createElement(W,null,o.createElement(B,{ariaControlsSuffix:l,closeOnClickOutside:n,closeOnEscape:r,onToggle:e},a(e)))},isOpened:c})},exports.Popover=function(e){var t=e.ariaHasPopup,n=void 0===t?"dialog":t,r=e.ariaControlsSuffix,a=e.closeOnClickOutside,i=e.closeOnEscape,c=e.content,s=e.lockScroll,l=void 0===s||s,u=e.offset,p=e.position,d=e.target,f=q(!1),g=f.opened,v=f.toggle,h=o.useRef(),m=o.useMemo((function(){return r||"".concat(N())}),[]);return o.createElement("div",{ref:h,"aria-expanded":g?"true":"false","aria-haspopup":n,"aria-controls":"rpv-core__popver-body-".concat(m)},d(v,g),g&&o.createElement(o.Fragment,null,l&&o.createElement(re,{closeOnEscape:i,onClose:v}),o.createElement(ne,{ariaControlsSuffix:m,closeOnClickOutside:a,offset:u,position:p,targetRef:h,onClose:v},c(v))))},exports.PrimaryButton=m,exports.ProgressBar=function(e){var t=e.progress,n=o.useContext(p).direction===exports.TextDirection.RightToLeft;return o.createElement("div",{className:d({"rpv-core__progress-bar":!0,"rpv-core__progress-bar--rtl":n})},o.createElement("div",{className:"rpv-core__progress-bar-progress",style:{width:"".concat(t,"%")}},t,"%"))},exports.Separator=function(){return o.createElement("div",{className:"rpv-core__separator"})},exports.Spinner=x,exports.Splitter=function(e){var t=e.constrain,n=o.useContext(p).direction===exports.TextDirection.RightToLeft,r=o.useRef(),a=o.useRef(),i=o.useRef(),c=o.useRef(0),s=o.useRef(0),l=o.useRef(0),u=o.useRef(0),d={capture:!0},f=function(e){var o=r.current,s=a.current,p=i.current;if(o&&s&&p){var d=u.current,f=e.clientX-c.current,g=l.current+(n?-f:f),v=o.parentElement.getBoundingClientRect().width,h=100*g/v;if(o.classList.add("rpv-core__splitter--resizing"),t){var m=v-g-d;if(!t({firstHalfPercentage:h,firstHalfSize:g,secondHalfPercentage:100*m/v,secondHalfSize:m}))return}s.style.width="".concat(h,"%"),document.body.classList.add("rpv-core__splitter-body--resizing"),s.classList.add("rpv-core__splitter-sibling--resizing"),p.classList.add("rpv-core__splitter-sibling--resizing")}},g=function(e){var t=r.current,n=a.current,o=i.current;t&&n&&o&&(document.body.classList.remove("rpv-core__splitter-body--resizing"),t.classList.remove("rpv-core__splitter--resizing"),n.classList.remove("rpv-core__splitter-sibling--resizing"),o.classList.remove("rpv-core__splitter-sibling--resizing"),document.removeEventListener("mousemove",f,d),document.removeEventListener("mouseup",g,d))};return o.useEffect((function(){var e=r.current;e&&(u.current=e.getBoundingClientRect().width,a.current=e.previousElementSibling,i.current=e.nextElementSibling)}),[]),o.createElement("div",{ref:r,className:"rpv-core__splitter",onMouseDown:function(e){var t=a.current;t&&(c.current=e.clientX,s.current=e.clientY,l.current=t.getBoundingClientRect().width,document.addEventListener("mousemove",f,d),document.addEventListener("mouseup",g,d))}})},exports.TextBox=w,exports.ThemeContext=p,exports.Tooltip=function(e){var t=e.ariaControlsSuffix,n=e.content,r=e.offset,a=e.position,i=e.target,c=q(!1),s=c.opened,l=c.toggle,u=o.useRef(),p=o.useRef(),d=o.useMemo((function(){return t||"".concat(N())}),[]);z((function(){u.current&&document.activeElement&&u.current.contains(document.activeElement)&&g()}));var f=function(){l(exports.ToggleStatus.Open)},g=function(){l(exports.ToggleStatus.Close)};return o.createElement(o.Fragment,null,o.createElement("div",{ref:u,"aria-describedby":"rpv-core__tooltip-body-".concat(d),onBlur:function(e){e.relatedTarget instanceof HTMLElement&&e.currentTarget.parentElement&&e.currentTarget.parentElement.contains(e.relatedTarget)?p.current&&(p.current.style.display="none"):g()},onFocus:f,onMouseEnter:f,onMouseLeave:g},i),s&&o.createElement(oe,{ariaControlsSuffix:d,contentRef:p,offset:r,position:a,targetRef:u},n()))},exports.Viewer=function(e){var t=e.characterMap,n=e.defaultScale,r=e.enableSmoothScroll,a=void 0===r||r,i=e.fileUrl,c=e.httpHeaders,s=void 0===c?{}:c,l=e.initialPage,u=void 0===l?0:l,d=e.pageLayout,f=e.initialRotation,v=void 0===f?0:f,h=e.localization,m=e.plugins,x=void 0===m?[]:m,w=e.renderError,E=e.renderLoader,b=e.renderPage,y=e.renderProtectedView,S=e.scrollMode,_=void 0===S?exports.ScrollMode.Vertical:S,P=e.setRenderRange,R=void 0===P?Bt:P,M=e.transformGetDocumentParams,k=e.theme,C=void 0===k?{direction:exports.TextDirection.LeftToRight,theme:"light"}:k,T=e.viewMode,O=void 0===T?exports.ViewMode.SinglePage:T,F=e.withCredentials,D=void 0!==F&&F,H=e.onDocumentAskPassword,N=e.onDocumentLoad,V=void 0===N?function(){}:N,z=e.onPageChange,B=void 0===z?function(){}:z,W=e.onRotate,j=void 0===W?function(){}:W,q=e.onRotatePage,U=void 0===q?function(){}:q,Z=e.onSwitchTheme,J=void 0===Z?function(){}:Z,G=e.onZoom,Y=void 0===G?function(){}:G,K=o.useState({data:i,name:"string"==typeof i?i:"",shouldLoad:!1}),X=K[0],Q=K[1],$=function(e,t){Q({data:t,name:e,shouldLoad:!0})},ee=o.useState(!1),te=ee[0],ne=ee[1],re=L(X);o.useEffect((function(){var e,t,n,r;e=re.data,r=typeof(t=i),"string"===(n=typeof e)&&"string"===r&&e===t||"object"===n&&"object"===r&&e.length===t.length&&e.every((function(e,n){return e===t[n]}))||Q({data:i,name:"string"==typeof i?i:"",shouldLoad:te})}),[i,te]);var oe=g({onVisibilityChanged:function(e){ne(e.isVisible),e.isVisible&&Q((function(e){return Object.assign({},e,{shouldLoad:!0})}))}}),ae="string"==typeof C?{direction:exports.TextDirection.LeftToRight,theme:C}:C,ie=o.useState(h||I),ce=ie[0],se=ie[1],le={l10n:ce,setL10n:se},ue=Object.assign({},{direction:ae.direction},zt(ae.theme||"light",J));return o.useEffect((function(){h&&se(h)}),[h]),o.createElement(A.Provider,{value:le},o.createElement(p.Provider,{value:ue},o.createElement("div",{ref:oe,className:"rpv-core__viewer rpv-core__viewer--".concat(ue.currentTheme),"data-testid":"core__viewer",style:{height:"100%",width:"100%"}},X.shouldLoad&&o.createElement(Vt,{characterMap:t,file:X.data,httpHeaders:s,render:function(e){return o.createElement(Ot,{defaultScale:n,doc:e,render:function(t,r){return o.createElement(Tt,{currentFile:{data:X.data,name:X.name},defaultScale:n,doc:e,enableSmoothScroll:a,initialPage:u,initialRotation:v,initialScale:r,pageLayout:d,pageSizes:t,plugins:x,renderPage:b,scrollMode:_,setRenderRange:R,viewMode:O,viewerState:{file:X,fullScreenMode:exports.FullScreenMode.Normal,pageIndex:-1,pageHeight:t[0].pageHeight,pageWidth:t[0].pageWidth,pagesRotation:new Map,rotation:v,scale:r,scrollMode:_,viewMode:O},onDocumentLoad:V,onOpenFile:$,onPageChange:B,onRotate:j,onRotatePage:U,onZoom:Y})},scrollMode:_,viewMode:O})},renderError:w,renderLoader:E,renderProtectedView:y,transformGetDocumentParams:M,withCredentials:D,onDocumentAskPassword:H}))))},exports.Worker=function(e){var t=e.children,n=e.workerUrl;return a.GlobalWorkerOptions.workerSrc=n,o.createElement(o.Fragment,null,t)},exports.chunk=ie,exports.classNames=d,exports.createStore=function(e){var t=e||{},n={},r=function(e,r){var o;t=l(l({},t),((o={})[e]=r,o)),(n[e]||[]).forEach((function(n){return n(t[e])}))},o=function(e){return t[e]};return{subscribe:function(e,t){n[e]=(n[e]||[]).concat(t)},unsubscribe:function(e,t){n[e]=(n[e]||[]).filter((function(e){return e!==t}))},update:function(e,t){r(e,t)},updateCurrentValue:function(e,t){var n=o(e);void 0!==n&&r(e,t(n))},get:function(e){return o(e)}}},exports.getDestination=fe,exports.getPage=de,exports.isFullScreenEnabled=R,exports.isMac=function(){return"undefined"!=typeof window&&/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)},exports.useDebounceCallback=C,exports.useIntersectionObserver=g,exports.useIsMounted=T,exports.useIsomorphicLayoutEffect=f,exports.usePrevious=L,exports.useRenderQueue=D;
