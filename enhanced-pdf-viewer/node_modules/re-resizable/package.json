{"name": "re-resizable", "version": "6.11.2", "description": "Resizable component for React.", "title": "re-resizable", "main": "./lib/index.es5.js", "module": "./lib/index.js", "jsnext:main": "./lib/index.js", "keywords": ["react", "resize", "resizable", "component"], "scripts": {"lint": "tslint -c tslint.json src/index.tsx", "tsc": "tsc -p tsconfig.json --skip<PERSON><PERSON><PERSON><PERSON><PERSON>", "build:prod:main": "rollup -c scripts/prod.js", "build:prod:es5": "rollup -c scripts/prod.es5.js", "build": "npm-run-all --serial build:prod:* && tsc", "start": "npm-run-all --parallel storybook", "test": "npm run test-ct", "test:ci": "npm run flow && npm run build", "prepublish": "npm run build", "format": "prettier --write '**/*.{tsx,ts}'", "format:ci": "prettier '**/*.{tsx,ts}'", "storybook": "start-storybook -p 6066", "build-storybook": "build-storybook", "deploy": "npm run build-storybook && gh-pages -d storybook-static", "test-ct": "playwright test -c playwright-ct.config.ts"}, "repository": {"type": "git", "url": "https://github.com/bokuweb/react-resizable-box.git"}, "author": "bokuweb", "license": "MIT", "bugs": {"url": "https://github.com/bokuweb/react-resizable-box/issues"}, "homepage": "https://github.com/bokuweb/react-resizable-box", "devDependencies": {"@babel/cli": "7.11.6", "@babel/core": "7.11.6", "@babel/eslint-parser": "7.11.0", "@babel/plugin-proposal-class-properties": "7.10.4", "@babel/plugin-transform-modules-commonjs": "7.10.4", "@babel/preset-react": "7.10.4", "@babel/preset-typescript": "7.10.4", "@babel/traverse": "7.23.2", "@babel/types": "7.11.5", "@emotion/core": "10.0.35", "@playwright/experimental-ct-react": "^1.43.1", "@storybook/addon-info": "5.3.21", "@storybook/addon-options": "5.3.21", "@storybook/react": "8.5.8", "@types/node": "22.13.5", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@types/sinon": "17.0.4", "babel-core": "7.0.0-bridge.0", "babel-loader": "9.2.1", "babel-plugin-external-helpers": "6.22.0", "babel-plugin-transform-class-properties": "6.24.1", "babel-plugin-transform-object-assign": "6.22.0", "babel-plugin-transform-object-rest-spread": "6.26.0", "babel-polyfill": "6.26.0", "babel-preset-env": "1.7.0", "babel-preset-es2015": "6.24.1", "babel-preset-flow": "6.23.0", "babel-preset-react": "6.24.1", "babel-register": "6.26.0", "cross-env": "7.0.3", "gh-pages": "5.0.0", "npm-run-all2": "5.0.2", "playwright-core": "^1.43.1", "prettier": "1.19.1", "react": "^19.0.0", "react-dom": "^19.0.0", "rollup": "1.32.1", "rollup-plugin-babel": "4.4.0", "rollup-plugin-commonjs": "10.1.0", "rollup-plugin-node-globals": "1.4.0", "rollup-plugin-node-resolve": "5.2.0", "rollup-plugin-replace": "2.2.0", "rollup-plugin-typescript2": "0.27.3", "rollup-watch": "4.3.1", "sinon": "9.0.3", "tslint": "6.1.3", "tslint-config-google": "1.0.1", "tslint-config-prettier": "1.18.0", "tslint-plugin-prettier": "2.3.0", "typescript": "5.7.3"}, "typings": "./lib/index.d.ts", "types": "./lib/index.d.ts", "files": ["lib"], "peerDependencies": {"react": "^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react-dom": "^16.13.1 || ^17.0.0 || ^18.0.0 || ^19.0.0"}}