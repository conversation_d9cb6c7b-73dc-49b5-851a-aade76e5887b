{"version": 3, "file": "unbound-method.js", "sourceRoot": "", "sources": ["../../src/rules/unbound-method.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,wEAAoE;AACpE,sDAAwC;AACxC,+CAAiC;AAEjC,kCAKiB;AAcjB;;;;;;;;;;GAUG;AACH,MAAM,iBAAiB,GAAG;IACxB,QAAQ;IACR,QAAQ;IACR,QAAQ,EAAE,wEAAwE;IAClF,QAAQ;IACR,QAAQ;IACR,OAAO;IACP,OAAO;IACP,MAAM;IACN,SAAS;IACT,SAAS;IACT,SAAS;IACT,MAAM;IACN,MAAM;IACN,MAAM;CACE,CAAC;AACX,MAAM,oBAAoB,GAAG,IAAI,GAAG,CAClC,iBAAiB,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE;IACpC,IAAI,CAAC,CAAC,SAAS,IAAI,MAAM,CAAC,EAAE,CAAC;QAC3B,+EAA+E;QAC/E,qEAAqE;QACrE,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,CAAC;IACjC,OAAO,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC;SACtC,MAAM,CACL,IAAI,CAAC,EAAE,CACL,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;QACrB,OAAQ,MAAkC,CAAC,IAAI,CAAC,KAAK,UAAU,CAClE;SACA,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,SAAS,IAAI,IAAI,EAAE,CAAC,CAAC;AACzC,CAAC,CAAC,CACH,CAAC;AAEF,MAAM,aAAa,GAAG,CACpB,MAAiB,EACjB,iBAA4C,EACnC,EAAE;IACX,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,sEAAsE;QACtE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,CACL,CAAC,CAAC,iBAAiB;QACnB,iBAAiB,KAAK,gBAAgB,CAAC,aAAa,EAAE,CACvD,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,IAAmB,EAAiB,EAAE,CACzD,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;AAE7D,MAAM,iBAAiB,GAAG,CAAC,IAA+B,EAAU,EAAE,CACpE,GAAG,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC;AAE9D,MAAM,YAAY,GAChB,oFAAoF,CAAC;AAEvF,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,gBAAgB;IACtB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,8DAA8D;YAChE,WAAW,EAAE,aAAa;YAC1B,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,OAAO,EAAE,YAAY;YACrB,4BAA4B,EAC1B,YAAY;gBACZ,IAAI;gBACJ,8HAA8H;SACjI;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,UAAU,EAAE;oBACV,YAAY,EAAE;wBACZ,WAAW,EACT,wEAAwE;wBAC1E,IAAI,EAAE,SAAS;qBAChB;iBACF;gBACD,oBAAoB,EAAE,KAAK;aAC5B;SACF;QACD,IAAI,EAAE,SAAS;KAChB;IACD,cAAc,EAAE;QACd;YACE,YAAY,EAAE,KAAK;SACpB;KACF;IACD,MAAM,CAAC,OAAO,EAAE,CAAC,EAAE,YAAY,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,iBAAiB,GAAG,QAAQ,CAAC,OAAO,CAAC,aAAa,CACtD,IAAA,0BAAW,EAAC,OAAO,CAAC,CACrB,CAAC;QAEF,SAAS,oBAAoB,CAC3B,IAAmB,EACnB,MAA6B;YAE7B,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,OAAO;YACT,CAAC;YAED,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,WAAW,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;YAC1E,IAAI,SAAS,EAAE,CAAC;gBACd,OAAO,CAAC,MAAM,CAAC;oBACb,SAAS,EACP,gBAAgB,KAAK,KAAK;wBACxB,CAAC,CAAC,8BAA8B;wBAChC,CAAC,CAAC,SAAS;oBACf,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,OAAO;YACL,gBAAgB,CAAC,IAA+B;gBAC9C,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;oBACpB,OAAO;gBACT,CAAC;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBAE/D,IACE,YAAY;oBACZ,oBAAoB,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;oBACjD,aAAa,CAAC,YAAY,EAAE,iBAAiB,CAAC,EAC9C,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,CAAC;YACjE,CAAC;YACD,0CAA0C,CACxC,IAAiE;gBAEjE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,GACtB,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;oBAC7C,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;oBACtB,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;gBAE9B,IAAI,QAAQ,IAAI,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,EAAE,CAAC;oBAC7D,MAAM,WAAW,GAAG,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,CAAC,CAAC;oBAC3D,MAAM,SAAS,GAAG,QAAQ,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;oBAEvD,MAAM,WAAW,GACf,WAAW,IAAI,aAAa,CAAC,WAAW,EAAE,iBAAiB,CAAC,CAAC;oBAE/D,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE;wBACnC,IACE,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,QAAQ;4BACzC,QAAQ,CAAC,GAAG,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAC/C,CAAC;4BACD,IACE,WAAW;gCACX,IAAA,mBAAY,EAAC,QAAQ,CAAC;gCACtB,oBAAoB,CAAC,GAAG,CACtB,GAAG,QAAQ,CAAC,IAAI,IAAI,QAAQ,CAAC,GAAG,CAAC,IAAI,EAAE,CACxC,EACD,CAAC;gCACD,OAAO;4BACT,CAAC;4BAED,oBAAoB,CAClB,QAAQ,CAAC,GAAG,EACZ,SAAS,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CACzC,CAAC;wBACJ,CAAC;oBACH,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,WAAW,CAClB,MAAiB,EACjB,YAAqB;IAErB,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,CAAC;IACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QACtB,sEAAsE;QACtE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;IAC9B,CAAC;IAED,QAAQ,gBAAgB,CAAC,IAAI,EAAE,CAAC;QAC9B,KAAK,EAAE,CAAC,UAAU,CAAC,mBAAmB;YACpC,OAAO;gBACL,SAAS,EACN,gBAA2C,CAAC,WAAW,EAAE,IAAI;oBAC9D,EAAE,CAAC,UAAU,CAAC,kBAAkB;aACnC,CAAC;QACJ,KAAK,EAAE,CAAC,UAAU,CAAC,iBAAiB,CAAC;QACrC,KAAK,EAAE,CAAC,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC;YACnC,MAAM,IAAI,GAAG,gBAES,CAAC;YACvB,MAAM,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;YACzC,MAAM,gBAAgB,GACpB,UAAU,EAAE,IAAI,CAAC,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,UAAU;gBAClD,wEAAwE;gBACxE,UAAU,CAAC,IAAI,CAAC,WAAW,KAAK,MAAM,CAAC;YACzC,MAAM,aAAa,GACjB,gBAAgB,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,KAAK,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC;YAE1E,OAAO;gBACL,SAAS,EACP,CAAC,aAAa;oBACd,CAAC,CACC,YAAY;wBACZ,OAAO,CAAC,gBAAgB,CACtB,IAAA,mBAAY,EAAC,gBAAgB,CAAC,EAC9B,EAAE,CAAC,UAAU,CAAC,aAAa,CAC5B,CACF;gBACH,gBAAgB;aACjB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,CAAC;AAC9B,CAAC;AAED,SAAS,SAAS,CAAC,IAAmB;IACpC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;IAE3B,QAAQ,MAAM,EAAE,IAAI,EAAE,CAAC;QACrB,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,IAAI,CAAC;QAEd,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;QAEhC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC;QAE9B,KAAK,sBAAc,CAAC,wBAAwB;YAC1C,OAAO,MAAM,CAAC,GAAG,KAAK,IAAI,CAAC;QAE7B,KAAK,sBAAc,CAAC,eAAe;YACjC,qCAAqC;YACrC,uCAAuC;YACvC,wCAAwC;YACxC,OAAO,CAAC,QAAQ,EAAE,GAAG,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAErE,KAAK,sBAAc,CAAC,gBAAgB;YAClC,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;QAE5E,KAAK,sBAAc,CAAC,oBAAoB;YACtC,OAAO,CACL,MAAM,CAAC,QAAQ,KAAK,GAAG;gBACvB,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;oBACnB,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBAC5C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,KAAK;wBACzC,MAAM,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;wBACpD,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,CAAC,CAAC,CAChE,CAAC;QAEJ,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;QAE3B,KAAK,sBAAc,CAAC,iBAAiB;YACnC,IAAI,MAAM,CAAC,QAAQ,KAAK,IAAI,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE,CAAC;gBACrD,qEAAqE;gBACrE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,oFAAoF;YACpF,0CAA0C;YAC1C,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC;IAC7B,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC"}