{"version": 3, "file": "optimize-client-rects.js", "sourceRoot": "", "sources": ["../../../src/lib/optimize-client-rects.ts"], "names": [], "mappings": "AAEA,MAAM,IAAI,GAAG,CAAC,KAAmB,EAAE,EAAE,CACnC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAClB,MAAM,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;IAEtE,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC;QACd,OAAO,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC;IACzB,CAAC;IAED,OAAO,GAAG,CAAC;AACb,CAAC,CAAC,CAAC;AAEL,MAAM,QAAQ,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAE,EAAE,CACtC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;IAC7B,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI;IAChB,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AAE7B,MAAM,QAAQ,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAE,OAAO,GAAG,CAAC,EAAE,EAAE,CACnD,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;IAC7B,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,OAAO;IACjC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC,GAAG,OAAO,CAAC;AAE1C,MAAM,MAAM,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAE,EAAE,CACpC,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;IAC7B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,GAAG;IACb,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI;IACf,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM;IACnC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;AAEtC,MAAM,MAAM,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAE,OAAO,GAAG,EAAE,EAAE,EAAE;IAClD,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;IAChC,MAAM,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,KAAK,CAAC;IAEhC,OAAO,CACL,CAAC,CAAC,UAAU,KAAK,CAAC,CAAC,UAAU;QAC7B,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,IAAI;QAChB,MAAM,IAAI,MAAM;QAChB,CAAC,CAAC,IAAI,GAAG,MAAM,IAAI,OAAO,CAC3B,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,WAAW,GAAG,CAAC,CAAQ,EAAE,CAAQ,EAAE,EAAE;IACzC,+BAA+B;IAC/B,CAAC,CAAC,KAAK,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,MAAM,mBAAmB,GAAG,CAAC,WAAyB,EAAgB,EAAE;IACtE,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;IAEhC,MAAM,QAAQ,GAAG,IAAI,GAAG,EAAE,CAAC;IAE3B,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;QACtC,OAAO,KAAK,CAAC,KAAK,CAAC,CAAC,SAAS,EAAE,EAAE;YAC/B,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,CAAC;IAEH,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,OAAO,SAAS,IAAI,CAAC,EAAE,CAAC;QACtB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;YACtB,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,EAAE;gBACtB,IAAI,CAAC,KAAK,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;oBAClD,OAAO;gBACT,CAAC;gBAED,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBACpB,OAAO;gBACT,CAAC;gBAED,IAAI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBACnB,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAClB,CAAC,CAAC,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,MAAM,EAAE,CAAC,CAAC,MAAM,CAAC,CAAC;oBAExC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC;gBAED,IAAI,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;oBACjB,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;oBAElB,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;gBAClB,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC,CAAC,CAAC;QACH,SAAS,IAAI,CAAC,CAAC;IACjB,CAAC;IAED,OAAO,SAAS,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC;AACzD,CAAC,CAAC;AAEF,eAAe,mBAAmB,CAAC"}