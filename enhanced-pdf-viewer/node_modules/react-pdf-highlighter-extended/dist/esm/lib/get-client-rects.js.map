{"version": 3, "file": "get-client-rects.js", "sourceRoot": "", "sources": ["../../../src/lib/get-client-rects.ts"], "names": [], "mappings": "AAEA,OAAO,mBAAmB,MAAM,yBAAyB,CAAC;AAE1D,MAAM,0BAA0B,GAAG,CAAC,UAAmB,EAAE,QAAiB,EAAE,EAAE;IAC5E,IAAI,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC,GAAG,EAAE,CAAC;QAClC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC;QACxC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,EAAE,CAAC;QACtC,OAAO,KAAK,CAAC;IACf,CAAC;IACD,IAAI,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,CAAC;QACpC,OAAO,KAAK,CAAC;IACf,CAAC;IAED,OAAO,IAAI,CAAC;AACd,CAAC,CAAC;AAEF,MAAM,cAAc,GAAG,CACrB,KAAY,EACZ,KAAa,EACb,iBAA0B,IAAI,EAChB,EAAE;IAChB,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,CAAC,CAAC;IAEvD,MAAM,KAAK,GAAY,EAAE,CAAC;IAE1B,KAAK,MAAM,UAAU,IAAI,WAAW,EAAE,CAAC;QACrC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;YACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAEnD,IACE,0BAA0B,CAAC,UAAU,EAAE,QAAQ,CAAC;gBAChD,UAAU,CAAC,KAAK,GAAG,CAAC;gBACpB,UAAU,CAAC,MAAM,GAAG,CAAC;gBACrB,UAAU,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK;gBACjC,UAAU,CAAC,IAAI,GAAG,QAAQ,CAAC,IAAI;gBAC/B,UAAU,CAAC,MAAM,GAAG,QAAQ,CAAC,MAAM,EACnC,CAAC;gBACD,MAAM,eAAe,GAAG;oBACtB,GAAG,EAAE,UAAU,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,GAAG;oBACxD,IAAI,EAAE,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC,IAAI;oBAC5D,KAAK,EAAE,UAAU,CAAC,KAAK;oBACvB,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,UAAU,EAAE,IAAI,CAAC,MAAM;iBACf,CAAC;gBAEX,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,cAAc,CAAC,CAAC,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;AAC7D,CAAC,CAAC;AAEF,eAAe,cAAc,CAAC"}