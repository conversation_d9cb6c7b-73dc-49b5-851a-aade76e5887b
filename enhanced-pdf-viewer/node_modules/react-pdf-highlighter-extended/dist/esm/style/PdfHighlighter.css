.PdfHighlighter {
  position: absolute;
  overflow: auto;
  width: 100%;
  height: 100%;
  background-color: #333;
}

/* Style the scrollbar */
.PdfHighlighter::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

.PdfHighlighter::-webkit-scrollbar-thumb {
  background-color: #9f9f9f;
  border-radius: 5px;
}

.PdfHighlighter::-webkit-scrollbar-thumb:hover {
  background-color: #d1d1d1;
}

.PdfHighlighter::-webkit-scrollbar-track {
  background-color: #2c2c2c;
  border-radius: 5px;
}

.PdfHighlighter::-webkit-scrollbar-track-piece {
  background-color: #2c2c2c;
}

.PdfHighlighter__tip-container {
  z-index: 6;
  position: absolute;
}

.PdfHighlighter--disable-selection {
  user-select: none;
  pointer-events: none;
}
