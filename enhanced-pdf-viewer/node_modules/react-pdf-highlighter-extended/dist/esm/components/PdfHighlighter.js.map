{"version": 3, "file": "PdfHighlighter.js", "sourceRoot": "", "sources": ["../../../src/components/PdfHighlighter.tsx"], "names": [], "mappings": "AAAA,OAAO,+BAA+B,CAAC;AACvC,OAAO,6BAA6B,CAAC;AACrC,OAAO,yBAAyB,CAAC;AAEjC,OAAO,QAAQ,MAAM,iBAAiB,CAAC;AAEvC,OAAO,KAAK,EAAE,EAIZ,eAAe,EACf,MAAM,EACN,QAAQ,GACT,MAAM,OAAO,CAAC;AACf,OAAO,EAAE,UAAU,EAAE,MAAM,kBAAkB,CAAC;AAC9C,OAAO,EACL,qBAAqB,GAEtB,MAAM,mCAAmC,CAAC;AAC3C,OAAO,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAChF,OAAO,eAAe,MAAM,0BAA0B,CAAC;AACvD,OAAO,cAAc,MAAM,yBAAyB,CAAC;AACrD,OAAO,qBAAqB,MAAM,iCAAiC,CAAC;AACpE,OAAO,EACL,SAAS,EACT,0BAA0B,EAC1B,iBAAiB,EACjB,SAAS,EACT,aAAa,GACd,MAAM,kBAAkB,CAAC;AAW1B,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAI9C,IAAI,QAA0B,EAAE,cAAsC,EAAE,SAA4B,CAAC;AAErG,CAAC,KAAK,IAAI,EAAE;IACV,8DAA8D;IAC9D,MAAM,KAAK,GAAG,MAAM,MAAM,CAAC,+BAA+B,CAAC,CAAC;IAC5D,QAAQ,GAAG,KAAK,CAAC,QAAQ,CAAC;IAC1B,cAAc,GAAG,KAAK,CAAC,cAAc,CAAC;IACtC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC;AAC9B,CAAC,CAAC,EAAE,CAAC;AAGL,MAAM,aAAa,GAAG,EAAE,CAAC;AACzB,MAAM,mBAAmB,GAAG,MAAM,CAAC;AACnC,MAAM,4BAA4B,GAAG,uBAAuB,CAAC;AAE7D,MAAM,0BAA0B,GAAG,CAAC,SAAsB,EAAE,EAAE;IAC5D,OAAO,0BAA0B,CAC/B,SAAS,EACT,iCAAiC,CAClC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,oBAAoB,GAAG,CAAC,MAAsC,EAAE,IAAa,EAAE,EAAE;IACrF,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,mCAAmC,EAAE,IAAI,CAAC,CAAC;AAC7E,CAAC,CAAC;AAoGF;;;;;;;;;GASG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,EAC7B,UAAU,EACV,YAAY,EACZ,aAAa,GAAG,mBAAmB,EACnC,WAAW,EAAE,mBAAmB,EAChC,sBAAsB,EACtB,sBAAsB,EACtB,YAAY,EACZ,mBAAmB,EACnB,mBAAmB,EACnB,WAAW,EACX,QAAQ,EACR,kBAAkB,GAAG,4BAA4B,EACjD,QAAQ,EACR,KAAK,GACe,EAAE,EAAE;IACxB,QAAQ;IACR,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAa,IAAI,CAAC,CAAC;IACjD,MAAM,CAAC,aAAa,EAAE,gBAAgB,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAE1D,OAAO;IACP,MAAM,gBAAgB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAC7D,MAAM,oBAAoB,GAAG,MAAM,CACjC,EAAE,CACH,CAAC;IACF,MAAM,iBAAiB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAC9D,MAAM,YAAY,GAAG,MAAM,CAAsB,IAAI,CAAC,CAAC;IACvD,MAAM,wBAAwB,GAAG,MAAM,CAAgB,IAAI,CAAC,CAAC;IAC7D,MAAM,4BAA4B,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IACnD,MAAM,mBAAmB,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC;IAC1C,MAAM,oBAAoB,GAAG,MAAM,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IAE/C,MAAM,WAAW,GAAG,MAAM,CAAgC,IAAI,QAAQ,EAAE,CAAC,CAAC;IAC1E,MAAM,cAAc,GAAG,MAAM,CAC3B,IAAI,cAAc,CAAC;QACjB,QAAQ,EAAE,WAAW,CAAC,OAAO;QAC7B,kBAAkB,EAAE,CAAC;KACtB,CAAC,CACH,CAAC;IACF,MAAM,iBAAiB,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAC9D,MAAM,SAAS,GAAG,MAAM,CAAwC,IAAI,CAAC,CAAC;IAEtE,wBAAwB;IACxB,eAAe,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAAE,OAAO;QAEtC,MAAM,qBAAqB,GAAG,QAAQ,CAAC,GAAG,EAAE;YAC1C,SAAS,CAAC,OAAO;gBACf,SAAS,CAAC,OAAO;oBACjB,IAAI,SAAS,CAAC;wBACZ,SAAS,EAAE,gBAAgB,CAAC,OAAQ;wBACpC,QAAQ,EAAE,WAAW,CAAC,OAAO;wBAC7B,aAAa,EAAE,CAAC;wBAChB,iBAAiB,EAAE,IAAI;wBACvB,WAAW,EAAE,cAAc,CAAC,OAAO;qBACpC,CAAC,CAAC;YAEL,SAAS,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAC3C,cAAc,CAAC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;YAChD,cAAc,CAAC,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;YACpD,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACzB,CAAC,EAAE,GAAG,CAAC,CAAC;QAER,qBAAqB,EAAE,CAAC;QAExB,OAAO,GAAG,EAAE;YACV,qBAAqB,CAAC,MAAM,EAAE,CAAC;QACjC,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC;IAEf,oCAAoC;IACpC,eAAe,CAAC,GAAG,EAAE;QACnB,IAAI,CAAC,gBAAgB,CAAC,OAAO;YAAE,OAAO;QAEtC,iBAAiB,CAAC,OAAO,GAAG,IAAI,cAAc,CAAC,gBAAgB,CAAC,CAAC;QACjE,iBAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;QAE5D,MAAM,GAAG,GAAG,gBAAgB,CAAC,OAAO,CAAC,aAAa,CAAC;QAEnD,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;QACnE,WAAW,CAAC,OAAO,CAAC,EAAE,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QACtD,GAAG,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAE/C,qBAAqB,EAAE,CAAC;QAExB,OAAO,GAAG,EAAE;YACV,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;YACvD,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,qBAAqB,CAAC,CAAC;YACpE,GAAG,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;YAClD,iBAAiB,CAAC,OAAO,EAAE,UAAU,EAAE,CAAC;QAC1C,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,YAAY,EAAE,UAAU,EAAE,mBAAmB,CAAC,CAAC,CAAC;IAEpD,kBAAkB;IAClB,MAAM,YAAY,GAAG,GAAG,EAAE;QACxB,YAAY,IAAI,YAAY,EAAE,CAAC;QAC/B,wBAAwB,CAAC,OAAO,GAAG,IAAI,CAAC;QACxC,qBAAqB,EAAE,CAAC;IAC1B,CAAC,CAAC;IAEF,MAAM,aAAa,GAAwB,GAAG,EAAE;QAC9C,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;QAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE,CAAC;QAEtD,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,IAAI,SAAS,CAAC,WAAW,IAAI,CAAC,SAAS,CAAC,OAAO;YACzE,OAAO;QAET,MAAM,KAAK,GAAG,SAAS,CAAC,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;QAExE,0DAA0D;QAC1D,IAAI,CAAC,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,CAAC,uBAAuB,CAAC;YAAE,OAAO;QAEzE,MAAM,KAAK,GAAG,iBAAiB,CAAC,KAAK,CAAC,CAAC;QACvC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAEzC,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;QAC3C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO;QAE/B,MAAM,gBAAgB,GAAqB;YACzC,YAAY,EAAE,eAAe,CAAC,KAAK,CAAC;YACpC,KAAK;SACN,CAAC;QAEF,MAAM,cAAc,GAAG,wBAAwB,CAC7C,gBAAgB,EAChB,SAAS,CAAC,OAAO,CAClB,CAAC;QAEF,MAAM,OAAO,GAAY;YACvB,IAAI,EAAE,SAAS,CAAC,QAAQ,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,8BAA8B;SACjF,CAAC;QAEF,YAAY,CAAC,OAAO,GAAG;YACrB,OAAO;YACP,IAAI,EAAE,MAAM;YACZ,QAAQ,EAAE,cAAc;YACxB,kBAAkB,EAAE,GAAG,EAAE;gBACvB,iBAAiB,CAAC,OAAO,GAAG;oBAC1B,OAAO,EAAE,OAAO;oBAChB,IAAI,EAAE,MAAM;oBACZ,QAAQ,EAAE,cAAc;iBACzB,CAAC;gBAEF,sBAAsB;oBACpB,sBAAsB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;gBACpD,kBAAkB,EAAE,CAAC;gBACrB,qBAAqB,EAAE,CAAC;gBACxB,OAAO,iBAAiB,CAAC,OAAO,CAAC;YACnC,CAAC;SACF,CAAC;QAEF,mBAAmB,IAAI,mBAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;QAEjE,YAAY;YACV,MAAM,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;IAClE,CAAC,CAAC;IAEF,MAAM,eAAe,GAAwB,CAAC,KAAK,EAAE,EAAE;QACrD,IACE,CAAC,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;YAC5B,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC,qCAAqC;UACvG,CAAC;YACD,OAAO;QACT,CAAC;QAED,MAAM,CAAC,IAAI,CAAC,CAAC;QACb,kBAAkB,EAAE,CAAC,CAAC,sFAAsF;QAC5G,oBAAoB,EAAE,CAAC;QACvB,oBAAoB,CAAC,KAAK,CAAC,CAAC;IAC9B,CAAC,CAAC;IAEF,MAAM,aAAa,GAAG,CAAC,KAAoB,EAAE,EAAE;QAC7C,IAAI,KAAK,CAAC,IAAI,KAAK,QAAQ,EAAE,CAAC;YAC5B,kBAAkB,EAAE,CAAC;YACrB,oBAAoB,EAAE,CAAC;YACvB,MAAM,CAAC,IAAI,CAAC,CAAC;QACf,CAAC;IACH,CAAC,CAAC;IAEF,MAAM,gBAAgB,GAAG,GAAG,EAAE;QAC5B,IAAI,SAAS,CAAC,OAAO,EAAE,CAAC;YACtB,SAAS,CAAC,OAAO,CAAC,iBAAiB,GAAG,aAAa,CAAC,QAAQ,EAAE,CAAC;QACjE,CAAC;IACH,CAAC,CAAC;IAEF,0BAA0B;IAC1B,MAAM,oBAAoB,GAAG,CAC3B,iBAAoC,EACpC,UAAkB,EAClB,EAAE;QACF,IAAI,CAAC,SAAS,CAAC,OAAO;YAAE,OAAO;QAE/B,iBAAiB,CAAC,SAAS,CAAC,MAAM,CAChC,oBAAC,qBAAqB,CAAC,QAAQ,IAAC,KAAK,EAAE,mBAAmB;YACxD,oBAAC,cAAc,IACb,gBAAgB,EAAE,qBAAqB,CAAC;oBACtC,GAAG,UAAU;oBACb,iBAAiB,CAAC,OAAO;iBAC1B,CAAC,EACF,UAAU,EAAE,UAAU,EACtB,qBAAqB,EAAE,wBAAwB,CAAC,OAAO,EACvD,MAAM,EAAE,SAAS,CAAC,OAAO,EACzB,iBAAiB,EAAE,iBAAiB,EACpC,QAAQ,EAAE,QAAQ,GAClB,CAC6B,CAClC,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,qBAAqB,GAAG,GAAG,EAAE;QACjC,IAAI,CAAC,SAAS,CAAC,OAAO;YAAE,OAAO;QAE/B,KAAK,IAAI,UAAU,GAAG,CAAC,EAAE,UAAU,IAAI,WAAW,CAAC,QAAQ,EAAE,UAAU,EAAE,EAAE,CAAC;YAC1E,MAAM,iBAAiB,GAAG,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;YAEnE,sFAAsF;YACtF,IAAI,iBAAiB,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC;gBAC9C,oBAAoB,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YACtD,CAAC;iBAAM,CAAC;gBACN,MAAM,EAAE,SAAS,EAAE,GACjB,SAAS,CAAC,OAAQ,CAAC,WAAW,CAAC,UAAU,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;gBACvD,IAAI,CAAC,SAAS;oBAAE,SAAS,CAAC,kCAAkC;gBAE5D,wEAAwE;gBACxE,MAAM,cAAc,GAAG,0BAA0B,CAC/C,SAAS,CAAC,GAAG,CACd,CAAC;gBAEF,IAAI,cAAc,EAAE,CAAC;oBACnB,MAAM,SAAS,GAAG,UAAU,CAAC,cAAc,CAAC,CAAC;oBAC7C,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG;wBACzC,SAAS;wBACT,SAAS,EAAE,cAAc;wBACzB,SAAS,EAAE,SAAS,CAAC,GAAG,EAAE,wEAAwE;qBACnG,CAAC;oBAEF,oBAAoB,CAClB,oBAAoB,CAAC,OAAO,CAAC,UAAU,CAAC,EACxC,UAAU,CACX,CAAC;gBACJ,CAAC;YACH,CAAC;QACH,CAAC;IACH,CAAC,CAAC;IAEF,QAAQ;IACR,MAAM,uBAAuB,GAAG,GAAG,EAAE;QACnC,OAAO,CACL,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC;YAC7B,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC;YAClC,4BAA4B,CAAC,OAAO;YACpC,mBAAmB,CAAC,OAAO,CAC5B,CAAC;IACJ,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,CAAC,IAAc,EAAE,EAAE;QAC9C,IAAI,IAAI,KAAK,SAAS,EAAE,CAAC;YACvB,mBAAmB,CAAC,OAAO,GAAG,IAAI,CAAC;QACrC,CAAC;aAAM,CAAC;YACN,mBAAmB,CAAC,OAAO,GAAG,CAAC,mBAAmB,CAAC,OAAO,CAAC;QAC7D,CAAC;QAED,yBAAyB;QACzB,IAAI,SAAS,CAAC,OAAO;YACnB,SAAS,CAAC,OAAO,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CACxC,mCAAmC,EACnC,mBAAmB,CAAC,OAAO,CAC5B,CAAC;IACN,CAAC,CAAC;IAEF,MAAM,oBAAoB,GAAG,GAAG,EAAE;QAChC,IAAI,sBAAsB,IAAI,iBAAiB,CAAC,OAAO;YACrD,sBAAsB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACpD,iBAAiB,CAAC,OAAO,GAAG,IAAI,CAAC;QACjC,qBAAqB,EAAE,CAAC;IAC1B,CAAC,CAAC;IAEF,MAAM,kBAAkB,GAAG,GAAG,EAAE;QAC9B,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;QAE5B,MAAM,SAAS,GAAG,gBAAgB,CAAC,OAAO,CAAC;QAC3C,MAAM,SAAS,GAAG,SAAS,CAAC,SAAS,CAAC,CAAC,YAAY,EAAE,CAAC;QACtD,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS;YAAE,OAAO;QACrC,SAAS,CAAC,eAAe,EAAE,CAAC;IAC9B,CAAC,CAAC;IAEF,MAAM,iBAAiB,GAAG,CAAC,SAAoB,EAAE,EAAE;QACjD,MAAM,EAAE,YAAY,EAAE,iBAAiB,EAAE,GAAG,SAAS,CAAC,QAAQ,CAAC;QAC/D,MAAM,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC;QAE3C,kEAAkE;QAClE,SAAS,CAAC,OAAQ,CAAC,SAAS,CAAC,mBAAmB,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC;QAEzE,MAAM,YAAY,GAAG,SAAS,CAAC,OAAQ,CAAC,WAAW,CACjD,UAAU,GAAG,CAAC,CACf,CAAC,QAAQ,CAAC;QAEX,SAAS,CAAC,OAAQ,CAAC,kBAAkB,CAAC;YACpC,UAAU;YACV,SAAS,EAAE;gBACT,IAAI,EAAE,kDAAkD;gBACxD,EAAE,IAAI,EAAE,KAAK,EAAE;gBACf,GAAG,YAAY,CAAC,iBAAiB,CAC/B,CAAC,EAAE,kBAAkB;gBACrB,gBAAgB,CAAC,YAAY,EAAE,YAAY,EAAE,iBAAiB,CAAC,CAAC,GAAG;oBACnE,aAAa,CACd;gBACD,CAAC,EAAE,kBAAkB;aACtB;SACF,CAAC,CAAC;QAEH,wBAAwB,CAAC,OAAO,GAAG,SAAS,CAAC,EAAE,CAAC;QAChD,qBAAqB,EAAE,CAAC;QAExB,+BAA+B;QAC/B,UAAU,CAAC,GAAG,EAAE;YACd,SAAS,CAAC,OAAQ,CAAC,SAAS,CAAC,gBAAgB,CAAC,QAAQ,EAAE,YAAY,EAAE;gBACpE,IAAI,EAAE,IAAI;aACX,CAAC,CAAC;QACL,CAAC,EAAE,GAAG,CAAC,CAAC;IACV,CAAC,CAAC;IAEF,MAAM,mBAAmB,GAAwB;QAC/C,uBAAuB;QACvB,mBAAmB,EAAE,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO;QAC/C,iBAAiB,EAAE,GAAG,EAAE,CAAC,iBAAiB,CAAC,OAAO;QAClD,oBAAoB;QACpB,oBAAoB;QACpB,gBAAgB,EAAE,GAAG,EAAE,CAAC,mBAAmB,CAAC,OAAO;QACnD,qBAAqB,EAAE,GAAG,EAAE,CAC1B,OAAO,CAAC,YAAY,CAAC,OAAO,CAAC,IAAI,4BAA4B,CAAC,OAAO;QACvE,iBAAiB;QACjB,SAAS,EAAE,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO;QAClC,MAAM,EAAE,GAAG,EAAE,CAAC,GAAG;QACjB,MAAM;QACN,iBAAiB,EAAE,oBAAoB,CAAC,OAAO;KAChD,CAAC;IAEF,QAAQ,CAAC,mBAAmB,CAAC,CAAC;IAE9B,OAAO,CACL,oBAAC,qBAAqB,CAAC,QAAQ,IAAC,KAAK,EAAE,mBAAmB;QACxD,6BACE,GAAG,EAAE,gBAAgB,EACrB,SAAS,EAAC,gBAAgB,EAC1B,aAAa,EAAE,eAAe,EAC9B,WAAW,EAAE,aAAa,EAC1B,KAAK,EAAE,KAAK;YAEZ,6BAAK,SAAS,EAAC,WAAW,GAAG;YAC7B,mCACG;;0BAEe,kBAAkB;;SAEnC,CACO;YACP,aAAa,IAAI,CAChB,oBAAC,YAAY,IACX,MAAM,EAAE,SAAS,CAAC,OAAQ,EAC1B,oBAAoB,EAAE,oBAAoB,GAC1C,CACH;YACA,aAAa,IAAI,mBAAmB,IAAI,CACvC,oBAAC,cAAc,IACb,MAAM,EAAE,SAAS,CAAC,OAAQ,EAC1B,QAAQ,EAAE,CAAC,SAAS,EAAE,EAAE,CACtB,CAAC,4BAA4B,CAAC,OAAO,GAAG,SAAS,CAAC,EAEpD,mBAAmB,EAAE,mBAAmB,EACxC,KAAK,EAAE,mBAAmB,EAC1B,WAAW,EAAE,GAAG,EAAE,CAAC,oBAAoB,CAAC,SAAS,CAAC,OAAQ,EAAE,IAAI,CAAC,EACjE,OAAO,EAAE,GAAG,EAAE;oBACZ,YAAY,CAAC,OAAO,GAAG,IAAI,CAAC;oBAC5B,oBAAoB,CAAC,SAAS,CAAC,OAAQ,EAAE,KAAK,CAAC,CAAC;gBAClD,CAAC,EACD,WAAW,EAAE,CACX,gBAAgB,EAChB,cAAc,EACd,KAAK,EACL,cAAc,EACd,EAAE;oBACF,YAAY,CAAC,OAAO,GAAG;wBACrB,OAAO,EAAE,EAAE,KAAK,EAAE;wBAClB,IAAI,EAAE,MAAM;wBACZ,QAAQ,EAAE,cAAc;wBACxB,kBAAkB,EAAE,GAAG,EAAE;4BACvB,iBAAiB,CAAC,OAAO,GAAG;gCAC1B,QAAQ,EAAE,cAAc;gCACxB,IAAI,EAAE,MAAM;gCACZ,OAAO,EAAE,EAAE,KAAK,EAAE;6BACnB,CAAC;4BACF,sBAAsB;gCACpB,sBAAsB,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC;4BACpD,cAAc,EAAE,CAAC;4BACjB,qBAAqB,EAAE,CAAC;4BACxB,OAAO,iBAAiB,CAAC,OAAO,CAAC;wBACnC,CAAC;qBACF,CAAC;oBAEF,mBAAmB,IAAI,mBAAmB,CAAC,YAAY,CAAC,OAAO,CAAC,CAAC;oBACjE,YAAY;wBACV,MAAM,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC,CAAC;gBAClE,CAAC,GACD,CACH,CACG,CACyB,CAClC,CAAC;AACJ,CAAC,CAAC"}