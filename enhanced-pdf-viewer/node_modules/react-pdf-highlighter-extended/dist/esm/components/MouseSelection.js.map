{"version": 3, "file": "MouseSelection.js", "sourceRoot": "", "sources": ["../../../src/components/MouseSelection.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,EAAiB,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC;AAE1E,OAAO,EAAE,SAAS,EAAE,kBAAkB,EAAE,aAAa,EAAE,MAAM,kBAAkB,CAAC;AAChF,OAAO,6BAA6B,CAAC;AAGrC,OAAO,EAAE,wBAAwB,EAAE,MAAM,oBAAoB,CAAC;AAC9D,OAAO,UAAU,MAAM,mBAAmB,CAAC;AAQ3C,MAAM,eAAe,GAAG,CAAC,KAAa,EAAE,GAAW,EAAQ,EAAE;IAC3D,OAAO;QACL,IAAI,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAC9B,GAAG,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;QAE7B,KAAK,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;QAChC,MAAM,EAAE,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC;KAClC,CAAC;AACJ,CAAC,CAAC;AAEF,MAAM,kBAAkB,GAAG,CACzB,SAAsB,EACtB,KAAa,EACb,KAAa,EACb,EAAE;IACF,MAAM,qBAAqB,GAAG,SAAS,CAAC,qBAAqB,EAAE,CAAC;IAChE,OAAO;QACL,CAAC,EAAE,KAAK,GAAG,qBAAqB,CAAC,IAAI,GAAG,SAAS,CAAC,UAAU;QAC5D,CAAC,EAAE,KAAK,GAAG,qBAAqB,CAAC,GAAG,GAAG,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,OAAO;KAC5E,CAAC;AACJ,CAAC,CAAC;AAmEF;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,cAAc,GAAG,CAAC,EAC7B,MAAM,EACN,WAAW,EACX,OAAO,EACP,WAAW,EACX,mBAAmB,EACnB,QAAQ,EACR,KAAK,GACe,EAAE,EAAE;IACxB,MAAM,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IACxD,MAAM,CAAC,GAAG,EAAE,MAAM,CAAC,GAAG,QAAQ,CAAgB,IAAI,CAAC,CAAC;IACpD,MAAM,CAAC,MAAM,EAAE,SAAS,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC;IAC5C,MAAM,OAAO,GAAG,MAAM,CAAwB,IAAI,CAAC,CAAC;IAEpD,6DAA6D;IAC7D,MAAM,cAAc,GAAG,MAAM,CAAqB,IAAI,CAAC,CAAC;IAExD,MAAM,KAAK,GAAG,GAAG,EAAE;QACjB,OAAO,IAAI,OAAO,EAAE,CAAC;QACrB,QAAQ,CAAC,IAAI,CAAC,CAAC;QACf,MAAM,CAAC,IAAI,CAAC,CAAC;QACb,SAAS,CAAC,KAAK,CAAC,CAAC;IACnB,CAAC,CAAC;IAEF,oCAAoC;IACpC,SAAS,CAAC,GAAG,EAAE;QACb,QAAQ,IAAI,QAAQ,CAAC,OAAO,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC,CAAC;QAC5C,IAAI,CAAC,OAAO,CAAC,OAAO;YAAE,OAAO;QAE7B,+BAA+B;QAC/B,MAAM,SAAS,GAAG,SAAS,CAAC,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAE3D,MAAM,aAAa,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC1C,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO;gBAAE,OAAO;YAEtD,MAAM,YAAY,GAAG,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAEjD,iEAAiE;YACjE,8CAA8C;YAC9C,MAAM,SAAS,GAAG,YAAY,CAAC,KAAK,IAAI,CAAC,IAAI,YAAY,CAAC,MAAM,IAAI,CAAC,CAAC;YAEtE,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC;gBAC/D,KAAK,EAAE,CAAC;gBACR,OAAO;YACT,CAAC;YAED,SAAS,CAAC,IAAI,CAAC,CAAC;YAEhB,MAAM,IAAI,GAAG,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC;YACxD,IAAI,CAAC,IAAI;gBAAE,OAAO;YAElB,MAAM,gBAAgB,GAAU;gBAC9B,GAAG,YAAY;gBACf,GAAG,EAAE,YAAY,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS;gBAC3C,IAAI,EAAE,YAAY,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU;gBAC9C,UAAU,EAAE,IAAI,CAAC,MAAM;aACxB,CAAC;YAEF,MAAM,gBAAgB,GAAqB;gBACzC,YAAY,EAAE,gBAAgB;gBAC9B,KAAK,EAAE,EAAE;aACV,CAAC;YAEF,MAAM,cAAc,GAAG,wBAAwB,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAE1E,MAAM,KAAK,GAAG,UAAU,CACtB,gBAAgB,EAChB,gBAAgB,CAAC,UAAU,EAC3B,MAAM,CACP,CAAC;YAEF,WAAW;gBACT,WAAW,CAAC,gBAAgB,EAAE,cAAc,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;QACvE,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC5C,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAC,KAAK,IAAI,MAAM;gBAAE,OAAO;YACjD,MAAM,CAAC,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;QAClE,CAAC,CAAC;QAEF,MAAM,eAAe,GAAG,CAAC,KAAiB,EAAE,EAAE;YAC5C,MAAM,WAAW,GAAG,CAAC,KAAiB,EAAE,EAAE,CACxC,mBAAmB,CAAC,KAAK,CAAC;gBAC1B,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;gBAC3B,OAAO,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC;YAEpD,iEAAiE;YACjE,MAAM,WAAW,GAAG,CAAC,KAAiB,EAAE,EAAE,CACxC,KAAK;gBACL,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,OAAO,CAAC,gCAAgC,CAAC,CAAC;YAErE,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;gBACxB,IAAI,WAAW,CAAC,KAAK,CAAC;oBAAE,KAAK,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,cAAc,CAAC,OAAO,GAAG,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;YACjD,WAAW,IAAI,WAAW,CAAC,KAAK,CAAC,CAAC;YAClC,QAAQ,CAAC,kBAAkB,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAClE,MAAM,CAAC,IAAI,CAAC,CAAC;YACb,SAAS,CAAC,KAAK,CAAC,CAAC;QACnB,CAAC,CAAC;QAEF;;;;;;;WAOG;QACH,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QACzD,SAAS,CAAC,gBAAgB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAEzD,QAAQ,CAAC,gBAAgB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QAEpD,OAAO,GAAG,EAAE;YACV,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC5D,SAAS,CAAC,mBAAmB,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;YAC5D,QAAQ,CAAC,mBAAmB,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;QACzD,CAAC,CAAC;IACJ,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC,CAAC;IAEjB,OAAO,CACL,6BAAK,SAAS,EAAC,0BAA0B,EAAC,GAAG,EAAE,OAAO,IACnD,KAAK,IAAI,GAAG,IAAI,CACf,6BACE,SAAS,EAAC,gBAAgB,EAC1B,KAAK,EAAE,EAAE,GAAG,eAAe,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE,GAAG,KAAK,EAAE,GACnD,CACH,CACG,CACP,CAAC;AACJ,CAAC,CAAC"}