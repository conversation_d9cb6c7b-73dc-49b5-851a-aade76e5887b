{"name": "react-draggable", "version": "4.4.6", "description": "React draggable component", "main": "build/cjs/cjs.js", "unpkg": "build/web/react-draggable.min.js", "scripts": {"test": "make test", "test-phantom": "make test-phantom", "test-debug": "karma start --browsers=Chrome --single-run=false --auto-watch=true", "test-firefox": "karma start --browsers=Firefox --single-run=false --auto-watch=true", "test-ie": "karma start --browsers=IE --single-run=false --auto-watch=true", "dev": "make dev", "build": "make clean build", "lint": "make lint", "flow": "flow"}, "files": ["/build", "/typings", "/web/react-draggable.min.js", "/web/react-draggable.min.js.map"], "typings": "./typings/index.d.ts", "types": "./typings/index.d.ts", "repository": {"type": "git", "url": "https://github.com/react-grid-layout/react-draggable.git"}, "keywords": ["react", "draggable", "react-component"], "author": "<PERSON>", "contributors": ["<PERSON> <<EMAIL>> (http://strml.net/)"], "license": "MIT", "bugs": {"url": "https://github.com/react-grid-layout/react-draggable/issues"}, "homepage": "https://github.com/react-grid-layout/react-draggable", "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/eslint-parser": "^7.22.15", "@babel/plugin-transform-class-properties": "^7.18.6", "@babel/plugin-transform-flow-comments": "^7.22.10", "@babel/preset-env": "^7.22.20", "@babel/preset-flow": "^7.22.15", "@babel/preset-react": "^7.22.15", "@types/node": "^20.7.0", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "assert": "^2.1.0", "babel-loader": "^9.1.3", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "eslint": "^8.50.0", "eslint-plugin-react": "^7.33.2", "flow-bin": "^0.217.0", "jasmine-core": "^5.1.1", "karma": "^6.4.2", "karma-chrome-launcher": "^3.2.0", "karma-cli": "2.0.0", "karma-firefox-launcher": "^2.1.2", "karma-ie-launcher": "^1.0.0", "karma-jasmine": "^5.1.0", "karma-phantomjs-launcher": "^1.0.4", "karma-phantomjs-shim": "^1.5.0", "karma-webpack": "^5.0.0", "lodash": "^4.17.4", "phantomjs-prebuilt": "^2.1.16", "pre-commit": "^1.2.2", "process": "^0.11.10", "puppeteer": "^21.3.5", "react": "^16.13.1", "react-dom": "^16.13.1", "react-frame-component": "^5.2.6", "react-test-renderer": "^16.13.1", "semver": "^7.5.4", "static-server": "^3.0.0", "typescript": "^5.2.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "resolutions": {"minimist": "^1.2.5"}, "precommit": ["lint", "test"], "dependencies": {"clsx": "^1.1.1", "prop-types": "^15.8.1"}, "peerDependencies": {"react": ">= 16.3.0", "react-dom": ">= 16.3.0"}}