cmd_Release/obj.target/canvas/src/color.o := c++ -o Release/obj.target/canvas/src/color.o ../src/color.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_JPEG' '-DHAVE_GIF' '-DHAVE_RSVG' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/src -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/v8/include -I../../nan -I/opt/local/include/cairo -I/opt/local/include -I/opt/local/include/glib-2.0 -I/opt/local/lib/glib-2.0/include -I/opt/local/include/pixman-1 -I/opt/local/include/freetype2 -I/opt/local/include/libpng16 -I/opt/local/include/pango-1.0 -I/opt/local/include/harfbuzz -I/opt/local/include/fribidi -I/opt/homebrew/include -I/opt/local/include/librsvg-2.0 -I/opt/local/include/gdk-pixbuf-2.0  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/color.o.d.raw   -c
Release/obj.target/canvas/src/color.o: ../src/color.cc ../src/color.h
../src/color.cc:
../src/color.h:
