cmd_Release/obj.target/canvas/src/CanvasRenderingContext2d.o := c++ -o Release/obj.target/canvas/src/CanvasRenderingContext2d.o ../src/CanvasRenderingContext2d.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_JPEG' '-DHAVE_GIF' '-DHAVE_RSVG' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/src -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/v8/include -I../../nan -I/opt/local/include/cairo -I/opt/local/include -I/opt/local/include/glib-2.0 -I/opt/local/lib/glib-2.0/include -I/opt/local/include/pixman-1 -I/opt/local/include/freetype2 -I/opt/local/include/libpng16 -I/opt/local/include/pango-1.0 -I/opt/local/include/harfbuzz -I/opt/local/include/fribidi -I/opt/homebrew/include -I/opt/local/include/librsvg-2.0 -I/opt/local/include/gdk-pixbuf-2.0  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/CanvasRenderingContext2d.o.d.raw   -c
Release/obj.target/canvas/src/CanvasRenderingContext2d.o: \
  ../src/CanvasRenderingContext2d.cc ../src/CanvasRenderingContext2d.h \
  /opt/local/include/cairo/cairo.h \
  /opt/local/include/cairo/cairo-version.h \
  /opt/local/include/cairo/cairo-features.h \
  /opt/local/include/cairo/cairo-deprecated.h ../src/Canvas.h \
  ../src/backend/Backend.h ../src/backend/../dll_visibility.h \
  ../../nan/nan.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/darwin.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-array-buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-local-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-handle-base.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-object.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-maybe.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-persistent-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-weak-callback-info.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-data.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-traced-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-container.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-context.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-snapshot.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-isolate.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-callbacks.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-promise.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-debug.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-script.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-memory-span.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-message.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-heap.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function-callback.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-statistics.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-unwinder.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-state-scope.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-date.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-exception.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-extension.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-external.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-template.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-initialization.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-source-location.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-json.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-locker.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask-queue.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive-object.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-proxy.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-regexp.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-typed-array.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value-serializer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-wasm.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_object_wrap.h \
  ../../nan/nan_callbacks.h ../../nan/nan_callbacks_12_inl.h \
  ../../nan/nan_maybe_43_inl.h ../../nan/nan_converters.h \
  ../../nan/nan_converters_43_inl.h ../../nan/nan_new.h \
  ../../nan/nan_implementation_12_inl.h \
  ../../nan/nan_persistent_12_inl.h ../../nan/nan_weak.h \
  ../../nan/nan_object_wrap.h ../../nan/nan_private.h \
  ../../nan/nan_typedarray_contents.h ../../nan/nan_json.h \
  ../../nan/nan_scriptorigin.h ../src/dll_visibility.h \
  /opt/local/include/pango-1.0/pango/pangocairo.h \
  /opt/local/include/pango-1.0/pango/pango.h \
  /opt/local/include/pango-1.0/pango/pango-attributes.h \
  /opt/local/include/pango-1.0/pango/pango-font.h \
  /opt/local/include/pango-1.0/pango/pango-coverage.h \
  /opt/local/include/glib-2.0/glib-object.h \
  /opt/local/include/glib-2.0/gobject/gbinding.h \
  /opt/local/include/glib-2.0/glib.h \
  /opt/local/include/glib-2.0/glib/galloca.h \
  /opt/local/include/glib-2.0/glib/gtypes.h \
  /opt/local/lib/glib-2.0/include/glibconfig.h \
  /opt/local/include/glib-2.0/glib/gmacros.h \
  /opt/local/include/glib-2.0/glib/gversionmacros.h \
  /opt/local/include/glib-2.0/glib/glib-visibility.h \
  /opt/local/include/glib-2.0/glib/garray.h \
  /opt/local/include/glib-2.0/glib/gasyncqueue.h \
  /opt/local/include/glib-2.0/glib/gthread.h \
  /opt/local/include/glib-2.0/glib/gatomic.h \
  /opt/local/include/glib-2.0/glib/glib-typeof.h \
  /opt/local/include/glib-2.0/glib/gerror.h \
  /opt/local/include/glib-2.0/glib/gquark.h \
  /opt/local/include/glib-2.0/glib/gutils.h \
  /opt/local/include/glib-2.0/glib/gbacktrace.h \
  /opt/local/include/glib-2.0/glib/gbase64.h \
  /opt/local/include/glib-2.0/glib/gbitlock.h \
  /opt/local/include/glib-2.0/glib/gbookmarkfile.h \
  /opt/local/include/glib-2.0/glib/gdatetime.h \
  /opt/local/include/glib-2.0/glib/gtimezone.h \
  /opt/local/include/glib-2.0/glib/gbytes.h \
  /opt/local/include/glib-2.0/glib/gcharset.h \
  /opt/local/include/glib-2.0/glib/gchecksum.h \
  /opt/local/include/glib-2.0/glib/gconvert.h \
  /opt/local/include/glib-2.0/glib/gdataset.h \
  /opt/local/include/glib-2.0/glib/gdate.h \
  /opt/local/include/glib-2.0/glib/gdir.h \
  /opt/local/include/glib-2.0/glib/genviron.h \
  /opt/local/include/glib-2.0/glib/gfileutils.h \
  /opt/local/include/glib-2.0/glib/ggettext.h \
  /opt/local/include/glib-2.0/glib/ghash.h \
  /opt/local/include/glib-2.0/glib/glist.h \
  /opt/local/include/glib-2.0/glib/gmem.h \
  /opt/local/include/glib-2.0/glib/gnode.h \
  /opt/local/include/glib-2.0/glib/ghmac.h \
  /opt/local/include/glib-2.0/glib/ghook.h \
  /opt/local/include/glib-2.0/glib/ghostutils.h \
  /opt/local/include/glib-2.0/glib/giochannel.h \
  /opt/local/include/glib-2.0/glib/gmain.h \
  /opt/local/include/glib-2.0/glib/gpoll.h \
  /opt/local/include/glib-2.0/glib/gslist.h \
  /opt/local/include/glib-2.0/glib/gstring.h \
  /opt/local/include/glib-2.0/glib/gunicode.h \
  /opt/local/include/glib-2.0/glib/gstrfuncs.h \
  /opt/local/include/glib-2.0/glib/gkeyfile.h \
  /opt/local/include/glib-2.0/glib/gmappedfile.h \
  /opt/local/include/glib-2.0/glib/gmarkup.h \
  /opt/local/include/glib-2.0/glib/gmessages.h \
  /opt/local/include/glib-2.0/glib/gvariant.h \
  /opt/local/include/glib-2.0/glib/gvarianttype.h \
  /opt/local/include/glib-2.0/glib/goption.h \
  /opt/local/include/glib-2.0/glib/gpathbuf.h \
  /opt/local/include/glib-2.0/glib/gpattern.h \
  /opt/local/include/glib-2.0/glib/gprimes.h \
  /opt/local/include/glib-2.0/glib/gqsort.h \
  /opt/local/include/glib-2.0/glib/gqueue.h \
  /opt/local/include/glib-2.0/glib/grand.h \
  /opt/local/include/glib-2.0/glib/grcbox.h \
  /opt/local/include/glib-2.0/glib/grefcount.h \
  /opt/local/include/glib-2.0/glib/grefstring.h \
  /opt/local/include/glib-2.0/glib/gregex.h \
  /opt/local/include/glib-2.0/glib/gscanner.h \
  /opt/local/include/glib-2.0/glib/gsequence.h \
  /opt/local/include/glib-2.0/glib/gshell.h \
  /opt/local/include/glib-2.0/glib/gslice.h \
  /opt/local/include/glib-2.0/glib/gspawn.h \
  /opt/local/include/glib-2.0/glib/gstringchunk.h \
  /opt/local/include/glib-2.0/glib/gstrvbuilder.h \
  /opt/local/include/glib-2.0/glib/gtestutils.h \
  /opt/local/include/glib-2.0/glib/gthreadpool.h \
  /opt/local/include/glib-2.0/glib/gtimer.h \
  /opt/local/include/glib-2.0/glib/gtrashstack.h \
  /opt/local/include/glib-2.0/glib/gtree.h \
  /opt/local/include/glib-2.0/glib/guri.h \
  /opt/local/include/glib-2.0/glib/guuid.h \
  /opt/local/include/glib-2.0/glib/gversion.h \
  /opt/local/include/glib-2.0/glib/deprecated/gallocator.h \
  /opt/local/include/glib-2.0/glib/deprecated/gcache.h \
  /opt/local/include/glib-2.0/glib/deprecated/gcompletion.h \
  /opt/local/include/glib-2.0/glib/deprecated/gmain.h \
  /opt/local/include/glib-2.0/glib/deprecated/grel.h \
  /opt/local/include/glib-2.0/glib/deprecated/gthread.h \
  /opt/local/include/glib-2.0/glib/glib-autocleanups.h \
  /opt/local/include/glib-2.0/gobject/gobject.h \
  /opt/local/include/glib-2.0/gobject/gtype.h \
  /opt/local/include/glib-2.0/gobject/gobject-visibility.h \
  /opt/local/include/glib-2.0/gobject/gvalue.h \
  /opt/local/include/glib-2.0/gobject/gparam.h \
  /opt/local/include/glib-2.0/gobject/gclosure.h \
  /opt/local/include/glib-2.0/gobject/gsignal.h \
  /opt/local/include/glib-2.0/gobject/gmarshal.h \
  /opt/local/include/glib-2.0/gobject/gboxed.h \
  /opt/local/include/glib-2.0/gobject/glib-types.h \
  /opt/local/include/glib-2.0/gobject/gbindinggroup.h \
  /opt/local/include/glib-2.0/gobject/genums.h \
  /opt/local/include/glib-2.0/gobject/glib-enumtypes.h \
  /opt/local/include/glib-2.0/gobject/gparamspecs.h \
  /opt/local/include/glib-2.0/gobject/gsignalgroup.h \
  /opt/local/include/glib-2.0/gobject/gsourceclosure.h \
  /opt/local/include/glib-2.0/gobject/gtypemodule.h \
  /opt/local/include/glib-2.0/gobject/gtypeplugin.h \
  /opt/local/include/glib-2.0/gobject/gvaluearray.h \
  /opt/local/include/glib-2.0/gobject/gvaluetypes.h \
  /opt/local/include/glib-2.0/gobject/gobject-autocleanups.h \
  /opt/local/include/pango-1.0/pango/pango-version-macros.h \
  /opt/local/include/pango-1.0/pango/pango-features.h \
  /opt/local/include/harfbuzz/hb.h /opt/local/include/harfbuzz/hb-blob.h \
  /opt/local/include/harfbuzz/hb-common.h \
  /opt/local/include/harfbuzz/hb-buffer.h \
  /opt/local/include/harfbuzz/hb-unicode.h \
  /opt/local/include/harfbuzz/hb-font.h \
  /opt/local/include/harfbuzz/hb-face.h \
  /opt/local/include/harfbuzz/hb-map.h \
  /opt/local/include/harfbuzz/hb-set.h \
  /opt/local/include/harfbuzz/hb-draw.h \
  /opt/local/include/harfbuzz/hb-paint.h \
  /opt/local/include/harfbuzz/hb-deprecated.h \
  /opt/local/include/harfbuzz/hb-shape.h \
  /opt/local/include/harfbuzz/hb-shape-plan.h \
  /opt/local/include/harfbuzz/hb-style.h \
  /opt/local/include/harfbuzz/hb-version.h \
  /opt/local/include/pango-1.0/pango/pango-types.h \
  /opt/local/include/pango-1.0/pango/pango-gravity.h \
  /opt/local/include/pango-1.0/pango/pango-matrix.h \
  /opt/local/include/pango-1.0/pango/pango-script.h \
  /opt/local/include/pango-1.0/pango/pango-language.h \
  /opt/local/include/pango-1.0/pango/pango-bidi-type.h \
  /opt/local/include/pango-1.0/pango/pango-direction.h \
  /opt/local/include/pango-1.0/pango/pango-color.h \
  /opt/local/include/pango-1.0/pango/pango-break.h \
  /opt/local/include/pango-1.0/pango/pango-item.h \
  /opt/local/include/pango-1.0/pango/pango-context.h \
  /opt/local/include/pango-1.0/pango/pango-fontmap.h \
  /opt/local/include/pango-1.0/pango/pango-fontset.h \
  /opt/local/include/pango-1.0/pango/pango-engine.h \
  /opt/local/include/pango-1.0/pango/pango-glyph.h \
  /opt/local/include/pango-1.0/pango/pango-enum-types.h \
  /opt/local/include/pango-1.0/pango/pango-fontset-simple.h \
  /opt/local/include/pango-1.0/pango/pango-glyph-item.h \
  /opt/local/include/pango-1.0/pango/pango-layout.h \
  /opt/local/include/pango-1.0/pango/pango-tabs.h \
  /opt/local/include/pango-1.0/pango/pango-markup.h \
  /opt/local/include/pango-1.0/pango/pango-renderer.h \
  /opt/local/include/pango-1.0/pango/pango-utils.h ../src/color.h \
  ../src/backend/ImageBackend.h /opt/local/include/cairo/cairo-pdf.h \
  ../src/CanvasGradient.h ../src/CanvasPattern.h ../src/Image.h \
  ../src/CanvasError.h /opt/local/include/jpeglib.h \
  /opt/local/include/jconfig.h /opt/local/include/jmorecfg.h \
  /opt/local/include/jerror.h /opt/local/include/gif_lib.h \
  /opt/local/include/librsvg-2.0/librsvg/rsvg.h \
  /opt/local/include/glib-2.0/gio/gio.h \
  /opt/local/include/glib-2.0/gio/giotypes.h \
  /opt/local/include/glib-2.0/gio/gioenums.h \
  /opt/local/include/glib-2.0/gio/gio-visibility.h \
  /opt/local/include/glib-2.0/gio/gaction.h \
  /opt/local/include/glib-2.0/gio/gactiongroup.h \
  /opt/local/include/glib-2.0/gio/gactiongroupexporter.h \
  /opt/local/include/glib-2.0/gio/gactionmap.h \
  /opt/local/include/glib-2.0/gio/gappinfo.h \
  /opt/local/include/glib-2.0/gio/gapplication.h \
  /opt/local/include/glib-2.0/gio/gapplicationcommandline.h \
  /opt/local/include/glib-2.0/gio/gasyncinitable.h \
  /opt/local/include/glib-2.0/gio/ginitable.h \
  /opt/local/include/glib-2.0/gio/gasyncresult.h \
  /opt/local/include/glib-2.0/gio/gbufferedinputstream.h \
  /opt/local/include/glib-2.0/gio/gfilterinputstream.h \
  /opt/local/include/glib-2.0/gio/ginputstream.h \
  /opt/local/include/glib-2.0/gio/gbufferedoutputstream.h \
  /opt/local/include/glib-2.0/gio/gfilteroutputstream.h \
  /opt/local/include/glib-2.0/gio/goutputstream.h \
  /opt/local/include/glib-2.0/gio/gbytesicon.h \
  /opt/local/include/glib-2.0/gio/gcancellable.h \
  /opt/local/include/glib-2.0/gio/gcharsetconverter.h \
  /opt/local/include/glib-2.0/gio/gconverter.h \
  /opt/local/include/glib-2.0/gio/gcontenttype.h \
  /opt/local/include/glib-2.0/gio/gconverterinputstream.h \
  /opt/local/include/glib-2.0/gio/gconverteroutputstream.h \
  /opt/local/include/glib-2.0/gio/gcredentials.h \
  /opt/local/include/glib-2.0/gio/gdatagrambased.h \
  /opt/local/include/glib-2.0/gio/gdatainputstream.h \
  /opt/local/include/glib-2.0/gio/gdataoutputstream.h \
  /opt/local/include/glib-2.0/gio/gdbusactiongroup.h \
  /opt/local/include/glib-2.0/gio/gdbusaddress.h \
  /opt/local/include/glib-2.0/gio/gdbusauthobserver.h \
  /opt/local/include/glib-2.0/gio/gdbusconnection.h \
  /opt/local/include/glib-2.0/gio/gdbuserror.h \
  /opt/local/include/glib-2.0/gio/gdbusinterface.h \
  /opt/local/include/glib-2.0/gio/gdbusinterfaceskeleton.h \
  /opt/local/include/glib-2.0/gio/gdbusintrospection.h \
  /opt/local/include/glib-2.0/gio/gdbusmenumodel.h \
  /opt/local/include/glib-2.0/gio/gdbusmessage.h \
  /opt/local/include/glib-2.0/gio/gdbusmethodinvocation.h \
  /opt/local/include/glib-2.0/gio/gdbusnameowning.h \
  /opt/local/include/glib-2.0/gio/gdbusnamewatching.h \
  /opt/local/include/glib-2.0/gio/gdbusobject.h \
  /opt/local/include/glib-2.0/gio/gdbusobjectmanager.h \
  /opt/local/include/glib-2.0/gio/gdbusobjectmanagerclient.h \
  /opt/local/include/glib-2.0/gio/gdbusobjectmanagerserver.h \
  /opt/local/include/glib-2.0/gio/gdbusobjectproxy.h \
  /opt/local/include/glib-2.0/gio/gdbusobjectskeleton.h \
  /opt/local/include/glib-2.0/gio/gdbusproxy.h \
  /opt/local/include/glib-2.0/gio/gdbusserver.h \
  /opt/local/include/glib-2.0/gio/gdbusutils.h \
  /opt/local/include/glib-2.0/gio/gdebugcontroller.h \
  /opt/local/include/glib-2.0/gio/gdebugcontrollerdbus.h \
  /opt/local/include/glib-2.0/gio/gdrive.h \
  /opt/local/include/glib-2.0/gio/gdtlsclientconnection.h \
  /opt/local/include/glib-2.0/gio/gdtlsconnection.h \
  /opt/local/include/glib-2.0/gio/gdtlsserverconnection.h \
  /opt/local/include/glib-2.0/gio/gemblemedicon.h \
  /opt/local/include/glib-2.0/gio/gicon.h \
  /opt/local/include/glib-2.0/gio/gemblem.h \
  /opt/local/include/glib-2.0/gio/gfile.h \
  /opt/local/include/glib-2.0/gio/gfileattribute.h \
  /opt/local/include/glib-2.0/gio/gfileenumerator.h \
  /opt/local/include/glib-2.0/gio/gfileicon.h \
  /opt/local/include/glib-2.0/gio/gfileinfo.h \
  /opt/local/include/glib-2.0/gio/gfileinputstream.h \
  /opt/local/include/glib-2.0/gio/gfileiostream.h \
  /opt/local/include/glib-2.0/gio/giostream.h \
  /opt/local/include/glib-2.0/gio/gioerror.h \
  /opt/local/include/glib-2.0/gio/gfilemonitor.h \
  /opt/local/include/glib-2.0/gio/gfilenamecompleter.h \
  /opt/local/include/glib-2.0/gio/gfileoutputstream.h \
  /opt/local/include/glib-2.0/gio/ginetaddress.h \
  /opt/local/include/glib-2.0/gio/ginetaddressmask.h \
  /opt/local/include/glib-2.0/gio/ginetsocketaddress.h \
  /opt/local/include/glib-2.0/gio/gsocketaddress.h \
  /opt/local/include/glib-2.0/gio/gioenumtypes.h \
  /opt/local/include/glib-2.0/gio/giomodule.h \
  /opt/local/include/glib-2.0/gmodule.h \
  /opt/local/include/glib-2.0/gmodule/gmodule-visibility.h \
  /opt/local/include/glib-2.0/gio/gioscheduler.h \
  /opt/local/include/glib-2.0/gio/glistmodel.h \
  /opt/local/include/glib-2.0/gio/gliststore.h \
  /opt/local/include/glib-2.0/gio/gloadableicon.h \
  /opt/local/include/glib-2.0/gio/gmemoryinputstream.h \
  /opt/local/include/glib-2.0/gio/gmemorymonitor.h \
  /opt/local/include/glib-2.0/gio/gmemoryoutputstream.h \
  /opt/local/include/glib-2.0/gio/gmenu.h \
  /opt/local/include/glib-2.0/gio/gmenumodel.h \
  /opt/local/include/glib-2.0/gio/gmenuexporter.h \
  /opt/local/include/glib-2.0/gio/gmount.h \
  /opt/local/include/glib-2.0/gio/gmountoperation.h \
  /opt/local/include/glib-2.0/gio/gnativesocketaddress.h \
  /opt/local/include/glib-2.0/gio/gnativevolumemonitor.h \
  /opt/local/include/glib-2.0/gio/gvolumemonitor.h \
  /opt/local/include/glib-2.0/gio/gnetworkaddress.h \
  /opt/local/include/glib-2.0/gio/gnetworkmonitor.h \
  /opt/local/include/glib-2.0/gio/gnetworkservice.h \
  /opt/local/include/glib-2.0/gio/gnotification.h \
  /opt/local/include/glib-2.0/gio/gpermission.h \
  /opt/local/include/glib-2.0/gio/gpollableinputstream.h \
  /opt/local/include/glib-2.0/gio/gpollableoutputstream.h \
  /opt/local/include/glib-2.0/gio/gpollableutils.h \
  /opt/local/include/glib-2.0/gio/gpowerprofilemonitor.h \
  /opt/local/include/glib-2.0/gio/gpropertyaction.h \
  /opt/local/include/glib-2.0/gio/gproxy.h \
  /opt/local/include/glib-2.0/gio/gproxyaddress.h \
  /opt/local/include/glib-2.0/gio/gproxyaddressenumerator.h \
  /opt/local/include/glib-2.0/gio/gsocketaddressenumerator.h \
  /opt/local/include/glib-2.0/gio/gproxyresolver.h \
  /opt/local/include/glib-2.0/gio/gremoteactiongroup.h \
  /opt/local/include/glib-2.0/gio/gresolver.h \
  /opt/local/include/glib-2.0/gio/gresource.h \
  /opt/local/include/glib-2.0/gio/gseekable.h \
  /opt/local/include/glib-2.0/gio/gsettings.h \
  /opt/local/include/glib-2.0/gio/gsettingsschema.h \
  /opt/local/include/glib-2.0/gio/gsimpleaction.h \
  /opt/local/include/glib-2.0/gio/gsimpleactiongroup.h \
  /opt/local/include/glib-2.0/gio/gsimpleasyncresult.h \
  /opt/local/include/glib-2.0/gio/gsimpleiostream.h \
  /opt/local/include/glib-2.0/gio/gsimplepermission.h \
  /opt/local/include/glib-2.0/gio/gsimpleproxyresolver.h \
  /opt/local/include/glib-2.0/gio/gsocket.h \
  /opt/local/include/glib-2.0/gio/gsocketclient.h \
  /opt/local/include/glib-2.0/gio/gsocketconnectable.h \
  /opt/local/include/glib-2.0/gio/gsocketconnection.h \
  /opt/local/include/glib-2.0/gio/gsocketcontrolmessage.h \
  /opt/local/include/glib-2.0/gio/gsocketlistener.h \
  /opt/local/include/glib-2.0/gio/gsocketservice.h \
  /opt/local/include/glib-2.0/gio/gsrvtarget.h \
  /opt/local/include/glib-2.0/gio/gsubprocess.h \
  /opt/local/include/glib-2.0/gio/gsubprocesslauncher.h \
  /opt/local/include/glib-2.0/gio/gtask.h \
  /opt/local/include/glib-2.0/gio/gtcpconnection.h \
  /opt/local/include/glib-2.0/gio/gtcpwrapperconnection.h \
  /opt/local/include/glib-2.0/gio/gtestdbus.h \
  /opt/local/include/glib-2.0/gio/gthemedicon.h \
  /opt/local/include/glib-2.0/gio/gthreadedsocketservice.h \
  /opt/local/include/glib-2.0/gio/gtlsbackend.h \
  /opt/local/include/glib-2.0/gio/gtlscertificate.h \
  /opt/local/include/glib-2.0/gio/gtlsclientconnection.h \
  /opt/local/include/glib-2.0/gio/gtlsconnection.h \
  /opt/local/include/glib-2.0/gio/gtlsdatabase.h \
  /opt/local/include/glib-2.0/gio/gtlsfiledatabase.h \
  /opt/local/include/glib-2.0/gio/gtlsinteraction.h \
  /opt/local/include/glib-2.0/gio/gtlspassword.h \
  /opt/local/include/glib-2.0/gio/gtlsserverconnection.h \
  /opt/local/include/glib-2.0/gio/gunixconnection.h \
  /opt/local/include/glib-2.0/gio/gunixcredentialsmessage.h \
  /opt/local/include/glib-2.0/gio/gunixfdlist.h \
  /opt/local/include/glib-2.0/gio/gunixsocketaddress.h \
  /opt/local/include/glib-2.0/gio/gvfs.h \
  /opt/local/include/glib-2.0/gio/gvolume.h \
  /opt/local/include/glib-2.0/gio/gzlibcompressor.h \
  /opt/local/include/glib-2.0/gio/gzlibdecompressor.h \
  /opt/local/include/glib-2.0/gio/gio-autocleanups.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-macros.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-features.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-core.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-transform.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-animation.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-simple-anim.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-io.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-loader.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-enum-types.h \
  /opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-autocleanups.h \
  /opt/local/include/librsvg-2.0/librsvg/rsvg-features.h \
  /opt/local/include/librsvg-2.0/librsvg/rsvg-version.h \
  /opt/local/include/librsvg-2.0/librsvg/rsvg-cairo.h ../src/ImageData.h \
  ../src/Point.h ../src/Util.h
../src/CanvasRenderingContext2d.cc:
../src/CanvasRenderingContext2d.h:
/opt/local/include/cairo/cairo.h:
/opt/local/include/cairo/cairo-version.h:
/opt/local/include/cairo/cairo-features.h:
/opt/local/include/cairo/cairo-deprecated.h:
../src/Canvas.h:
../src/backend/Backend.h:
../src/backend/../dll_visibility.h:
../../nan/nan.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/darwin.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-array-buffer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-local-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-handle-base.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-object.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-maybe.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-persistent-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-weak-callback-info.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-data.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-traced-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-container.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-context.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-snapshot.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-isolate.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-callbacks.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-promise.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-debug.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-script.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-memory-span.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-message.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-heap.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function-callback.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-statistics.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-unwinder.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-state-scope.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-date.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-exception.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-extension.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-external.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-template.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-initialization.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-source-location.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-json.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-locker.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask-queue.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive-object.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-proxy.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-regexp.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-typed-array.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value-serializer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-wasm.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_buffer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_object_wrap.h:
../../nan/nan_callbacks.h:
../../nan/nan_callbacks_12_inl.h:
../../nan/nan_maybe_43_inl.h:
../../nan/nan_converters.h:
../../nan/nan_converters_43_inl.h:
../../nan/nan_new.h:
../../nan/nan_implementation_12_inl.h:
../../nan/nan_persistent_12_inl.h:
../../nan/nan_weak.h:
../../nan/nan_object_wrap.h:
../../nan/nan_private.h:
../../nan/nan_typedarray_contents.h:
../../nan/nan_json.h:
../../nan/nan_scriptorigin.h:
../src/dll_visibility.h:
/opt/local/include/pango-1.0/pango/pangocairo.h:
/opt/local/include/pango-1.0/pango/pango.h:
/opt/local/include/pango-1.0/pango/pango-attributes.h:
/opt/local/include/pango-1.0/pango/pango-font.h:
/opt/local/include/pango-1.0/pango/pango-coverage.h:
/opt/local/include/glib-2.0/glib-object.h:
/opt/local/include/glib-2.0/gobject/gbinding.h:
/opt/local/include/glib-2.0/glib.h:
/opt/local/include/glib-2.0/glib/galloca.h:
/opt/local/include/glib-2.0/glib/gtypes.h:
/opt/local/lib/glib-2.0/include/glibconfig.h:
/opt/local/include/glib-2.0/glib/gmacros.h:
/opt/local/include/glib-2.0/glib/gversionmacros.h:
/opt/local/include/glib-2.0/glib/glib-visibility.h:
/opt/local/include/glib-2.0/glib/garray.h:
/opt/local/include/glib-2.0/glib/gasyncqueue.h:
/opt/local/include/glib-2.0/glib/gthread.h:
/opt/local/include/glib-2.0/glib/gatomic.h:
/opt/local/include/glib-2.0/glib/glib-typeof.h:
/opt/local/include/glib-2.0/glib/gerror.h:
/opt/local/include/glib-2.0/glib/gquark.h:
/opt/local/include/glib-2.0/glib/gutils.h:
/opt/local/include/glib-2.0/glib/gbacktrace.h:
/opt/local/include/glib-2.0/glib/gbase64.h:
/opt/local/include/glib-2.0/glib/gbitlock.h:
/opt/local/include/glib-2.0/glib/gbookmarkfile.h:
/opt/local/include/glib-2.0/glib/gdatetime.h:
/opt/local/include/glib-2.0/glib/gtimezone.h:
/opt/local/include/glib-2.0/glib/gbytes.h:
/opt/local/include/glib-2.0/glib/gcharset.h:
/opt/local/include/glib-2.0/glib/gchecksum.h:
/opt/local/include/glib-2.0/glib/gconvert.h:
/opt/local/include/glib-2.0/glib/gdataset.h:
/opt/local/include/glib-2.0/glib/gdate.h:
/opt/local/include/glib-2.0/glib/gdir.h:
/opt/local/include/glib-2.0/glib/genviron.h:
/opt/local/include/glib-2.0/glib/gfileutils.h:
/opt/local/include/glib-2.0/glib/ggettext.h:
/opt/local/include/glib-2.0/glib/ghash.h:
/opt/local/include/glib-2.0/glib/glist.h:
/opt/local/include/glib-2.0/glib/gmem.h:
/opt/local/include/glib-2.0/glib/gnode.h:
/opt/local/include/glib-2.0/glib/ghmac.h:
/opt/local/include/glib-2.0/glib/ghook.h:
/opt/local/include/glib-2.0/glib/ghostutils.h:
/opt/local/include/glib-2.0/glib/giochannel.h:
/opt/local/include/glib-2.0/glib/gmain.h:
/opt/local/include/glib-2.0/glib/gpoll.h:
/opt/local/include/glib-2.0/glib/gslist.h:
/opt/local/include/glib-2.0/glib/gstring.h:
/opt/local/include/glib-2.0/glib/gunicode.h:
/opt/local/include/glib-2.0/glib/gstrfuncs.h:
/opt/local/include/glib-2.0/glib/gkeyfile.h:
/opt/local/include/glib-2.0/glib/gmappedfile.h:
/opt/local/include/glib-2.0/glib/gmarkup.h:
/opt/local/include/glib-2.0/glib/gmessages.h:
/opt/local/include/glib-2.0/glib/gvariant.h:
/opt/local/include/glib-2.0/glib/gvarianttype.h:
/opt/local/include/glib-2.0/glib/goption.h:
/opt/local/include/glib-2.0/glib/gpathbuf.h:
/opt/local/include/glib-2.0/glib/gpattern.h:
/opt/local/include/glib-2.0/glib/gprimes.h:
/opt/local/include/glib-2.0/glib/gqsort.h:
/opt/local/include/glib-2.0/glib/gqueue.h:
/opt/local/include/glib-2.0/glib/grand.h:
/opt/local/include/glib-2.0/glib/grcbox.h:
/opt/local/include/glib-2.0/glib/grefcount.h:
/opt/local/include/glib-2.0/glib/grefstring.h:
/opt/local/include/glib-2.0/glib/gregex.h:
/opt/local/include/glib-2.0/glib/gscanner.h:
/opt/local/include/glib-2.0/glib/gsequence.h:
/opt/local/include/glib-2.0/glib/gshell.h:
/opt/local/include/glib-2.0/glib/gslice.h:
/opt/local/include/glib-2.0/glib/gspawn.h:
/opt/local/include/glib-2.0/glib/gstringchunk.h:
/opt/local/include/glib-2.0/glib/gstrvbuilder.h:
/opt/local/include/glib-2.0/glib/gtestutils.h:
/opt/local/include/glib-2.0/glib/gthreadpool.h:
/opt/local/include/glib-2.0/glib/gtimer.h:
/opt/local/include/glib-2.0/glib/gtrashstack.h:
/opt/local/include/glib-2.0/glib/gtree.h:
/opt/local/include/glib-2.0/glib/guri.h:
/opt/local/include/glib-2.0/glib/guuid.h:
/opt/local/include/glib-2.0/glib/gversion.h:
/opt/local/include/glib-2.0/glib/deprecated/gallocator.h:
/opt/local/include/glib-2.0/glib/deprecated/gcache.h:
/opt/local/include/glib-2.0/glib/deprecated/gcompletion.h:
/opt/local/include/glib-2.0/glib/deprecated/gmain.h:
/opt/local/include/glib-2.0/glib/deprecated/grel.h:
/opt/local/include/glib-2.0/glib/deprecated/gthread.h:
/opt/local/include/glib-2.0/glib/glib-autocleanups.h:
/opt/local/include/glib-2.0/gobject/gobject.h:
/opt/local/include/glib-2.0/gobject/gtype.h:
/opt/local/include/glib-2.0/gobject/gobject-visibility.h:
/opt/local/include/glib-2.0/gobject/gvalue.h:
/opt/local/include/glib-2.0/gobject/gparam.h:
/opt/local/include/glib-2.0/gobject/gclosure.h:
/opt/local/include/glib-2.0/gobject/gsignal.h:
/opt/local/include/glib-2.0/gobject/gmarshal.h:
/opt/local/include/glib-2.0/gobject/gboxed.h:
/opt/local/include/glib-2.0/gobject/glib-types.h:
/opt/local/include/glib-2.0/gobject/gbindinggroup.h:
/opt/local/include/glib-2.0/gobject/genums.h:
/opt/local/include/glib-2.0/gobject/glib-enumtypes.h:
/opt/local/include/glib-2.0/gobject/gparamspecs.h:
/opt/local/include/glib-2.0/gobject/gsignalgroup.h:
/opt/local/include/glib-2.0/gobject/gsourceclosure.h:
/opt/local/include/glib-2.0/gobject/gtypemodule.h:
/opt/local/include/glib-2.0/gobject/gtypeplugin.h:
/opt/local/include/glib-2.0/gobject/gvaluearray.h:
/opt/local/include/glib-2.0/gobject/gvaluetypes.h:
/opt/local/include/glib-2.0/gobject/gobject-autocleanups.h:
/opt/local/include/pango-1.0/pango/pango-version-macros.h:
/opt/local/include/pango-1.0/pango/pango-features.h:
/opt/local/include/harfbuzz/hb.h:
/opt/local/include/harfbuzz/hb-blob.h:
/opt/local/include/harfbuzz/hb-common.h:
/opt/local/include/harfbuzz/hb-buffer.h:
/opt/local/include/harfbuzz/hb-unicode.h:
/opt/local/include/harfbuzz/hb-font.h:
/opt/local/include/harfbuzz/hb-face.h:
/opt/local/include/harfbuzz/hb-map.h:
/opt/local/include/harfbuzz/hb-set.h:
/opt/local/include/harfbuzz/hb-draw.h:
/opt/local/include/harfbuzz/hb-paint.h:
/opt/local/include/harfbuzz/hb-deprecated.h:
/opt/local/include/harfbuzz/hb-shape.h:
/opt/local/include/harfbuzz/hb-shape-plan.h:
/opt/local/include/harfbuzz/hb-style.h:
/opt/local/include/harfbuzz/hb-version.h:
/opt/local/include/pango-1.0/pango/pango-types.h:
/opt/local/include/pango-1.0/pango/pango-gravity.h:
/opt/local/include/pango-1.0/pango/pango-matrix.h:
/opt/local/include/pango-1.0/pango/pango-script.h:
/opt/local/include/pango-1.0/pango/pango-language.h:
/opt/local/include/pango-1.0/pango/pango-bidi-type.h:
/opt/local/include/pango-1.0/pango/pango-direction.h:
/opt/local/include/pango-1.0/pango/pango-color.h:
/opt/local/include/pango-1.0/pango/pango-break.h:
/opt/local/include/pango-1.0/pango/pango-item.h:
/opt/local/include/pango-1.0/pango/pango-context.h:
/opt/local/include/pango-1.0/pango/pango-fontmap.h:
/opt/local/include/pango-1.0/pango/pango-fontset.h:
/opt/local/include/pango-1.0/pango/pango-engine.h:
/opt/local/include/pango-1.0/pango/pango-glyph.h:
/opt/local/include/pango-1.0/pango/pango-enum-types.h:
/opt/local/include/pango-1.0/pango/pango-fontset-simple.h:
/opt/local/include/pango-1.0/pango/pango-glyph-item.h:
/opt/local/include/pango-1.0/pango/pango-layout.h:
/opt/local/include/pango-1.0/pango/pango-tabs.h:
/opt/local/include/pango-1.0/pango/pango-markup.h:
/opt/local/include/pango-1.0/pango/pango-renderer.h:
/opt/local/include/pango-1.0/pango/pango-utils.h:
../src/color.h:
../src/backend/ImageBackend.h:
/opt/local/include/cairo/cairo-pdf.h:
../src/CanvasGradient.h:
../src/CanvasPattern.h:
../src/Image.h:
../src/CanvasError.h:
/opt/local/include/jpeglib.h:
/opt/local/include/jconfig.h:
/opt/local/include/jmorecfg.h:
/opt/local/include/jerror.h:
/opt/local/include/gif_lib.h:
/opt/local/include/librsvg-2.0/librsvg/rsvg.h:
/opt/local/include/glib-2.0/gio/gio.h:
/opt/local/include/glib-2.0/gio/giotypes.h:
/opt/local/include/glib-2.0/gio/gioenums.h:
/opt/local/include/glib-2.0/gio/gio-visibility.h:
/opt/local/include/glib-2.0/gio/gaction.h:
/opt/local/include/glib-2.0/gio/gactiongroup.h:
/opt/local/include/glib-2.0/gio/gactiongroupexporter.h:
/opt/local/include/glib-2.0/gio/gactionmap.h:
/opt/local/include/glib-2.0/gio/gappinfo.h:
/opt/local/include/glib-2.0/gio/gapplication.h:
/opt/local/include/glib-2.0/gio/gapplicationcommandline.h:
/opt/local/include/glib-2.0/gio/gasyncinitable.h:
/opt/local/include/glib-2.0/gio/ginitable.h:
/opt/local/include/glib-2.0/gio/gasyncresult.h:
/opt/local/include/glib-2.0/gio/gbufferedinputstream.h:
/opt/local/include/glib-2.0/gio/gfilterinputstream.h:
/opt/local/include/glib-2.0/gio/ginputstream.h:
/opt/local/include/glib-2.0/gio/gbufferedoutputstream.h:
/opt/local/include/glib-2.0/gio/gfilteroutputstream.h:
/opt/local/include/glib-2.0/gio/goutputstream.h:
/opt/local/include/glib-2.0/gio/gbytesicon.h:
/opt/local/include/glib-2.0/gio/gcancellable.h:
/opt/local/include/glib-2.0/gio/gcharsetconverter.h:
/opt/local/include/glib-2.0/gio/gconverter.h:
/opt/local/include/glib-2.0/gio/gcontenttype.h:
/opt/local/include/glib-2.0/gio/gconverterinputstream.h:
/opt/local/include/glib-2.0/gio/gconverteroutputstream.h:
/opt/local/include/glib-2.0/gio/gcredentials.h:
/opt/local/include/glib-2.0/gio/gdatagrambased.h:
/opt/local/include/glib-2.0/gio/gdatainputstream.h:
/opt/local/include/glib-2.0/gio/gdataoutputstream.h:
/opt/local/include/glib-2.0/gio/gdbusactiongroup.h:
/opt/local/include/glib-2.0/gio/gdbusaddress.h:
/opt/local/include/glib-2.0/gio/gdbusauthobserver.h:
/opt/local/include/glib-2.0/gio/gdbusconnection.h:
/opt/local/include/glib-2.0/gio/gdbuserror.h:
/opt/local/include/glib-2.0/gio/gdbusinterface.h:
/opt/local/include/glib-2.0/gio/gdbusinterfaceskeleton.h:
/opt/local/include/glib-2.0/gio/gdbusintrospection.h:
/opt/local/include/glib-2.0/gio/gdbusmenumodel.h:
/opt/local/include/glib-2.0/gio/gdbusmessage.h:
/opt/local/include/glib-2.0/gio/gdbusmethodinvocation.h:
/opt/local/include/glib-2.0/gio/gdbusnameowning.h:
/opt/local/include/glib-2.0/gio/gdbusnamewatching.h:
/opt/local/include/glib-2.0/gio/gdbusobject.h:
/opt/local/include/glib-2.0/gio/gdbusobjectmanager.h:
/opt/local/include/glib-2.0/gio/gdbusobjectmanagerclient.h:
/opt/local/include/glib-2.0/gio/gdbusobjectmanagerserver.h:
/opt/local/include/glib-2.0/gio/gdbusobjectproxy.h:
/opt/local/include/glib-2.0/gio/gdbusobjectskeleton.h:
/opt/local/include/glib-2.0/gio/gdbusproxy.h:
/opt/local/include/glib-2.0/gio/gdbusserver.h:
/opt/local/include/glib-2.0/gio/gdbusutils.h:
/opt/local/include/glib-2.0/gio/gdebugcontroller.h:
/opt/local/include/glib-2.0/gio/gdebugcontrollerdbus.h:
/opt/local/include/glib-2.0/gio/gdrive.h:
/opt/local/include/glib-2.0/gio/gdtlsclientconnection.h:
/opt/local/include/glib-2.0/gio/gdtlsconnection.h:
/opt/local/include/glib-2.0/gio/gdtlsserverconnection.h:
/opt/local/include/glib-2.0/gio/gemblemedicon.h:
/opt/local/include/glib-2.0/gio/gicon.h:
/opt/local/include/glib-2.0/gio/gemblem.h:
/opt/local/include/glib-2.0/gio/gfile.h:
/opt/local/include/glib-2.0/gio/gfileattribute.h:
/opt/local/include/glib-2.0/gio/gfileenumerator.h:
/opt/local/include/glib-2.0/gio/gfileicon.h:
/opt/local/include/glib-2.0/gio/gfileinfo.h:
/opt/local/include/glib-2.0/gio/gfileinputstream.h:
/opt/local/include/glib-2.0/gio/gfileiostream.h:
/opt/local/include/glib-2.0/gio/giostream.h:
/opt/local/include/glib-2.0/gio/gioerror.h:
/opt/local/include/glib-2.0/gio/gfilemonitor.h:
/opt/local/include/glib-2.0/gio/gfilenamecompleter.h:
/opt/local/include/glib-2.0/gio/gfileoutputstream.h:
/opt/local/include/glib-2.0/gio/ginetaddress.h:
/opt/local/include/glib-2.0/gio/ginetaddressmask.h:
/opt/local/include/glib-2.0/gio/ginetsocketaddress.h:
/opt/local/include/glib-2.0/gio/gsocketaddress.h:
/opt/local/include/glib-2.0/gio/gioenumtypes.h:
/opt/local/include/glib-2.0/gio/giomodule.h:
/opt/local/include/glib-2.0/gmodule.h:
/opt/local/include/glib-2.0/gmodule/gmodule-visibility.h:
/opt/local/include/glib-2.0/gio/gioscheduler.h:
/opt/local/include/glib-2.0/gio/glistmodel.h:
/opt/local/include/glib-2.0/gio/gliststore.h:
/opt/local/include/glib-2.0/gio/gloadableicon.h:
/opt/local/include/glib-2.0/gio/gmemoryinputstream.h:
/opt/local/include/glib-2.0/gio/gmemorymonitor.h:
/opt/local/include/glib-2.0/gio/gmemoryoutputstream.h:
/opt/local/include/glib-2.0/gio/gmenu.h:
/opt/local/include/glib-2.0/gio/gmenumodel.h:
/opt/local/include/glib-2.0/gio/gmenuexporter.h:
/opt/local/include/glib-2.0/gio/gmount.h:
/opt/local/include/glib-2.0/gio/gmountoperation.h:
/opt/local/include/glib-2.0/gio/gnativesocketaddress.h:
/opt/local/include/glib-2.0/gio/gnativevolumemonitor.h:
/opt/local/include/glib-2.0/gio/gvolumemonitor.h:
/opt/local/include/glib-2.0/gio/gnetworkaddress.h:
/opt/local/include/glib-2.0/gio/gnetworkmonitor.h:
/opt/local/include/glib-2.0/gio/gnetworkservice.h:
/opt/local/include/glib-2.0/gio/gnotification.h:
/opt/local/include/glib-2.0/gio/gpermission.h:
/opt/local/include/glib-2.0/gio/gpollableinputstream.h:
/opt/local/include/glib-2.0/gio/gpollableoutputstream.h:
/opt/local/include/glib-2.0/gio/gpollableutils.h:
/opt/local/include/glib-2.0/gio/gpowerprofilemonitor.h:
/opt/local/include/glib-2.0/gio/gpropertyaction.h:
/opt/local/include/glib-2.0/gio/gproxy.h:
/opt/local/include/glib-2.0/gio/gproxyaddress.h:
/opt/local/include/glib-2.0/gio/gproxyaddressenumerator.h:
/opt/local/include/glib-2.0/gio/gsocketaddressenumerator.h:
/opt/local/include/glib-2.0/gio/gproxyresolver.h:
/opt/local/include/glib-2.0/gio/gremoteactiongroup.h:
/opt/local/include/glib-2.0/gio/gresolver.h:
/opt/local/include/glib-2.0/gio/gresource.h:
/opt/local/include/glib-2.0/gio/gseekable.h:
/opt/local/include/glib-2.0/gio/gsettings.h:
/opt/local/include/glib-2.0/gio/gsettingsschema.h:
/opt/local/include/glib-2.0/gio/gsimpleaction.h:
/opt/local/include/glib-2.0/gio/gsimpleactiongroup.h:
/opt/local/include/glib-2.0/gio/gsimpleasyncresult.h:
/opt/local/include/glib-2.0/gio/gsimpleiostream.h:
/opt/local/include/glib-2.0/gio/gsimplepermission.h:
/opt/local/include/glib-2.0/gio/gsimpleproxyresolver.h:
/opt/local/include/glib-2.0/gio/gsocket.h:
/opt/local/include/glib-2.0/gio/gsocketclient.h:
/opt/local/include/glib-2.0/gio/gsocketconnectable.h:
/opt/local/include/glib-2.0/gio/gsocketconnection.h:
/opt/local/include/glib-2.0/gio/gsocketcontrolmessage.h:
/opt/local/include/glib-2.0/gio/gsocketlistener.h:
/opt/local/include/glib-2.0/gio/gsocketservice.h:
/opt/local/include/glib-2.0/gio/gsrvtarget.h:
/opt/local/include/glib-2.0/gio/gsubprocess.h:
/opt/local/include/glib-2.0/gio/gsubprocesslauncher.h:
/opt/local/include/glib-2.0/gio/gtask.h:
/opt/local/include/glib-2.0/gio/gtcpconnection.h:
/opt/local/include/glib-2.0/gio/gtcpwrapperconnection.h:
/opt/local/include/glib-2.0/gio/gtestdbus.h:
/opt/local/include/glib-2.0/gio/gthemedicon.h:
/opt/local/include/glib-2.0/gio/gthreadedsocketservice.h:
/opt/local/include/glib-2.0/gio/gtlsbackend.h:
/opt/local/include/glib-2.0/gio/gtlscertificate.h:
/opt/local/include/glib-2.0/gio/gtlsclientconnection.h:
/opt/local/include/glib-2.0/gio/gtlsconnection.h:
/opt/local/include/glib-2.0/gio/gtlsdatabase.h:
/opt/local/include/glib-2.0/gio/gtlsfiledatabase.h:
/opt/local/include/glib-2.0/gio/gtlsinteraction.h:
/opt/local/include/glib-2.0/gio/gtlspassword.h:
/opt/local/include/glib-2.0/gio/gtlsserverconnection.h:
/opt/local/include/glib-2.0/gio/gunixconnection.h:
/opt/local/include/glib-2.0/gio/gunixcredentialsmessage.h:
/opt/local/include/glib-2.0/gio/gunixfdlist.h:
/opt/local/include/glib-2.0/gio/gunixsocketaddress.h:
/opt/local/include/glib-2.0/gio/gvfs.h:
/opt/local/include/glib-2.0/gio/gvolume.h:
/opt/local/include/glib-2.0/gio/gzlibcompressor.h:
/opt/local/include/glib-2.0/gio/gzlibdecompressor.h:
/opt/local/include/glib-2.0/gio/gio-autocleanups.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-macros.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-features.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-core.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-transform.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-animation.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-simple-anim.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-io.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-loader.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-enum-types.h:
/opt/local/include/gdk-pixbuf-2.0/gdk-pixbuf/gdk-pixbuf-autocleanups.h:
/opt/local/include/librsvg-2.0/librsvg/rsvg-features.h:
/opt/local/include/librsvg-2.0/librsvg/rsvg-version.h:
/opt/local/include/librsvg-2.0/librsvg/rsvg-cairo.h:
../src/ImageData.h:
../src/Point.h:
../src/Util.h:
