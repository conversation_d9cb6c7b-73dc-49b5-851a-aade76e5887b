{"name": "enhanced-pdf-viewer", "version": "1.0.0", "description": "Enhanced PDF viewer combining <PERSON><PERSON>'s panning/rotation with react-pdf-highlighter-extended's rectangle highlighting", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"lodash.debounce": "^4.0.8", "pdfjs-dist": "4.4.168", "react": "^18.2.0", "react-dom": "^18.2.0", "react-pdf-highlighter-extended": "^8.1.0", "zustand": "^4.4.7"}, "devDependencies": {"@types/lodash.debounce": "^4.0.9", "@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}