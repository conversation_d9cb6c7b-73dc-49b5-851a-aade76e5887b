<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF Viewer with Annotations</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .toolbar {
            background: #2c3e50;
            color: white;
            padding: 10px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .toolbar h1 {
            font-size: 18px;
            margin-right: auto;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            background: #3498db;
            color: white;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }

        .btn:hover {
            background: #2980b9;
        }

        .btn.active {
            background: #e74c3c;
        }

        .btn:disabled {
            background: #7f8c8d;
            cursor: not-allowed;
        }

        .file-input {
            margin-right: 15px;
        }

        .file-input input {
            display: none;
        }

        .file-input label {
            padding: 8px 16px;
            background: #27ae60;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .file-input label:hover {
            background: #229954;
        }

        .viewer-container {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        .pdf-container {
            flex: 1;
            position: relative;
            overflow: auto;
            background: #e0e0e0;
            padding: 20px;
        }

        .pdf-viewer {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
        }

        .pdf-object {
            width: 100%;
            height: 800px;
            border: none;
        }

        .annotation-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 10;
        }

        .annotation-layer.drawing {
            pointer-events: all;
            cursor: crosshair;
        }

        .rectangle-annotation {
            position: absolute;
            border: 3px solid #ff0000;
            background: rgba(255, 255, 0, 0.3);
            pointer-events: none;
        }

        .preview-rectangle {
            position: absolute;
            border: 2px dashed #ff0000;
            background: rgba(255, 255, 0, 0.2);
            pointer-events: none;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #ddd;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .annotation-list {
            list-style: none;
        }

        .annotation-item {
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }

        .annotation-item.rectangle {
            border-left-color: #e74c3c;
        }

        .annotation-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .stats {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .zoom-display {
            min-width: 60px;
            text-align: center;
            background: #34495e;
            padding: 8px 12px;
            border-radius: 4px;
        }

        .no-pdf {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #7f8c8d;
            font-size: 18px;
        }

        .no-pdf .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <h1>📄 PDF Viewer with Annotations</h1>
        
        <div class="file-input">
            <input type="file" id="fileInput" accept=".pdf">
            <label for="fileInput">📁 Upload PDF</label>
        </div>

        <button class="btn" onclick="loadSamplePDF()">📄 Load Sample</button>

        <div class="zoom-controls">
            <button class="btn" onclick="zoomOut()">🔍-</button>
            <span class="zoom-display" id="zoomDisplay">100%</span>
            <button class="btn" onclick="zoomIn()">🔍+</button>
        </div>

        <button class="btn" id="textModeBtn" onclick="setMode('text')">📝 Text</button>
        <button class="btn active" id="rectangleModeBtn" onclick="setMode('rectangle')">⬜ Rectangle</button>
        
        <button class="btn" onclick="clearAnnotations()">🗑️ Clear All</button>
    </div>

    <div class="viewer-container">
        <div class="pdf-container">
            <div id="noPdfMessage" class="no-pdf">
                <div class="icon">📄</div>
                <div>No PDF loaded</div>
                <div style="font-size: 14px; margin-top: 10px;">Upload a PDF or load the sample to get started</div>
            </div>
            
            <div id="pdfViewer" class="pdf-viewer" style="display: none;">
                <object id="pdfObject" class="pdf-object" type="application/pdf"></object>
                <div id="annotationLayer" class="annotation-layer"></div>
            </div>
        </div>

        <div class="sidebar">
            <div class="stats">
                <h3>📊 Statistics</h3>
                <div class="stats-item">
                    <span>Total Annotations:</span>
                    <span id="totalAnnotations">0</span>
                </div>
                <div class="stats-item">
                    <span>Rectangle Annotations:</span>
                    <span id="rectangleCount">0</span>
                </div>
                <div class="stats-item">
                    <span>Text Annotations:</span>
                    <span id="textCount">0</span>
                </div>
            </div>

            <h3>📝 Annotations</h3>
            <ul id="annotationList" class="annotation-list">
                <li style="color: #999; font-style: italic;">No annotations yet</li>
            </ul>
        </div>
    </div>

    <script>
        let currentMode = 'rectangle';
        let isDrawing = false;
        let startX, startY;
        let annotations = [];
        let currentZoom = 1;
        let previewRect = null;

        function setMode(mode) {
            currentMode = mode;
            
            // Update button states
            document.getElementById('textModeBtn').classList.toggle('active', mode === 'text');
            document.getElementById('rectangleModeBtn').classList.toggle('active', mode === 'rectangle');
            
            // Update annotation layer
            const layer = document.getElementById('annotationLayer');
            layer.classList.toggle('drawing', mode === 'rectangle');
            
            console.log('Mode set to:', mode);
        }

        function loadSamplePDF() {
            loadPDF('sample.pdf');
        }

        function loadPDF(url) {
            const pdfObject = document.getElementById('pdfObject');
            const pdfViewer = document.getElementById('pdfViewer');
            const noPdfMessage = document.getElementById('noPdfMessage');
            
            pdfObject.data = url;
            pdfViewer.style.display = 'block';
            noPdfMessage.style.display = 'none';
            
            // Clear existing annotations
            clearAnnotations();
            
            console.log('Loading PDF:', url);
        }

        function zoomIn() {
            currentZoom = Math.min(currentZoom * 1.25, 3);
            updateZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom * 0.8, 0.5);
            updateZoom();
        }

        function updateZoom() {
            const pdfViewer = document.getElementById('pdfViewer');
            pdfViewer.style.transform = `scale(${currentZoom})`;
            pdfViewer.style.transformOrigin = 'top center';
            
            document.getElementById('zoomDisplay').textContent = Math.round(currentZoom * 100) + '%';
        }

        function createAnnotation(x, y, width, height) {
            const annotation = {
                id: Date.now(),
                type: currentMode,
                x: x,
                y: y,
                width: width,
                height: height,
                timestamp: new Date().toLocaleString()
            };
            
            annotations.push(annotation);
            renderAnnotation(annotation);
            updateStats();
            updateAnnotationList();
            
            console.log('Created annotation:', annotation);
        }

        function renderAnnotation(annotation) {
            const layer = document.getElementById('annotationLayer');
            const rect = document.createElement('div');
            rect.className = 'rectangle-annotation';
            rect.style.left = annotation.x + 'px';
            rect.style.top = annotation.y + 'px';
            rect.style.width = annotation.width + 'px';
            rect.style.height = annotation.height + 'px';
            rect.dataset.id = annotation.id;
            
            layer.appendChild(rect);
        }

        function clearAnnotations() {
            annotations = [];
            document.getElementById('annotationLayer').innerHTML = '';
            updateStats();
            updateAnnotationList();
            console.log('Cleared all annotations');
        }

        function updateStats() {
            document.getElementById('totalAnnotations').textContent = annotations.length;
            document.getElementById('rectangleCount').textContent = annotations.filter(a => a.type === 'rectangle').length;
            document.getElementById('textCount').textContent = annotations.filter(a => a.type === 'text').length;
        }

        function updateAnnotationList() {
            const list = document.getElementById('annotationList');
            
            if (annotations.length === 0) {
                list.innerHTML = '<li style="color: #999; font-style: italic;">No annotations yet</li>';
                return;
            }
            
            list.innerHTML = annotations.map(annotation => `
                <li class="annotation-item ${annotation.type}">
                    <strong>${annotation.type.charAt(0).toUpperCase() + annotation.type.slice(1)} Annotation</strong>
                    <div>Position: ${Math.round(annotation.x)}, ${Math.round(annotation.y)}</div>
                    <div>Size: ${Math.round(annotation.width)} × ${Math.round(annotation.height)}</div>
                    <div class="annotation-meta">Created: ${annotation.timestamp}</div>
                </li>
            `).join('');
        }

        // Mouse event handlers for annotation layer
        document.getElementById('annotationLayer').addEventListener('mousedown', function(e) {
            if (currentMode === 'rectangle') {
                isDrawing = true;
                const rect = this.getBoundingClientRect();
                startX = e.clientX - rect.left;
                startY = e.clientY - rect.top;
                
                // Create preview rectangle
                previewRect = document.createElement('div');
                previewRect.className = 'preview-rectangle';
                previewRect.style.left = startX + 'px';
                previewRect.style.top = startY + 'px';
                previewRect.style.width = '0px';
                previewRect.style.height = '0px';
                this.appendChild(previewRect);
                
                e.preventDefault();
            }
        });

        document.getElementById('annotationLayer').addEventListener('mousemove', function(e) {
            if (isDrawing && previewRect) {
                const rect = this.getBoundingClientRect();
                const currentX = e.clientX - rect.left;
                const currentY = e.clientY - rect.top;
                
                const width = Math.abs(currentX - startX);
                const height = Math.abs(currentY - startY);
                const left = Math.min(startX, currentX);
                const top = Math.min(startY, currentY);
                
                previewRect.style.left = left + 'px';
                previewRect.style.top = top + 'px';
                previewRect.style.width = width + 'px';
                previewRect.style.height = height + 'px';
            }
        });

        document.getElementById('annotationLayer').addEventListener('mouseup', function(e) {
            if (isDrawing && previewRect) {
                const rect = this.getBoundingClientRect();
                const endX = e.clientX - rect.left;
                const endY = e.clientY - rect.top;
                
                const width = Math.abs(endX - startX);
                const height = Math.abs(endY - startY);
                
                // Only create annotation if rectangle is big enough
                if (width > 10 && height > 10) {
                    const left = Math.min(startX, endX);
                    const top = Math.min(startY, endY);
                    createAnnotation(left, top, width, height);
                }
                
                // Remove preview rectangle
                this.removeChild(previewRect);
                previewRect = null;
                isDrawing = false;
            }
        });

        // File upload handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                const url = URL.createObjectURL(file);
                loadPDF(url);
            } else {
                alert('Please select a valid PDF file.');
            }
        });

        // Initialize
        setMode('rectangle');
        updateStats();
    </script>
</body>
</html>
