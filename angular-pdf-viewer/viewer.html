<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React-PDF-Highlighter Style Viewer</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f8f9fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .toolbar {
            background: white;
            border-bottom: 1px solid #e1e5e9;
            padding: 12px 24px;
            display: flex;
            align-items: center;
            gap: 16px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .toolbar h1 {
            font-size: 16px;
            font-weight: 600;
            color: #495057;
            margin-right: auto;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            background: white;
            color: #495057;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            transition: all 0.2s ease;
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }

        .btn:hover {
            background: #f8f9fa;
            border-color: #adb5bd;
        }

        .btn.active {
            background: #007bff;
            color: white;
            border-color: #007bff;
        }

        .btn:disabled {
            background: #f8f9fa;
            color: #6c757d;
            cursor: not-allowed;
            opacity: 0.6;
        }

        .file-input {
            margin-right: 15px;
        }

        .file-input input {
            display: none;
        }

        .file-input label {
            padding: 8px 16px;
            background: #27ae60;
            color: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .file-input label:hover {
            background: #229954;
        }

        .viewer-container {
            flex: 1;
            display: flex;
            overflow: hidden;
            background: #f8f9fa;
        }

        .pdf-container {
            flex: 1;
            overflow: auto;
            background: #f1f3f4;
            position: relative;
        }

        .pdf-pages-container {
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
        }

        .pdf-page {
            position: relative;
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
            border-radius: 4px;
            overflow: hidden;
        }

        .page-canvas {
            display: block;
            width: 100%;
            height: auto;
        }

        .text-layer {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            opacity: 0.2;
            line-height: 1.0;
        }

        .text-layer.text-selection-enabled {
            opacity: 1;
        }

        .text-layer span {
            color: transparent;
            position: absolute;
            white-space: pre;
            cursor: text;
            transform-origin: 0% 0%;
        }

        .highlight-layer {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .highlight {
            position: absolute;
            background: rgba(255, 226, 143, 0.6);
            border: 1px solid rgba(255, 193, 7, 0.8);
            pointer-events: none;
        }

        .highlight.rectangle {
            background: rgba(255, 235, 59, 0.4);
            border: 2px solid #ff5722;
        }

        .highlight.text {
            background: rgba(33, 150, 243, 0.3);
            border: 1px solid #2196f3;
        }

        .annotation-layer {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }

        .annotation-layer.drawing {
            pointer-events: all;
            cursor: crosshair;
        }

        .drawing-preview {
            position: absolute;
            border: 2px dashed #ff5722;
            background: rgba(255, 235, 59, 0.2);
            pointer-events: none;
        }

        .annotation-layer {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
            z-index: 10;
        }

        .annotation-layer.drawing {
            pointer-events: all;
            cursor: crosshair;
        }

        .rectangle-annotation {
            position: absolute;
            border: 3px solid #ff0000;
            background: rgba(255, 255, 0, 0.3);
            pointer-events: none;
        }

        .preview-rectangle {
            position: absolute;
            border: 2px dashed #ff0000;
            background: rgba(255, 255, 0, 0.2);
            pointer-events: none;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-left: 1px solid #ddd;
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar h3 {
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .annotation-list {
            list-style: none;
        }

        .annotation-item {
            padding: 10px;
            margin-bottom: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #3498db;
        }

        .annotation-item.rectangle {
            border-left-color: #e74c3c;
        }

        .annotation-meta {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        .stats {
            background: #ecf0f1;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }

        .stats-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .zoom-display {
            min-width: 60px;
            text-align: center;
            background: #34495e;
            padding: 8px 12px;
            border-radius: 4px;
        }

        .no-pdf {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #7f8c8d;
            font-size: 18px;
        }

        .no-pdf .icon {
            font-size: 64px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="toolbar">
        <h1>📄 PDF Viewer with Annotations</h1>

        <div class="file-input">
            <input type="file" id="fileInput" accept=".pdf">
            <label for="fileInput">📁 Upload PDF</label>
        </div>

        <button class="btn" onclick="loadSamplePDF()">📄 Load Sample</button>

        <div class="zoom-controls">
            <button class="btn" onclick="zoomOut()">🔍-</button>
            <span class="zoom-display" id="zoomDisplay">100%</span>
            <button class="btn" onclick="zoomIn()">🔍+</button>
        </div>

        <button class="btn" id="textModeBtn" onclick="setMode('text')">📝 Text</button>
        <button class="btn active" id="rectangleModeBtn" onclick="setMode('rectangle')">⬜ Rectangle</button>

        <button class="btn" onclick="clearAnnotations()">🗑️ Clear All</button>
    </div>

    <div class="viewer-container">
        <div class="pdf-container">
            <div id="noPdfMessage" class="no-pdf">
                <div class="icon">📄</div>
                <div>No PDF loaded</div>
                <div style="font-size: 14px; margin-top: 10px;">Upload a PDF or load the sample to get started</div>
            </div>

            <div id="pdfPagesContainer" class="pdf-pages-container" style="display: none;">
                <!-- PDF pages will be rendered here -->
            </div>
        </div>

        <div class="sidebar">
            <div class="stats">
                <h3>📊 Statistics</h3>
                <div class="stats-item">
                    <span>Total Annotations:</span>
                    <span id="totalAnnotations">0</span>
                </div>
                <div class="stats-item">
                    <span>Rectangle Annotations:</span>
                    <span id="rectangleCount">0</span>
                </div>
                <div class="stats-item">
                    <span>Text Annotations:</span>
                    <span id="textCount">0</span>
                </div>
            </div>

            <h3>📝 Annotations</h3>
            <ul id="annotationList" class="annotation-list">
                <li style="color: #999; font-style: italic;">No annotations yet</li>
            </ul>
        </div>
    </div>

    <script>
        // Configure PDF.js
        pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';

        let currentMode = 'rectangle';
        let isDrawing = false;
        let startX, startY;
        let annotations = [];
        let currentZoom = 1;
        let previewRect = null;
        let pdfDoc = null;
        let pageTextContent = new Map();

        function setMode(mode) {
            currentMode = mode;

            // Update button states
            document.getElementById('textModeBtn').classList.toggle('active', mode === 'text');
            document.getElementById('rectangleModeBtn').classList.toggle('active', mode === 'rectangle');

            // Update text layer visibility
            const textLayers = document.querySelectorAll('.text-layer');
            textLayers.forEach(layer => {
                layer.classList.toggle('text-selection-enabled', mode === 'text');
            });

            // Update annotation layers
            const annotationLayers = document.querySelectorAll('.annotation-layer');
            annotationLayers.forEach(layer => {
                layer.classList.toggle('drawing', mode === 'rectangle');
            });

            console.log('Mode set to:', mode);
        }

        function loadSamplePDF() {
            loadPDF('sample.pdf');
        }

        async function loadPDF(url) {
            try {
                console.log('Loading PDF:', url);

                // Load PDF document
                const loadingTask = pdfjsLib.getDocument(url);
                pdfDoc = await loadingTask.promise;

                console.log('PDF loaded successfully, pages:', pdfDoc.numPages);

                // Clear existing content
                const container = document.getElementById('pdfPagesContainer');
                container.innerHTML = '';

                // Render all pages
                for (let pageNum = 1; pageNum <= pdfDoc.numPages; pageNum++) {
                    await renderPage(pageNum);
                }

                // Show PDF container
                document.getElementById('pdfPagesContainer').style.display = 'flex';
                document.getElementById('noPdfMessage').style.display = 'none';

                // Clear existing annotations
                clearAnnotations();

            } catch (error) {
                console.error('Error loading PDF:', error);
                alert('Error loading PDF: ' + error.message);
            }
        }

        async function renderPage(pageNum) {
            try {
                const page = await pdfDoc.getPage(pageNum);
                const viewport = page.getViewport({ scale: currentZoom });

                // Create page container
                const pageDiv = document.createElement('div');
                pageDiv.className = 'pdf-page';
                pageDiv.dataset.pageNumber = pageNum;

                // Create canvas for PDF content
                const canvas = document.createElement('canvas');
                canvas.className = 'page-canvas';
                const context = canvas.getContext('2d');

                canvas.height = viewport.height;
                canvas.width = viewport.width;

                // Render PDF page
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                await page.render(renderContext).promise;

                // Create text layer
                const textLayerDiv = document.createElement('div');
                textLayerDiv.className = 'text-layer';

                // Get text content
                const textContent = await page.getTextContent();
                pageTextContent.set(pageNum, textContent);

                // Render text layer
                textContent.items.forEach((textItem, index) => {
                    const textSpan = document.createElement('span');
                    textSpan.textContent = textItem.str;
                    textSpan.style.left = textItem.transform[4] + 'px';
                    textSpan.style.top = (viewport.height - textItem.transform[5]) + 'px';
                    textSpan.style.fontSize = Math.sqrt(textItem.transform[0] * textItem.transform[0] + textItem.transform[1] * textItem.transform[1]) + 'px';
                    textSpan.style.fontFamily = textItem.fontName || 'sans-serif';
                    textLayerDiv.appendChild(textSpan);
                });

                // Create highlight layer
                const highlightLayerDiv = document.createElement('div');
                highlightLayerDiv.className = 'highlight-layer';

                // Create annotation layer
                const annotationLayerDiv = document.createElement('div');
                annotationLayerDiv.className = 'annotation-layer';
                annotationLayerDiv.dataset.pageNumber = pageNum;

                // Add mouse event listeners for rectangle drawing
                setupAnnotationEvents(annotationLayerDiv, pageNum);

                // Assemble page
                pageDiv.appendChild(canvas);
                pageDiv.appendChild(textLayerDiv);
                pageDiv.appendChild(highlightLayerDiv);
                pageDiv.appendChild(annotationLayerDiv);

                // Add to container
                document.getElementById('pdfPagesContainer').appendChild(pageDiv);

                console.log(`Page ${pageNum} rendered successfully`);

            } catch (error) {
                console.error(`Error rendering page ${pageNum}:`, error);
            }
        }

        function zoomIn() {
            currentZoom = Math.min(currentZoom * 1.25, 3);
            updateZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom * 0.8, 0.5);
            updateZoom();
        }

        function updateZoom() {
            // Re-render all pages with new zoom
            if (pdfDoc) {
                loadPDF(pdfDoc.url || 'sample.pdf');
            }

            document.getElementById('zoomDisplay').textContent = Math.round(currentZoom * 100) + '%';
        }

        function setupAnnotationEvents(annotationLayer, pageNum) {
            let isDrawing = false;
            let startX, startY;
            let previewRect = null;

            annotationLayer.addEventListener('mousedown', function(e) {
                if (currentMode === 'rectangle') {
                    isDrawing = true;
                    const rect = this.getBoundingClientRect();
                    startX = e.clientX - rect.left;
                    startY = e.clientY - rect.top;

                    // Create preview rectangle
                    previewRect = document.createElement('div');
                    previewRect.className = 'drawing-preview';
                    previewRect.style.left = startX + 'px';
                    previewRect.style.top = startY + 'px';
                    previewRect.style.width = '0px';
                    previewRect.style.height = '0px';
                    this.appendChild(previewRect);

                    e.preventDefault();
                }
            });

            annotationLayer.addEventListener('mousemove', function(e) {
                if (isDrawing && previewRect) {
                    const rect = this.getBoundingClientRect();
                    const currentX = e.clientX - rect.left;
                    const currentY = e.clientY - rect.top;

                    const width = Math.abs(currentX - startX);
                    const height = Math.abs(currentY - startY);
                    const left = Math.min(startX, currentX);
                    const top = Math.min(startY, currentY);

                    previewRect.style.left = left + 'px';
                    previewRect.style.top = top + 'px';
                    previewRect.style.width = width + 'px';
                    previewRect.style.height = height + 'px';
                }
            });

            annotationLayer.addEventListener('mouseup', function(e) {
                if (isDrawing && previewRect) {
                    const rect = this.getBoundingClientRect();
                    const endX = e.clientX - rect.left;
                    const endY = e.clientY - rect.top;

                    const width = Math.abs(endX - startX);
                    const height = Math.abs(endY - startY);

                    // Only create annotation if rectangle is big enough
                    if (width > 10 && height > 10) {
                        const left = Math.min(startX, endX);
                        const top = Math.min(startY, endY);
                        createAnnotation(left, top, width, height, pageNum);
                    }

                    // Remove preview rectangle
                    this.removeChild(previewRect);
                    previewRect = null;
                    isDrawing = false;
                }
            });
        }

        function createAnnotation(x, y, width, height, pageNum) {
            const annotation = {
                id: Date.now(),
                type: currentMode,
                x: x,
                y: y,
                width: width,
                height: height,
                pageNumber: pageNum,
                timestamp: new Date().toLocaleString()
            };

            annotations.push(annotation);
            renderAnnotation(annotation);
            updateStats();
            updateAnnotationList();

            console.log('Created annotation:', annotation);
        }

        function renderAnnotation(annotation) {
            const pageDiv = document.querySelector(`[data-page-number="${annotation.pageNumber}"]`);
            if (!pageDiv) return;

            const highlightLayer = pageDiv.querySelector('.highlight-layer');
            const highlight = document.createElement('div');
            highlight.className = `highlight ${annotation.type}`;
            highlight.style.left = annotation.x + 'px';
            highlight.style.top = annotation.y + 'px';
            highlight.style.width = annotation.width + 'px';
            highlight.style.height = annotation.height + 'px';
            highlight.dataset.id = annotation.id;

            highlightLayer.appendChild(highlight);
        }

        function clearAnnotations() {
            annotations = [];
            // Clear all highlight layers
            const highlightLayers = document.querySelectorAll('.highlight-layer');
            highlightLayers.forEach(layer => {
                layer.innerHTML = '';
            });
            updateStats();
            updateAnnotationList();
            console.log('Cleared all annotations');
        }

        function updateStats() {
            document.getElementById('totalAnnotations').textContent = annotations.length;
            document.getElementById('rectangleCount').textContent = annotations.filter(a => a.type === 'rectangle').length;
            document.getElementById('textCount').textContent = annotations.filter(a => a.type === 'text').length;
        }

        function updateAnnotationList() {
            const list = document.getElementById('annotationList');

            if (annotations.length === 0) {
                list.innerHTML = '<li style="color: #999; font-style: italic;">No annotations yet</li>';
                return;
            }

            list.innerHTML = annotations.map(annotation => `
                <li class="annotation-item ${annotation.type}">
                    <strong>${annotation.type.charAt(0).toUpperCase() + annotation.type.slice(1)} Annotation</strong>
                    <div>Page: ${annotation.pageNumber}</div>
                    <div>Position: ${Math.round(annotation.x)}, ${Math.round(annotation.y)}</div>
                    <div>Size: ${Math.round(annotation.width)} × ${Math.round(annotation.height)}</div>
                    <div class="annotation-meta">Created: ${annotation.timestamp}</div>
                </li>
            `).join('');
        }

        // File upload handler
        document.getElementById('fileInput').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file && file.type === 'application/pdf') {
                const url = URL.createObjectURL(file);
                loadPDF(url);
            } else {
                alert('Please select a valid PDF file.');
            }
        });

        // Initialize
        setMode('rectangle');
        updateStats();
    </script>
</body>
</html>
