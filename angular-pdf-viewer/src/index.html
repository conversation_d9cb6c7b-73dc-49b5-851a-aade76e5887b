<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>Angular PDF Viewer</title>
  <base href="/">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <style>
    body {
      margin: 0;
      padding: 0;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      background-color: #f5f5f5;
    }
    
    .app-loading {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      flex-direction: column;
      gap: 20px;
    }
    
    .app-loading .spinner {
      width: 50px;
      height: 50px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #007acc;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .app-loading h2 {
      color: #333;
      margin: 0;
    }
    
    .app-loading p {
      color: #666;
      margin: 0;
    }
  </style>
</head>
<body>
  <app-root>
    <div class="app-loading">
      <div class="spinner"></div>
      <h2>Loading Angular PDF Viewer</h2>
      <p>Initializing advanced PDF features...</p>
    </div>
  </app-root>
</body>
</html>
