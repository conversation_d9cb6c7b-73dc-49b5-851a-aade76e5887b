import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { PdfViewerComponent } from './pdf-viewer/pdf-viewer.component';
import { Highlight, SearchResult } from './services/pdf.service';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [CommonModule, FormsModule, PdfViewerComponent],
  template: `
    <div class="app-container">
      <!-- Header -->
      <header class="app-header">
        <div class="header-content">
          <h1 class="app-title">
            <span class="icon">📄</span>
            Angular PDF Viewer
          </h1>
          <div class="header-controls">
            <div class="url-input-group">
              <label for="pdfUrl">PDF URL:</label>
              <input 
                id="pdfUrl"
                type="url" 
                class="form-control"
                [(ngModel)]="pdfUrl" 
                placeholder="Enter PDF URL..."
                (keyup.enter)="loadPDF()">
              <button class="btn btn-primary" (click)="loadPDF()">
                <span class="icon">🔄</span>
                Load PDF
              </button>
            </div>
            <div class="sample-pdfs">
              <label>Quick Load:</label>
              <button 
                *ngFor="let sample of samplePDFs" 
                class="btn btn-secondary btn-sm"
                (click)="loadSamplePDF(sample.url)"
                [title]="sample.description">
                {{ sample.name }}
              </button>
            </div>
          </div>
        </div>
      </header>

      <!-- Main Content -->
      <main class="app-main">
        <div class="pdf-viewer-wrapper">
          <app-pdf-viewer
            [url]="currentPdfUrl"
            [zoom]="zoom"
            [enableAnnotations]="enableAnnotations"
            [enableSearch]="enableSearch"
            [enableTextSelection]="enableTextSelection"
            (zoomChange)="onZoomChange($event)"
            (highlightAdded)="onHighlightAdded($event)"
            (searchResultsChanged)="onSearchResultsChanged($event)">
          </app-pdf-viewer>
        </div>

        <!-- Sidebar -->
        <aside class="app-sidebar" [class.collapsed]="sidebarCollapsed">
          <div class="sidebar-header">
            <h3>PDF Tools</h3>
            <button class="btn btn-sm" (click)="toggleSidebar()">
              <span class="icon">{{ sidebarCollapsed ? '▶' : '◀' }}</span>
            </button>
          </div>

          <div class="sidebar-content" *ngIf="!sidebarCollapsed">
            <!-- Feature Toggles -->
            <div class="feature-section">
              <h4>Features</h4>
              <div class="feature-toggles">
                <label class="toggle-label">
                  <input type="checkbox" [(ngModel)]="enableAnnotations">
                  <span class="toggle-text">Annotations</span>
                </label>
                <label class="toggle-label">
                  <input type="checkbox" [(ngModel)]="enableSearch">
                  <span class="toggle-text">Search</span>
                </label>
                <label class="toggle-label">
                  <input type="checkbox" [(ngModel)]="enableTextSelection">
                  <span class="toggle-text">Text Selection</span>
                </label>
              </div>
            </div>

            <!-- Zoom Control -->
            <div class="zoom-section">
              <h4>Zoom</h4>
              <div class="zoom-controls">
                <input 
                  type="range" 
                  min="25" 
                  max="500" 
                  [value]="zoom * 100"
                  (input)="onZoomSliderChange($event)"
                  class="zoom-slider">
                <span class="zoom-value">{{ (zoom * 100) | number:'1.0-0' }}%</span>
              </div>
            </div>

            <!-- Statistics -->
            <div class="stats-section">
              <h4>Statistics</h4>
              <div class="stats-grid">
                <div class="stat-item">
                  <span class="stat-label">Highlights:</span>
                  <span class="stat-value">{{ highlightCount }}</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Search Results:</span>
                  <span class="stat-value">{{ searchResultCount }}</span>
                </div>
              </div>
            </div>

            <!-- Actions -->
            <div class="actions-section">
              <h4>Actions</h4>
              <div class="action-buttons">
                <button class="btn btn-success btn-sm" (click)="exportHighlights()">
                  <span class="icon">💾</span>
                  Export Highlights
                </button>
                <button class="btn btn-danger btn-sm" (click)="clearHighlights()">
                  <span class="icon">🗑️</span>
                  Clear All
                </button>
              </div>
            </div>
          </div>
        </aside>
      </main>

      <!-- Footer -->
      <footer class="app-footer">
        <div class="footer-content">
          <p>&copy; 2024 Angular PDF Viewer - Native PDF viewing with advanced features</p>
          <div class="footer-links">
            <a href="#" class="footer-link">Documentation</a>
            <a href="#" class="footer-link">GitHub</a>
            <a href="#" class="footer-link">Support</a>
          </div>
        </div>
      </footer>
    </div>
  `,
  styleUrls: ['./app.component.scss']
})
export class AppComponent {
  title = 'Angular PDF Viewer';
  
  // PDF Configuration
  pdfUrl = '';
  currentPdfUrl = 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf';
  zoom = 1;
  
  // Feature flags
  enableAnnotations = true;
  enableSearch = true;
  enableTextSelection = true;
  
  // UI State
  sidebarCollapsed = false;
  highlightCount = 0;
  searchResultCount = 0;
  
  // Sample PDFs for quick testing
  samplePDFs = [
    {
      name: 'TracemonKey',
      url: 'https://mozilla.github.io/pdf.js/web/compressed.tracemonkey-pldi-09.pdf',
      description: 'Mozilla PDF.js sample document'
    },
    {
      name: 'Lorem Ipsum',
      url: 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf',
      description: 'Simple test PDF'
    }
  ];

  loadPDF(): void {
    if (this.pdfUrl.trim()) {
      this.currentPdfUrl = this.pdfUrl.trim();
      console.log('Loading PDF:', this.currentPdfUrl);
    }
  }

  loadSamplePDF(url: string): void {
    this.pdfUrl = url;
    this.currentPdfUrl = url;
    console.log('Loading sample PDF:', url);
  }

  onZoomChange(newZoom: number): void {
    this.zoom = newZoom;
  }

  onZoomSliderChange(event: any): void {
    const newZoom = parseInt(event.target.value) / 100;
    this.zoom = newZoom;
  }

  onHighlightAdded(highlight: Highlight): void {
    this.highlightCount++;
    console.log('Highlight added:', highlight);
  }

  onSearchResultsChanged(results: SearchResult[]): void {
    this.searchResultCount = results.length;
    console.log('Search results changed:', results.length);
  }

  toggleSidebar(): void {
    this.sidebarCollapsed = !this.sidebarCollapsed;
  }

  exportHighlights(): void {
    // Implementation would export highlights as JSON
    console.log('Exporting highlights...');
    alert('Export functionality would be implemented here');
  }

  clearHighlights(): void {
    if (confirm('Are you sure you want to clear all highlights?')) {
      // Implementation would clear all highlights
      console.log('Clearing all highlights...');
      this.highlightCount = 0;
    }
  }
}
