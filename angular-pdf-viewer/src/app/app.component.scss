.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f9fa;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #007acc 0%, #0056b3 100%);
  color: white;
  padding: 16px 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  flex-wrap: wrap;
}

.app-title {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 12px;
  
  .icon {
    font-size: 28px;
  }
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.url-input-group {
  display: flex;
  align-items: center;
  gap: 12px;
  
  label {
    font-weight: 500;
    white-space: nowrap;
  }
  
  input {
    min-width: 300px;
    background: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    
    &:focus {
      background: white;
      border-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
    }
  }
}

.sample-pdfs {
  display: flex;
  align-items: center;
  gap: 8px;
  
  label {
    font-weight: 500;
    white-space: nowrap;
  }
}

/* Main Content */
.app-main {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.pdf-viewer-wrapper {
  flex: 1;
  overflow: hidden;
  background: white;
  border-right: 1px solid #e9ecef;
}

/* Sidebar */
.app-sidebar {
  width: 320px;
  background: white;
  border-left: 1px solid #e9ecef;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  
  &.collapsed {
    width: 60px;
  }
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #f8f9fa;
  
  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #495057;
  }
}

.sidebar-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.feature-section,
.zoom-section,
.stats-section,
.actions-section {
  margin-bottom: 24px;
  
  h4 {
    margin: 0 0 12px 0;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.feature-toggles {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f8f9fa;
  }
  
  input[type="checkbox"] {
    margin: 0;
  }
  
  .toggle-text {
    font-size: 14px;
    color: #495057;
  }
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.zoom-slider {
  flex: 1;
  height: 6px;
  border-radius: 3px;
  background: #e9ecef;
  outline: none;
  
  &::-webkit-slider-thumb {
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007acc;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  &::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #007acc;
    cursor: pointer;
    border: 2px solid white;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
}

.zoom-value {
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  min-width: 50px;
  text-align: right;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 8px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 4px;
  border-left: 3px solid #007acc;
}

.stat-label {
  font-size: 14px;
  color: #6c757d;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #495057;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Footer */
.app-footer {
  background: #343a40;
  color: #adb5bd;
  padding: 16px 24px;
  border-top: 1px solid #495057;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-wrap: wrap;
  gap: 16px;
  
  p {
    margin: 0;
    font-size: 14px;
  }
}

.footer-links {
  display: flex;
  gap: 16px;
}

.footer-link {
  color: #adb5bd;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.2s;
  
  &:hover {
    color: #007acc;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .app-sidebar {
    width: 280px;
    
    &.collapsed {
      width: 60px;
    }
  }
  
  .url-input-group input {
    min-width: 250px;
  }
}

@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .app-title {
    justify-content: center;
  }
  
  .header-controls {
    flex-direction: column;
    gap: 16px;
  }
  
  .url-input-group {
    flex-direction: column;
    align-items: stretch;
    
    input {
      min-width: auto;
    }
  }
  
  .sample-pdfs {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .app-main {
    flex-direction: column;
  }
  
  .app-sidebar {
    width: 100%;
    max-height: 300px;
    border-left: none;
    border-top: 1px solid #e9ecef;
    
    &.collapsed {
      max-height: 60px;
      width: 100%;
    }
  }
  
  .footer-content {
    flex-direction: column;
    text-align: center;
  }
}

/* Animations */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Icon Styles */
.icon {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}
