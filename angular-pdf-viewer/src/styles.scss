/* Global Styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: #f5f5f5;
  color: #333;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* But<PERSON> Styles */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  
  &:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #007acc;
  }
  
  &:active {
    transform: translateY(1px);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background: #007acc;
    color: white;
    border-color: #007acc;
    
    &:hover:not(:disabled) {
      background: #0056b3;
    }
  }
  
  &.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
    
    &:hover:not(:disabled) {
      background: #545b62;
    }
  }
  
  &.btn-success {
    background: #28a745;
    color: white;
    border-color: #28a745;
    
    &:hover:not(:disabled) {
      background: #218838;
    }
  }
  
  &.btn-danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
    
    &:hover:not(:disabled) {
      background: #c82333;
    }
  }
  
  &.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
  }
  
  &.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
  }
}

/* Input Styles */
.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  
  &:focus {
    color: #495057;
    background-color: #fff;
    border-color: #007acc;
    outline: 0;
    box-shadow: 0 0 0 2px rgba(0, 122, 204, 0.25);
  }
  
  &::placeholder {
    color: #6c757d;
    opacity: 1;
  }
  
  &:disabled {
    background-color: #e9ecef;
    opacity: 1;
  }
}

/* Utility Classes */
.d-flex {
  display: flex !important;
}

.d-inline-flex {
  display: inline-flex !important;
}

.flex-column {
  flex-direction: column !important;
}

.flex-row {
  flex-direction: row !important;
}

.justify-content-center {
  justify-content: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.align-items-center {
  align-items: center !important;
}

.align-items-start {
  align-items: flex-start !important;
}

.gap-1 { gap: 4px !important; }
.gap-2 { gap: 8px !important; }
.gap-3 { gap: 12px !important; }
.gap-4 { gap: 16px !important; }
.gap-5 { gap: 20px !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 4px !important; }
.m-2 { margin: 8px !important; }
.m-3 { margin: 12px !important; }
.m-4 { margin: 16px !important; }
.m-5 { margin: 20px !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 4px !important; }
.p-2 { padding: 8px !important; }
.p-3 { padding: 12px !important; }
.p-4 { padding: 16px !important; }
.p-5 { padding: 20px !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-primary { color: #007acc !important; }
.text-secondary { color: #6c757d !important; }
.text-success { color: #28a745 !important; }
.text-danger { color: #dc3545 !important; }
.text-warning { color: #ffc107 !important; }
.text-info { color: #17a2b8 !important; }
.text-muted { color: #6c757d !important; }

.bg-primary { background-color: #007acc !important; }
.bg-secondary { background-color: #6c757d !important; }
.bg-success { background-color: #28a745 !important; }
.bg-danger { background-color: #dc3545 !important; }
.bg-warning { background-color: #ffc107 !important; }
.bg-info { background-color: #17a2b8 !important; }
.bg-light { background-color: #f8f9fa !important; }
.bg-dark { background-color: #343a40 !important; }

.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }

.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.3s ease-in;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateY(-10px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Responsive Design */
@media (max-width: 768px) {
  .d-md-none {
    display: none !important;
  }
  
  .flex-md-column {
    flex-direction: column !important;
  }
}
