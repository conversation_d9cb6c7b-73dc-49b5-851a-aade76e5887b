sudo: false

env:
  - CXX=clang++ npm_config_v8_enable_pointer_compression=0 npm_config_v8_enable_31bit_smis_on_64bit_arch=0

language: node_js

os:
  - linux
  - osx

osx_image: xcode10

node_js:
  - "10"
  - "12"
  - "14"

install:
  - npm install --build-from-source

before_deploy:
  - ARCHIVE_NAME="${TRAVIS_TAG:-latest}-$TRAVIS_OS_NAME-`uname -m`.tar"
  - npm run prebuildify
  - if [[ "$TRAVIS_OS_NAME" == "linux" ]]; then ARCH=ia32 npm run prebuildify; fi
  - tar --create --verbose --file="$ARCHIVE_NAME" --directory "$TRAVIS_BUILD_DIR/prebuilds" .

deploy:
  provider: releases
  draft: false
  prerelease: true
  file: "$ARCHIVE_NAME"
  skip_cleanup: true
  on:
    tags: true
    node: 12
  api_key: $PREBUILD_GITHUB_TOKEN
