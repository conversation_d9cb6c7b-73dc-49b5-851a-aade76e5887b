cmd_Release/obj.target/nice_napi/binding.o := c++ -o Release/obj.target/nice_napi/binding.o ../binding.cc '-DNODE_GYP_MODULE_NAME=nice_napi' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/src -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/v8/include -I/Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=10.7 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/nice_napi/binding.o.d.raw   -c
Release/obj.target/nice_napi/binding.o: ../binding.cc \
  /Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h \
  /Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi-inl.h \
  /Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi-inl.deprecated.h
../binding.cc:
/Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h:
/Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi-inl.h:
/Users/<USER>/Documents/PDF_TEST/angular-pdf-viewer/node_modules/node-addon-api/napi-inl.deprecated.h:
