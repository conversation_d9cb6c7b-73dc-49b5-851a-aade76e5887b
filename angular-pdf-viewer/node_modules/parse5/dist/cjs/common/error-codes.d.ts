import type { Location } from './token.js';
export interface ParserError extends Location {
    code: ERR;
}
export type ParserErrorHandler = (error: ParserError) => void;
export declare enum ERR {
    controlCharacterInInputStream = "control-character-in-input-stream",
    noncharacterInInputStream = "noncharacter-in-input-stream",
    surrogateInInputStream = "surrogate-in-input-stream",
    nonVoidHtmlElementStartTagWithTrailingSolidus = "non-void-html-element-start-tag-with-trailing-solidus",
    endTagWithAttributes = "end-tag-with-attributes",
    endTagWithTrailingSolidus = "end-tag-with-trailing-solidus",
    unexpectedSolidusInTag = "unexpected-solidus-in-tag",
    unexpectedNullCharacter = "unexpected-null-character",
    unexpectedQuestionMarkInsteadOfTagName = "unexpected-question-mark-instead-of-tag-name",
    invalidFirstCharacterOfTagName = "invalid-first-character-of-tag-name",
    unexpectedEqualsSignBeforeAttributeName = "unexpected-equals-sign-before-attribute-name",
    missingEndTagName = "missing-end-tag-name",
    unexpectedCharacterInAttributeName = "unexpected-character-in-attribute-name",
    unknownNamedCharacterReference = "unknown-named-character-reference",
    missingSemicolonAfterCharacterReference = "missing-semicolon-after-character-reference",
    unexpectedCharacterAfterDoctypeSystemIdentifier = "unexpected-character-after-doctype-system-identifier",
    unexpectedCharacterInUnquotedAttributeValue = "unexpected-character-in-unquoted-attribute-value",
    eofBeforeTagName = "eof-before-tag-name",
    eofInTag = "eof-in-tag",
    missingAttributeValue = "missing-attribute-value",
    missingWhitespaceBetweenAttributes = "missing-whitespace-between-attributes",
    missingWhitespaceAfterDoctypePublicKeyword = "missing-whitespace-after-doctype-public-keyword",
    missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers = "missing-whitespace-between-doctype-public-and-system-identifiers",
    missingWhitespaceAfterDoctypeSystemKeyword = "missing-whitespace-after-doctype-system-keyword",
    missingQuoteBeforeDoctypePublicIdentifier = "missing-quote-before-doctype-public-identifier",
    missingQuoteBeforeDoctypeSystemIdentifier = "missing-quote-before-doctype-system-identifier",
    missingDoctypePublicIdentifier = "missing-doctype-public-identifier",
    missingDoctypeSystemIdentifier = "missing-doctype-system-identifier",
    abruptDoctypePublicIdentifier = "abrupt-doctype-public-identifier",
    abruptDoctypeSystemIdentifier = "abrupt-doctype-system-identifier",
    cdataInHtmlContent = "cdata-in-html-content",
    incorrectlyOpenedComment = "incorrectly-opened-comment",
    eofInScriptHtmlCommentLikeText = "eof-in-script-html-comment-like-text",
    eofInDoctype = "eof-in-doctype",
    nestedComment = "nested-comment",
    abruptClosingOfEmptyComment = "abrupt-closing-of-empty-comment",
    eofInComment = "eof-in-comment",
    incorrectlyClosedComment = "incorrectly-closed-comment",
    eofInCdata = "eof-in-cdata",
    absenceOfDigitsInNumericCharacterReference = "absence-of-digits-in-numeric-character-reference",
    nullCharacterReference = "null-character-reference",
    surrogateCharacterReference = "surrogate-character-reference",
    characterReferenceOutsideUnicodeRange = "character-reference-outside-unicode-range",
    controlCharacterReference = "control-character-reference",
    noncharacterCharacterReference = "noncharacter-character-reference",
    missingWhitespaceBeforeDoctypeName = "missing-whitespace-before-doctype-name",
    missingDoctypeName = "missing-doctype-name",
    invalidCharacterSequenceAfterDoctypeName = "invalid-character-sequence-after-doctype-name",
    duplicateAttribute = "duplicate-attribute",
    nonConformingDoctype = "non-conforming-doctype",
    missingDoctype = "missing-doctype",
    misplacedDoctype = "misplaced-doctype",
    endTagWithoutMatchingOpenElement = "end-tag-without-matching-open-element",
    closingOfElementWithOpenChildElements = "closing-of-element-with-open-child-elements",
    disallowedContentInNoscriptInHead = "disallowed-content-in-noscript-in-head",
    openElementsLeftAfterEof = "open-elements-left-after-eof",
    abandonedHeadElementChild = "abandoned-head-element-child",
    misplacedStartTagForHeadElement = "misplaced-start-tag-for-head-element",
    nestedNoscriptInHead = "nested-noscript-in-head",
    eofInElementThatCanContainOnlyText = "eof-in-element-that-can-contain-only-text"
}
