const set = require('regenerate')(0xB5, 0x1BF, 0x259, 0x26F, 0x275, 0x27D, 0x280, 0x292, 0x345, 0x37F, 0x386, 0x38C, 0x10C7, 0x10CD, 0x1D79, 0x1D7D, 0x1D8E, 0x1E9E, 0x1F59, 0x1F5B, 0x1F5D, 0x1FBE, 0x2126, 0x2132, 0x214E, 0x2D27, 0x2D2D, 0xAB53);
set.addRange(0x41, 0x5A).addRange(0x61, 0x7A).addRange(0xC0, 0xD6).addRange(0xD8, 0xF6).addRange(0xF8, 0x137).addRange(0x139, 0x18C).addRange(0x18E, 0x1A9).addRange(0x1AC, 0x1B9).addRange(0x1BC, 0x1BD).addRange(0x1C4, 0x220).addRange(0x222, 0x233).addRange(0x23A, 0x254).addRange(0x256, 0x257).addRange(0x25B, 0x25C).addRange(0x260, 0x261).addRange(0x263, 0x266).addRange(0x268, 0x26C).addRange(0x271, 0x272).addRange(0x282, 0x283).addRange(0x287, 0x28C).addRange(0x29D, 0x29E).addRange(0x370, 0x373).addRange(0x376, 0x377).addRange(0x37B, 0x37D).addRange(0x388, 0x38A).addRange(0x38E, 0x3A1).addRange(0x3A3, 0x3D1).addRange(0x3D5, 0x3F5).addRange(0x3F7, 0x3FB).addRange(0x3FD, 0x481).addRange(0x48A, 0x52F).addRange(0x531, 0x556).addRange(0x561, 0x587).addRange(0x10A0, 0x10C5).addRange(0x10D0, 0x10FA).addRange(0x10FD, 0x10FF).addRange(0x13A0, 0x13F5).addRange(0x13F8, 0x13FD).addRange(0x1C80, 0x1C8A).addRange(0x1C90, 0x1CBA).addRange(0x1CBD, 0x1CBF).addRange(0x1E00, 0x1E9B).addRange(0x1EA0, 0x1F15).addRange(0x1F18, 0x1F1D).addRange(0x1F20, 0x1F45).addRange(0x1F48, 0x1F4D).addRange(0x1F50, 0x1F57).addRange(0x1F5F, 0x1F7D).addRange(0x1F80, 0x1FB4).addRange(0x1FB6, 0x1FBC).addRange(0x1FC2, 0x1FC4);
set.addRange(0x1FC6, 0x1FCC).addRange(0x1FD0, 0x1FD3).addRange(0x1FD6, 0x1FDB).addRange(0x1FE0, 0x1FEC).addRange(0x1FF2, 0x1FF4).addRange(0x1FF6, 0x1FFC).addRange(0x212A, 0x212B).addRange(0x2160, 0x217F).addRange(0x2183, 0x2184).addRange(0x24B6, 0x24E9).addRange(0x2C00, 0x2C70).addRange(0x2C72, 0x2C73).addRange(0x2C75, 0x2C76).addRange(0x2C7E, 0x2CE3).addRange(0x2CEB, 0x2CEE).addRange(0x2CF2, 0x2CF3).addRange(0x2D00, 0x2D25).addRange(0xA640, 0xA66D).addRange(0xA680, 0xA69B).addRange(0xA722, 0xA72F).addRange(0xA732, 0xA76F).addRange(0xA779, 0xA787).addRange(0xA78B, 0xA78D).addRange(0xA790, 0xA794).addRange(0xA796, 0xA7AE).addRange(0xA7B0, 0xA7CD).addRange(0xA7D0, 0xA7D1).addRange(0xA7D6, 0xA7DC).addRange(0xA7F5, 0xA7F6).addRange(0xAB70, 0xABBF).addRange(0xFB00, 0xFB06).addRange(0xFB13, 0xFB17).addRange(0xFF21, 0xFF3A).addRange(0xFF41, 0xFF5A).addRange(0x10400, 0x1044F).addRange(0x104B0, 0x104D3).addRange(0x104D8, 0x104FB).addRange(0x10570, 0x1057A).addRange(0x1057C, 0x1058A).addRange(0x1058C, 0x10592).addRange(0x10594, 0x10595).addRange(0x10597, 0x105A1).addRange(0x105A3, 0x105B1).addRange(0x105B3, 0x105B9).addRange(0x105BB, 0x105BC).addRange(0x10C80, 0x10CB2).addRange(0x10CC0, 0x10CF2).addRange(0x10D50, 0x10D65).addRange(0x10D70, 0x10D85).addRange(0x118A0, 0x118DF).addRange(0x16E40, 0x16E7F);
set.addRange(0x1E900, 0x1E943);
exports.characters = set;
