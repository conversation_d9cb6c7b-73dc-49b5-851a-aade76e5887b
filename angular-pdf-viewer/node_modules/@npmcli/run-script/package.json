{"name": "@npmcli/run-script", "version": "7.0.4", "description": "Run a lifecycle script for a package (descendant of npm-lifecycle)", "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "eslint": "eslint", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "postlint": "template-oss-check", "snap": "tap", "posttest": "npm run lint", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.21.3", "spawk": "^1.8.1", "tap": "^16.0.1"}, "dependencies": {"@npmcli/node-gyp": "^3.0.0", "@npmcli/package-json": "^5.0.0", "@npmcli/promise-spawn": "^7.0.0", "node-gyp": "^10.0.0", "which": "^4.0.0"}, "files": ["bin/", "lib/"], "main": "lib/run-script.js", "repository": {"type": "git", "url": "https://github.com/npm/run-script.git"}, "engines": {"node": "^16.14.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.21.3", "publish": "true"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}