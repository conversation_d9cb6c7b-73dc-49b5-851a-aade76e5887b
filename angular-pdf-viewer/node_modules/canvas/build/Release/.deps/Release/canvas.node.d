cmd_Release/canvas.node := c++ -bundle -undefined dynamic_lookup -Wl,-search_paths_first -mmacosx-version-min=11.0 -arch arm64 -L./Release -stdlib=libc++  -o Release/canvas.node Release/obj.target/canvas/src/backend/Backend.o Release/obj.target/canvas/src/backend/ImageBackend.o Release/obj.target/canvas/src/backend/PdfBackend.o Release/obj.target/canvas/src/backend/SvgBackend.o Release/obj.target/canvas/src/bmp/BMPParser.o Release/obj.target/canvas/src/Backends.o Release/obj.target/canvas/src/Canvas.o Release/obj.target/canvas/src/CanvasGradient.o Release/obj.target/canvas/src/CanvasPattern.o Release/obj.target/canvas/src/CanvasRenderingContext2d.o Release/obj.target/canvas/src/closure.o Release/obj.target/canvas/src/color.o Release/obj.target/canvas/src/Image.o Release/obj.target/canvas/src/ImageData.o Release/obj.target/canvas/src/init.o Release/obj.target/canvas/src/register_font.o -L/opt/local/lib -lpixman-1 -lcairo -lpng16 -lpangocairo-1.0 -lpango-1.0 -lgobject-2.0 -lglib-2.0 -lintl -lharfbuzz -lfreetype -ljpeg -L/opt/homebrew/lib -lgif -lrsvg-2 -lm -lgio-2.0 -lgdk_pixbuf-2.0
