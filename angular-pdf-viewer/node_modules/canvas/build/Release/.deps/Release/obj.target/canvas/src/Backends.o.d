cmd_Release/obj.target/canvas/src/Backends.o := c++ -o Release/obj.target/canvas/src/Backends.o ../src/Backends.cc '-DNODE_GYP_MODULE_NAME=canvas' '-DUSING_UV_SHARED=1' '-DUSING_V8_SHARED=1' '-DV8_DEPRECATION_WARNINGS=1' '-D_GLIBCXX_USE_CXX11_ABI=1' '-D_DARWIN_USE_64_BIT_INODE=1' '-D_LARGEFILE_SOURCE' '-D_FILE_OFFSET_BITS=64' '-DOPENSSL_NO_PINSHARED' '-DOPENSSL_THREADS' '-DHAVE_JPEG' '-DHAVE_GIF' '-DHAVE_RSVG' '-DBUILDING_NODE_EXTENSION' -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/src -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/config -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/openssl/openssl/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/uv/include -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/zlib -I/Users/<USER>/Library/Caches/node-gyp/22.13.1/deps/v8/include -I../../nan -I/opt/local/include/cairo -I/opt/local/include -I/opt/local/include/glib-2.0 -I/opt/local/lib/glib-2.0/include -I/opt/local/include/pixman-1 -I/opt/local/include/freetype2 -I/opt/local/include/libpng16 -I/opt/local/include/pango-1.0 -I/opt/local/include/harfbuzz -I/opt/local/include/fribidi -I/opt/homebrew/include -I/opt/local/include/librsvg-2.0 -I/opt/local/include/gdk-pixbuf-2.0  -O3 -gdwarf-2 -fno-strict-aliasing -mmacosx-version-min=11.0 -arch arm64 -Wall -Wendif-labels -W -Wno-unused-parameter -std=gnu++17 -stdlib=libc++ -fno-rtti -MMD -MF ./Release/.deps/Release/obj.target/canvas/src/Backends.o.d.raw   -c
Release/obj.target/canvas/src/Backends.o: ../src/Backends.cc \
  ../src/Backends.h ../src/backend/Backend.h \
  /opt/local/include/cairo/cairo.h \
  /opt/local/include/cairo/cairo-version.h \
  /opt/local/include/cairo/cairo-features.h \
  /opt/local/include/cairo/cairo-deprecated.h \
  ../src/backend/../dll_visibility.h ../../nan/nan.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/errno.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/unix.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/threadpool.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/darwin.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/cppgc/common.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8config.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-array-buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-local-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-handle-base.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-internal.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-object.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-maybe.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-persistent-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-weak-callback-info.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-data.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-traced-handle.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-container.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-context.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-snapshot.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-isolate.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-callbacks.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-promise.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-debug.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-script.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-memory-span.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-message.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-heap.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function-callback.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-statistics.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-unwinder.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-state-scope.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-date.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-exception.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-extension.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-external.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-template.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-initialization.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-platform.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-source-location.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-json.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-locker.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask-queue.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive-object.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-proxy.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-regexp.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-typed-array.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value-serializer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-version.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-wasm.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_buffer.h \
  /Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_object_wrap.h \
  ../../nan/nan_callbacks.h ../../nan/nan_callbacks_12_inl.h \
  ../../nan/nan_maybe_43_inl.h ../../nan/nan_converters.h \
  ../../nan/nan_converters_43_inl.h ../../nan/nan_new.h \
  ../../nan/nan_implementation_12_inl.h \
  ../../nan/nan_persistent_12_inl.h ../../nan/nan_weak.h \
  ../../nan/nan_object_wrap.h ../../nan/nan_private.h \
  ../../nan/nan_typedarray_contents.h ../../nan/nan_json.h \
  ../../nan/nan_scriptorigin.h ../src/backend/ImageBackend.h \
  ../src/backend/PdfBackend.h ../src/backend/../closure.h \
  ../src/Canvas.h ../src/dll_visibility.h \
  /opt/local/include/pango-1.0/pango/pangocairo.h \
  /opt/local/include/pango-1.0/pango/pango.h \
  /opt/local/include/pango-1.0/pango/pango-attributes.h \
  /opt/local/include/pango-1.0/pango/pango-font.h \
  /opt/local/include/pango-1.0/pango/pango-coverage.h \
  /opt/local/include/glib-2.0/glib-object.h \
  /opt/local/include/glib-2.0/gobject/gbinding.h \
  /opt/local/include/glib-2.0/glib.h \
  /opt/local/include/glib-2.0/glib/galloca.h \
  /opt/local/include/glib-2.0/glib/gtypes.h \
  /opt/local/lib/glib-2.0/include/glibconfig.h \
  /opt/local/include/glib-2.0/glib/gmacros.h \
  /opt/local/include/glib-2.0/glib/gversionmacros.h \
  /opt/local/include/glib-2.0/glib/glib-visibility.h \
  /opt/local/include/glib-2.0/glib/garray.h \
  /opt/local/include/glib-2.0/glib/gasyncqueue.h \
  /opt/local/include/glib-2.0/glib/gthread.h \
  /opt/local/include/glib-2.0/glib/gatomic.h \
  /opt/local/include/glib-2.0/glib/glib-typeof.h \
  /opt/local/include/glib-2.0/glib/gerror.h \
  /opt/local/include/glib-2.0/glib/gquark.h \
  /opt/local/include/glib-2.0/glib/gutils.h \
  /opt/local/include/glib-2.0/glib/gbacktrace.h \
  /opt/local/include/glib-2.0/glib/gbase64.h \
  /opt/local/include/glib-2.0/glib/gbitlock.h \
  /opt/local/include/glib-2.0/glib/gbookmarkfile.h \
  /opt/local/include/glib-2.0/glib/gdatetime.h \
  /opt/local/include/glib-2.0/glib/gtimezone.h \
  /opt/local/include/glib-2.0/glib/gbytes.h \
  /opt/local/include/glib-2.0/glib/gcharset.h \
  /opt/local/include/glib-2.0/glib/gchecksum.h \
  /opt/local/include/glib-2.0/glib/gconvert.h \
  /opt/local/include/glib-2.0/glib/gdataset.h \
  /opt/local/include/glib-2.0/glib/gdate.h \
  /opt/local/include/glib-2.0/glib/gdir.h \
  /opt/local/include/glib-2.0/glib/genviron.h \
  /opt/local/include/glib-2.0/glib/gfileutils.h \
  /opt/local/include/glib-2.0/glib/ggettext.h \
  /opt/local/include/glib-2.0/glib/ghash.h \
  /opt/local/include/glib-2.0/glib/glist.h \
  /opt/local/include/glib-2.0/glib/gmem.h \
  /opt/local/include/glib-2.0/glib/gnode.h \
  /opt/local/include/glib-2.0/glib/ghmac.h \
  /opt/local/include/glib-2.0/glib/ghook.h \
  /opt/local/include/glib-2.0/glib/ghostutils.h \
  /opt/local/include/glib-2.0/glib/giochannel.h \
  /opt/local/include/glib-2.0/glib/gmain.h \
  /opt/local/include/glib-2.0/glib/gpoll.h \
  /opt/local/include/glib-2.0/glib/gslist.h \
  /opt/local/include/glib-2.0/glib/gstring.h \
  /opt/local/include/glib-2.0/glib/gunicode.h \
  /opt/local/include/glib-2.0/glib/gstrfuncs.h \
  /opt/local/include/glib-2.0/glib/gkeyfile.h \
  /opt/local/include/glib-2.0/glib/gmappedfile.h \
  /opt/local/include/glib-2.0/glib/gmarkup.h \
  /opt/local/include/glib-2.0/glib/gmessages.h \
  /opt/local/include/glib-2.0/glib/gvariant.h \
  /opt/local/include/glib-2.0/glib/gvarianttype.h \
  /opt/local/include/glib-2.0/glib/goption.h \
  /opt/local/include/glib-2.0/glib/gpathbuf.h \
  /opt/local/include/glib-2.0/glib/gpattern.h \
  /opt/local/include/glib-2.0/glib/gprimes.h \
  /opt/local/include/glib-2.0/glib/gqsort.h \
  /opt/local/include/glib-2.0/glib/gqueue.h \
  /opt/local/include/glib-2.0/glib/grand.h \
  /opt/local/include/glib-2.0/glib/grcbox.h \
  /opt/local/include/glib-2.0/glib/grefcount.h \
  /opt/local/include/glib-2.0/glib/grefstring.h \
  /opt/local/include/glib-2.0/glib/gregex.h \
  /opt/local/include/glib-2.0/glib/gscanner.h \
  /opt/local/include/glib-2.0/glib/gsequence.h \
  /opt/local/include/glib-2.0/glib/gshell.h \
  /opt/local/include/glib-2.0/glib/gslice.h \
  /opt/local/include/glib-2.0/glib/gspawn.h \
  /opt/local/include/glib-2.0/glib/gstringchunk.h \
  /opt/local/include/glib-2.0/glib/gstrvbuilder.h \
  /opt/local/include/glib-2.0/glib/gtestutils.h \
  /opt/local/include/glib-2.0/glib/gthreadpool.h \
  /opt/local/include/glib-2.0/glib/gtimer.h \
  /opt/local/include/glib-2.0/glib/gtrashstack.h \
  /opt/local/include/glib-2.0/glib/gtree.h \
  /opt/local/include/glib-2.0/glib/guri.h \
  /opt/local/include/glib-2.0/glib/guuid.h \
  /opt/local/include/glib-2.0/glib/gversion.h \
  /opt/local/include/glib-2.0/glib/deprecated/gallocator.h \
  /opt/local/include/glib-2.0/glib/deprecated/gcache.h \
  /opt/local/include/glib-2.0/glib/deprecated/gcompletion.h \
  /opt/local/include/glib-2.0/glib/deprecated/gmain.h \
  /opt/local/include/glib-2.0/glib/deprecated/grel.h \
  /opt/local/include/glib-2.0/glib/deprecated/gthread.h \
  /opt/local/include/glib-2.0/glib/glib-autocleanups.h \
  /opt/local/include/glib-2.0/gobject/gobject.h \
  /opt/local/include/glib-2.0/gobject/gtype.h \
  /opt/local/include/glib-2.0/gobject/gobject-visibility.h \
  /opt/local/include/glib-2.0/gobject/gvalue.h \
  /opt/local/include/glib-2.0/gobject/gparam.h \
  /opt/local/include/glib-2.0/gobject/gclosure.h \
  /opt/local/include/glib-2.0/gobject/gsignal.h \
  /opt/local/include/glib-2.0/gobject/gmarshal.h \
  /opt/local/include/glib-2.0/gobject/gboxed.h \
  /opt/local/include/glib-2.0/gobject/glib-types.h \
  /opt/local/include/glib-2.0/gobject/gbindinggroup.h \
  /opt/local/include/glib-2.0/gobject/genums.h \
  /opt/local/include/glib-2.0/gobject/glib-enumtypes.h \
  /opt/local/include/glib-2.0/gobject/gparamspecs.h \
  /opt/local/include/glib-2.0/gobject/gsignalgroup.h \
  /opt/local/include/glib-2.0/gobject/gsourceclosure.h \
  /opt/local/include/glib-2.0/gobject/gtypemodule.h \
  /opt/local/include/glib-2.0/gobject/gtypeplugin.h \
  /opt/local/include/glib-2.0/gobject/gvaluearray.h \
  /opt/local/include/glib-2.0/gobject/gvaluetypes.h \
  /opt/local/include/glib-2.0/gobject/gobject-autocleanups.h \
  /opt/local/include/pango-1.0/pango/pango-version-macros.h \
  /opt/local/include/pango-1.0/pango/pango-features.h \
  /opt/local/include/harfbuzz/hb.h /opt/local/include/harfbuzz/hb-blob.h \
  /opt/local/include/harfbuzz/hb-common.h \
  /opt/local/include/harfbuzz/hb-buffer.h \
  /opt/local/include/harfbuzz/hb-unicode.h \
  /opt/local/include/harfbuzz/hb-font.h \
  /opt/local/include/harfbuzz/hb-face.h \
  /opt/local/include/harfbuzz/hb-map.h \
  /opt/local/include/harfbuzz/hb-set.h \
  /opt/local/include/harfbuzz/hb-draw.h \
  /opt/local/include/harfbuzz/hb-paint.h \
  /opt/local/include/harfbuzz/hb-deprecated.h \
  /opt/local/include/harfbuzz/hb-shape.h \
  /opt/local/include/harfbuzz/hb-shape-plan.h \
  /opt/local/include/harfbuzz/hb-style.h \
  /opt/local/include/harfbuzz/hb-version.h \
  /opt/local/include/pango-1.0/pango/pango-types.h \
  /opt/local/include/pango-1.0/pango/pango-gravity.h \
  /opt/local/include/pango-1.0/pango/pango-matrix.h \
  /opt/local/include/pango-1.0/pango/pango-script.h \
  /opt/local/include/pango-1.0/pango/pango-language.h \
  /opt/local/include/pango-1.0/pango/pango-bidi-type.h \
  /opt/local/include/pango-1.0/pango/pango-direction.h \
  /opt/local/include/pango-1.0/pango/pango-color.h \
  /opt/local/include/pango-1.0/pango/pango-break.h \
  /opt/local/include/pango-1.0/pango/pango-item.h \
  /opt/local/include/pango-1.0/pango/pango-context.h \
  /opt/local/include/pango-1.0/pango/pango-fontmap.h \
  /opt/local/include/pango-1.0/pango/pango-fontset.h \
  /opt/local/include/pango-1.0/pango/pango-engine.h \
  /opt/local/include/pango-1.0/pango/pango-glyph.h \
  /opt/local/include/pango-1.0/pango/pango-enum-types.h \
  /opt/local/include/pango-1.0/pango/pango-fontset-simple.h \
  /opt/local/include/pango-1.0/pango/pango-glyph-item.h \
  /opt/local/include/pango-1.0/pango/pango-layout.h \
  /opt/local/include/pango-1.0/pango/pango-tabs.h \
  /opt/local/include/pango-1.0/pango/pango-markup.h \
  /opt/local/include/pango-1.0/pango/pango-renderer.h \
  /opt/local/include/pango-1.0/pango/pango-utils.h \
  /opt/local/include/jpeglib.h /opt/local/include/jconfig.h \
  /opt/local/include/jmorecfg.h /opt/local/include/png.h \
  /opt/local/include/pnglibconf.h /opt/local/include/pngconf.h \
  ../src/backend/SvgBackend.h
../src/Backends.cc:
../src/Backends.h:
../src/backend/Backend.h:
/opt/local/include/cairo/cairo.h:
/opt/local/include/cairo/cairo-version.h:
/opt/local/include/cairo/cairo-features.h:
/opt/local/include/cairo/cairo-deprecated.h:
../src/backend/../dll_visibility.h:
../../nan/nan.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/errno.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/unix.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/threadpool.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/uv/darwin.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/cppgc/common.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8config.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-array-buffer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-local-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-handle-base.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-internal.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-object.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-maybe.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-persistent-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-weak-callback-info.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-data.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-traced-handle.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-container.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-context.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-snapshot.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-isolate.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-callbacks.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-promise.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-debug.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-script.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-memory-span.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-message.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-heap.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function-callback.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-statistics.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-unwinder.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-embedder-state-scope.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-date.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-exception.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-extension.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-external.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-function.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-template.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-initialization.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-platform.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-source-location.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-json.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-locker.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-microtask-queue.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-primitive-object.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-proxy.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-regexp.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-typed-array.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-value-serializer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-version.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/v8-wasm.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/js_native_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_api_types.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_buffer.h:
/Users/<USER>/Library/Caches/node-gyp/22.13.1/include/node/node_object_wrap.h:
../../nan/nan_callbacks.h:
../../nan/nan_callbacks_12_inl.h:
../../nan/nan_maybe_43_inl.h:
../../nan/nan_converters.h:
../../nan/nan_converters_43_inl.h:
../../nan/nan_new.h:
../../nan/nan_implementation_12_inl.h:
../../nan/nan_persistent_12_inl.h:
../../nan/nan_weak.h:
../../nan/nan_object_wrap.h:
../../nan/nan_private.h:
../../nan/nan_typedarray_contents.h:
../../nan/nan_json.h:
../../nan/nan_scriptorigin.h:
../src/backend/ImageBackend.h:
../src/backend/PdfBackend.h:
../src/backend/../closure.h:
../src/Canvas.h:
../src/dll_visibility.h:
/opt/local/include/pango-1.0/pango/pangocairo.h:
/opt/local/include/pango-1.0/pango/pango.h:
/opt/local/include/pango-1.0/pango/pango-attributes.h:
/opt/local/include/pango-1.0/pango/pango-font.h:
/opt/local/include/pango-1.0/pango/pango-coverage.h:
/opt/local/include/glib-2.0/glib-object.h:
/opt/local/include/glib-2.0/gobject/gbinding.h:
/opt/local/include/glib-2.0/glib.h:
/opt/local/include/glib-2.0/glib/galloca.h:
/opt/local/include/glib-2.0/glib/gtypes.h:
/opt/local/lib/glib-2.0/include/glibconfig.h:
/opt/local/include/glib-2.0/glib/gmacros.h:
/opt/local/include/glib-2.0/glib/gversionmacros.h:
/opt/local/include/glib-2.0/glib/glib-visibility.h:
/opt/local/include/glib-2.0/glib/garray.h:
/opt/local/include/glib-2.0/glib/gasyncqueue.h:
/opt/local/include/glib-2.0/glib/gthread.h:
/opt/local/include/glib-2.0/glib/gatomic.h:
/opt/local/include/glib-2.0/glib/glib-typeof.h:
/opt/local/include/glib-2.0/glib/gerror.h:
/opt/local/include/glib-2.0/glib/gquark.h:
/opt/local/include/glib-2.0/glib/gutils.h:
/opt/local/include/glib-2.0/glib/gbacktrace.h:
/opt/local/include/glib-2.0/glib/gbase64.h:
/opt/local/include/glib-2.0/glib/gbitlock.h:
/opt/local/include/glib-2.0/glib/gbookmarkfile.h:
/opt/local/include/glib-2.0/glib/gdatetime.h:
/opt/local/include/glib-2.0/glib/gtimezone.h:
/opt/local/include/glib-2.0/glib/gbytes.h:
/opt/local/include/glib-2.0/glib/gcharset.h:
/opt/local/include/glib-2.0/glib/gchecksum.h:
/opt/local/include/glib-2.0/glib/gconvert.h:
/opt/local/include/glib-2.0/glib/gdataset.h:
/opt/local/include/glib-2.0/glib/gdate.h:
/opt/local/include/glib-2.0/glib/gdir.h:
/opt/local/include/glib-2.0/glib/genviron.h:
/opt/local/include/glib-2.0/glib/gfileutils.h:
/opt/local/include/glib-2.0/glib/ggettext.h:
/opt/local/include/glib-2.0/glib/ghash.h:
/opt/local/include/glib-2.0/glib/glist.h:
/opt/local/include/glib-2.0/glib/gmem.h:
/opt/local/include/glib-2.0/glib/gnode.h:
/opt/local/include/glib-2.0/glib/ghmac.h:
/opt/local/include/glib-2.0/glib/ghook.h:
/opt/local/include/glib-2.0/glib/ghostutils.h:
/opt/local/include/glib-2.0/glib/giochannel.h:
/opt/local/include/glib-2.0/glib/gmain.h:
/opt/local/include/glib-2.0/glib/gpoll.h:
/opt/local/include/glib-2.0/glib/gslist.h:
/opt/local/include/glib-2.0/glib/gstring.h:
/opt/local/include/glib-2.0/glib/gunicode.h:
/opt/local/include/glib-2.0/glib/gstrfuncs.h:
/opt/local/include/glib-2.0/glib/gkeyfile.h:
/opt/local/include/glib-2.0/glib/gmappedfile.h:
/opt/local/include/glib-2.0/glib/gmarkup.h:
/opt/local/include/glib-2.0/glib/gmessages.h:
/opt/local/include/glib-2.0/glib/gvariant.h:
/opt/local/include/glib-2.0/glib/gvarianttype.h:
/opt/local/include/glib-2.0/glib/goption.h:
/opt/local/include/glib-2.0/glib/gpathbuf.h:
/opt/local/include/glib-2.0/glib/gpattern.h:
/opt/local/include/glib-2.0/glib/gprimes.h:
/opt/local/include/glib-2.0/glib/gqsort.h:
/opt/local/include/glib-2.0/glib/gqueue.h:
/opt/local/include/glib-2.0/glib/grand.h:
/opt/local/include/glib-2.0/glib/grcbox.h:
/opt/local/include/glib-2.0/glib/grefcount.h:
/opt/local/include/glib-2.0/glib/grefstring.h:
/opt/local/include/glib-2.0/glib/gregex.h:
/opt/local/include/glib-2.0/glib/gscanner.h:
/opt/local/include/glib-2.0/glib/gsequence.h:
/opt/local/include/glib-2.0/glib/gshell.h:
/opt/local/include/glib-2.0/glib/gslice.h:
/opt/local/include/glib-2.0/glib/gspawn.h:
/opt/local/include/glib-2.0/glib/gstringchunk.h:
/opt/local/include/glib-2.0/glib/gstrvbuilder.h:
/opt/local/include/glib-2.0/glib/gtestutils.h:
/opt/local/include/glib-2.0/glib/gthreadpool.h:
/opt/local/include/glib-2.0/glib/gtimer.h:
/opt/local/include/glib-2.0/glib/gtrashstack.h:
/opt/local/include/glib-2.0/glib/gtree.h:
/opt/local/include/glib-2.0/glib/guri.h:
/opt/local/include/glib-2.0/glib/guuid.h:
/opt/local/include/glib-2.0/glib/gversion.h:
/opt/local/include/glib-2.0/glib/deprecated/gallocator.h:
/opt/local/include/glib-2.0/glib/deprecated/gcache.h:
/opt/local/include/glib-2.0/glib/deprecated/gcompletion.h:
/opt/local/include/glib-2.0/glib/deprecated/gmain.h:
/opt/local/include/glib-2.0/glib/deprecated/grel.h:
/opt/local/include/glib-2.0/glib/deprecated/gthread.h:
/opt/local/include/glib-2.0/glib/glib-autocleanups.h:
/opt/local/include/glib-2.0/gobject/gobject.h:
/opt/local/include/glib-2.0/gobject/gtype.h:
/opt/local/include/glib-2.0/gobject/gobject-visibility.h:
/opt/local/include/glib-2.0/gobject/gvalue.h:
/opt/local/include/glib-2.0/gobject/gparam.h:
/opt/local/include/glib-2.0/gobject/gclosure.h:
/opt/local/include/glib-2.0/gobject/gsignal.h:
/opt/local/include/glib-2.0/gobject/gmarshal.h:
/opt/local/include/glib-2.0/gobject/gboxed.h:
/opt/local/include/glib-2.0/gobject/glib-types.h:
/opt/local/include/glib-2.0/gobject/gbindinggroup.h:
/opt/local/include/glib-2.0/gobject/genums.h:
/opt/local/include/glib-2.0/gobject/glib-enumtypes.h:
/opt/local/include/glib-2.0/gobject/gparamspecs.h:
/opt/local/include/glib-2.0/gobject/gsignalgroup.h:
/opt/local/include/glib-2.0/gobject/gsourceclosure.h:
/opt/local/include/glib-2.0/gobject/gtypemodule.h:
/opt/local/include/glib-2.0/gobject/gtypeplugin.h:
/opt/local/include/glib-2.0/gobject/gvaluearray.h:
/opt/local/include/glib-2.0/gobject/gvaluetypes.h:
/opt/local/include/glib-2.0/gobject/gobject-autocleanups.h:
/opt/local/include/pango-1.0/pango/pango-version-macros.h:
/opt/local/include/pango-1.0/pango/pango-features.h:
/opt/local/include/harfbuzz/hb.h:
/opt/local/include/harfbuzz/hb-blob.h:
/opt/local/include/harfbuzz/hb-common.h:
/opt/local/include/harfbuzz/hb-buffer.h:
/opt/local/include/harfbuzz/hb-unicode.h:
/opt/local/include/harfbuzz/hb-font.h:
/opt/local/include/harfbuzz/hb-face.h:
/opt/local/include/harfbuzz/hb-map.h:
/opt/local/include/harfbuzz/hb-set.h:
/opt/local/include/harfbuzz/hb-draw.h:
/opt/local/include/harfbuzz/hb-paint.h:
/opt/local/include/harfbuzz/hb-deprecated.h:
/opt/local/include/harfbuzz/hb-shape.h:
/opt/local/include/harfbuzz/hb-shape-plan.h:
/opt/local/include/harfbuzz/hb-style.h:
/opt/local/include/harfbuzz/hb-version.h:
/opt/local/include/pango-1.0/pango/pango-types.h:
/opt/local/include/pango-1.0/pango/pango-gravity.h:
/opt/local/include/pango-1.0/pango/pango-matrix.h:
/opt/local/include/pango-1.0/pango/pango-script.h:
/opt/local/include/pango-1.0/pango/pango-language.h:
/opt/local/include/pango-1.0/pango/pango-bidi-type.h:
/opt/local/include/pango-1.0/pango/pango-direction.h:
/opt/local/include/pango-1.0/pango/pango-color.h:
/opt/local/include/pango-1.0/pango/pango-break.h:
/opt/local/include/pango-1.0/pango/pango-item.h:
/opt/local/include/pango-1.0/pango/pango-context.h:
/opt/local/include/pango-1.0/pango/pango-fontmap.h:
/opt/local/include/pango-1.0/pango/pango-fontset.h:
/opt/local/include/pango-1.0/pango/pango-engine.h:
/opt/local/include/pango-1.0/pango/pango-glyph.h:
/opt/local/include/pango-1.0/pango/pango-enum-types.h:
/opt/local/include/pango-1.0/pango/pango-fontset-simple.h:
/opt/local/include/pango-1.0/pango/pango-glyph-item.h:
/opt/local/include/pango-1.0/pango/pango-layout.h:
/opt/local/include/pango-1.0/pango/pango-tabs.h:
/opt/local/include/pango-1.0/pango/pango-markup.h:
/opt/local/include/pango-1.0/pango/pango-renderer.h:
/opt/local/include/pango-1.0/pango/pango-utils.h:
/opt/local/include/jpeglib.h:
/opt/local/include/jconfig.h:
/opt/local/include/jmorecfg.h:
/opt/local/include/png.h:
/opt/local/include/pnglibconf.h:
/opt/local/include/pngconf.h:
../src/backend/SvgBackend.h:
