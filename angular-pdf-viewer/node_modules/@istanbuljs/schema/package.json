{"name": "@istanbuljs/schema", "version": "0.1.3", "description": "Schemas describing various structures used by nyc and istanbuljs", "main": "index.js", "scripts": {"release": "standard-version --sign", "pretest": "xo", "test": "tap", "snap": "npm test -- --snapshot"}, "engines": {"node": ">=8"}, "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/istanbuljs/schema.git"}, "bugs": {"url": "https://github.com/istanbuljs/schema/issues"}, "homepage": "https://github.com/istanbuljs/schema#readme", "devDependencies": {"standard-version": "^7.0.0", "tap": "^14.6.7", "xo": "^0.25.3"}}