{"name": "minipass-collect", "version": "2.0.1", "description": "A Minipass stream that collects all the data into a single chunk", "author": "<PERSON> <<EMAIL>> (https://izs.me)", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags"}, "tap": {"check-coverage": true}, "devDependencies": {"tap": "^16.3.8"}, "dependencies": {"minipass": "^7.0.3"}, "files": ["index.js"], "engines": {"node": ">=16 || 14 >=14.17"}, "repository": "https://github.com/isaacs/minipass-collect"}