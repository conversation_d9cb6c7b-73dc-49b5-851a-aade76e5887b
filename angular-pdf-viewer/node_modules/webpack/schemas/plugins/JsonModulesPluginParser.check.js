/*
 * This file was automatically generated.
 * DO NOT MODIFY BY HAND.
 * Run `yarn special-lint-fix` to update
 */
"use strict";function r(t,{instancePath:e="",parentData:a,parentDataProperty:o,rootData:n=t}={}){if(!t||"object"!=typeof t||Array.isArray(t))return r.errors=[{params:{type:"object"}}],!1;{const e=0;for(const e in t)if("parse"!==e)return r.errors=[{params:{additionalProperty:e}}],!1;if(0===e&&void 0!==t.parse&&!(t.parse instanceof Function))return r.errors=[{params:{}}],!1}return r.errors=null,!0}module.exports=r,module.exports.default=r;