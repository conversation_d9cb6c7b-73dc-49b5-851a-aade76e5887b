/*
	MIT License http://www.opensource.org/licenses/mit-license.php
*/

"use strict";

const { register } = require("./serialization");

/** @typedef {import("../serialization/ObjectMiddleware").Constructor} Constructor */

class ClassSerializer {
	constructor(Constructor) {
		this.Constructor = Constructor;
	}

	serialize(obj, context) {
		obj.serialize(context);
	}

	deserialize(context) {
		if (typeof this.Constructor.deserialize === "function") {
			return this.Constructor.deserialize(context);
		}
		const obj = new this.Constructor();
		obj.deserialize(context);
		return obj;
	}
}

/**
 * @param {Constructor} Constructor the constructor
 * @param {string} request the request which will be required when deserializing
 * @param {string | null} [name] the name to make multiple serializer unique when sharing a request
 */
module.exports = (Constructor, request, name = null) => {
	register(Constructor, request, name, new ClassSerializer(Constructor));
};
