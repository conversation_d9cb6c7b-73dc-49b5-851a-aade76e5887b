<% if(!!viewEncapsulation) { %>import { ViewEncapsulation } from '@angular/core';
<% }%>import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';

<% if(!!viewEncapsulation) { %>
platformBrowserDynamic().bootstrapModule(AppModule, {
  defaultEncapsulation: ViewEncapsulation.<%= viewEncapsulation %>
})
  .catch(err => console.error(err));<% } else { %>
platformBrowserDynamic().bootstrapModule(AppModule)
  .catch(err => console.error(err));
<% } %>