import { TestBed } from '@angular/core/testing';
import { ResolveFn } from '@angular/router';

import { <%= camelize(name) %>Resolver } from './<%= dasherize(name) %>.resolver';

describe('<%= camelize(name) %>Resolver', () => {
  const executeResolver: ResolveFn<boolean> = (...resolverParameters) => 
      TestBed.runInInjectionContext(() => <%= camelize(name) %>Resolver(...resolverParameters));

  beforeEach(() => {
    TestBed.configureTestingModule({});
  });

  it('should be created', () => {
    expect(executeResolver).toBeTruthy();
  });
});
