{"name": "@schematics/angular", "version": "17.3.17", "description": "Schematics specific to Angular", "homepage": "https://github.com/angular/angular-cli", "keywords": ["Angular CLI", "Angular DevKit", "angular", "blueprints", "code generation", "devkit", "schematics", "sdk"], "exports": {"./package.json": "./package.json", "./utility": "./utility/index.js", "./utility/*": "./utility/*.js", "./migrations/migration-collection.json": "./migrations/migration-collection.json", "./*": "./*.js", "./private/components": "./private/components.js"}, "schematics": "./collection.json", "dependencies": {"@angular-devkit/core": "17.3.17", "@angular-devkit/schematics": "17.3.17", "jsonc-parser": "3.2.1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular-cli.git"}, "engines": {"node": "^18.13.0 || >=20.9.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "author": "Angular Authors", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular-cli/issues"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}