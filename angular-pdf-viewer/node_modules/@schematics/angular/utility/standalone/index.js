"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.addRootProvider = exports.addRootImport = void 0;
var rules_1 = require("./rules");
Object.defineProperty(exports, "addRootImport", { enumerable: true, get: function () { return rules_1.addRootImport; } });
Object.defineProperty(exports, "addRootProvider", { enumerable: true, get: function () { return rules_1.addRootProvider; } });
