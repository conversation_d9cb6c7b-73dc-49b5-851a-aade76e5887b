"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.Implement = void 0;
var Implement;
(function (Implement) {
    Implement["CanActivate"] = "CanActivate";
    Implement["CanActivateChild"] = "CanActivateChild";
    Implement["CanDeactivate"] = "CanDeactivate";
    Implement["CanMatch"] = "CanMatch";
})(Implement || (exports.Implement = Implement = {}));
