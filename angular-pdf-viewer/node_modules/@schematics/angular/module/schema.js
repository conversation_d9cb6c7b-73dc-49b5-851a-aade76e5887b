"use strict";
// THIS FILE IS AUTOMATICALLY GENERATED. TO UPDATE THIS FILE YOU NEED TO CHANGE THE
// CORRESPONDING JSON SCHEMA FILE, THEN RUN devkit-admin build (or bazel build ...).
Object.defineProperty(exports, "__esModule", { value: true });
exports.RoutingScope = void 0;
/**
 * The scope for the new routing module.
 */
var RoutingScope;
(function (RoutingScope) {
    RoutingScope["Child"] = "Child";
    RoutingScope["Root"] = "Root";
})(RoutingScope || (exports.RoutingScope = RoutingScope = {}));
