{"name": "karma-jasmine", "version": "5.1.0", "description": "A Karma plugin - adapter for Jasmine testing framework.", "main": "lib/index.js", "files": ["lib/*.js"], "scripts": {"build": "grunt build", "lint": "eslint \"**/*.js\"", "lint:fix": "eslint --fix \"**/*.js\"", "commitlint": "commitlint", "test": "npm run test:unit && npm run test:e2e && npm run test:integration", "test:unit": "jasmine", "test:e2e": "karma start karma.conf.js", "test:integration": "bash tools/integration-tests.sh", "release": "semantic-release"}, "repository": {"type": "git", "url": "git://github.com/karma-runner/karma-jasmine.git"}, "keywords": ["karma-plugin", "karma-adapter", "jasmine"], "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "dependencies": {"jasmine-core": "^4.1.0"}, "devDependencies": {"@commitlint/cli": "^16.2.3", "@commitlint/config-angular": "^16.2.3", "@semantic-release/changelog": "^6.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^8.0.4", "@semantic-release/npm": "^9.0.1", "eslint": "^7.32.0", "eslint-config-standard": "^16.0.3", "eslint-plugin-import": "^2.26.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^5.2.0", "eslint-plugin-standard": "^4.1.0", "grunt": "^1.5.2", "husky": "^4.3.8", "jasmine": "^4.1.0", "karma": "^6.3.18", "karma-firefox-launcher": "^2.1.2", "semantic-release": "^19.0.2"}, "peerDependencies": {"karma": "^6.0.0"}, "engines": {"node": ">=12"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "license": "MIT", "contributors": ["<PERSON><PERSON><PERSON> <<EMAIL>>", "johnj<PERSON>ton <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "XhmikosR <<EMAIL>>", "olegskl <<EMAIL>>", "semantic-release-bot <<EMAIL>>", "dependabot[bot] <49699333+dependabot[bot]@users.noreply.github.com>", "dignifiedquire <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>yer <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <jansen<PERSON><EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> N <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<PERSON><PERSON><PERSON><PERSON>@GoogleMail.com>", "<PERSON><PERSON><PERSON><PERSON> <dtychs<PERSON><PERSON>@users.noreply.github.com>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <g<PERSON><PERSON><PERSON>@pivotal.io>", "<PERSON> <<EMAIL>>", "<PERSON> <joa<PERSON><PERSON><PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "Limon Monte <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "Milan Lempera <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON>, <PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <Vladimir.<PERSON>@hotmail.com>", "<PERSON><PERSON> <<EMAIL>>", "jiverson <<EMAIL>>", "rpark <<EMAIL>>", "strille <<EMAIL>>"]}