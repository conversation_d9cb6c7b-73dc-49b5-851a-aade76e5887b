/// <reference types="node" />
import { WebpackChunkModule } from './WebpackChunkModule';
declare class WebpackInnerModuleIterator {
    private requireResolve;
    constructor(requireResolve: RequireResolve);
    iterateModules(chunkModule: WebpackChunkModule, callback: (module: WebpackChunkModule) => void): void;
    private internalCallback;
    getActualFilename(filename: string | null | undefined): string | null;
}
export { WebpackInnerModuleIterator };
