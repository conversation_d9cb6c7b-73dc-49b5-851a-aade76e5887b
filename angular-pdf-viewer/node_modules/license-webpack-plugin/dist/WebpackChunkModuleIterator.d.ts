import { WebpackChunk } from './WebpackChunk';
import { WebpackChunkModule } from './WebpackChunkModule';
import { WebpackCompilation } from './WebpackCompilation';
import { WebpackStats } from './WebpackStats';
declare class WebpackChunkModuleIterator {
    private statsIterator;
    iterateModules(compilation: WebpackCompilation, chunk: WebpackChunk, stats: WebpackStats | undefined, callback: (module: WebpackChunkModule) => void): void;
}
export { WebpackChunkModuleIterator };
