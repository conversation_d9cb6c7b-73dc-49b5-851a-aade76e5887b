{"name": "date-format", "version": "4.0.14", "description": "Formatting Date objects as strings since 2013", "main": "lib/index.js", "files": ["lib", "CHANGELOG.md"], "repository": {"type": "git", "url": "https://github.com/nomiddlename/date-format.git"}, "engines": {"node": ">=4.0"}, "scripts": {"lint": "eslint lib/* test/*", "pretest": "eslint lib/* test/*", "test": "nyc --check-coverage mocha"}, "keywords": ["date", "format", "string"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "readmeFilename": "README.md", "gitHead": "bf59015ab6c9e86454b179374f29debbdb403522", "devDependencies": {"eslint": "^8.24.0", "eslint-plugin-mocha": "^10.1.0", "mocha": "^10.0.0", "nyc": "^15.1.0", "should": "^13.2.3"}, "nyc": {"include": ["lib/**"], "branches": 100, "lines": 100, "functions": 100}}