{"name": "emojis-list", "description": "Complete list of standard emojis.", "homepage": "https://nidecoc.io/Kikobeats/emojis-list", "version": "3.0.0", "main": "./index.js", "author": {"email": "<EMAIL>", "name": "<PERSON><PERSON>", "url": "https://github.com/Kikobeats"}, "repository": {"type": "git", "url": "git+https://github.com/kikobeats/emojis-list.git"}, "bugs": {"url": "https://github.com/Kikobeats/emojis-list/issues"}, "keywords": ["archive", "complete", "emoji", "list", "standard"], "devDependencies": {"acho": "latest", "browserify": "latest", "cheerio": "latest", "got": ">=5 <6", "standard": "latest"}, "engines": {"node": ">= 4"}, "files": ["index.js"], "scripts": {"pretest": "standard update.js", "test": "echo 'YOLO'", "update": "node update"}, "license": "MIT"}