{"name": "ssri", "version": "10.0.6", "description": "Standard Subresource Integrity library -- parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "main": "lib/index.js", "files": ["bin/", "lib/"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish", "posttest": "npm run lint", "test": "tap", "coverage": "tap", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "postlint": "template-oss-check", "template-oss-apply": "template-oss-apply --force", "lintfix": "npm run lint -- --fix", "snap": "tap"}, "tap": {"check-coverage": true, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "repository": {"type": "git", "url": "git+https://github.com/npm/ssri.git"}, "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "author": "GitHub Inc.", "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": "true"}}