{"name": "media-typer", "description": "Simple RFC 6838 media type parser and formatter", "version": "0.3.0", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "repository": "jshttp/media-typer", "devDependencies": {"istanbul": "0.3.2", "mocha": "~1.21.4", "should": "~4.0.4"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}}