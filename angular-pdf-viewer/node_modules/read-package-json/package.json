{"name": "read-package-json", "version": "7.0.1", "author": "GitHub Inc.", "description": "The thing npm uses to read package.json files with semantics and defaults and validation", "repository": {"type": "git", "url": "git+https://github.com/npm/read-package-json.git"}, "main": "lib/read-json.js", "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "release": "standard-version -s", "test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"glob": "^10.2.2", "json-parse-even-better-errors": "^3.0.0", "normalize-package-data": "^6.0.0", "npm-normalize-package-bin": "^3.0.0"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "license": "ISC", "files": ["bin/", "lib/"], "engines": {"node": "^16.14.0 || >=18.0.0"}, "tap": {"branches": 73, "functions": 77, "lines": 77, "statements": 77, "nyc-arg": ["--exclude", "tap-snapshots/**"]}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": "true"}}