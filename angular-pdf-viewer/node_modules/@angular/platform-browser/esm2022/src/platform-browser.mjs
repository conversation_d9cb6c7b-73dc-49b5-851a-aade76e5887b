/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.
import { makeStateKey as makeStateKeyFromCore, TransferState as TransferStateFromCore } from '@angular/core';
/**
 * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.
 *
 * Example:
 *
 * ```
 * const COUNTER_KEY = makeStateKey<number>('counter');
 * let value = 10;
 *
 * transferState.set(COUNTER_KEY, value);
 * ```
 *
 * @publicApi
 * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.
 */
// The below is a workaround to add a deprecated message.
export const makeStateKey = makeStateKeyFromCore;
// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.
export const TransferState = TransferStateFromCore;
export { bootstrapApplication, BrowserModule, createApplication, platformBrowser, provideProtractorTestingSupport } from './browser';
export { Meta } from './browser/meta';
export { Title } from './browser/title';
export { disableDebugTools, enableDebugTools } from './browser/tools/tools';
export { By } from './dom/debug/by';
export { REMOVE_STYLES_ON_COMPONENT_DESTROY } from './dom/dom_renderer';
export { EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin } from './dom/events/event_manager';
export { HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerModule } from './dom/events/hammer_gestures';
export { DomSanitizer } from './security/dom_sanitization_service';
export { provideClientHydration, HydrationFeatureKind, withHttpTransferCacheOptions, withNoHttpTransferCache } from './hydration';
export * from './private_export';
export { VERSION } from './version';
//# sourceMappingURL=data:application/json;base64,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