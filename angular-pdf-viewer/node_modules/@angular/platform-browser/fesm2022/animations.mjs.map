{"version": 3, "file": "animations.mjs", "sources": ["../../../../../../packages/platform-browser/animations/src/providers.ts", "../../../../../../packages/platform-browser/animations/src/module.ts", "../../../../../../packages/platform-browser/animations/src/animations.ts", "../../../../../../packages/platform-browser/animations/public_api.ts", "../../../../../../packages/platform-browser/animations/index.ts", "../../../../../../packages/platform-browser/animations/animations.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {AnimationDriver, NoopAnimationDriver, ɵAnimationEngine as AnimationEngine, ɵAnimationRendererFactory as AnimationRendererFactory, ɵAnimationStyleNormalizer as AnimationStyleNormalizer, ɵWebAnimationsDriver as WebAnimationsDriver, ɵWebAnimationsStyleNormalizer as WebAnimationsStyleNormalizer} from '@angular/animations/browser';\nimport {DOCUMENT} from '@angular/common';\nimport {ANIMATION_MODULE_TYPE, inject, Inject, Injectable, NgZone, OnDestroy, Provider, RendererFactory2, ɵChangeDetectionScheduler as ChangeDetectionScheduler} from '@angular/core';\nimport {ɵDomRendererFactory2 as DomRendererFactory2} from '@angular/platform-browser';\n\n@Injectable()\nexport class InjectableAnimationEngine extends AnimationEngine implements OnDestroy {\n  // The `ApplicationRef` is injected here explicitly to force the dependency ordering.\n  // Since the `ApplicationRef` should be created earlier before the `AnimationEngine`, they\n  // both have `ngOnDestroy` hooks and `flush()` must be called after all views are destroyed.\n  constructor(\n      @Inject(DOCUMENT) doc: Document, driver: AnimationDriver,\n      normalizer: AnimationStyleNormalizer) {\n    super(doc, driver, normalizer, inject(ChangeDetectionScheduler, {optional: true}));\n  }\n\n  ngOnDestroy(): void {\n    this.flush();\n  }\n}\n\nexport function instantiateDefaultStyleNormalizer() {\n  return new WebAnimationsStyleNormalizer();\n}\n\nexport function instantiateRendererFactory(\n    renderer: DomRendererFactory2, engine: AnimationEngine, zone: NgZone) {\n  return new AnimationRendererFactory(renderer, engine, zone);\n}\n\nconst SHARED_ANIMATION_PROVIDERS: Provider[] = [\n  {provide: AnimationStyleNormalizer, useFactory: instantiateDefaultStyleNormalizer},\n  {provide: AnimationEngine, useClass: InjectableAnimationEngine}, {\n    provide: RendererFactory2,\n    useFactory: instantiateRendererFactory,\n    deps: [DomRendererFactory2, AnimationEngine, NgZone]\n  }\n];\n\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserModule.\n */\nexport const BROWSER_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useFactory: () => new WebAnimationsDriver()},\n  {provide: ANIMATION_MODULE_TYPE, useValue: 'BrowserAnimations'}, ...SHARED_ANIMATION_PROVIDERS\n];\n\n/**\n * Separate providers from the actual module so that we can do a local modification in Google3 to\n * include them in the BrowserTestingModule.\n */\nexport const BROWSER_NOOP_ANIMATIONS_PROVIDERS: Provider[] = [\n  {provide: AnimationDriver, useClass: NoopAnimationDriver},\n  {provide: ANIMATION_MODULE_TYPE, useValue: 'NoopAnimations'}, ...SHARED_ANIMATION_PROVIDERS\n];\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nimport {ModuleWithProviders, NgModule, Provider, ɵperformanceMarkFeature as performanceMarkFeature} from '@angular/core';\nimport {BrowserModule} from '@angular/platform-browser';\n\nimport {BROWSER_ANIMATIONS_PROVIDERS, BROWSER_NOOP_ANIMATIONS_PROVIDERS} from './providers';\n\n/**\n * Object used to configure the behavior of {@link BrowserAnimationsModule}\n * @publicApi\n */\nexport interface BrowserAnimationsModuleConfig {\n  /**\n   *  Whether animations should be disabled. Passing this is identical to providing the\n   * `NoopAnimationsModule`, but it can be controlled based on a runtime value.\n   */\n  disableAnimations?: boolean;\n}\n\n/**\n * Exports `BrowserModule` with additional [dependency-injection providers](guide/glossary#provider)\n * for use with animations. See [Animations](guide/animations).\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: BROWSER_ANIMATIONS_PROVIDERS,\n})\nexport class BrowserAnimationsModule {\n  /**\n   * Configures the module based on the specified object.\n   *\n   * @param config Object used to configure the behavior of the `BrowserAnimationsModule`.\n   * @see {@link BrowserAnimationsModuleConfig}\n   *\n   * @usageNotes\n   * When registering the `BrowserAnimationsModule`, you can use the `withConfig`\n   * function as follows:\n   * ```\n   * @NgModule({\n   *   imports: [BrowserAnimationsModule.withConfig(config)]\n   * })\n   * class MyNgModule {}\n   * ```\n   */\n  static withConfig(config: BrowserAnimationsModuleConfig):\n      ModuleWithProviders<BrowserAnimationsModule> {\n    return {\n      ngModule: BrowserAnimationsModule,\n      providers: config.disableAnimations ? BROWSER_NOOP_ANIMATIONS_PROVIDERS :\n                                            BROWSER_ANIMATIONS_PROVIDERS\n    };\n  }\n}\n\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to enable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to enable animations in an application\n * bootstrapped using the `bootstrapApplication` function. In this scenario there\n * is no need to import the `BrowserAnimationsModule` NgModule at all, just add\n * providers returned by this function to the `providers` list as show below.\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nexport function provideAnimations(): Provider[] {\n  performanceMarkFeature('NgEagerAnimations');\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideAnimations` call results in app code.\n  return [...BROWSER_ANIMATIONS_PROVIDERS];\n}\n\n/**\n * A null player that must be imported to allow disabling of animations.\n * @publicApi\n */\n@NgModule({\n  exports: [BrowserModule],\n  providers: BROWSER_NOOP_ANIMATIONS_PROVIDERS,\n})\nexport class NoopAnimationsModule {\n}\n\n/**\n * Returns the set of [dependency-injection providers](guide/glossary#provider)\n * to disable animations in an application. See [animations guide](guide/animations)\n * to learn more about animations in Angular.\n *\n * @usageNotes\n *\n * The function is useful when you want to bootstrap an application using\n * the `bootstrapApplication` function, but you need to disable animations\n * (for example, when running tests).\n *\n * ```typescript\n * bootstrapApplication(RootComponent, {\n *   providers: [\n *     provideNoopAnimations()\n *   ]\n * });\n * ```\n *\n * @publicApi\n */\nexport function provideNoopAnimations(): Provider[] {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideNoopAnimations` call results in app code.\n  return [...BROWSER_NOOP_ANIMATIONS_PROVIDERS];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all animation APIs of the animation browser package.\n */\nexport {ANIMATION_MODULE_TYPE} from '@angular/core';\nexport {BrowserAnimationsModule, BrowserAnimationsModuleConfig, NoopAnimationsModule, provideAnimations, provideNoopAnimations} from './module';\n\nexport * from './private_export';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/animations';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["AnimationEngine", "ChangeDetectionScheduler", "WebAnimationsStyleNormalizer", "AnimationRendererFactory", "AnimationStyleNormalizer", "DomRendererFactory2", "WebAnimationsDriver", "performanceMarkFeature"], "mappings": ";;;;;;;;;;;;;;AAcM,MAAO,yBAA0B,SAAQA,gBAAe,CAAA;;;;AAI5D,IAAA,WAAA,CACsB,GAAa,EAAE,MAAuB,EACxD,UAAoC,EAAA;AACtC,QAAA,KAAK,CAAC,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,CAACC,yBAAwB,EAAE,EAAC,QAAQ,EAAE,IAAI,EAAC,CAAC,CAAC,CAAC;KACpF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,KAAK,EAAE,CAAC;KACd;AAZU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,yBAAyB,kBAKxB,QAAQ,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,eAAA,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,yBAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HALT,yBAAyB,EAAA,CAAA,CAAA,EAAA;;sGAAzB,yBAAyB,EAAA,UAAA,EAAA,CAAA;kBADrC,UAAU;;0BAMJ,MAAM;2BAAC,QAAQ,CAAA;;SAUN,iCAAiC,GAAA;IAC/C,OAAO,IAAIC,6BAA4B,EAAE,CAAC;AAC5C,CAAC;SAEe,0BAA0B,CACtC,QAA6B,EAAE,MAAuB,EAAE,IAAY,EAAA;IACtE,OAAO,IAAIC,yBAAwB,CAAC,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC;AAC9D,CAAC;AAED,MAAM,0BAA0B,GAAe;AAC7C,IAAA,EAAC,OAAO,EAAEC,yBAAwB,EAAE,UAAU,EAAE,iCAAiC,EAAC;IAClF,EAAC,OAAO,EAAEJ,gBAAe,EAAE,QAAQ,EAAE,yBAAyB,EAAC,EAAE;AAC/D,QAAA,OAAO,EAAE,gBAAgB;AACzB,QAAA,UAAU,EAAE,0BAA0B;AACtC,QAAA,IAAI,EAAE,CAACK,oBAAmB,EAAEL,gBAAe,EAAE,MAAM,CAAC;AACrD,KAAA;CACF,CAAC;AAEF;;;AAGG;AACI,MAAM,4BAA4B,GAAe;AACtD,IAAA,EAAC,OAAO,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM,IAAIM,oBAAmB,EAAE,EAAC;IACvE,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,mBAAmB,EAAC,EAAE,GAAG,0BAA0B;CAC/F,CAAC;AAEF;;;AAGG;AACI,MAAM,iCAAiC,GAAe;AAC3D,IAAA,EAAC,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,mBAAmB,EAAC;IACzD,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,gBAAgB,EAAC,EAAE,GAAG,0BAA0B;CAC5F;;ACvCD;;;;AAIG;MAKU,uBAAuB,CAAA;AAClC;;;;;;;;;;;;;;;AAeG;IACH,OAAO,UAAU,CAAC,MAAqC,EAAA;QAErD,OAAO;AACL,YAAA,QAAQ,EAAE,uBAAuB;YACjC,SAAS,EAAE,MAAM,CAAC,iBAAiB,GAAG,iCAAiC;gBACjC,4BAA4B;SACnE,CAAC;KACH;yHAxBU,uBAAuB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAAvB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,uBAAuB,YAHxB,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;0HAGZ,uBAAuB,EAAA,SAAA,EAFvB,4BAA4B,EAAA,OAAA,EAAA,CAD7B,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAGZ,uBAAuB,EAAA,UAAA,EAAA,CAAA;kBAJnC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE,4BAA4B;AACxC,iBAAA,CAAA;;AA4BD;;;;;;;;;;;;;;;;;;;;;AAqBG;SACa,iBAAiB,GAAA;IAC/BC,uBAAsB,CAAC,mBAAmB,CAAC,CAAC;;;AAG5C,IAAA,OAAO,CAAC,GAAG,4BAA4B,CAAC,CAAC;AAC3C,CAAC;AAED;;;AAGG;MAKU,oBAAoB,CAAA;yHAApB,oBAAoB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;AAApB,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,YAHrB,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;0HAGZ,oBAAoB,EAAA,SAAA,EAFpB,iCAAiC,EAAA,OAAA,EAAA,CADlC,aAAa,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAGZ,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBAJhC,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;oBACR,OAAO,EAAE,CAAC,aAAa,CAAC;AACxB,oBAAA,SAAS,EAAE,iCAAiC;AAC7C,iBAAA,CAAA;;AAID;;;;;;;;;;;;;;;;;;;;AAoBG;SACa,qBAAqB,GAAA;;;AAGnC,IAAA,OAAO,CAAC,GAAG,iCAAiC,CAAC,CAAC;AAChD;;ACrHA;;;;AAIG;;ACJH;;;;AAIG;;ACJH;;ACRA;;AAEG;;;;"}