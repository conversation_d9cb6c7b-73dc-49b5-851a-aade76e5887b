{"version": 3, "file": "platform-browser.mjs", "sources": ["../../../../../../packages/platform-browser/src/browser/generic_browser_adapter.ts", "../../../../../../packages/platform-browser/src/browser/browser_adapter.ts", "../../../../../../packages/platform-browser/src/browser/testability.ts", "../../../../../../packages/platform-browser/src/browser/xhr.ts", "../../../../../../packages/platform-browser/src/dom/events/event_manager.ts", "../../../../../../packages/platform-browser/src/dom/shared_styles_host.ts", "../../../../../../packages/platform-browser/src/dom/dom_renderer.ts", "../../../../../../packages/platform-browser/src/dom/events/dom_events.ts", "../../../../../../packages/platform-browser/src/dom/events/key_events.ts", "../../../../../../packages/platform-browser/src/browser.ts", "../../../../../../packages/platform-browser/src/browser/meta.ts", "../../../../../../packages/platform-browser/src/browser/title.ts", "../../../../../../packages/platform-browser/src/dom/util.ts", "../../../../../../packages/platform-browser/src/browser/tools/common_tools.ts", "../../../../../../packages/platform-browser/src/browser/tools/tools.ts", "../../../../../../packages/platform-browser/src/dom/debug/by.ts", "../../../../../../packages/platform-browser/src/dom/events/hammer_gestures.ts", "../../../../../../packages/platform-browser/src/security/dom_sanitization_service.ts", "../../../../../../packages/platform-browser/src/hydration.ts", "../../../../../../packages/platform-browser/src/version.ts", "../../../../../../packages/platform-browser/src/platform-browser.ts", "../../../../../../packages/platform-browser/public_api.ts", "../../../../../../packages/platform-browser/index.ts", "../../../../../../packages/platform-browser/platform-browser.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵDomAdapter as DomAdapter} from '@angular/common';\n\n\n\n/**\n * Provides DOM operations in any browser environment.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\nexport abstract class GenericBrowserDomAdapter extends DomAdapter {\n  override readonly supportsDOMEvents: boolean = true;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵparseCookieValue as parseCookieValue, ɵsetRootDomAdapter as setRootDomAdapter} from '@angular/common';\n\nimport {GenericBrowserDomAdapter} from './generic_browser_adapter';\n\n/**\n * A `DomAdapter` powered by full browser DOM APIs.\n *\n * @security Tread carefully! Interacting with the DOM directly is dangerous and\n * can introduce XSS risks.\n */\n/* tslint:disable:requireParameterType no-console */\nexport class BrowserDomAdapter extends GenericBrowserDomAdapter {\n  static makeCurrent() {\n    setRootDomAdapter(new BrowserDomAdapter());\n  }\n\n  override onAndCancel(el: Node, evt: any, listener: any): Function {\n    el.addEventListener(evt, listener);\n    return () => {\n      el.removeEventListener(evt, listener);\n    };\n  }\n  override dispatchEvent(el: Node, evt: any) {\n    el.dispatchEvent(evt);\n  }\n  override remove(node: Node): void {\n    if (node.parentNode) {\n      node.parentNode.removeChild(node);\n    }\n  }\n  override createElement(tagName: string, doc?: Document): HTMLElement {\n    doc = doc || this.getDefaultDocument();\n    return doc.createElement(tagName);\n  }\n  override createHtmlDocument(): Document {\n    return document.implementation.createHTMLDocument('fakeTitle');\n  }\n  override getDefaultDocument(): Document {\n    return document;\n  }\n\n  override isElementNode(node: Node): boolean {\n    return node.nodeType === Node.ELEMENT_NODE;\n  }\n\n  override isShadowRoot(node: any): boolean {\n    return node instanceof DocumentFragment;\n  }\n\n  /** @deprecated No longer being used in Ivy code. To be removed in version 14. */\n  override getGlobalEventTarget(doc: Document, target: string): EventTarget|null {\n    if (target === 'window') {\n      return window;\n    }\n    if (target === 'document') {\n      return doc;\n    }\n    if (target === 'body') {\n      return doc.body;\n    }\n    return null;\n  }\n  override getBaseHref(doc: Document): string|null {\n    const href = getBaseElementHref();\n    return href == null ? null : relativePath(href);\n  }\n  override resetBaseElement(): void {\n    baseElement = null;\n  }\n  override getUserAgent(): string {\n    return window.navigator.userAgent;\n  }\n  override getCookie(name: string): string|null {\n    return parseCookieValue(document.cookie, name);\n  }\n}\n\nlet baseElement: HTMLElement|null = null;\nfunction getBaseElementHref(): string|null {\n  baseElement = baseElement || document.querySelector('base');\n  return baseElement ? baseElement.getAttribute('href') : null;\n}\n\nfunction relativePath(url: string): string {\n  // The base URL doesn't really matter, we just need it so relative paths have something\n  // to resolve against. In the browser `HTMLBaseElement.href` is always absolute.\n  return new URL(url, document.baseURI).pathname;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵgetDOM as getDOM} from '@angular/common';\nimport {GetTestability, Testability, TestabilityRegistry, ɵglobal as global, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport class BrowserGetTestability implements GetTestability {\n  addToWindow(registry: TestabilityRegistry): void {\n    global['getAngularTestability'] = (elem: any, findInAncestors: boolean = true) => {\n      const testability = registry.findTestabilityInTree(elem, findInAncestors);\n      if (testability == null) {\n        throw new RuntimeError(\n            RuntimeErrorCode.TESTABILITY_NOT_FOUND,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                'Could not find testability for element.');\n      }\n      return testability;\n    };\n\n    global['getAllAngularTestabilities'] = () => registry.getAllTestabilities();\n\n    global['getAllAngularRootElements'] = () => registry.getAllRootElements();\n\n    const whenAllStable = (callback: () => void) => {\n      const testabilities = global['getAllAngularTestabilities']() as Testability[];\n      let count = testabilities.length;\n      const decrement = function() {\n        count--;\n        if (count == 0) {\n          callback();\n        }\n      };\n      testabilities.forEach((testability) => {\n        testability.whenStable(decrement);\n      });\n    };\n\n    if (!global['frameworkStabilizers']) {\n      global['frameworkStabilizers'] = [];\n    }\n    global['frameworkStabilizers'].push(whenAllStable);\n  }\n\n  findTestabilityInTree(registry: TestabilityRegistry, elem: any, findInAncestors: boolean):\n      Testability|null {\n    if (elem == null) {\n      return null;\n    }\n    const t = registry.getTestability(elem);\n    if (t != null) {\n      return t;\n    } else if (!findInAncestors) {\n      return null;\n    }\n    if (getDOM().isShadowRoot(elem)) {\n      return this.findTestabilityInTree(registry, (<any>elem).host, true);\n    }\n    return this.findTestabilityInTree(registry, elem.parentElement, true);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {XhrFactory} from '@angular/common';\nimport {Injectable} from '@angular/core';\n\n/**\n * A factory for `HttpXhrBackend` that uses the `XMLHttpRequest` browser API.\n */\n@Injectable()\nexport class BrowserXhr implements XhrFactory {\n  build(): XMLHttpRequest {\n    return new XMLHttpRequest();\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n\nimport {Inject, Injectable, InjectionToken, NgZone, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../../errors';\n\n/**\n * The injection token for plugins of the `EventManager` service.\n *\n * @publicApi\n */\nexport const EVENT_MANAGER_PLUGINS =\n    new InjectionToken<EventManagerPlugin[]>(ngDevMode ? 'EventManagerPlugins' : '');\n\n/**\n * An injectable service that provides event management for Angular\n * through a browser plug-in.\n *\n * @publicApi\n */\n@Injectable()\nexport class EventManager {\n  private _plugins: EventManagerPlugin[];\n  private _eventNameToPlugin = new Map<string, EventManagerPlugin>();\n\n  /**\n   * Initializes an instance of the event-manager service.\n   */\n  constructor(@Inject(EVENT_MANAGER_PLUGINS) plugins: EventManagerPlugin[], private _zone: NgZone) {\n    plugins.forEach((plugin) => {\n      plugin.manager = this;\n    });\n    this._plugins = plugins.slice().reverse();\n  }\n\n  /**\n   * Registers a handler for a specific element and event.\n   *\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns  A callback function that can be used to remove the handler.\n   */\n  addEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const plugin = this._findPluginFor(eventName);\n    return plugin.addEventListener(element, eventName, handler);\n  }\n\n  /**\n   * Retrieves the compilation zone in which event listeners are registered.\n   */\n  getZone(): NgZone {\n    return this._zone;\n  }\n\n  /** @internal */\n  _findPluginFor(eventName: string): EventManagerPlugin {\n    let plugin = this._eventNameToPlugin.get(eventName);\n    if (plugin) {\n      return plugin;\n    }\n\n    const plugins = this._plugins;\n    plugin = plugins.find((plugin) => plugin.supports(eventName));\n    if (!plugin) {\n      throw new RuntimeError(\n          RuntimeErrorCode.NO_PLUGIN_FOR_EVENT,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n              `No event manager plugin found for event ${eventName}`);\n    }\n\n    this._eventNameToPlugin.set(eventName, plugin);\n    return plugin;\n  }\n}\n\n/**\n * The plugin definition for the `EventManager` class\n *\n * It can be used as a base class to create custom manager plugins, i.e. you can create your own\n * class that extends the `EventManagerPlugin` one.\n *\n * @publicApi\n */\nexport abstract class EventManagerPlugin {\n  // TODO: remove (has some usage in G3)\n  constructor(private _doc: any) {}\n\n  // Using non-null assertion because it's set by EventManager's constructor\n  manager!: EventManager;\n\n  /**\n   * Should return `true` for every event name that should be supported by this plugin\n   */\n  abstract supports(eventName: string): boolean;\n\n  /**\n   * Implement the behaviour for the supported events\n   */\n  abstract addEventListener(element: HTMLElement, eventName: string, handler: Function): Function;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT, isPlatformServer} from '@angular/common';\nimport {APP_ID, CSP_NONCE, Inject, Injectable, OnDestroy, Optional, PLATFORM_ID} from '@angular/core';\n\n/** The style elements attribute name used to set value of `APP_ID` token. */\nconst APP_ID_ATTRIBUTE_NAME = 'ng-app-id';\n\n@Injectable()\nexport class SharedStylesHost implements OnDestroy {\n  // Maps all registered host nodes to a list of style nodes that have been added to the host node.\n  private readonly styleRef = new Map < string /** Style string */, {\n    elements: HTMLStyleElement[];\n    usage: number\n  }\n  > ();\n  private readonly hostNodes = new Set<Node>();\n  private readonly styleNodesInDOM: Map<string, HTMLStyleElement>|null;\n  private readonly platformIsServer: boolean;\n\n  constructor(\n      @Inject(DOCUMENT) private readonly doc: Document,\n      @Inject(APP_ID) private readonly appId: string,\n      @Inject(CSP_NONCE) @Optional() private nonce?: string|null,\n      @Inject(PLATFORM_ID) readonly platformId: object = {}) {\n    this.styleNodesInDOM = this.collectServerRenderedStyles();\n    this.platformIsServer = isPlatformServer(platformId);\n    this.resetHostNodes();\n  }\n\n  addStyles(styles: string[]): void {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, 1);\n\n      if (usageCount === 1) {\n        this.onStyleAdded(style);\n      }\n    }\n  }\n\n  removeStyles(styles: string[]): void {\n    for (const style of styles) {\n      const usageCount = this.changeUsageCount(style, -1);\n\n      if (usageCount <= 0) {\n        this.onStyleRemoved(style);\n      }\n    }\n  }\n\n  ngOnDestroy(): void {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    if (styleNodesInDOM) {\n      styleNodesInDOM.forEach((node) => node.remove());\n      styleNodesInDOM.clear();\n    }\n\n    for (const style of this.getAllStyles()) {\n      this.onStyleRemoved(style);\n    }\n\n    this.resetHostNodes();\n  }\n\n  addHost(hostNode: Node): void {\n    this.hostNodes.add(hostNode);\n\n    for (const style of this.getAllStyles()) {\n      this.addStyleToHost(hostNode, style);\n    }\n  }\n\n  removeHost(hostNode: Node): void {\n    this.hostNodes.delete(hostNode);\n  }\n\n  private getAllStyles(): IterableIterator<string> {\n    return this.styleRef.keys();\n  }\n\n  private onStyleAdded(style: string): void {\n    for (const host of this.hostNodes) {\n      this.addStyleToHost(host, style);\n    }\n  }\n\n  private onStyleRemoved(style: string): void {\n    const styleRef = this.styleRef;\n    styleRef.get(style)?.elements?.forEach((node) => node.remove());\n    styleRef.delete(style);\n  }\n\n  private collectServerRenderedStyles(): Map<string, HTMLStyleElement>|null {\n    const styles = this.doc.head?.querySelectorAll<HTMLStyleElement>(\n        `style[${APP_ID_ATTRIBUTE_NAME}=\"${this.appId}\"]`);\n\n    if (styles?.length) {\n      const styleMap = new Map<string, HTMLStyleElement>();\n\n      styles.forEach((style) => {\n        if (style.textContent != null) {\n          styleMap.set(style.textContent, style);\n        }\n      });\n\n      return styleMap;\n    }\n\n    return null;\n  }\n\n  private changeUsageCount(style: string, delta: number): number {\n    const map = this.styleRef;\n    if (map.has(style)) {\n      const styleRefValue = map.get(style)!;\n      styleRefValue.usage += delta;\n\n      return styleRefValue.usage;\n    }\n\n    map.set(style, {usage: delta, elements: []});\n    return delta;\n  }\n\n  private getStyleElement(host: Node, style: string): HTMLStyleElement {\n    const styleNodesInDOM = this.styleNodesInDOM;\n    const styleEl = styleNodesInDOM?.get(style);\n    if (styleEl?.parentNode === host) {\n      // `styleNodesInDOM` cannot be undefined due to the above `styleNodesInDOM?.get`.\n      styleNodesInDOM!.delete(style);\n\n      styleEl.removeAttribute(APP_ID_ATTRIBUTE_NAME);\n\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        // This attribute is solely used for debugging purposes.\n        styleEl.setAttribute('ng-style-reused', '');\n      }\n\n      return styleEl;\n    } else {\n      const styleEl = this.doc.createElement('style');\n\n      if (this.nonce) {\n        styleEl.setAttribute('nonce', this.nonce);\n      }\n\n      styleEl.textContent = style;\n\n      if (this.platformIsServer) {\n        styleEl.setAttribute(APP_ID_ATTRIBUTE_NAME, this.appId);\n      }\n\n      host.appendChild(styleEl);\n\n      return styleEl;\n    }\n  }\n\n  private addStyleToHost(host: Node, style: string): void {\n    const styleEl = this.getStyleElement(host, style);\n    const styleRef = this.styleRef;\n    const styleElRef = styleRef.get(style)?.elements;\n    if (styleElRef) {\n      styleElRef.push(styleEl);\n    } else {\n      styleRef.set(style, {elements: [styleEl], usage: 1});\n    }\n  }\n\n  private resetHostNodes(): void {\n    const hostNodes = this.hostNodes;\n    hostNodes.clear();\n    // Re-add the head element back since this is the default host.\n    hostNodes.add(this.doc.head);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT, isPlatformServer, ɵgetDOM as getDOM} from '@angular/common';\nimport {APP_ID, CSP_NONCE, Inject, Injectable, InjectionToken, NgZone, OnDestroy, PLATFORM_ID, Renderer2, RendererFactory2, RendererStyleFlags2, RendererType2, ViewEncapsulation, ɵRuntimeError as RuntimeError} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nimport {EventManager} from './events/event_manager';\nimport {SharedStylesHost} from './shared_styles_host';\n\nexport const NAMESPACE_URIS: {[ns: string]: string} = {\n  'svg': 'http://www.w3.org/2000/svg',\n  'xhtml': 'http://www.w3.org/1999/xhtml',\n  'xlink': 'http://www.w3.org/1999/xlink',\n  'xml': 'http://www.w3.org/XML/1998/namespace',\n  'xmlns': 'http://www.w3.org/2000/xmlns/',\n  'math': 'http://www.w3.org/1998/MathML/',\n};\n\nconst COMPONENT_REGEX = /%COMP%/g;\n\nexport const COMPONENT_VARIABLE = '%COMP%';\nexport const HOST_ATTR = `_nghost-${COMPONENT_VARIABLE}`;\nexport const CONTENT_ATTR = `_ngcontent-${COMPONENT_VARIABLE}`;\n\n/**\n * The default value for the `REMOVE_STYLES_ON_COMPONENT_DESTROY` DI token.\n */\nconst REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT = true;\n\n/**\n * A [DI token](guide/glossary#di-token \"DI token definition\") that indicates whether styles\n * of destroyed components should be removed from DOM.\n *\n * By default, the value is set to `true`.\n * @publicApi\n */\nexport const REMOVE_STYLES_ON_COMPONENT_DESTROY =\n    new InjectionToken<boolean>(ngDevMode ? 'RemoveStylesOnCompDestroy' : '', {\n      providedIn: 'root',\n      factory: () => REMOVE_STYLES_ON_COMPONENT_DESTROY_DEFAULT,\n    });\n\nexport function shimContentAttribute(componentShortId: string): string {\n  return CONTENT_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nexport function shimHostAttribute(componentShortId: string): string {\n  return HOST_ATTR.replace(COMPONENT_REGEX, componentShortId);\n}\n\nexport function shimStylesContent(compId: string, styles: string[]): string[] {\n  return styles.map(s => s.replace(COMPONENT_REGEX, compId));\n}\n\n@Injectable()\nexport class DomRendererFactory2 implements RendererFactory2, OnDestroy {\n  private readonly rendererByCompId =\n      new Map<string, EmulatedEncapsulationDomRenderer2|NoneEncapsulationDomRenderer>();\n  private readonly defaultRenderer: Renderer2;\n  private readonly platformIsServer: boolean;\n\n  constructor(\n      private readonly eventManager: EventManager,\n      private readonly sharedStylesHost: SharedStylesHost,\n      @Inject(APP_ID) private readonly appId: string,\n      @Inject(REMOVE_STYLES_ON_COMPONENT_DESTROY) private removeStylesOnCompDestroy: boolean,\n      @Inject(DOCUMENT) private readonly doc: Document,\n      @Inject(PLATFORM_ID) readonly platformId: Object,\n      readonly ngZone: NgZone,\n      @Inject(CSP_NONCE) private readonly nonce: string|null = null,\n  ) {\n    this.platformIsServer = isPlatformServer(platformId);\n    this.defaultRenderer =\n        new DefaultDomRenderer2(eventManager, doc, ngZone, this.platformIsServer);\n  }\n\n  createRenderer(element: any, type: RendererType2|null): Renderer2 {\n    if (!element || !type) {\n      return this.defaultRenderer;\n    }\n\n    if (this.platformIsServer && type.encapsulation === ViewEncapsulation.ShadowDom) {\n      // Domino does not support shadow DOM.\n      type = {...type, encapsulation: ViewEncapsulation.Emulated};\n    }\n\n    const renderer = this.getOrCreateRenderer(element, type);\n    // Renderers have different logic due to different encapsulation behaviours.\n    // Ex: for emulated, an attribute is added to the element.\n    if (renderer instanceof EmulatedEncapsulationDomRenderer2) {\n      renderer.applyToHost(element);\n    } else if (renderer instanceof NoneEncapsulationDomRenderer) {\n      renderer.applyStyles();\n    }\n\n    return renderer;\n  }\n\n  private getOrCreateRenderer(element: any, type: RendererType2): Renderer2 {\n    const rendererByCompId = this.rendererByCompId;\n    let renderer = rendererByCompId.get(type.id);\n\n    if (!renderer) {\n      const doc = this.doc;\n      const ngZone = this.ngZone;\n      const eventManager = this.eventManager;\n      const sharedStylesHost = this.sharedStylesHost;\n      const removeStylesOnCompDestroy = this.removeStylesOnCompDestroy;\n      const platformIsServer = this.platformIsServer;\n\n      switch (type.encapsulation) {\n        case ViewEncapsulation.Emulated:\n          renderer = new EmulatedEncapsulationDomRenderer2(\n              eventManager, sharedStylesHost, type, this.appId, removeStylesOnCompDestroy, doc,\n              ngZone, platformIsServer);\n          break;\n        case ViewEncapsulation.ShadowDom:\n          return new ShadowDomRenderer(\n              eventManager, sharedStylesHost, element, type, doc, ngZone, this.nonce,\n              platformIsServer);\n        default:\n          renderer = new NoneEncapsulationDomRenderer(\n              eventManager, sharedStylesHost, type, removeStylesOnCompDestroy, doc, ngZone,\n              platformIsServer);\n          break;\n      }\n\n      rendererByCompId.set(type.id, renderer);\n    }\n\n    return renderer;\n  }\n\n  ngOnDestroy() {\n    this.rendererByCompId.clear();\n  }\n}\n\nclass DefaultDomRenderer2 implements Renderer2 {\n  data: {[key: string]: any} = Object.create(null);\n\n  /**\n   * By default this renderer throws when encountering synthetic properties\n   * This can be disabled for example by the AsyncAnimationRendererFactory\n   */\n  throwOnSyntheticProps = true;\n\n  constructor(\n      private readonly eventManager: EventManager, private readonly doc: Document,\n      private readonly ngZone: NgZone, private readonly platformIsServer: boolean) {}\n\n  destroy(): void {}\n\n  destroyNode = null;\n\n  createElement(name: string, namespace?: string): any {\n    if (namespace) {\n      // TODO: `|| namespace` was added in\n      // https://github.com/angular/angular/commit/2b9cc8503d48173492c29f5a271b61126104fbdb to\n      // support how Ivy passed around the namespace URI rather than short name at the time. It did\n      // not, however extend the support to other parts of the system (setAttribute, setAttribute,\n      // and the ServerRenderer). We should decide what exactly the semantics for dealing with\n      // namespaces should be and make it consistent.\n      // Related issues:\n      // https://github.com/angular/angular/issues/44028\n      // https://github.com/angular/angular/issues/44883\n      return this.doc.createElementNS(NAMESPACE_URIS[namespace] || namespace, name);\n    }\n\n    return this.doc.createElement(name);\n  }\n\n  createComment(value: string): any {\n    return this.doc.createComment(value);\n  }\n\n  createText(value: string): any {\n    return this.doc.createTextNode(value);\n  }\n\n  appendChild(parent: any, newChild: any): void {\n    const targetParent = isTemplateNode(parent) ? parent.content : parent;\n    targetParent.appendChild(newChild);\n  }\n\n  insertBefore(parent: any, newChild: any, refChild: any): void {\n    if (parent) {\n      const targetParent = isTemplateNode(parent) ? parent.content : parent;\n      targetParent.insertBefore(newChild, refChild);\n    }\n  }\n\n  removeChild(parent: any, oldChild: any): void {\n    if (parent) {\n      parent.removeChild(oldChild);\n    }\n  }\n\n  selectRootElement(selectorOrNode: string|any, preserveContent?: boolean): any {\n    let el: any = typeof selectorOrNode === 'string' ? this.doc.querySelector(selectorOrNode) :\n                                                       selectorOrNode;\n    if (!el) {\n      throw new RuntimeError(\n          RuntimeErrorCode.ROOT_NODE_NOT_FOUND,\n          (typeof ngDevMode === 'undefined' || ngDevMode) &&\n              `The selector \"${selectorOrNode}\" did not match any elements`);\n    }\n    if (!preserveContent) {\n      el.textContent = '';\n    }\n    return el;\n  }\n\n  parentNode(node: any): any {\n    return node.parentNode;\n  }\n\n  nextSibling(node: any): any {\n    return node.nextSibling;\n  }\n\n  setAttribute(el: any, name: string, value: string, namespace?: string): void {\n    if (namespace) {\n      name = namespace + ':' + name;\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.setAttributeNS(namespaceUri, name, value);\n      } else {\n        el.setAttribute(name, value);\n      }\n    } else {\n      el.setAttribute(name, value);\n    }\n  }\n\n  removeAttribute(el: any, name: string, namespace?: string): void {\n    if (namespace) {\n      const namespaceUri = NAMESPACE_URIS[namespace];\n      if (namespaceUri) {\n        el.removeAttributeNS(namespaceUri, name);\n      } else {\n        el.removeAttribute(`${namespace}:${name}`);\n      }\n    } else {\n      el.removeAttribute(name);\n    }\n  }\n\n  addClass(el: any, name: string): void {\n    el.classList.add(name);\n  }\n\n  removeClass(el: any, name: string): void {\n    el.classList.remove(name);\n  }\n\n  setStyle(el: any, style: string, value: any, flags: RendererStyleFlags2): void {\n    if (flags & (RendererStyleFlags2.DashCase | RendererStyleFlags2.Important)) {\n      el.style.setProperty(style, value, flags & RendererStyleFlags2.Important ? 'important' : '');\n    } else {\n      el.style[style] = value;\n    }\n  }\n\n  removeStyle(el: any, style: string, flags: RendererStyleFlags2): void {\n    if (flags & RendererStyleFlags2.DashCase) {\n      // removeProperty has no effect when used on camelCased properties.\n      el.style.removeProperty(style);\n    } else {\n      el.style[style] = '';\n    }\n  }\n\n  setProperty(el: any, name: string, value: any): void {\n    if (el == null) {\n      return;\n    }\n\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps &&\n        checkNoSyntheticProp(name, 'property');\n    el[name] = value;\n  }\n\n  setValue(node: any, value: string): void {\n    node.nodeValue = value;\n  }\n\n  listen(target: 'window'|'document'|'body'|any, event: string, callback: (event: any) => boolean):\n      () => void {\n    (typeof ngDevMode === 'undefined' || ngDevMode) && this.throwOnSyntheticProps &&\n        checkNoSyntheticProp(event, 'listener');\n    if (typeof target === 'string') {\n      target = getDOM().getGlobalEventTarget(this.doc, target);\n      if (!target) {\n        throw new Error(`Unsupported event target ${target} for event ${event}`);\n      }\n    }\n\n    return this.eventManager.addEventListener(\n               target, event, this.decoratePreventDefault(callback)) as VoidFunction;\n  }\n\n  private decoratePreventDefault(eventHandler: Function): Function {\n    // `DebugNode.triggerEventHandler` needs to know if the listener was created with\n    // decoratePreventDefault or is a listener added outside the Angular context so it can handle\n    // the two differently. In the first case, the special '__ngUnwrap__' token is passed to the\n    // unwrap the listener (see below).\n    return (event: any) => {\n      // Ivy uses '__ngUnwrap__' as a special token that allows us to unwrap the function\n      // so that it can be invoked programmatically by `DebugNode.triggerEventHandler`. The\n      // debug_node can inspect the listener toString contents for the existence of this special\n      // token. Because the token is a string literal, it is ensured to not be modified by compiled\n      // code.\n      if (event === '__ngUnwrap__') {\n        return eventHandler;\n      }\n\n      // Run the event handler inside the ngZone because event handlers are not patched\n      // by Zone on the server. This is required only for tests.\n      const allowDefaultBehavior = this.platformIsServer ?\n          this.ngZone.runGuarded(() => eventHandler(event)) :\n          eventHandler(event);\n      if (allowDefaultBehavior === false) {\n        event.preventDefault();\n      }\n\n      return undefined;\n    };\n  }\n}\n\nconst AT_CHARCODE = (() => '@'.charCodeAt(0))();\nfunction checkNoSyntheticProp(name: string, nameKind: string) {\n  if (name.charCodeAt(0) === AT_CHARCODE) {\n    throw new RuntimeError(\n        RuntimeErrorCode.UNEXPECTED_SYNTHETIC_PROPERTY,\n        `Unexpected synthetic ${nameKind} ${name} found. Please make sure that:\n  - Either \\`BrowserAnimationsModule\\` or \\`NoopAnimationsModule\\` are imported in your application.\n  - There is corresponding configuration for the animation named \\`${\n            name}\\` defined in the \\`animations\\` field of the \\`@Component\\` decorator (see https://angular.io/api/core/Component#animations).`);\n  }\n}\n\n\nfunction isTemplateNode(node: any): node is HTMLTemplateElement {\n  return node.tagName === 'TEMPLATE' && node.content !== undefined;\n}\n\nclass ShadowDomRenderer extends DefaultDomRenderer2 {\n  private shadowRoot: any;\n\n  constructor(\n      eventManager: EventManager,\n      private sharedStylesHost: SharedStylesHost,\n      private hostEl: any,\n      component: RendererType2,\n      doc: Document,\n      ngZone: NgZone,\n      nonce: string|null,\n      platformIsServer: boolean,\n  ) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.shadowRoot = (hostEl as any).attachShadow({mode: 'open'});\n\n    this.sharedStylesHost.addHost(this.shadowRoot);\n    const styles = shimStylesContent(component.id, component.styles);\n\n    for (const style of styles) {\n      const styleEl = document.createElement('style');\n\n      if (nonce) {\n        styleEl.setAttribute('nonce', nonce);\n      }\n\n      styleEl.textContent = style;\n      this.shadowRoot.appendChild(styleEl);\n    }\n  }\n\n  private nodeOrShadowRoot(node: any): any {\n    return node === this.hostEl ? this.shadowRoot : node;\n  }\n\n  override appendChild(parent: any, newChild: any): void {\n    return super.appendChild(this.nodeOrShadowRoot(parent), newChild);\n  }\n  override insertBefore(parent: any, newChild: any, refChild: any): void {\n    return super.insertBefore(this.nodeOrShadowRoot(parent), newChild, refChild);\n  }\n  override removeChild(parent: any, oldChild: any): void {\n    return super.removeChild(this.nodeOrShadowRoot(parent), oldChild);\n  }\n  override parentNode(node: any): any {\n    return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(node)));\n  }\n\n  override destroy() {\n    this.sharedStylesHost.removeHost(this.shadowRoot);\n  }\n}\n\nclass NoneEncapsulationDomRenderer extends DefaultDomRenderer2 {\n  private readonly styles: string[];\n\n  constructor(\n      eventManager: EventManager,\n      private readonly sharedStylesHost: SharedStylesHost,\n      component: RendererType2,\n      private removeStylesOnCompDestroy: boolean,\n      doc: Document,\n      ngZone: NgZone,\n      platformIsServer: boolean,\n      compId?: string,\n  ) {\n    super(eventManager, doc, ngZone, platformIsServer);\n    this.styles = compId ? shimStylesContent(compId, component.styles) : component.styles;\n  }\n\n  applyStyles(): void {\n    this.sharedStylesHost.addStyles(this.styles);\n  }\n\n  override destroy(): void {\n    if (!this.removeStylesOnCompDestroy) {\n      return;\n    }\n\n    this.sharedStylesHost.removeStyles(this.styles);\n  }\n}\n\nclass EmulatedEncapsulationDomRenderer2 extends NoneEncapsulationDomRenderer {\n  private contentAttr: string;\n  private hostAttr: string;\n\n  constructor(\n      eventManager: EventManager, sharedStylesHost: SharedStylesHost, component: RendererType2,\n      appId: string, removeStylesOnCompDestroy: boolean, doc: Document, ngZone: NgZone,\n      platformIsServer: boolean) {\n    const compId = appId + '-' + component.id;\n    super(\n        eventManager, sharedStylesHost, component, removeStylesOnCompDestroy, doc, ngZone,\n        platformIsServer, compId);\n    this.contentAttr = shimContentAttribute(compId);\n    this.hostAttr = shimHostAttribute(compId);\n  }\n\n  applyToHost(element: any): void {\n    this.applyStyles();\n    this.setAttribute(element, this.hostAttr, '');\n  }\n\n  override createElement(parent: any, name: string): Element {\n    const el = super.createElement(parent, name);\n    super.setAttribute(el, this.contentAttr, '');\n    return el;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\nimport {EventManagerPlugin} from './event_manager';\n\n@Injectable()\nexport class DomEventsPlugin extends EventManagerPlugin {\n  constructor(@Inject(DOCUMENT) doc: any) {\n    super(doc);\n  }\n\n  // This plugin should come last in the list of plugins, because it accepts all\n  // events.\n  override supports(eventName: string): boolean {\n    return true;\n  }\n\n  override addEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    element.addEventListener(eventName, handler as EventListener, false);\n    return () => this.removeEventListener(element, eventName, handler as EventListener);\n  }\n\n  removeEventListener(target: any, eventName: string, callback: Function): void {\n    return target.removeEventListener(eventName, callback as EventListener);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable, NgZone} from '@angular/core';\n\nimport {EventManagerPlugin} from './event_manager';\n\n/**\n * Defines supported modifiers for key events.\n */\nconst MODIFIER_KEYS = ['alt', 'control', 'meta', 'shift'];\n\n// The following values are here for cross-browser compatibility and to match the W3C standard\n// cf https://www.w3.org/TR/DOM-Level-3-Events-key/\nconst _keyMap: {[k: string]: string} = {\n  '\\b': 'Backspace',\n  '\\t': 'Tab',\n  '\\x7F': 'Delete',\n  '\\x1B': 'Escape',\n  'Del': 'Delete',\n  'Esc': 'Escape',\n  'Left': 'ArrowLeft',\n  'Right': 'ArrowRight',\n  'Up': 'ArrowUp',\n  'Down': 'ArrowDown',\n  'Menu': 'ContextMenu',\n  'Scroll': 'ScrollLock',\n  'Win': 'OS'\n};\n\n/**\n * Retrieves modifiers from key-event objects.\n */\nconst MODIFIER_KEY_GETTERS: {[key: string]: (event: KeyboardEvent) => boolean} = {\n  'alt': (event: KeyboardEvent) => event.altKey,\n  'control': (event: KeyboardEvent) => event.ctrlKey,\n  'meta': (event: KeyboardEvent) => event.metaKey,\n  'shift': (event: KeyboardEvent) => event.shiftKey\n};\n\n/**\n * A browser plug-in that provides support for handling of key events in Angular.\n */\n@Injectable()\nexport class KeyEventsPlugin extends EventManagerPlugin {\n  /**\n   * Initializes an instance of the browser plug-in.\n   * @param doc The document in which key events will be detected.\n   */\n  constructor(@Inject(DOCUMENT) doc: any) {\n    super(doc);\n  }\n\n  /**\n   * Reports whether a named key event is supported.\n   * @param eventName The event name to query.\n   * @return True if the named key event is supported.\n   */\n  override supports(eventName: string): boolean {\n    return KeyEventsPlugin.parseEventName(eventName) != null;\n  }\n\n  /**\n   * Registers a handler for a specific element and key event.\n   * @param element The HTML element to receive event notifications.\n   * @param eventName The name of the key event to listen for.\n   * @param handler A function to call when the notification occurs. Receives the\n   * event object as an argument.\n   * @returns The key event that was registered.\n   */\n  override addEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const parsedEvent = KeyEventsPlugin.parseEventName(eventName)!;\n\n    const outsideHandler =\n        KeyEventsPlugin.eventCallback(parsedEvent['fullKey'], handler, this.manager.getZone());\n\n    return this.manager.getZone().runOutsideAngular(() => {\n      return getDOM().onAndCancel(element, parsedEvent['domEventName'], outsideHandler);\n    });\n  }\n\n  /**\n   * Parses the user provided full keyboard event definition and normalizes it for\n   * later internal use. It ensures the string is all lowercase, converts special\n   * characters to a standard spelling, and orders all the values consistently.\n   *\n   * @param eventName The name of the key event to listen for.\n   * @returns an object with the full, normalized string, and the dom event name\n   * or null in the case when the event doesn't match a keyboard event.\n   */\n  static parseEventName(eventName: string): {fullKey: string, domEventName: string}|null {\n    const parts: string[] = eventName.toLowerCase().split('.');\n\n    const domEventName = parts.shift();\n    if ((parts.length === 0) || !(domEventName === 'keydown' || domEventName === 'keyup')) {\n      return null;\n    }\n\n    const key = KeyEventsPlugin._normalizeKey(parts.pop()!);\n\n    let fullKey = '';\n    let codeIX = parts.indexOf('code');\n    if (codeIX > -1) {\n      parts.splice(codeIX, 1);\n      fullKey = 'code.';\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      const index: number = parts.indexOf(modifierName);\n      if (index > -1) {\n        parts.splice(index, 1);\n        fullKey += modifierName + '.';\n      }\n    });\n    fullKey += key;\n\n    if (parts.length != 0 || key.length === 0) {\n      // returning null instead of throwing to let another plugin process the event\n      return null;\n    }\n\n    // NOTE: Please don't rewrite this as so, as it will break JSCompiler property renaming.\n    //       The code must remain in the `result['domEventName']` form.\n    // return {domEventName, fullKey};\n    const result: {fullKey: string, domEventName: string} = {} as any;\n    result['domEventName'] = domEventName;\n    result['fullKey'] = fullKey;\n    return result;\n  }\n\n  /**\n   * Determines whether the actual keys pressed match the configured key code string.\n   * The `fullKeyCode` event is normalized in the `parseEventName` method when the\n   * event is attached to the DOM during the `addEventListener` call. This is unseen\n   * by the end user and is normalized for internal consistency and parsing.\n   *\n   * @param event The keyboard event.\n   * @param fullKeyCode The normalized user defined expected key event string\n   * @returns boolean.\n   */\n  static matchEventFullKeyCode(event: KeyboardEvent, fullKeyCode: string): boolean {\n    let keycode = _keyMap[event.key] || event.key;\n    let key = '';\n    if (fullKeyCode.indexOf('code.') > -1) {\n      keycode = event.code;\n      key = 'code.';\n    }\n    // the keycode could be unidentified so we have to check here\n    if (keycode == null || !keycode) return false;\n    keycode = keycode.toLowerCase();\n    if (keycode === ' ') {\n      keycode = 'space';  // for readability\n    } else if (keycode === '.') {\n      keycode = 'dot';  // because '.' is used as a separator in event names\n    }\n    MODIFIER_KEYS.forEach(modifierName => {\n      if (modifierName !== keycode) {\n        const modifierGetter = MODIFIER_KEY_GETTERS[modifierName];\n        if (modifierGetter(event)) {\n          key += modifierName + '.';\n        }\n      }\n    });\n    key += keycode;\n    return key === fullKeyCode;\n  }\n\n  /**\n   * Configures a handler callback for a key event.\n   * @param fullKey The event name that combines all simultaneous keystrokes.\n   * @param handler The function that responds to the key event.\n   * @param zone The zone in which the event occurred.\n   * @returns A callback function.\n   */\n  static eventCallback(fullKey: string, handler: Function, zone: NgZone): Function {\n    return (event: KeyboardEvent) => {\n      if (KeyEventsPlugin.matchEventFullKeyCode(event, fullKey)) {\n        zone.runGuarded(() => handler(event));\n      }\n    };\n  }\n\n  /** @internal */\n  static _normalizeKey(keyName: string): string {\n    return keyName === 'esc' ? 'escape' : keyName;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {CommonModule, DOCUMENT, XhrFactory, ɵPLATFORM_BROWSER_ID as PLATFORM_BROWSER_ID} from '@angular/common';\nimport {APP_ID, ApplicationConfig as ApplicationConfigFromCore, ApplicationModule, ApplicationRef, createPlatformFactory, ErrorHandler, Inject, InjectionToken, ModuleWithProviders, NgModule, NgZone, Optional, PLATFORM_ID, PLATFORM_INITIALIZER, platformCore, PlatformRef, Provider, RendererFactory2, SkipSelf, StaticProvider, Testability, TestabilityRegistry, Type, ɵINJECTOR_SCOPE as INJECTOR_SCOPE, ɵinternalCreateApplication as internalCreateApplication, ɵRuntimeError as RuntimeError, ɵsetDocument, ɵTESTABILITY as TESTABILITY, ɵTESTABILITY_GETTER as TESTABILITY_GETTER} from '@angular/core';\n\nimport {BrowserDomAdapter} from './browser/browser_adapter';\nimport {BrowserGetTestability} from './browser/testability';\nimport {BrowserXhr} from './browser/xhr';\nimport {DomRendererFactory2} from './dom/dom_renderer';\nimport {DomEventsPlugin} from './dom/events/dom_events';\nimport {EVENT_MANAGER_PLUGINS, EventManager} from './dom/events/event_manager';\nimport {KeyEventsPlugin} from './dom/events/key_events';\nimport {SharedStylesHost} from './dom/shared_styles_host';\nimport {RuntimeErrorCode} from './errors';\n\n\n/**\n * Set of config options available during the application bootstrap operation.\n *\n * @publicApi\n *\n * @deprecated\n * `ApplicationConfig` has moved, please import `ApplicationConfig` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\ntype ApplicationConfig = ApplicationConfigFromCore;\nexport {ApplicationConfig};\n\n/**\n * Bootstraps an instance of an Angular application and renders a standalone component as the\n * application's root component. More information about standalone components can be found in [this\n * guide](guide/standalone-components).\n *\n * @usageNotes\n * The root component passed into this function *must* be a standalone one (should have the\n * `standalone: true` flag in the `@Component` decorator config).\n *\n * ```typescript\n * @Component({\n *   standalone: true,\n *   template: 'Hello world!'\n * })\n * class RootComponent {}\n *\n * const appRef: ApplicationRef = await bootstrapApplication(RootComponent);\n * ```\n *\n * You can add the list of providers that should be available in the application injector by\n * specifying the `providers` field in an object passed as the second argument:\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     {provide: BACKEND_URL, useValue: 'https://yourdomain.com/api'}\n *   ]\n * });\n * ```\n *\n * The `importProvidersFrom` helper method can be used to collect all providers from any\n * existing NgModule (and transitively from all NgModules that it imports):\n *\n * ```typescript\n * await bootstrapApplication(RootComponent, {\n *   providers: [\n *     importProvidersFrom(SomeNgModule)\n *   ]\n * });\n * ```\n *\n * Note: the `bootstrapApplication` method doesn't include [Testability](api/core/Testability) by\n * default. You can add [Testability](api/core/Testability) by getting the list of necessary\n * providers using `provideProtractorTestingSupport()` function and adding them into the `providers`\n * array, for example:\n *\n * ```typescript\n * import {provideProtractorTestingSupport} from '@angular/platform-browser';\n *\n * await bootstrapApplication(RootComponent, {providers: [provideProtractorTestingSupport()]});\n * ```\n *\n * @param rootComponent A reference to a standalone component that should be rendered.\n * @param options Extra configuration for the bootstrap operation, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nexport function bootstrapApplication(\n    rootComponent: Type<unknown>, options?: ApplicationConfig): Promise<ApplicationRef> {\n  return internalCreateApplication({rootComponent, ...createProvidersConfig(options)});\n}\n\n/**\n * Create an instance of an Angular application without bootstrapping any components. This is useful\n * for the situation where one wants to decouple application environment creation (a platform and\n * associated injectors) from rendering components on a screen. Components can be subsequently\n * bootstrapped on the returned `ApplicationRef`.\n *\n * @param options Extra configuration for the application environment, see `ApplicationConfig` for\n *     additional info.\n * @returns A promise that returns an `ApplicationRef` instance once resolved.\n *\n * @publicApi\n */\nexport function createApplication(options?: ApplicationConfig) {\n  return internalCreateApplication(createProvidersConfig(options));\n}\n\nfunction createProvidersConfig(options?: ApplicationConfig) {\n  return {\n    appProviders: [\n      ...BROWSER_MODULE_PROVIDERS,\n      ...(options?.providers ?? []),\n    ],\n    platformProviders: INTERNAL_BROWSER_PLATFORM_PROVIDERS\n  };\n}\n\n/**\n * Returns a set of providers required to setup [Testability](api/core/Testability) for an\n * application bootstrapped using the `bootstrapApplication` function. The set of providers is\n * needed to support testing an application with Protractor (which relies on the Testability APIs\n * to be present).\n *\n * @returns An array of providers required to setup Testability for an application and make it\n *     available for testing using Protractor.\n *\n * @publicApi\n */\nexport function provideProtractorTestingSupport(): Provider[] {\n  // Return a copy to prevent changes to the original array in case any in-place\n  // alterations are performed to the `provideProtractorTestingSupport` call results in app\n  // code.\n  return [...TESTABILITY_PROVIDERS];\n}\n\nexport function initDomAdapter() {\n  BrowserDomAdapter.makeCurrent();\n}\n\nexport function errorHandler(): ErrorHandler {\n  return new ErrorHandler();\n}\n\nexport function _document(): any {\n  // Tell ivy about the global document\n  ɵsetDocument(document);\n  return document;\n}\n\nexport const INTERNAL_BROWSER_PLATFORM_PROVIDERS: StaticProvider[] = [\n  {provide: PLATFORM_ID, useValue: PLATFORM_BROWSER_ID},\n  {provide: PLATFORM_INITIALIZER, useValue: initDomAdapter, multi: true},\n  {provide: DOCUMENT, useFactory: _document, deps: []},\n];\n\n/**\n * A factory function that returns a `PlatformRef` instance associated with browser service\n * providers.\n *\n * @publicApi\n */\nexport const platformBrowser: (extraProviders?: StaticProvider[]) => PlatformRef =\n    createPlatformFactory(platformCore, 'browser', INTERNAL_BROWSER_PLATFORM_PROVIDERS);\n\n/**\n * Internal marker to signal whether providers from the `BrowserModule` are already present in DI.\n * This is needed to avoid loading `BrowserModule` providers twice. We can't rely on the\n * `BrowserModule` presence itself, since the standalone-based bootstrap just imports\n * `BrowserModule` providers without referencing the module itself.\n */\nconst BROWSER_MODULE_PROVIDERS_MARKER = new InjectionToken(\n    (typeof ngDevMode === 'undefined' || ngDevMode) ? 'BrowserModule Providers Marker' : '');\n\nconst TESTABILITY_PROVIDERS = [\n  {\n    provide: TESTABILITY_GETTER,\n    useClass: BrowserGetTestability,\n    deps: [],\n  },\n  {\n    provide: TESTABILITY,\n    useClass: Testability,\n    deps: [NgZone, TestabilityRegistry, TESTABILITY_GETTER]\n  },\n  {\n    provide: Testability,  // Also provide as `Testability` for backwards-compatibility.\n    useClass: Testability,\n    deps: [NgZone, TestabilityRegistry, TESTABILITY_GETTER]\n  }\n];\n\nconst BROWSER_MODULE_PROVIDERS: Provider[] = [\n  {provide: INJECTOR_SCOPE, useValue: 'root'},\n  {provide: ErrorHandler, useFactory: errorHandler, deps: []}, {\n    provide: EVENT_MANAGER_PLUGINS,\n    useClass: DomEventsPlugin,\n    multi: true,\n    deps: [DOCUMENT, NgZone, PLATFORM_ID]\n  },\n  {provide: EVENT_MANAGER_PLUGINS, useClass: KeyEventsPlugin, multi: true, deps: [DOCUMENT]},\n  DomRendererFactory2, SharedStylesHost, EventManager,\n  {provide: RendererFactory2, useExisting: DomRendererFactory2},\n  {provide: XhrFactory, useClass: BrowserXhr, deps: []},\n  (typeof ngDevMode === 'undefined' || ngDevMode) ?\n      {provide: BROWSER_MODULE_PROVIDERS_MARKER, useValue: true} :\n      []\n];\n\n/**\n * Exports required infrastructure for all Angular apps.\n * Included by default in all Angular apps created with the CLI\n * `new` command.\n * Re-exports `CommonModule` and `ApplicationModule`, making their\n * exports and providers available to all apps.\n *\n * @publicApi\n */\n@NgModule({\n  providers: [...BROWSER_MODULE_PROVIDERS, ...TESTABILITY_PROVIDERS],\n  exports: [CommonModule, ApplicationModule],\n})\nexport class BrowserModule {\n  constructor(@Optional() @SkipSelf() @Inject(BROWSER_MODULE_PROVIDERS_MARKER)\n              providersAlreadyPresent: boolean|null) {\n    if ((typeof ngDevMode === 'undefined' || ngDevMode) && providersAlreadyPresent) {\n      throw new RuntimeError(\n          RuntimeErrorCode.BROWSER_MODULE_ALREADY_LOADED,\n          `Providers from the \\`BrowserModule\\` have already been loaded. If you need access ` +\n              `to common directives such as NgIf and NgFor, import the \\`CommonModule\\` instead.`);\n    }\n  }\n\n  /**\n   * Configures a browser-based app to transition from a server-rendered app, if\n   * one is present on the page.\n   *\n   * @param params An object containing an identifier for the app to transition.\n   * The ID must match between the client and server versions of the app.\n   * @returns The reconfigured `BrowserModule` to import into the app's root `AppModule`.\n   *\n   * @deprecated Use {@link APP_ID} instead to set the application ID.\n   */\n  static withServerTransition(params: {appId: string}): ModuleWithProviders<BrowserModule> {\n    return {\n      ngModule: BrowserModule,\n      providers: [\n        {provide: APP_ID, useValue: params.appId},\n      ],\n    };\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT, ɵDomAdapter as DomAdapter, ɵgetDOM as getDOM} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\n/**\n * Represents the attributes of an HTML `<meta>` element. The element itself is\n * represented by the internal `HTMLMetaElement`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see {@link Meta}\n *\n * @publicApi\n */\nexport type MetaDefinition = {\n  charset?: string;\n  content?: string;\n  httpEquiv?: string;\n  id?: string;\n  itemprop?: string;\n  name?: string;\n  property?: string;\n  scheme?: string;\n  url?: string;\n}&{\n  // TODO(IgorMinar): this type looks wrong\n  [prop: string]: string;\n};\n\n/**\n * A service for managing HTML `<meta>` tags.\n *\n * Properties of the `MetaDefinition` object match the attributes of the\n * HTML `<meta>` tag. These tags define document metadata that is important for\n * things like configuring a Content Security Policy, defining browser compatibility\n * and security settings, setting HTTP Headers, defining rich content for social sharing,\n * and Search Engine Optimization (SEO).\n *\n * To identify specific `<meta>` tags in a document, use an attribute selection\n * string in the format `\"tag_attribute='value string'\"`.\n * For example, an `attrSelector` value of `\"name='description'\"` matches a tag\n * whose `name` attribute has the value `\"description\"`.\n * Selectors are used with the `querySelector()` Document method,\n * in the format `meta[{attrSelector}]`.\n *\n * @see [HTML meta tag](https://developer.mozilla.org/docs/Web/HTML/Element/meta)\n * @see [Document.querySelector()](https://developer.mozilla.org/docs/Web/API/Document/querySelector)\n *\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class Meta {\n  private _dom: DomAdapter;\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    this._dom = getDOM();\n  }\n  /**\n   * Retrieves or creates a specific `<meta>` tag element in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * If an existing element is found, it is returned and is not modified in any way.\n   * @param tag The definition of a `<meta>` element to match or create.\n   * @param forceCreation True to create a new element without checking whether one already exists.\n   * @returns The existing element with the same attributes and values if found,\n   * the new element if no match is found, or `null` if the tag parameter is not defined.\n   */\n  addTag(tag: MetaDefinition, forceCreation: boolean = false): HTMLMetaElement|null {\n    if (!tag) return null;\n    return this._getOrCreateElement(tag, forceCreation);\n  }\n\n  /**\n   * Retrieves or creates a set of `<meta>` tag elements in the current HTML document.\n   * In searching for an existing tag, Angular attempts to match the `name` or `property` attribute\n   * values in the provided tag definition, and verifies that all other attribute values are equal.\n   * @param tags An array of tag definitions to match or create.\n   * @param forceCreation True to create new elements without checking whether they already exist.\n   * @returns The matching elements if found, or the new elements.\n   */\n  addTags(tags: MetaDefinition[], forceCreation: boolean = false): HTMLMetaElement[] {\n    if (!tags) return [];\n    return tags.reduce((result: HTMLMetaElement[], tag: MetaDefinition) => {\n      if (tag) {\n        result.push(this._getOrCreateElement(tag, forceCreation));\n      }\n      return result;\n    }, []);\n  }\n\n  /**\n   * Retrieves a `<meta>` tag element in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching element, if any.\n   */\n  getTag(attrSelector: string): HTMLMetaElement|null {\n    if (!attrSelector) return null;\n    return this._doc.querySelector(`meta[${attrSelector}]`) || null;\n  }\n\n  /**\n   * Retrieves a set of `<meta>` tag elements in the current HTML document.\n   * @param attrSelector The tag attribute and value to match against, in the format\n   * `\"tag_attribute='value string'\"`.\n   * @returns The matching elements, if any.\n   */\n  getTags(attrSelector: string): HTMLMetaElement[] {\n    if (!attrSelector) return [];\n    const list /*NodeList*/ = this._doc.querySelectorAll(`meta[${attrSelector}]`);\n    return list ? [].slice.call(list) : [];\n  }\n\n  /**\n   * Modifies an existing `<meta>` tag element in the current HTML document.\n   * @param tag The tag description with which to replace the existing tag content.\n   * @param selector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   * If not supplied, matches a tag with the same `name` or `property` attribute value as the\n   * replacement tag.\n   * @return The modified element.\n   */\n  updateTag(tag: MetaDefinition, selector?: string): HTMLMetaElement|null {\n    if (!tag) return null;\n    selector = selector || this._parseSelector(tag);\n    const meta: HTMLMetaElement = this.getTag(selector)!;\n    if (meta) {\n      return this._setMetaElementAttributes(tag, meta);\n    }\n    return this._getOrCreateElement(tag, true);\n  }\n\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param attrSelector A tag attribute and value to match against, to identify\n   * an existing tag. A string in the format `\"tag_attribute=`value string`\"`.\n   */\n  removeTag(attrSelector: string): void {\n    this.removeTagElement(this.getTag(attrSelector)!);\n  }\n\n  /**\n   * Removes an existing `<meta>` tag element from the current HTML document.\n   * @param meta The tag definition to match against to identify an existing tag.\n   */\n  removeTagElement(meta: HTMLMetaElement): void {\n    if (meta) {\n      this._dom.remove(meta);\n    }\n  }\n\n  private _getOrCreateElement(meta: MetaDefinition, forceCreation: boolean = false):\n      HTMLMetaElement {\n    if (!forceCreation) {\n      const selector: string = this._parseSelector(meta);\n      // It's allowed to have multiple elements with the same name so it's not enough to\n      // just check that element with the same name already present on the page. We also need to\n      // check if element has tag attributes\n      const elem = this.getTags(selector).filter(elem => this._containsAttributes(meta, elem))[0];\n      if (elem !== undefined) return elem;\n    }\n    const element: HTMLMetaElement = this._dom.createElement('meta') as HTMLMetaElement;\n    this._setMetaElementAttributes(meta, element);\n    const head = this._doc.getElementsByTagName('head')[0];\n    head.appendChild(element);\n    return element;\n  }\n\n  private _setMetaElementAttributes(tag: MetaDefinition, el: HTMLMetaElement): HTMLMetaElement {\n    Object.keys(tag).forEach(\n        (prop: string) => el.setAttribute(this._getMetaKeyMap(prop), tag[prop]));\n    return el;\n  }\n\n  private _parseSelector(tag: MetaDefinition): string {\n    const attr: string = tag.name ? 'name' : 'property';\n    return `${attr}=\"${tag[attr]}\"`;\n  }\n\n  private _containsAttributes(tag: MetaDefinition, elem: HTMLMetaElement): boolean {\n    return Object.keys(tag).every(\n        (key: string) => elem.getAttribute(this._getMetaKeyMap(key)) === tag[key]);\n  }\n\n  private _getMetaKeyMap(prop: string): string {\n    return META_KEYS_MAP[prop] || prop;\n  }\n}\n\n/**\n * Mapping for MetaDefinition properties with their correct meta attribute names\n */\nconst META_KEYS_MAP: {[prop: string]: string;} = {\n  httpEquiv: 'http-equiv'\n};\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable} from '@angular/core';\n\n/**\n * A service that can be used to get and set the title of a current HTML document.\n *\n * Since an Angular application can't be bootstrapped on the entire HTML document (`<html>` tag)\n * it is not possible to bind to the `text` property of the `HTMLTitleElement` elements\n * (representing the `<title>` tag). Instead, this service can be used to set and get the current\n * title value.\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root'})\nexport class Title {\n  constructor(@Inject(DOCUMENT) private _doc: any) {}\n  /**\n   * Get the title of the current HTML document.\n   */\n  getTitle(): string {\n    return this._doc.title;\n  }\n\n  /**\n   * Set the title of the current HTML document.\n   * @param newTitle\n   */\n  setTitle(newTitle: string) {\n    this._doc.title = newTitle || '';\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵglobal as global} from '@angular/core';\n\n/**\n * Exports the value under a given `name` in the global property `ng`. For example `ng.probe` if\n * `name` is `'probe'`.\n * @param name Name under which it will be exported. Keep in mind this will be a property of the\n * global `ng` object.\n * @param value The value to export.\n */\nexport function exportNgVar(name: string, value: any): void {\n  if (typeof COMPILED === 'undefined' || !COMPILED) {\n    // Note: we can't export `ng` when using closure enhanced optimization as:\n    // - closure declares globals itself for minified names, which sometimes clobber our `ng` global\n    // - we can't declare a closure extern as the namespace `ng` is already used within Google\n    //   for typings for angularJS (via `goog.provide('ng....')`).\n    const ng = global['ng'] = (global['ng'] as {[key: string]: any} | undefined) || {};\n    ng[name] = value;\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ApplicationRef, ComponentRef} from '@angular/core';\n\nexport class ChangeDetectionPerfRecord {\n  constructor(public msPerTick: number, public numTicks: number) {}\n}\n\n/**\n * Entry point for all Angular profiling-related debug tools. This object\n * corresponds to the `ng.profiler` in the dev console.\n */\nexport class AngularProfiler {\n  appRef: ApplicationRef;\n\n  constructor(ref: ComponentRef<any>) {\n    this.appRef = ref.injector.get(ApplicationRef);\n  }\n\n  // tslint:disable:no-console\n  /**\n   * Exercises change detection in a loop and then prints the average amount of\n   * time in milliseconds how long a single round of change detection takes for\n   * the current state of the UI. It runs a minimum of 5 rounds for a minimum\n   * of 500 milliseconds.\n   *\n   * Optionally, a user may pass a `config` parameter containing a map of\n   * options. Supported options are:\n   *\n   * `record` (boolean) - causes the profiler to record a CPU profile while\n   * it exercises the change detector. Example:\n   *\n   * ```\n   * ng.profiler.timeChangeDetection({record: true})\n   * ```\n   */\n  timeChangeDetection(config: any): ChangeDetectionPerfRecord {\n    const record = config && config['record'];\n    const profileName = 'Change Detection';\n    // Profiler is not available in Android browsers without dev tools opened\n    if (record && 'profile' in console && typeof console.profile === 'function') {\n      console.profile(profileName);\n    }\n    const start = performance.now();\n    let numTicks = 0;\n    while (numTicks < 5 || (performance.now() - start) < 500) {\n      this.appRef.tick();\n      numTicks++;\n    }\n    const end = performance.now();\n    if (record && 'profileEnd' in console && typeof console.profileEnd === 'function') {\n      console.profileEnd(profileName);\n    }\n    const msPerTick = (end - start) / numTicks;\n    console.log(`ran ${numTicks} change detection cycles`);\n    console.log(`${msPerTick.toFixed(2)} ms per check`);\n\n    return new ChangeDetectionPerfRecord(msPerTick, numTicks);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ComponentRef} from '@angular/core';\nimport {exportNgVar} from '../../dom/util';\nimport {AngularProfiler} from './common_tools';\n\nconst PROFILER_GLOBAL_NAME = 'profiler';\n\n/**\n * Enabled Angular debug tools that are accessible via your browser's\n * developer console.\n *\n * Usage:\n *\n * 1. Open developer console (e.g. in Chrome Ctrl + Shift + j)\n * 1. Type `ng.` (usually the console will show auto-complete suggestion)\n * 1. Try the change detection profiler `ng.profiler.timeChangeDetection()`\n *    then hit Enter.\n *\n * @publicApi\n */\nexport function enableDebugTools<T>(ref: ComponentRef<T>): ComponentRef<T> {\n  exportNgVar(PROFILER_GLOBAL_NAME, new AngularProfiler(ref));\n  return ref;\n}\n\n/**\n * Disables Angular tools.\n *\n * @publicApi\n */\nexport function disableDebugTools(): void {\n  exportNgVar(PROFILER_GLOBAL_NAME, null);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {ɵgetDOM as getDOM} from '@angular/common';\nimport {DebugElement, DebugNode, Predicate, Type} from '@angular/core';\n\n\n\n/**\n * Predicates for use with {@link DebugElement}'s query functions.\n *\n * @publicApi\n */\nexport class By {\n  /**\n   * Match all nodes.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_all'}\n   */\n  static all(): Predicate<DebugNode> {\n    return () => true;\n  }\n\n  /**\n   * Match elements by the given CSS selector.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_css'}\n   */\n  static css(selector: string): Predicate<DebugElement> {\n    return (debugElement) => {\n      return debugElement.nativeElement != null ?\n          elementMatches(debugElement.nativeElement, selector) :\n          false;\n    };\n  }\n\n  /**\n   * Match nodes that have the given directive present.\n   *\n   * @usageNotes\n   * ### Example\n   *\n   * {@example platform-browser/dom/debug/ts/by/by.ts region='by_directive'}\n   */\n  static directive(type: Type<any>): Predicate<DebugNode> {\n    return (debugNode) => debugNode.providerTokens!.indexOf(type) !== -1;\n  }\n}\n\nfunction elementMatches(n: any, selector: string): boolean {\n  if (getDOM().isElementNode(n)) {\n    return n.matches && n.matches(selector) ||\n        n.msMatchesSelector && n.msMatchesSelector(selector) ||\n        n.webkitMatchesSelector && n.webkitMatchesSelector(selector);\n  }\n\n  return false;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {Inject, Injectable, InjectionToken, NgModule, Optional, Provider, ɵConsole as Console} from '@angular/core';\n\nimport {EVENT_MANAGER_PLUGINS, EventManagerPlugin} from './event_manager';\n\n\n\n/**\n * Supported HammerJS recognizer event names.\n */\nconst EVENT_NAMES = {\n  // pan\n  'pan': true,\n  'panstart': true,\n  'panmove': true,\n  'panend': true,\n  'pancancel': true,\n  'panleft': true,\n  'panright': true,\n  'panup': true,\n  'pandown': true,\n  // pinch\n  'pinch': true,\n  'pinchstart': true,\n  'pinchmove': true,\n  'pinchend': true,\n  'pinchcancel': true,\n  'pinchin': true,\n  'pinchout': true,\n  // press\n  'press': true,\n  'pressup': true,\n  // rotate\n  'rotate': true,\n  'rotatestart': true,\n  'rotatemove': true,\n  'rotateend': true,\n  'rotatecancel': true,\n  // swipe\n  'swipe': true,\n  'swipeleft': true,\n  'swiperight': true,\n  'swipeup': true,\n  'swipedown': true,\n  // tap\n  'tap': true,\n  'doubletap': true\n};\n\n/**\n * DI token for providing [HammerJS](https://hammerjs.github.io/) support to Angular.\n * @see {@link HammerGestureConfig}\n *\n * @ngModule HammerModule\n * @publicApi\n */\nexport const HAMMER_GESTURE_CONFIG = new InjectionToken<HammerGestureConfig>('HammerGestureConfig');\n\n\n/**\n * Function that loads HammerJS, returning a promise that is resolved once HammerJs is loaded.\n *\n * @publicApi\n */\nexport type HammerLoader = () => Promise<void>;\n\n/**\n * Injection token used to provide a {@link HammerLoader} to Angular.\n *\n * @publicApi\n */\nexport const HAMMER_LOADER = new InjectionToken<HammerLoader>('HammerLoader');\n\nexport interface HammerInstance {\n  on(eventName: string, callback?: Function): void;\n  off(eventName: string, callback?: Function): void;\n  destroy?(): void;\n}\n\n/**\n * An injectable [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n * for gesture recognition. Configures specific event recognition.\n * @publicApi\n */\n@Injectable()\nexport class HammerGestureConfig {\n  /**\n   * A set of supported event names for gestures to be used in Angular.\n   * Angular supports all built-in recognizers, as listed in\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  events: string[] = [];\n\n  /**\n   * Maps gesture event names to a set of configuration options\n   * that specify overrides to the default values for specific properties.\n   *\n   * The key is a supported event name to be configured,\n   * and the options object contains a set of properties, with override values\n   * to be applied to the named recognizer event.\n   * For example, to disable recognition of the rotate event, specify\n   *  `{\"rotate\": {\"enable\": false}}`.\n   *\n   * Properties that are not present take the HammerJS default values.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   *\n   */\n  overrides: {[key: string]: Object} = {};\n\n  /**\n   * Properties whose default values can be overridden for a given event.\n   * Different sets of properties apply to different events.\n   * For information about which properties are supported for which events,\n   * and their allowed and default values, see\n   * [HammerJS documentation](https://hammerjs.github.io/).\n   */\n  options?: {\n    cssProps?: any;\n    domEvents?: boolean;\n    enable?: boolean | ((manager: any) => boolean);\n    preset?: any[];\n    touchAction?: string;\n    recognizers?: any[];\n    inputClass?: any;\n    inputTarget?: EventTarget;\n  };\n\n  /**\n   * Creates a [HammerJS Manager](https://hammerjs.github.io/api/#hammermanager)\n   * and attaches it to a given HTML element.\n   * @param element The element that will recognize gestures.\n   * @returns A HammerJS event-manager object.\n   */\n  buildHammer(element: HTMLElement): HammerInstance {\n    const mc = new Hammer!(element, this.options);\n\n    mc.get('pinch').set({enable: true});\n    mc.get('rotate').set({enable: true});\n\n    for (const eventName in this.overrides) {\n      mc.get(eventName).set(this.overrides[eventName]);\n    }\n\n    return mc;\n  }\n}\n\n/**\n * Event plugin that adds Hammer support to an application.\n *\n * @ngModule HammerModule\n */\n@Injectable()\nexport class HammerGesturesPlugin extends EventManagerPlugin {\n  private _loaderPromise: Promise<void>|null = null;\n\n  constructor(\n      @Inject(DOCUMENT) doc: any,\n      @Inject(HAMMER_GESTURE_CONFIG) private _config: HammerGestureConfig, private console: Console,\n      @Optional() @Inject(HAMMER_LOADER) private loader?: HammerLoader|null) {\n    super(doc);\n  }\n\n  override supports(eventName: string): boolean {\n    if (!EVENT_NAMES.hasOwnProperty(eventName.toLowerCase()) && !this.isCustomEvent(eventName)) {\n      return false;\n    }\n\n    if (!(window as any).Hammer && !this.loader) {\n      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n        this.console.warn(\n            `The \"${eventName}\" event cannot be bound because Hammer.JS is not ` +\n            `loaded and no custom loader has been specified.`);\n      }\n      return false;\n    }\n\n    return true;\n  }\n\n  override addEventListener(element: HTMLElement, eventName: string, handler: Function): Function {\n    const zone = this.manager.getZone();\n    eventName = eventName.toLowerCase();\n\n    // If Hammer is not present but a loader is specified, we defer adding the event listener\n    // until Hammer is loaded.\n    if (!(window as any).Hammer && this.loader) {\n      this._loaderPromise = this._loaderPromise || zone.runOutsideAngular(() => this.loader!());\n      // This `addEventListener` method returns a function to remove the added listener.\n      // Until Hammer is loaded, the returned function needs to *cancel* the registration rather\n      // than remove anything.\n      let cancelRegistration = false;\n      let deregister: Function = () => {\n        cancelRegistration = true;\n      };\n\n      zone.runOutsideAngular(\n          () => this._loaderPromise!\n                    .then(() => {\n                      // If Hammer isn't actually loaded when the custom loader resolves, give up.\n                      if (!(window as any).Hammer) {\n                        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                          this.console.warn(\n                              `The custom HAMMER_LOADER completed, but Hammer.JS is not present.`);\n                        }\n                        deregister = () => {};\n                        return;\n                      }\n\n                      if (!cancelRegistration) {\n                        // Now that Hammer is loaded and the listener is being loaded for real,\n                        // the deregistration function changes from canceling registration to\n                        // removal.\n                        deregister = this.addEventListener(element, eventName, handler);\n                      }\n                    })\n                    .catch(() => {\n                      if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                        this.console.warn(\n                            `The \"${eventName}\" event cannot be bound because the custom ` +\n                            `Hammer.JS loader failed.`);\n                      }\n                      deregister = () => {};\n                    }));\n\n      // Return a function that *executes* `deregister` (and not `deregister` itself) so that we\n      // can change the behavior of `deregister` once the listener is added. Using a closure in\n      // this way allows us to avoid any additional data structures to track listener removal.\n      return () => {\n        deregister();\n      };\n    }\n\n    return zone.runOutsideAngular(() => {\n      // Creating the manager bind events, must be done outside of angular\n      const mc = this._config.buildHammer(element);\n      const callback = function(eventObj: HammerInput) {\n        zone.runGuarded(function() {\n          handler(eventObj);\n        });\n      };\n      mc.on(eventName, callback);\n      return () => {\n        mc.off(eventName, callback);\n        // destroy mc to prevent memory leak\n        if (typeof mc.destroy === 'function') {\n          mc.destroy();\n        }\n      };\n    });\n  }\n\n  isCustomEvent(eventName: string): boolean {\n    return this._config.events.indexOf(eventName) > -1;\n  }\n}\n\n/**\n * Adds support for HammerJS.\n *\n * Import this module at the root of your application so that Angular can work with\n * HammerJS to detect gesture events.\n *\n * Note that applications still need to include the HammerJS script itself. This module\n * simply sets up the coordination layer between HammerJS and Angular's `EventManager`.\n *\n * @publicApi\n */\n@NgModule({\n  providers: [\n    {\n      provide: EVENT_MANAGER_PLUGINS,\n      useClass: HammerGesturesPlugin,\n      multi: true,\n      deps: [DOCUMENT, HAMMER_GESTURE_CONFIG, Console, [new Optional(), HAMMER_LOADER]]\n    },\n    {provide: HAMMER_GESTURE_CONFIG, useClass: HammerGestureConfig, deps: []},\n  ]\n})\nexport class HammerModule {\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {DOCUMENT} from '@angular/common';\nimport {forwardRef, Inject, Injectable, Sanitizer, SecurityContext, ɵ_sanitizeHtml as _sanitizeHtml, ɵ_sanitizeUrl as _sanitizeUrl, ɵallowSanitizationBypassAndThrow as allowSanitizationBypassOrThrow, ɵbypassSanitizationTrustHtml as bypassSanitizationTrustHtml, ɵbypassSanitizationTrustResourceUrl as bypassSanitizationTrustResourceUrl, ɵbypassSanitizationTrustScript as bypassSanitizationTrustScript, ɵbypassSanitizationTrustStyle as bypassSanitizationTrustStyle, ɵbypassSanitizationTrustUrl as bypassSanitizationTrustUrl, ɵBypassType as BypassType, ɵRuntimeError as RuntimeError, ɵunwrapSafeValue as unwrapSafeValue, ɵXSS_SECURITY_URL as XSS_SECURITY_URL} from '@angular/core';\n\nimport {RuntimeErrorCode} from '../errors';\n\nexport {SecurityContext};\n\n/**\n * Marker interface for a value that's safe to use in a particular context.\n *\n * @publicApi\n */\nexport interface SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as HTML.\n *\n * @publicApi\n */\nexport interface SafeHtml extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as style (CSS).\n *\n * @publicApi\n */\nexport interface SafeStyle extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as JavaScript.\n *\n * @publicApi\n */\nexport interface SafeScript extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL linking to a document.\n *\n * @publicApi\n */\nexport interface SafeUrl extends SafeValue {}\n\n/**\n * Marker interface for a value that's safe to use as a URL to load executable code from.\n *\n * @publicApi\n */\nexport interface SafeResourceUrl extends SafeValue {}\n\n/**\n * DomSanitizer helps preventing Cross Site Scripting Security bugs (XSS) by sanitizing\n * values to be safe to use in the different DOM contexts.\n *\n * For example, when binding a URL in an `<a [href]=\"someValue\">` hyperlink, `someValue` will be\n * sanitized so that an attacker cannot inject e.g. a `javascript:` URL that would execute code on\n * the website.\n *\n * In specific situations, it might be necessary to disable sanitization, for example if the\n * application genuinely needs to produce a `javascript:` style link with a dynamic value in it.\n * Users can bypass security by constructing a value with one of the `bypassSecurityTrust...`\n * methods, and then binding to that value from the template.\n *\n * These situations should be very rare, and extraordinary care must be taken to avoid creating a\n * Cross Site Scripting (XSS) security bug!\n *\n * When using `bypassSecurityTrust...`, make sure to call the method as early as possible and as\n * close as possible to the source of the value, to make it easy to verify no security bug is\n * created by its use.\n *\n * It is not required (and not recommended) to bypass security if the value is safe, e.g. a URL that\n * does not start with a suspicious protocol, or an HTML snippet that does not contain dangerous\n * code. The sanitizer leaves safe values intact.\n *\n * @security Calling any of the `bypassSecurityTrust...` APIs disables Angular's built-in\n * sanitization for the value passed in. Carefully check and audit all values and code paths going\n * into this call. Make sure any user data is appropriately escaped for this security context.\n * For more detail, see the [Security Guide](https://g.co/ng/security).\n *\n * @publicApi\n */\n@Injectable({providedIn: 'root', useExisting: forwardRef(() => DomSanitizerImpl)})\nexport abstract class DomSanitizer implements Sanitizer {\n  /**\n   * Gets a safe value from either a known safe value or a value with unknown safety.\n   *\n   * If the given value is already a `SafeValue`, this method returns the unwrapped value.\n   * If the security context is HTML and the given value is a plain string, this method\n   * sanitizes the string, removing any potentially unsafe content.\n   * For any other security context, this method throws an error if provided\n   * with a plain string.\n   */\n  abstract sanitize(context: SecurityContext, value: SafeValue|string|null): string|null;\n\n  /**\n   * Bypass security and trust the given value to be safe HTML. Only use this when the bound HTML\n   * is unsafe (e.g. contains `<script>` tags) and the code should be executed. The sanitizer will\n   * leave safe HTML intact, so in most situations this method should not be used.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustHtml(value: string): SafeHtml;\n\n  /**\n   * Bypass security and trust the given value to be safe style value (CSS).\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustStyle(value: string): SafeStyle;\n\n  /**\n   * Bypass security and trust the given value to be safe JavaScript.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustScript(value: string): SafeScript;\n\n  /**\n   * Bypass security and trust the given value to be a safe style URL, i.e. a value that can be used\n   * in hyperlinks or `<img src>`.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustUrl(value: string): SafeUrl;\n\n  /**\n   * Bypass security and trust the given value to be a safe resource URL, i.e. a location that may\n   * be used to load executable code from, like `<script src>`, or `<iframe src>`.\n   *\n   * **WARNING:** calling this method with untrusted user data exposes your application to XSS\n   * security risks!\n   */\n  abstract bypassSecurityTrustResourceUrl(value: string): SafeResourceUrl;\n}\n\n@Injectable({providedIn: 'root'})\nexport class DomSanitizerImpl extends DomSanitizer {\n  constructor(@Inject(DOCUMENT) private _doc: any) {\n    super();\n  }\n\n  override sanitize(ctx: SecurityContext, value: SafeValue|string|null): string|null {\n    if (value == null) return null;\n    switch (ctx) {\n      case SecurityContext.NONE:\n        return value as string;\n      case SecurityContext.HTML:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Html)) {\n          return unwrapSafeValue(value);\n        }\n        return _sanitizeHtml(this._doc, String(value)).toString();\n      case SecurityContext.STYLE:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Style)) {\n          return unwrapSafeValue(value);\n        }\n        return value as string;\n      case SecurityContext.SCRIPT:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Script)) {\n          return unwrapSafeValue(value);\n        }\n        throw new RuntimeError(\n            RuntimeErrorCode.SANITIZATION_UNSAFE_SCRIPT,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                'unsafe value used in a script context');\n      case SecurityContext.URL:\n        if (allowSanitizationBypassOrThrow(value, BypassType.Url)) {\n          return unwrapSafeValue(value);\n        }\n        return _sanitizeUrl(String(value));\n      case SecurityContext.RESOURCE_URL:\n        if (allowSanitizationBypassOrThrow(value, BypassType.ResourceUrl)) {\n          return unwrapSafeValue(value);\n        }\n        throw new RuntimeError(\n            RuntimeErrorCode.SANITIZATION_UNSAFE_RESOURCE_URL,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `unsafe value used in a resource URL context (see ${XSS_SECURITY_URL})`);\n      default:\n        throw new RuntimeError(\n            RuntimeErrorCode.SANITIZATION_UNEXPECTED_CTX,\n            (typeof ngDevMode === 'undefined' || ngDevMode) &&\n                `Unexpected SecurityContext ${ctx} (see ${XSS_SECURITY_URL})`);\n    }\n  }\n\n  override bypassSecurityTrustHtml(value: string): SafeHtml {\n    return bypassSanitizationTrustHtml(value);\n  }\n  override bypassSecurityTrustStyle(value: string): SafeStyle {\n    return bypassSanitizationTrustStyle(value);\n  }\n  override bypassSecurityTrustScript(value: string): SafeScript {\n    return bypassSanitizationTrustScript(value);\n  }\n  override bypassSecurityTrustUrl(value: string): SafeUrl {\n    return bypassSanitizationTrustUrl(value);\n  }\n  override bypassSecurityTrustResourceUrl(value: string): SafeResourceUrl {\n    return bypassSanitizationTrustResourceUrl(value);\n  }\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {HttpTransferCacheOptions, ɵwithHttpTransferCache} from '@angular/common/http';\nimport {ENVIRONMENT_INITIALIZER, EnvironmentProviders, inject, makeEnvironmentProviders, NgZone, Provider, ɵConsole as Console, ɵformatRuntimeError as formatRuntimeError, ɵwithDomHydration as withDomHydration,} from '@angular/core';\n\nimport {RuntimeErrorCode} from './errors';\n\n/**\n * The list of features as an enum to uniquely type each `HydrationFeature`.\n * @see {@link HydrationFeature}\n *\n * @publicApi\n */\nexport enum HydrationFeatureKind {\n  NoHttpTransferCache,\n  HttpTransferCacheOptions,\n}\n\n/**\n * Helper type to represent a Hydration feature.\n *\n * @publicApi\n */\nexport interface HydrationFeature<FeatureKind extends HydrationFeatureKind> {\n  ɵkind: FeatureKind;\n  ɵproviders: Provider[];\n}\n\n/**\n * Helper function to create an object that represents a Hydration feature.\n */\nfunction hydrationFeature<FeatureKind extends HydrationFeatureKind>(\n    ɵkind: FeatureKind, ɵproviders: Provider[] = [],\n    ɵoptions: unknown = {}): HydrationFeature<FeatureKind> {\n  return {ɵkind, ɵproviders};\n}\n\n/**\n * Disables HTTP transfer cache. Effectively causes HTTP requests to be performed twice: once on the\n * server and other one on the browser.\n *\n * @publicApi\n */\nexport function withNoHttpTransferCache():\n    HydrationFeature<HydrationFeatureKind.NoHttpTransferCache> {\n  // This feature has no providers and acts as a flag that turns off\n  // HTTP transfer cache (which otherwise is turned on by default).\n  return hydrationFeature(HydrationFeatureKind.NoHttpTransferCache);\n}\n\n/**\n * The function accepts a an object, which allows to configure cache parameters,\n * such as which headers should be included (no headers are included by default),\n * wether POST requests should be cached or a callback function to determine if a\n * particular request should be cached.\n *\n * @publicApi\n */\nexport function withHttpTransferCacheOptions(\n    options: HttpTransferCacheOptions,\n    ): HydrationFeature<HydrationFeatureKind.HttpTransferCacheOptions> {\n  // This feature has no providers and acts as a flag to pass options to the HTTP transfer cache.\n  return hydrationFeature(\n      HydrationFeatureKind.HttpTransferCacheOptions, ɵwithHttpTransferCache(options));\n}\n\n/**\n * Returns an `ENVIRONMENT_INITIALIZER` token setup with a function\n * that verifies whether compatible ZoneJS was used in an application\n * and logs a warning in a console if it's not the case.\n */\nfunction provideZoneJsCompatibilityDetector(): Provider[] {\n  return [{\n    provide: ENVIRONMENT_INITIALIZER,\n    useValue: () => {\n      const ngZone = inject(NgZone);\n      // Checking `ngZone instanceof NgZone` would be insufficient here,\n      // because custom implementations might use NgZone as a base class.\n      if (ngZone.constructor !== NgZone) {\n        const console = inject(Console);\n        const message = formatRuntimeError(\n            RuntimeErrorCode.UNSUPPORTED_ZONEJS_INSTANCE,\n            'Angular detected that hydration was enabled for an application ' +\n                'that uses a custom or a noop Zone.js implementation. ' +\n                'This is not yet a fully supported configuration.');\n        // tslint:disable-next-line:no-console\n        console.warn(message);\n      }\n    },\n    multi: true,\n  }];\n}\n\n/**\n * Sets up providers necessary to enable hydration functionality for the application.\n *\n * By default, the function enables the recommended set of features for the optimal\n * performance for most of the applications. It includes the following features:\n *\n * * Reconciling DOM hydration. Learn more about it [here](guide/hydration).\n * * [`HttpClient`](api/common/http/HttpClient) response caching while running on the server and\n * transferring this cache to the client to avoid extra HTTP requests. Learn more about data caching\n * [here](/guide/ssr#caching-data-when-using-httpclient).\n *\n * These functions allow you to disable some of the default features or configure features\n * * {@link withNoHttpTransferCache} to disable HTTP transfer cache\n * * {@link withHttpTransferCacheOptions} to configure some HTTP transfer cache options\n *\n * @usageNotes\n *\n * Basic example of how you can enable hydration in your application when\n * `bootstrapApplication` function is used:\n * ```\n * bootstrapApplication(AppComponent, {\n *   providers: [provideClientHydration()]\n * });\n * ```\n *\n * Alternatively if you are using NgModules, you would add `provideClientHydration`\n * to your root app module's provider list.\n * ```\n * @NgModule({\n *   declarations: [RootCmp],\n *   bootstrap: [RootCmp],\n *   providers: [provideClientHydration()],\n * })\n * export class AppModule {}\n * ```\n *\n * @see {@link withNoHttpTransferCache}\n * @see {@link withHttpTransferCacheOptions}\n *\n * @param features Optional features to configure additional router behaviors.\n * @returns A set of providers to enable hydration.\n *\n * @publicApi\n */\nexport function provideClientHydration(...features: HydrationFeature<HydrationFeatureKind>[]):\n    EnvironmentProviders {\n  const providers: Provider[] = [];\n  const featuresKind = new Set<HydrationFeatureKind>();\n  const hasHttpTransferCacheOptions =\n      featuresKind.has(HydrationFeatureKind.HttpTransferCacheOptions);\n\n  for (const {ɵproviders, ɵkind} of features) {\n    featuresKind.add(ɵkind);\n\n    if (ɵproviders.length) {\n      providers.push(ɵproviders);\n    }\n  }\n\n  if (typeof ngDevMode !== 'undefined' && ngDevMode &&\n      featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) && hasHttpTransferCacheOptions) {\n    // TODO: Make this a runtime error\n    throw new Error(\n        'Configuration error: found both withHttpTransferCacheOptions() and withNoHttpTransferCache() in the same call to provideClientHydration(), which is a contradiction.');\n  }\n\n  return makeEnvironmentProviders([\n    (typeof ngDevMode !== 'undefined' && ngDevMode) ? provideZoneJsCompatibilityDetector() : [],\n    withDomHydration(),\n    ((featuresKind.has(HydrationFeatureKind.NoHttpTransferCache) || hasHttpTransferCacheOptions) ?\n         [] :\n         ɵwithHttpTransferCache({})),\n    providers,\n  ]);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of the platform-browser package.\n */\n\nimport {Version} from '@angular/core';\n\n/**\n * @publicApi\n */\nexport const VERSION = new Version('17.3.12');\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Re-export TransferState to the public API of the `platform-browser` for backwards-compatibility.\nimport {makeStateKey as makeStateKeyFromCore, StateKey as StateKeyFromCore, TransferState as TransferStateFromCore} from '@angular/core';\n\n/**\n * Create a `StateKey<T>` that can be used to store value of type T with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n *\n * @publicApi\n * @deprecated `makeStateKey` has moved, please import `makeStateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nexport const makeStateKey = makeStateKeyFromCore;\n\n/**\n *\n * A key value store that is transferred from the application on the server side to the application\n * on the client side.\n *\n * The `TransferState` is available as an injectable token.\n * On the client, just inject this token using DI and use it, it will be lazily initialized.\n * On the server it's already included if `renderApplication` function is used. Otherwise, import\n * the `ServerTransferStateModule` module to make the `TransferState` available.\n *\n * The values in the store are serialized/deserialized using JSON.stringify/JSON.parse. So only\n * boolean, number, string, null and non-class objects will be serialized and deserialized in a\n * non-lossy manner.\n *\n * @publicApi\n *\n * @deprecated `TransferState` has moved, please import `TransferState` from `@angular/core`\n *     instead.\n */\n// The below is a workaround to add a deprecated message.\nexport type TransferState = TransferStateFromCore;\n// The below type is needed for G3 due to JSC_CONFORMANCE_VIOLATION.\nexport const TransferState: {new (): TransferStateFromCore} = TransferStateFromCore;\n\n/**\n * A type-safe key to use with `TransferState`.\n *\n * Example:\n *\n * ```\n * const COUNTER_KEY = makeStateKey<number>('counter');\n * let value = 10;\n *\n * transferState.set(COUNTER_KEY, value);\n * ```\n * @publicApi\n *\n * @deprecated `StateKey` has moved, please import `StateKey` from `@angular/core` instead.\n */\n// The below is a workaround to add a deprecated message.\nexport type StateKey<T> = StateKeyFromCore<T>;\n\nexport {ApplicationConfig, bootstrapApplication, BrowserModule, createApplication, platformBrowser, provideProtractorTestingSupport} from './browser';\nexport {Meta, MetaDefinition} from './browser/meta';\nexport {Title} from './browser/title';\nexport {disableDebugTools, enableDebugTools} from './browser/tools/tools';\nexport {By} from './dom/debug/by';\nexport {REMOVE_STYLES_ON_COMPONENT_DESTROY} from './dom/dom_renderer';\nexport {EVENT_MANAGER_PLUGINS, EventManager, EventManagerPlugin} from './dom/events/event_manager';\nexport {HAMMER_GESTURE_CONFIG, HAMMER_LOADER, HammerGestureConfig, HammerLoader, HammerModule} from './dom/events/hammer_gestures';\nexport {DomSanitizer, SafeHtml, SafeResourceUrl, SafeScript, SafeStyle, SafeUrl, SafeValue} from './security/dom_sanitization_service';\nexport {HydrationFeature, provideClientHydration, HydrationFeatureKind, withHttpTransferCacheOptions, withNoHttpTransferCache} from './hydration';\n\nexport * from './private_export';\nexport {VERSION} from './version';\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\nexport * from './src/platform-browser';\n// This file only reexports content of the `src` folder. Keep it that way.\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// This file is not used to build this module. It is only used during editing\n// by the TypeScript language service and during build for verification. `ngc`\n// replaces this file with production index.ts when it rewrites private symbol\n// names.\n\nexport * from './public_api';\n", "/**\n * Generated bundle index. Do not edit.\n */\n\nexport * from './index';\n"], "names": ["DomAdapter", "setRootDomAdapter", "parseCookieValue", "global", "RuntimeError", "getDOM", "i1.EventManager", "i2.SharedStylesHost", "internalCreateApplication", "PLATFORM_BROWSER_ID", "TESTABILITY_GETTER", "TESTABILITY", "INJECTOR_SCOPE", "<PERSON><PERSON><PERSON>", "allowSanitizationBypassOrThrow", "unwrapSafeValue", "_sanitizeHtml", "_sanitizeUrl", "XSS_SECURITY_URL", "bypassSanitizationTrustHtml", "bypassSanitizationTrustStyle", "bypassSanitizationTrustScript", "bypassSanitizationTrustUrl", "bypassSanitizationTrustResourceUrl", "formatRuntimeError", "withDomHydration", "makeStateKeyFromCore", "TransferStateFromCore"], "mappings": ";;;;;;;;;;;;AAYA;;;;;AAKG;AACG,MAAgB,wBAAyB,SAAQA,WAAU,CAAA;AAAjE,IAAA,WAAA,GAAA;;QACoB,IAAiB,CAAA,iBAAA,GAAY,IAAI,CAAC;KACrD;AAAA;;ACRD;;;;;AAKG;AACH;AACM,MAAO,iBAAkB,SAAQ,wBAAwB,CAAA;AAC7D,IAAA,OAAO,WAAW,GAAA;AAChB,QAAAC,kBAAiB,CAAC,IAAI,iBAAiB,EAAE,CAAC,CAAC;KAC5C;AAEQ,IAAA,WAAW,CAAC,EAAQ,EAAE,GAAQ,EAAE,QAAa,EAAA;AACpD,QAAA,EAAE,CAAC,gBAAgB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACnC,QAAA,OAAO,MAAK;AACV,YAAA,EAAE,CAAC,mBAAmB,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;AACxC,SAAC,CAAC;KACH;IACQ,aAAa,CAAC,EAAQ,EAAE,GAAQ,EAAA;AACvC,QAAA,EAAE,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;KACvB;AACQ,IAAA,MAAM,CAAC,IAAU,EAAA;AACxB,QAAA,IAAI,IAAI,CAAC,UAAU,EAAE;AACnB,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;SACnC;KACF;IACQ,aAAa,CAAC,OAAe,EAAE,GAAc,EAAA;AACpD,QAAA,GAAG,GAAG,GAAG,IAAI,IAAI,CAAC,kBAAkB,EAAE,CAAC;AACvC,QAAA,OAAO,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;KACnC;IACQ,kBAAkB,GAAA;QACzB,OAAO,QAAQ,CAAC,cAAc,CAAC,kBAAkB,CAAC,WAAW,CAAC,CAAC;KAChE;IACQ,kBAAkB,GAAA;AACzB,QAAA,OAAO,QAAQ,CAAC;KACjB;AAEQ,IAAA,aAAa,CAAC,IAAU,EAAA;AAC/B,QAAA,OAAO,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY,CAAC;KAC5C;AAEQ,IAAA,YAAY,CAAC,IAAS,EAAA;QAC7B,OAAO,IAAI,YAAY,gBAAgB,CAAC;KACzC;;IAGQ,oBAAoB,CAAC,GAAa,EAAE,MAAc,EAAA;AACzD,QAAA,IAAI,MAAM,KAAK,QAAQ,EAAE;AACvB,YAAA,OAAO,MAAM,CAAC;SACf;AACD,QAAA,IAAI,MAAM,KAAK,UAAU,EAAE;AACzB,YAAA,OAAO,GAAG,CAAC;SACZ;AACD,QAAA,IAAI,MAAM,KAAK,MAAM,EAAE;YACrB,OAAO,GAAG,CAAC,IAAI,CAAC;SACjB;AACD,QAAA,OAAO,IAAI,CAAC;KACb;AACQ,IAAA,WAAW,CAAC,GAAa,EAAA;AAChC,QAAA,MAAM,IAAI,GAAG,kBAAkB,EAAE,CAAC;AAClC,QAAA,OAAO,IAAI,IAAI,IAAI,GAAG,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,CAAC;KACjD;IACQ,gBAAgB,GAAA;QACvB,WAAW,GAAG,IAAI,CAAC;KACpB;IACQ,YAAY,GAAA;AACnB,QAAA,OAAO,MAAM,CAAC,SAAS,CAAC,SAAS,CAAC;KACnC;AACQ,IAAA,SAAS,CAAC,IAAY,EAAA;QAC7B,OAAOC,iBAAgB,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;KAChD;AACF,CAAA;AAED,IAAI,WAAW,GAAqB,IAAI,CAAC;AACzC,SAAS,kBAAkB,GAAA;IACzB,WAAW,GAAG,WAAW,IAAI,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;AAC5D,IAAA,OAAO,WAAW,GAAG,WAAW,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC/D,CAAC;AAED,SAAS,YAAY,CAAC,GAAW,EAAA;;;IAG/B,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,CAAC,QAAQ,CAAC;AACjD;;MClFa,qBAAqB,CAAA;AAChC,IAAA,WAAW,CAAC,QAA6B,EAAA;QACvCC,OAAM,CAAC,uBAAuB,CAAC,GAAG,CAAC,IAAS,EAAE,eAAA,GAA2B,IAAI,KAAI;YAC/E,MAAM,WAAW,GAAG,QAAQ,CAAC,qBAAqB,CAAC,IAAI,EAAE,eAAe,CAAC,CAAC;AAC1E,YAAA,IAAI,WAAW,IAAI,IAAI,EAAE;gBACvB,MAAM,IAAIC,aAAY,CAAA,IAAA,+CAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC1C,oBAAA,yCAAyC,CAAC,CAAC;aACpD;AACD,YAAA,OAAO,WAAW,CAAC;AACrB,SAAC,CAAC;QAEFD,OAAM,CAAC,4BAA4B,CAAC,GAAG,MAAM,QAAQ,CAAC,mBAAmB,EAAE,CAAC;QAE5EA,OAAM,CAAC,2BAA2B,CAAC,GAAG,MAAM,QAAQ,CAAC,kBAAkB,EAAE,CAAC;AAE1E,QAAA,MAAM,aAAa,GAAG,CAAC,QAAoB,KAAI;AAC7C,YAAA,MAAM,aAAa,GAAGA,OAAM,CAAC,4BAA4B,CAAC,EAAmB,CAAC;AAC9E,YAAA,IAAI,KAAK,GAAG,aAAa,CAAC,MAAM,CAAC;AACjC,YAAA,MAAM,SAAS,GAAG,YAAA;AAChB,gBAAA,KAAK,EAAE,CAAC;AACR,gBAAA,IAAI,KAAK,IAAI,CAAC,EAAE;AACd,oBAAA,QAAQ,EAAE,CAAC;iBACZ;AACH,aAAC,CAAC;AACF,YAAA,aAAa,CAAC,OAAO,CAAC,CAAC,WAAW,KAAI;AACpC,gBAAA,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC;AACpC,aAAC,CAAC,CAAC;AACL,SAAC,CAAC;AAEF,QAAA,IAAI,CAACA,OAAM,CAAC,sBAAsB,CAAC,EAAE;AACnC,YAAAA,OAAM,CAAC,sBAAsB,CAAC,GAAG,EAAE,CAAC;SACrC;QACDA,OAAM,CAAC,sBAAsB,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;KACpD;AAED,IAAA,qBAAqB,CAAC,QAA6B,EAAE,IAAS,EAAE,eAAwB,EAAA;AAEtF,QAAA,IAAI,IAAI,IAAI,IAAI,EAAE;AAChB,YAAA,OAAO,IAAI,CAAC;SACb;QACD,MAAM,CAAC,GAAG,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACxC,QAAA,IAAI,CAAC,IAAI,IAAI,EAAE;AACb,YAAA,OAAO,CAAC,CAAC;SACV;aAAM,IAAI,CAAC,eAAe,EAAE;AAC3B,YAAA,OAAO,IAAI,CAAC;SACb;QACD,IAAIE,OAAM,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,EAAE;AAC/B,YAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAQ,IAAK,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;SACrE;AACD,QAAA,OAAO,IAAI,CAAC,qBAAqB,CAAC,QAAQ,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;KACvE;AACF;;ACvDD;;AAEG;MAEU,UAAU,CAAA;IACrB,KAAK,GAAA;QACH,OAAO,IAAI,cAAc,EAAE,CAAC;KAC7B;yHAHU,UAAU,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAV,UAAU,EAAA,CAAA,CAAA,EAAA;;sGAAV,UAAU,EAAA,UAAA,EAAA,CAAA;kBADtB,UAAU;;;ACDX;;;;AAIG;AACU,MAAA,qBAAqB,GAC9B,IAAI,cAAc,CAAuB,SAAS,GAAG,qBAAqB,GAAG,EAAE,EAAE;AAErF;;;;;AAKG;MAEU,YAAY,CAAA;AAIvB;;AAEG;IACH,WAA2C,CAAA,OAA6B,EAAU,KAAa,EAAA;QAAb,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;AALvF,QAAA,IAAA,CAAA,kBAAkB,GAAG,IAAI,GAAG,EAA8B,CAAC;AAMjE,QAAA,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,KAAI;AACzB,YAAA,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;AACxB,SAAC,CAAC,CAAC;QACH,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,OAAO,EAAE,CAAC;KAC3C;AAED;;;;;;;;AAQG;AACH,IAAA,gBAAgB,CAAC,OAAoB,EAAE,SAAiB,EAAE,OAAiB,EAAA;QACzE,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,CAAC;QAC9C,OAAO,MAAM,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;KAC7D;AAED;;AAEG;IACH,OAAO,GAAA;QACL,OAAO,IAAI,CAAC,KAAK,CAAC;KACnB;;AAGD,IAAA,cAAc,CAAC,SAAiB,EAAA;QAC9B,IAAI,MAAM,GAAG,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;AACV,YAAA,OAAO,MAAM,CAAC;SACf;AAED,QAAA,MAAM,OAAO,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC9B,QAAA,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC;QAC9D,IAAI,CAAC,MAAM,EAAE;YACX,MAAM,IAAID,aAAY,CAAA,IAAA,6CAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC1C,CAA2C,wCAAA,EAAA,SAAS,CAAE,CAAA,CAAC,CAAC;SACjE;QAED,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC;AAC/C,QAAA,OAAO,MAAM,CAAC;KACf;AArDU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,kBAOH,qBAAqB,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAP9B,YAAY,EAAA,CAAA,CAAA,EAAA;;sGAAZ,YAAY,EAAA,UAAA,EAAA,CAAA;kBADxB,UAAU;;0BAQI,MAAM;2BAAC,qBAAqB,CAAA;;AAiD3C;;;;;;;AAOG;MACmB,kBAAkB,CAAA;;AAEtC,IAAA,WAAA,CAAoB,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;KAAI;AAclC;;ACjGD;AACA,MAAM,qBAAqB,GAAG,WAAW,CAAC;MAG7B,gBAAgB,CAAA;AAW3B,IAAA,WAAA,CACuC,GAAa,EACf,KAAa,EACP,KAAmB,EAC5B,aAAqB,EAAE,EAAA;QAHlB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAU;QACf,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACP,IAAK,CAAA,KAAA,GAAL,KAAK,CAAc;QAC5B,IAAU,CAAA,UAAA,GAAV,UAAU,CAAa;;AAbxC,QAAA,IAAA,CAAA,QAAQ,GAAG,IAAI,GAAG,EAI/B,CAAC;AACY,QAAA,IAAA,CAAA,SAAS,GAAG,IAAI,GAAG,EAAQ,CAAC;AAS3C,QAAA,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,2BAA2B,EAAE,CAAC;AAC1D,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;QACrD,IAAI,CAAC,cAAc,EAAE,CAAC;KACvB;AAED,IAAA,SAAS,CAAC,MAAgB,EAAA;AACxB,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AAEnD,YAAA,IAAI,UAAU,KAAK,CAAC,EAAE;AACpB,gBAAA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;aAC1B;SACF;KACF;AAED,IAAA,YAAY,CAAC,MAAgB,EAAA;AAC3B,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;AAEpD,YAAA,IAAI,UAAU,IAAI,CAAC,EAAE;AACnB,gBAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;aAC5B;SACF;KACF;IAED,WAAW,GAAA;AACT,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,IAAI,eAAe,EAAE;AACnB,YAAA,eAAe,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;YACjD,eAAe,CAAC,KAAK,EAAE,CAAC;SACzB;QAED,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACvC,YAAA,IAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAC5B;QAED,IAAI,CAAC,cAAc,EAAE,CAAC;KACvB;AAED,IAAA,OAAO,CAAC,QAAc,EAAA;AACpB,QAAA,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;QAE7B,KAAK,MAAM,KAAK,IAAI,IAAI,CAAC,YAAY,EAAE,EAAE;AACvC,YAAA,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;SACtC;KACF;AAED,IAAA,UAAU,CAAC,QAAc,EAAA;AACvB,QAAA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;KACjC;IAEO,YAAY,GAAA;AAClB,QAAA,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;KAC7B;AAEO,IAAA,YAAY,CAAC,KAAa,EAAA;AAChC,QAAA,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;AACjC,YAAA,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAClC;KACF;AAEO,IAAA,cAAc,CAAC,KAAa,EAAA;AAClC,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;AAChE,QAAA,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;KACxB;IAEO,2BAA2B,GAAA;AACjC,QAAA,MAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,gBAAgB,CAC1C,CAAA,MAAA,EAAS,qBAAqB,CAAK,EAAA,EAAA,IAAI,CAAC,KAAK,CAAA,EAAA,CAAI,CAAC,CAAC;AAEvD,QAAA,IAAI,MAAM,EAAE,MAAM,EAAE;AAClB,YAAA,MAAM,QAAQ,GAAG,IAAI,GAAG,EAA4B,CAAC;AAErD,YAAA,MAAM,CAAC,OAAO,CAAC,CAAC,KAAK,KAAI;AACvB,gBAAA,IAAI,KAAK,CAAC,WAAW,IAAI,IAAI,EAAE;oBAC7B,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;iBACxC;AACH,aAAC,CAAC,CAAC;AAEH,YAAA,OAAO,QAAQ,CAAC;SACjB;AAED,QAAA,OAAO,IAAI,CAAC;KACb;IAEO,gBAAgB,CAAC,KAAa,EAAE,KAAa,EAAA;AACnD,QAAA,MAAM,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC;AAC1B,QAAA,IAAI,GAAG,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE;YAClB,MAAM,aAAa,GAAG,GAAG,CAAC,GAAG,CAAC,KAAK,CAAE,CAAC;AACtC,YAAA,aAAa,CAAC,KAAK,IAAI,KAAK,CAAC;YAE7B,OAAO,aAAa,CAAC,KAAK,CAAC;SAC5B;AAED,QAAA,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,EAAC,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,EAAE,EAAC,CAAC,CAAC;AAC7C,QAAA,OAAO,KAAK,CAAC;KACd;IAEO,eAAe,CAAC,IAAU,EAAE,KAAa,EAAA;AAC/C,QAAA,MAAM,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC;QAC7C,MAAM,OAAO,GAAG,eAAe,EAAE,GAAG,CAAC,KAAK,CAAC,CAAC;AAC5C,QAAA,IAAI,OAAO,EAAE,UAAU,KAAK,IAAI,EAAE;;AAEhC,YAAA,eAAgB,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AAE/B,YAAA,OAAO,CAAC,eAAe,CAAC,qBAAqB,CAAC,CAAC;AAE/C,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;;AAEjD,gBAAA,OAAO,CAAC,YAAY,CAAC,iBAAiB,EAAE,EAAE,CAAC,CAAC;aAC7C;AAED,YAAA,OAAO,OAAO,CAAC;SAChB;aAAM;YACL,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;AAEhD,YAAA,IAAI,IAAI,CAAC,KAAK,EAAE;gBACd,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;aAC3C;AAED,YAAA,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAE5B,YAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACzB,OAAO,CAAC,YAAY,CAAC,qBAAqB,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC;aACzD;AAED,YAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAE1B,YAAA,OAAO,OAAO,CAAC;SAChB;KACF;IAEO,cAAc,CAAC,IAAU,EAAE,KAAa,EAAA;QAC9C,MAAM,OAAO,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;AAClD,QAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,CAAC;QAC/B,MAAM,UAAU,GAAG,QAAQ,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,QAAQ,CAAC;QACjD,IAAI,UAAU,EAAE;AACd,YAAA,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC1B;aAAM;AACL,YAAA,QAAQ,CAAC,GAAG,CAAC,KAAK,EAAE,EAAC,QAAQ,EAAE,CAAC,OAAO,CAAC,EAAE,KAAK,EAAE,CAAC,EAAC,CAAC,CAAC;SACtD;KACF;IAEO,cAAc,GAAA;AACpB,QAAA,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;QACjC,SAAS,CAAC,KAAK,EAAE,CAAC;;QAElB,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KAC9B;AArKU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBAYf,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,MAAM,EACN,EAAA,EAAA,KAAA,EAAA,SAAS,6BACT,WAAW,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAfZ,gBAAgB,EAAA,CAAA,CAAA,EAAA;;sGAAhB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;;0BAaJ,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,MAAM,CAAA;;0BACb,MAAM;2BAAC,SAAS,CAAA;;0BAAG,QAAQ;;0BAC3B,MAAM;2BAAC,WAAW,CAAA;;;ACdlB,MAAM,cAAc,GAA2B;AACpD,IAAA,KAAK,EAAE,4BAA4B;AACnC,IAAA,OAAO,EAAE,8BAA8B;AACvC,IAAA,OAAO,EAAE,8BAA8B;AACvC,IAAA,KAAK,EAAE,sCAAsC;AAC7C,IAAA,OAAO,EAAE,+BAA+B;AACxC,IAAA,MAAM,EAAE,gCAAgC;CACzC,CAAC;AAEF,MAAM,eAAe,GAAG,SAAS,CAAC;AAE3B,MAAM,kBAAkB,GAAG,QAAQ,CAAC;AACpC,MAAM,SAAS,GAAG,CAAW,QAAA,EAAA,kBAAkB,EAAE,CAAC;AAClD,MAAM,YAAY,GAAG,CAAc,WAAA,EAAA,kBAAkB,EAAE,CAAC;AAE/D;;AAEG;AACH,MAAM,0CAA0C,GAAG,IAAI,CAAC;AAExD;;;;;;AAMG;AACU,MAAA,kCAAkC,GAC3C,IAAI,cAAc,CAAU,SAAS,GAAG,2BAA2B,GAAG,EAAE,EAAE;AACxE,IAAA,UAAU,EAAE,MAAM;AAClB,IAAA,OAAO,EAAE,MAAM,0CAA0C;AAC1D,CAAA,EAAE;AAED,SAAU,oBAAoB,CAAC,gBAAwB,EAAA;IAC3D,OAAO,YAAY,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;AACjE,CAAC;AAEK,SAAU,iBAAiB,CAAC,gBAAwB,EAAA;IACxD,OAAO,SAAS,CAAC,OAAO,CAAC,eAAe,EAAE,gBAAgB,CAAC,CAAC;AAC9D,CAAC;AAEe,SAAA,iBAAiB,CAAC,MAAc,EAAE,MAAgB,EAAA;AAChE,IAAA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC,CAAC,CAAC;AAC7D,CAAC;MAGY,mBAAmB,CAAA;AAM9B,IAAA,WAAA,CACqB,YAA0B,EAC1B,gBAAkC,EAClB,KAAa,EACM,yBAAkC,EACnD,GAAa,EAClB,UAAkB,EACvC,MAAc,EACa,QAAqB,IAAI,EAAA;QAP5C,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;QAC1B,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAClB,IAAK,CAAA,KAAA,GAAL,KAAK,CAAQ;QACM,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAAS;QACnD,IAAG,CAAA,GAAA,GAAH,GAAG,CAAU;QAClB,IAAU,CAAA,UAAA,GAAV,UAAU,CAAQ;QACvC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QACa,IAAK,CAAA,KAAA,GAAL,KAAK,CAAoB;AAbhD,QAAA,IAAA,CAAA,gBAAgB,GAC7B,IAAI,GAAG,EAA0E,CAAC;AAcpF,QAAA,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;AACrD,QAAA,IAAI,CAAC,eAAe;AAChB,YAAA,IAAI,mBAAmB,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,gBAAgB,CAAC,CAAC;KAC/E;IAED,cAAc,CAAC,OAAY,EAAE,IAAwB,EAAA;AACnD,QAAA,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE;YACrB,OAAO,IAAI,CAAC,eAAe,CAAC;SAC7B;AAED,QAAA,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,aAAa,KAAK,iBAAiB,CAAC,SAAS,EAAE;;YAE/E,IAAI,GAAG,EAAC,GAAG,IAAI,EAAE,aAAa,EAAE,iBAAiB,CAAC,QAAQ,EAAC,CAAC;SAC7D;QAED,MAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;;;AAGzD,QAAA,IAAI,QAAQ,YAAY,iCAAiC,EAAE;AACzD,YAAA,QAAQ,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SAC/B;AAAM,aAAA,IAAI,QAAQ,YAAY,4BAA4B,EAAE;YAC3D,QAAQ,CAAC,WAAW,EAAE,CAAC;SACxB;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;IAEO,mBAAmB,CAAC,OAAY,EAAE,IAAmB,EAAA;AAC3D,QAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;QAC/C,IAAI,QAAQ,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAE7C,IAAI,CAAC,QAAQ,EAAE;AACb,YAAA,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC;AACrB,YAAA,MAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;AAC3B,YAAA,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;AACvC,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAC/C,YAAA,MAAM,yBAAyB,GAAG,IAAI,CAAC,yBAAyB,CAAC;AACjE,YAAA,MAAM,gBAAgB,GAAG,IAAI,CAAC,gBAAgB,CAAC;AAE/C,YAAA,QAAQ,IAAI,CAAC,aAAa;gBACxB,KAAK,iBAAiB,CAAC,QAAQ;oBAC7B,QAAQ,GAAG,IAAI,iCAAiC,CAC5C,YAAY,EAAE,gBAAgB,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,yBAAyB,EAAE,GAAG,EAChF,MAAM,EAAE,gBAAgB,CAAC,CAAC;oBAC9B,MAAM;gBACR,KAAK,iBAAiB,CAAC,SAAS;oBAC9B,OAAO,IAAI,iBAAiB,CACxB,YAAY,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,KAAK,EACtE,gBAAgB,CAAC,CAAC;AACxB,gBAAA;AACE,oBAAA,QAAQ,GAAG,IAAI,4BAA4B,CACvC,YAAY,EAAE,gBAAgB,EAAE,IAAI,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EAC5E,gBAAgB,CAAC,CAAC;oBACtB,MAAM;aACT;YAED,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,CAAC;SACzC;AAED,QAAA,OAAO,QAAQ,CAAC;KACjB;IAED,WAAW,GAAA;AACT,QAAA,IAAI,CAAC,gBAAgB,CAAC,KAAK,EAAE,CAAC;KAC/B;yHAhFU,mBAAmB,EAAA,IAAA,EAAA,CAAA,EAAA,KAAA,EAAAE,YAAA,EAAA,EAAA,EAAA,KAAA,EAAAC,gBAAA,EAAA,EAAA,EAAA,KAAA,EASlB,MAAM,EACN,EAAA,EAAA,KAAA,EAAA,kCAAkC,aAClC,QAAQ,EAAA,EAAA,EAAA,KAAA,EACR,WAAW,EAAA,EAAA,EAAA,KAAA,EAAA,EAAA,CAAA,MAAA,EAAA,EAAA,EAAA,KAAA,EAEX,SAAS,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAdV,mBAAmB,EAAA,CAAA,CAAA,EAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,UAAU;;0BAUJ,MAAM;2BAAC,MAAM,CAAA;;0BACb,MAAM;2BAAC,kCAAkC,CAAA;;0BACzC,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,WAAW,CAAA;;0BAElB,MAAM;2BAAC,SAAS,CAAA;;AAqEvB,MAAM,mBAAmB,CAAA;AASvB,IAAA,WAAA,CACqB,YAA0B,EAAmB,GAAa,EAC1D,MAAc,EAAmB,gBAAyB,EAAA;QAD1D,IAAY,CAAA,YAAA,GAAZ,YAAY,CAAc;QAAmB,IAAG,CAAA,GAAA,GAAH,GAAG,CAAU;QAC1D,IAAM,CAAA,MAAA,GAAN,MAAM,CAAQ;QAAmB,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAS;AAV/E,QAAA,IAAA,CAAA,IAAI,GAAyB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;AAEjD;;;AAGG;QACH,IAAqB,CAAA,qBAAA,GAAG,IAAI,CAAC;QAQ7B,IAAW,CAAA,WAAA,GAAG,IAAI,CAAC;KAJgE;AAEnF,IAAA,OAAO,MAAW;IAIlB,aAAa,CAAC,IAAY,EAAE,SAAkB,EAAA;QAC5C,IAAI,SAAS,EAAE;;;;;;;;;;AAUb,YAAA,OAAO,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,SAAS,EAAE,IAAI,CAAC,CAAC;SAC/E;QAED,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;KACrC;AAED,IAAA,aAAa,CAAC,KAAa,EAAA;QACzB,OAAO,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;KACtC;AAED,IAAA,UAAU,CAAC,KAAa,EAAA;QACtB,OAAO,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;KACvC;IAED,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;AACpC,QAAA,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AACtE,QAAA,YAAY,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;KACpC;AAED,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAA;QACpD,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,MAAM,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC;AACtE,YAAA,YAAY,CAAC,YAAY,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;SAC/C;KACF;IAED,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;QACpC,IAAI,MAAM,EAAE;AACV,YAAA,MAAM,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;SAC9B;KACF;IAED,iBAAiB,CAAC,cAA0B,EAAE,eAAyB,EAAA;AACrE,QAAA,IAAI,EAAE,GAAQ,OAAO,cAAc,KAAK,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC,cAAc,CAAC;AACtC,YAAA,cAAc,CAAC;QAClE,IAAI,CAAC,EAAE,EAAE;YACP,MAAM,IAAIH,aAAY,CAAA,CAAA,IAAA,6CAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;gBAC1C,CAAiB,cAAA,EAAA,cAAc,CAA8B,4BAAA,CAAA,CAAC,CAAC;SACxE;QACD,IAAI,CAAC,eAAe,EAAE;AACpB,YAAA,EAAE,CAAC,WAAW,GAAG,EAAE,CAAC;SACrB;AACD,QAAA,OAAO,EAAE,CAAC;KACX;AAED,IAAA,UAAU,CAAC,IAAS,EAAA;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC;KACxB;AAED,IAAA,WAAW,CAAC,IAAS,EAAA;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC;KACzB;AAED,IAAA,YAAY,CAAC,EAAO,EAAE,IAAY,EAAE,KAAa,EAAE,SAAkB,EAAA;QACnE,IAAI,SAAS,EAAE;AACb,YAAA,IAAI,GAAG,SAAS,GAAG,GAAG,GAAG,IAAI,CAAC;AAC9B,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,YAAY,EAAE;gBAChB,EAAE,CAAC,cAAc,CAAC,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC;aAC9C;iBAAM;AACL,gBAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;aAC9B;SACF;aAAM;AACL,YAAA,EAAE,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;SAC9B;KACF;AAED,IAAA,eAAe,CAAC,EAAO,EAAE,IAAY,EAAE,SAAkB,EAAA;QACvD,IAAI,SAAS,EAAE;AACb,YAAA,MAAM,YAAY,GAAG,cAAc,CAAC,SAAS,CAAC,CAAC;YAC/C,IAAI,YAAY,EAAE;AAChB,gBAAA,EAAE,CAAC,iBAAiB,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;aAC1C;iBAAM;gBACL,EAAE,CAAC,eAAe,CAAC,CAAA,EAAG,SAAS,CAAI,CAAA,EAAA,IAAI,CAAE,CAAA,CAAC,CAAC;aAC5C;SACF;aAAM;AACL,YAAA,EAAE,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;SAC1B;KACF;IAED,QAAQ,CAAC,EAAO,EAAE,IAAY,EAAA;AAC5B,QAAA,EAAE,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;KACxB;IAED,WAAW,CAAC,EAAO,EAAE,IAAY,EAAA;AAC/B,QAAA,EAAE,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;KAC3B;AAED,IAAA,QAAQ,CAAC,EAAO,EAAE,KAAa,EAAE,KAAU,EAAE,KAA0B,EAAA;AACrE,QAAA,IAAI,KAAK,IAAI,mBAAmB,CAAC,QAAQ,GAAG,mBAAmB,CAAC,SAAS,CAAC,EAAE;YAC1E,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,GAAG,mBAAmB,CAAC,SAAS,GAAG,WAAW,GAAG,EAAE,CAAC,CAAC;SAC9F;aAAM;AACL,YAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,KAAK,CAAC;SACzB;KACF;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,KAAa,EAAE,KAA0B,EAAA;AAC5D,QAAA,IAAI,KAAK,GAAG,mBAAmB,CAAC,QAAQ,EAAE;;AAExC,YAAA,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC;SAChC;aAAM;AACL,YAAA,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC;SACtB;KACF;AAED,IAAA,WAAW,CAAC,EAAO,EAAE,IAAY,EAAE,KAAU,EAAA;AAC3C,QAAA,IAAI,EAAE,IAAI,IAAI,EAAE;YACd,OAAO;SACR;QAED,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,IAAI,CAAC,qBAAqB;AACzE,YAAA,oBAAoB,CAAC,IAAI,EAAE,UAAU,CAAC,CAAC;AAC3C,QAAA,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;KAClB;IAED,QAAQ,CAAC,IAAS,EAAE,KAAa,EAAA;AAC/B,QAAA,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC;KACxB;AAED,IAAA,MAAM,CAAC,MAAsC,EAAE,KAAa,EAAE,QAAiC,EAAA;QAE7F,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,IAAI,CAAC,qBAAqB;AACzE,YAAA,oBAAoB,CAAC,KAAK,EAAE,UAAU,CAAC,CAAC;AAC5C,QAAA,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAC9B,YAAA,MAAM,GAAGC,OAAM,EAAE,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;YACzD,IAAI,CAAC,MAAM,EAAE;gBACX,MAAM,IAAI,KAAK,CAAC,CAAA,yBAAA,EAA4B,MAAM,CAAc,WAAA,EAAA,KAAK,CAAE,CAAA,CAAC,CAAC;aAC1E;SACF;AAED,QAAA,OAAO,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAC9B,MAAM,EAAE,KAAK,EAAE,IAAI,CAAC,sBAAsB,CAAC,QAAQ,CAAC,CAAiB,CAAC;KAClF;AAEO,IAAA,sBAAsB,CAAC,YAAsB,EAAA;;;;;QAKnD,OAAO,CAAC,KAAU,KAAI;;;;;;AAMpB,YAAA,IAAI,KAAK,KAAK,cAAc,EAAE;AAC5B,gBAAA,OAAO,YAAY,CAAC;aACrB;;;AAID,YAAA,MAAM,oBAAoB,GAAG,IAAI,CAAC,gBAAgB;AAC9C,gBAAA,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,CAAC;gBACjD,YAAY,CAAC,KAAK,CAAC,CAAC;AACxB,YAAA,IAAI,oBAAoB,KAAK,KAAK,EAAE;gBAClC,KAAK,CAAC,cAAc,EAAE,CAAC;aACxB;AAED,YAAA,OAAO,SAAS,CAAC;AACnB,SAAC,CAAC;KACH;AACF,CAAA;AAED,MAAM,WAAW,GAAG,CAAC,MAAM,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,GAAG,CAAC;AAChD,SAAS,oBAAoB,CAAC,IAAY,EAAE,QAAgB,EAAA;IAC1D,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;AACtC,QAAA,MAAM,IAAID,aAAY,CAAA,IAAA,uDAElB,CAAwB,qBAAA,EAAA,QAAQ,IAAI,IAAI,CAAA;;qEAGpC,IAAI,CAAA,8HAAA,CAAgI,CAAC,CAAC;KAC/I;AACH,CAAC;AAGD,SAAS,cAAc,CAAC,IAAS,EAAA;IAC/B,OAAO,IAAI,CAAC,OAAO,KAAK,UAAU,IAAI,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC;AACnE,CAAC;AAED,MAAM,iBAAkB,SAAQ,mBAAmB,CAAA;AAGjD,IAAA,WAAA,CACI,YAA0B,EAClB,gBAAkC,EAClC,MAAW,EACnB,SAAwB,EACxB,GAAa,EACb,MAAc,EACd,KAAkB,EAClB,gBAAyB,EAAA;QAE3B,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QARzC,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAClC,IAAM,CAAA,MAAA,GAAN,MAAM,CAAK;AAQrB,QAAA,IAAI,CAAC,UAAU,GAAI,MAAc,CAAC,YAAY,CAAC,EAAC,IAAI,EAAE,MAAM,EAAC,CAAC,CAAC;QAE/D,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;AAC/C,QAAA,MAAM,MAAM,GAAG,iBAAiB,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,CAAC,MAAM,CAAC,CAAC;AAEjE,QAAA,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;YAC1B,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;YAEhD,IAAI,KAAK,EAAE;AACT,gBAAA,OAAO,CAAC,YAAY,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;aACtC;AAED,YAAA,OAAO,CAAC,WAAW,GAAG,KAAK,CAAC;AAC5B,YAAA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;SACtC;KACF;AAEO,IAAA,gBAAgB,CAAC,IAAS,EAAA;AAChC,QAAA,OAAO,IAAI,KAAK,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC;KACtD;IAEQ,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;AAC7C,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;KACnE;AACQ,IAAA,YAAY,CAAC,MAAW,EAAE,QAAa,EAAE,QAAa,EAAA;AAC7D,QAAA,OAAO,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;KAC9E;IACQ,WAAW,CAAC,MAAW,EAAE,QAAa,EAAA;AAC7C,QAAA,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,EAAE,QAAQ,CAAC,CAAC;KACnE;AACQ,IAAA,UAAU,CAAC,IAAS,EAAA;AAC3B,QAAA,OAAO,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;KAC7E;IAEQ,OAAO,GAAA;QACd,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;KACnD;AACF,CAAA;AAED,MAAM,4BAA6B,SAAQ,mBAAmB,CAAA;AAG5D,IAAA,WAAA,CACI,YAA0B,EACT,gBAAkC,EACnD,SAAwB,EAChB,yBAAkC,EAC1C,GAAa,EACb,MAAc,EACd,gBAAyB,EACzB,MAAe,EAAA;QAEjB,KAAK,CAAC,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,gBAAgB,CAAC,CAAC;QARhC,IAAgB,CAAA,gBAAA,GAAhB,gBAAgB,CAAkB;QAE3C,IAAyB,CAAA,yBAAA,GAAzB,yBAAyB,CAAS;QAO5C,IAAI,CAAC,MAAM,GAAG,MAAM,GAAG,iBAAiB,CAAC,MAAM,EAAE,SAAS,CAAC,MAAM,CAAC,GAAG,SAAS,CAAC,MAAM,CAAC;KACvF;IAED,WAAW,GAAA;QACT,IAAI,CAAC,gBAAgB,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KAC9C;IAEQ,OAAO,GAAA;AACd,QAAA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACnC,OAAO;SACR;QAED,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;KACjD;AACF,CAAA;AAED,MAAM,iCAAkC,SAAQ,4BAA4B,CAAA;AAI1E,IAAA,WAAA,CACI,YAA0B,EAAE,gBAAkC,EAAE,SAAwB,EACxF,KAAa,EAAE,yBAAkC,EAAE,GAAa,EAAE,MAAc,EAChF,gBAAyB,EAAA;QAC3B,MAAM,MAAM,GAAG,KAAK,GAAG,GAAG,GAAG,SAAS,CAAC,EAAE,CAAC;AAC1C,QAAA,KAAK,CACD,YAAY,EAAE,gBAAgB,EAAE,SAAS,EAAE,yBAAyB,EAAE,GAAG,EAAE,MAAM,EACjF,gBAAgB,EAAE,MAAM,CAAC,CAAC;AAC9B,QAAA,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC,MAAM,CAAC,CAAC;AAChD,QAAA,IAAI,CAAC,QAAQ,GAAG,iBAAiB,CAAC,MAAM,CAAC,CAAC;KAC3C;AAED,IAAA,WAAW,CAAC,OAAY,EAAA;QACtB,IAAI,CAAC,WAAW,EAAE,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,CAAC,CAAC;KAC/C;IAEQ,aAAa,CAAC,MAAW,EAAE,IAAY,EAAA;QAC9C,MAAM,EAAE,GAAG,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QAC7C,KAAK,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC;AAC7C,QAAA,OAAO,EAAE,CAAC;KACX;AACF;;AClcK,MAAO,eAAgB,SAAQ,kBAAkB,CAAA;AACrD,IAAA,WAAA,CAA8B,GAAQ,EAAA;QACpC,KAAK,CAAC,GAAG,CAAC,CAAC;KACZ;;;AAIQ,IAAA,QAAQ,CAAC,SAAiB,EAAA;AACjC,QAAA,OAAO,IAAI,CAAC;KACb;AAEQ,IAAA,gBAAgB,CAAC,OAAoB,EAAE,SAAiB,EAAE,OAAiB,EAAA;QAClF,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,OAAwB,EAAE,KAAK,CAAC,CAAC;AACrE,QAAA,OAAO,MAAM,IAAI,CAAC,mBAAmB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAwB,CAAC,CAAC;KACrF;AAED,IAAA,mBAAmB,CAAC,MAAW,EAAE,SAAiB,EAAE,QAAkB,EAAA;QACpE,OAAO,MAAM,CAAC,mBAAmB,CAAC,SAAS,EAAE,QAAyB,CAAC,CAAC;KACzE;AAlBU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,kBACN,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HADjB,eAAe,EAAA,CAAA,CAAA,EAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;0BAEI,MAAM;2BAAC,QAAQ,CAAA;;;ACF9B;;AAEG;AACH,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;AAE1D;AACA;AACA,MAAM,OAAO,GAA0B;AACrC,IAAA,IAAI,EAAE,WAAW;AACjB,IAAA,IAAI,EAAE,KAAK;AACX,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,MAAM,EAAE,QAAQ;AAChB,IAAA,KAAK,EAAE,QAAQ;AACf,IAAA,KAAK,EAAE,QAAQ;AACf,IAAA,MAAM,EAAE,WAAW;AACnB,IAAA,OAAO,EAAE,YAAY;AACrB,IAAA,IAAI,EAAE,SAAS;AACf,IAAA,MAAM,EAAE,WAAW;AACnB,IAAA,MAAM,EAAE,aAAa;AACrB,IAAA,QAAQ,EAAE,YAAY;AACtB,IAAA,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF;;AAEG;AACH,MAAM,oBAAoB,GAAuD;IAC/E,KAAK,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,MAAM;IAC7C,SAAS,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,OAAO;IAClD,MAAM,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,OAAO;IAC/C,OAAO,EAAE,CAAC,KAAoB,KAAK,KAAK,CAAC,QAAQ;CAClD,CAAC;AAEF;;AAEG;AAEG,MAAO,eAAgB,SAAQ,kBAAkB,CAAA;AACrD;;;AAGG;AACH,IAAA,WAAA,CAA8B,GAAQ,EAAA;QACpC,KAAK,CAAC,GAAG,CAAC,CAAC;KACZ;AAED;;;;AAIG;AACM,IAAA,QAAQ,CAAC,SAAiB,EAAA;QACjC,OAAO,eAAe,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC;KAC1D;AAED;;;;;;;AAOG;AACM,IAAA,gBAAgB,CAAC,OAAoB,EAAE,SAAiB,EAAE,OAAiB,EAAA;QAClF,MAAM,WAAW,GAAG,eAAe,CAAC,cAAc,CAAC,SAAS,CAAE,CAAC;QAE/D,MAAM,cAAc,GAChB,eAAe,CAAC,aAAa,CAAC,WAAW,CAAC,SAAS,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;QAE3F,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,iBAAiB,CAAC,MAAK;AACnD,YAAA,OAAOC,OAAM,EAAE,CAAC,WAAW,CAAC,OAAO,EAAE,WAAW,CAAC,cAAc,CAAC,EAAE,cAAc,CAAC,CAAC;AACpF,SAAC,CAAC,CAAC;KACJ;AAED;;;;;;;;AAQG;IACH,OAAO,cAAc,CAAC,SAAiB,EAAA;QACrC,MAAM,KAAK,GAAa,SAAS,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;AAE3D,QAAA,MAAM,YAAY,GAAG,KAAK,CAAC,KAAK,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,CAAC,KAAK,EAAE,YAAY,KAAK,SAAS,IAAI,YAAY,KAAK,OAAO,CAAC,EAAE;AACrF,YAAA,OAAO,IAAI,CAAC;SACb;QAED,MAAM,GAAG,GAAG,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC,GAAG,EAAG,CAAC,CAAC;QAExD,IAAI,OAAO,GAAG,EAAE,CAAC;QACjB,IAAI,MAAM,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;AACnC,QAAA,IAAI,MAAM,GAAG,CAAC,CAAC,EAAE;AACf,YAAA,KAAK,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;YACxB,OAAO,GAAG,OAAO,CAAC;SACnB;AACD,QAAA,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;YACnC,MAAM,KAAK,GAAW,KAAK,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;AAClD,YAAA,IAAI,KAAK,GAAG,CAAC,CAAC,EAAE;AACd,gBAAA,KAAK,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;AACvB,gBAAA,OAAO,IAAI,YAAY,GAAG,GAAG,CAAC;aAC/B;AACH,SAAC,CAAC,CAAC;QACH,OAAO,IAAI,GAAG,CAAC;AAEf,QAAA,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEzC,YAAA,OAAO,IAAI,CAAC;SACb;;;;QAKD,MAAM,MAAM,GAA4C,EAAS,CAAC;AAClE,QAAA,MAAM,CAAC,cAAc,CAAC,GAAG,YAAY,CAAC;AACtC,QAAA,MAAM,CAAC,SAAS,CAAC,GAAG,OAAO,CAAC;AAC5B,QAAA,OAAO,MAAM,CAAC;KACf;AAED;;;;;;;;;AASG;AACH,IAAA,OAAO,qBAAqB,CAAC,KAAoB,EAAE,WAAmB,EAAA;AACpE,QAAA,IAAI,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC;QAC9C,IAAI,GAAG,GAAG,EAAE,CAAC;QACb,IAAI,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE;AACrC,YAAA,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;YACrB,GAAG,GAAG,OAAO,CAAC;SACf;;AAED,QAAA,IAAI,OAAO,IAAI,IAAI,IAAI,CAAC,OAAO;AAAE,YAAA,OAAO,KAAK,CAAC;AAC9C,QAAA,OAAO,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;AAChC,QAAA,IAAI,OAAO,KAAK,GAAG,EAAE;AACnB,YAAA,OAAO,GAAG,OAAO,CAAC;SACnB;AAAM,aAAA,IAAI,OAAO,KAAK,GAAG,EAAE;AAC1B,YAAA,OAAO,GAAG,KAAK,CAAC;SACjB;AACD,QAAA,aAAa,CAAC,OAAO,CAAC,YAAY,IAAG;AACnC,YAAA,IAAI,YAAY,KAAK,OAAO,EAAE;AAC5B,gBAAA,MAAM,cAAc,GAAG,oBAAoB,CAAC,YAAY,CAAC,CAAC;AAC1D,gBAAA,IAAI,cAAc,CAAC,KAAK,CAAC,EAAE;AACzB,oBAAA,GAAG,IAAI,YAAY,GAAG,GAAG,CAAC;iBAC3B;aACF;AACH,SAAC,CAAC,CAAC;QACH,GAAG,IAAI,OAAO,CAAC;QACf,OAAO,GAAG,KAAK,WAAW,CAAC;KAC5B;AAED;;;;;;AAMG;AACH,IAAA,OAAO,aAAa,CAAC,OAAe,EAAE,OAAiB,EAAE,IAAY,EAAA;QACnE,OAAO,CAAC,KAAoB,KAAI;YAC9B,IAAI,eAAe,CAAC,qBAAqB,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE;gBACzD,IAAI,CAAC,UAAU,CAAC,MAAM,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC;aACvC;AACH,SAAC,CAAC;KACH;;IAGD,OAAO,aAAa,CAAC,OAAe,EAAA;QAClC,OAAO,OAAO,KAAK,KAAK,GAAG,QAAQ,GAAG,OAAO,CAAC;KAC/C;AA5IU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,eAAe,kBAKN,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HALjB,eAAe,EAAA,CAAA,CAAA,EAAA;;sGAAf,eAAe,EAAA,UAAA,EAAA,CAAA;kBAD3B,UAAU;;0BAMI,MAAM;2BAAC,QAAQ,CAAA;;;ACrB9B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0DG;AACa,SAAA,oBAAoB,CAChC,aAA4B,EAAE,OAA2B,EAAA;AAC3D,IAAA,OAAOG,0BAAyB,CAAC,EAAC,aAAa,EAAE,GAAG,qBAAqB,CAAC,OAAO,CAAC,EAAC,CAAC,CAAC;AACvF,CAAC;AAED;;;;;;;;;;;AAWG;AACG,SAAU,iBAAiB,CAAC,OAA2B,EAAA;AAC3D,IAAA,OAAOA,0BAAyB,CAAC,qBAAqB,CAAC,OAAO,CAAC,CAAC,CAAC;AACnE,CAAC;AAED,SAAS,qBAAqB,CAAC,OAA2B,EAAA;IACxD,OAAO;AACL,QAAA,YAAY,EAAE;AACZ,YAAA,GAAG,wBAAwB;AAC3B,YAAA,IAAI,OAAO,EAAE,SAAS,IAAI,EAAE,CAAC;AAC9B,SAAA;AACD,QAAA,iBAAiB,EAAE,mCAAmC;KACvD,CAAC;AACJ,CAAC;AAED;;;;;;;;;;AAUG;SACa,+BAA+B,GAAA;;;;AAI7C,IAAA,OAAO,CAAC,GAAG,qBAAqB,CAAC,CAAC;AACpC,CAAC;SAEe,cAAc,GAAA;IAC5B,iBAAiB,CAAC,WAAW,EAAE,CAAC;AAClC,CAAC;SAEe,YAAY,GAAA;IAC1B,OAAO,IAAI,YAAY,EAAE,CAAC;AAC5B,CAAC;SAEe,SAAS,GAAA;;IAEvB,YAAY,CAAC,QAAQ,CAAC,CAAC;AACvB,IAAA,OAAO,QAAQ,CAAC;AAClB,CAAC;AAEY,MAAA,mCAAmC,GAAqB;AACnE,IAAA,EAAC,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAEC,oBAAmB,EAAC;IACrD,EAAC,OAAO,EAAE,oBAAoB,EAAE,QAAQ,EAAE,cAAc,EAAE,KAAK,EAAE,IAAI,EAAC;IACtE,EAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,EAAC;EACpD;AAEF;;;;;AAKG;AACI,MAAM,eAAe,GACxB,qBAAqB,CAAC,YAAY,EAAE,SAAS,EAAE,mCAAmC,EAAE;AAExF;;;;;AAKG;AACH,MAAM,+BAA+B,GAAG,IAAI,cAAc,CACtD,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,gCAAgC,GAAG,EAAE,CAAC,CAAC;AAE7F,MAAM,qBAAqB,GAAG;AAC5B,IAAA;AACE,QAAA,OAAO,EAAEC,mBAAkB;AAC3B,QAAA,QAAQ,EAAE,qBAAqB;AAC/B,QAAA,IAAI,EAAE,EAAE;AACT,KAAA;AACD,IAAA;AACE,QAAA,OAAO,EAAEC,YAAW;AACpB,QAAA,QAAQ,EAAE,WAAW;AACrB,QAAA,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAED,mBAAkB,CAAC;AACxD,KAAA;AACD,IAAA;QACE,OAAO,EAAE,WAAW;AACpB,QAAA,QAAQ,EAAE,WAAW;AACrB,QAAA,IAAI,EAAE,CAAC,MAAM,EAAE,mBAAmB,EAAEA,mBAAkB,CAAC;AACxD,KAAA;CACF,CAAC;AAEF,MAAM,wBAAwB,GAAe;AAC3C,IAAA,EAAC,OAAO,EAAEE,eAAc,EAAE,QAAQ,EAAE,MAAM,EAAC;AAC3C,IAAA,EAAC,OAAO,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,IAAI,EAAE,EAAE,EAAC,EAAE;AAC3D,QAAA,OAAO,EAAE,qBAAqB;AAC9B,QAAA,QAAQ,EAAE,eAAe;AACzB,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,MAAM,EAAE,WAAW,CAAC;AACtC,KAAA;AACD,IAAA,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,eAAe,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,QAAQ,CAAC,EAAC;IAC1F,mBAAmB,EAAE,gBAAgB,EAAE,YAAY;AACnD,IAAA,EAAC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,mBAAmB,EAAC;IAC7D,EAAC,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE,EAAC;IACrD,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;QAC1C,EAAC,OAAO,EAAE,+BAA+B,EAAE,QAAQ,EAAE,IAAI,EAAC;QAC1D,EAAE;CACP,CAAC;AAEF;;;;;;;;AAQG;MAKU,aAAa,CAAA;AACxB,IAAA,WAAA,CACY,uBAAqC,EAAA;QAC/C,IAAI,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,KAAK,uBAAuB,EAAE;YAC9E,MAAM,IAAIR,aAAY,CAAA,IAAA,uDAElB,CAAoF,kFAAA,CAAA;AAChF,gBAAA,CAAA,iFAAA,CAAmF,CAAC,CAAC;SAC9F;KACF;AAED;;;;;;;;;AASG;IACH,OAAO,oBAAoB,CAAC,MAAuB,EAAA;QACjD,OAAO;AACL,YAAA,QAAQ,EAAE,aAAa;AACvB,YAAA,SAAS,EAAE;gBACT,EAAC,OAAO,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,CAAC,KAAK,EAAC;AAC1C,aAAA;SACF,CAAC;KACH;AA5BU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,aAAa,kBACoB,+BAA+B,EAAA,QAAA,EAAA,IAAA,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HADhE,aAAa,EAAA,OAAA,EAAA,CAFd,YAAY,EAAE,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;0HAE9B,aAAa,EAAA,SAAA,EAHb,CAAC,GAAG,wBAAwB,EAAE,GAAG,qBAAqB,CAAC,EAAA,OAAA,EAAA,CACxD,YAAY,EAAE,iBAAiB,CAAA,EAAA,CAAA,CAAA,EAAA;;sGAE9B,aAAa,EAAA,UAAA,EAAA,CAAA;kBAJzB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE,CAAC,GAAG,wBAAwB,EAAE,GAAG,qBAAqB,CAAC;AAClE,oBAAA,OAAO,EAAE,CAAC,YAAY,EAAE,iBAAiB,CAAC;AAC3C,iBAAA,CAAA;;0BAEc,QAAQ;;0BAAI,QAAQ;;0BAAI,MAAM;2BAAC,+BAA+B,CAAA;;;AClM7E;;;;;;;;;;;;;;;;;;;;;AAqBG;MAEU,IAAI,CAAA;AAEf,IAAA,WAAA,CAAsC,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;AAC7C,QAAA,IAAI,CAAC,IAAI,GAAGC,OAAM,EAAE,CAAC;KACtB;AACD;;;;;;;;;AASG;AACH,IAAA,MAAM,CAAC,GAAmB,EAAE,aAAA,GAAyB,KAAK,EAAA;AACxD,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,IAAI,CAAC;QACtB,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC;KACrD;AAED;;;;;;;AAOG;AACH,IAAA,OAAO,CAAC,IAAsB,EAAE,aAAA,GAAyB,KAAK,EAAA;AAC5D,QAAA,IAAI,CAAC,IAAI;AAAE,YAAA,OAAO,EAAE,CAAC;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,CAAC,MAAyB,EAAE,GAAmB,KAAI;YACpE,IAAI,GAAG,EAAE;AACP,gBAAA,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,aAAa,CAAC,CAAC,CAAC;aAC3D;AACD,YAAA,OAAO,MAAM,CAAC;SACf,EAAE,EAAE,CAAC,CAAC;KACR;AAED;;;;;AAKG;AACH,IAAA,MAAM,CAAC,YAAoB,EAAA;AACzB,QAAA,IAAI,CAAC,YAAY;AAAE,YAAA,OAAO,IAAI,CAAC;AAC/B,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAQ,KAAA,EAAA,YAAY,CAAG,CAAA,CAAA,CAAC,IAAI,IAAI,CAAC;KACjE;AAED;;;;;AAKG;AACH,IAAA,OAAO,CAAC,YAAoB,EAAA;AAC1B,QAAA,IAAI,CAAC,YAAY;AAAE,YAAA,OAAO,EAAE,CAAC;AAC7B,QAAA,MAAM,IAAI,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAA,KAAA,EAAQ,YAAY,CAAA,CAAA,CAAG,CAAC,CAAC;AAC9E,QAAA,OAAO,IAAI,GAAG,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC;KACxC;AAED;;;;;;;;AAQG;IACH,SAAS,CAAC,GAAmB,EAAE,QAAiB,EAAA;AAC9C,QAAA,IAAI,CAAC,GAAG;AAAE,YAAA,OAAO,IAAI,CAAC;QACtB,QAAQ,GAAG,QAAQ,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAChD,MAAM,IAAI,GAAoB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAE,CAAC;QACrD,IAAI,IAAI,EAAE;YACR,OAAO,IAAI,CAAC,yBAAyB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;SAClD;QACD,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,IAAI,CAAC,CAAC;KAC5C;AAED;;;;AAIG;AACH,IAAA,SAAS,CAAC,YAAoB,EAAA;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAE,CAAC,CAAC;KACnD;AAED;;;AAGG;AACH,IAAA,gBAAgB,CAAC,IAAqB,EAAA;QACpC,IAAI,IAAI,EAAE;AACR,YAAA,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;SACxB;KACF;AAEO,IAAA,mBAAmB,CAAC,IAAoB,EAAE,aAAA,GAAyB,KAAK,EAAA;QAE9E,IAAI,CAAC,aAAa,EAAE;YAClB,MAAM,QAAQ,GAAW,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;;;;YAInD,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,IAAI,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC5F,IAAI,IAAI,KAAK,SAAS;AAAE,gBAAA,OAAO,IAAI,CAAC;SACrC;QACD,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAoB,CAAC;AACpF,QAAA,IAAI,CAAC,yBAAyB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,QAAA,MAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACvD,QAAA,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;AAC1B,QAAA,OAAO,OAAO,CAAC;KAChB;IAEO,yBAAyB,CAAC,GAAmB,EAAE,EAAmB,EAAA;AACxE,QAAA,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,CACpB,CAAC,IAAY,KAAK,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;AAC7E,QAAA,OAAO,EAAE,CAAC;KACX;AAEO,IAAA,cAAc,CAAC,GAAmB,EAAA;AACxC,QAAA,MAAM,IAAI,GAAW,GAAG,CAAC,IAAI,GAAG,MAAM,GAAG,UAAU,CAAC;QACpD,OAAO,CAAA,EAAG,IAAI,CAAK,EAAA,EAAA,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC;KACjC;IAEO,mBAAmB,CAAC,GAAmB,EAAE,IAAqB,EAAA;AACpE,QAAA,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,KAAK,CACzB,CAAC,GAAW,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;KAChF;AAEO,IAAA,cAAc,CAAC,IAAY,EAAA;AACjC,QAAA,OAAO,aAAa,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC;KACpC;AAtIU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAI,kBAEK,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AAFjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,IAAI,cADQ,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,IAAI,EAAA,UAAA,EAAA,CAAA;kBADhB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAGjB,MAAM;2BAAC,QAAQ,CAAA;;AAuI9B;;AAEG;AACH,MAAM,aAAa,GAA8B;AAC/C,IAAA,SAAS,EAAE,YAAY;CACxB;;AC7LD;;;;;;;;;AASG;MAEU,KAAK,CAAA;AAChB,IAAA,WAAA,CAAsC,IAAS,EAAA;QAAT,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;KAAI;AACnD;;AAEG;IACH,QAAQ,GAAA;AACN,QAAA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;KACxB;AAED;;;AAGG;AACH,IAAA,QAAQ,CAAC,QAAgB,EAAA;QACvB,IAAI,CAAC,IAAI,CAAC,KAAK,GAAG,QAAQ,IAAI,EAAE,CAAC;KAClC;AAfU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,kBACI,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AADjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,KAAK,cADO,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,KAAK,EAAA,UAAA,EAAA,CAAA;kBADjB,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAEjB,MAAM;2BAAC,QAAQ,CAAA;;;ACb9B;;;;;;AAMG;AACa,SAAA,WAAW,CAAC,IAAY,EAAE,KAAU,EAAA;IAClD,IAAI,OAAO,QAAQ,KAAK,WAAW,IAAI,CAAC,QAAQ,EAAE;;;;;AAKhD,QAAA,MAAM,EAAE,GAAGF,OAAM,CAAC,IAAI,CAAC,GAAIA,OAAM,CAAC,IAAI,CAAsC,IAAI,EAAE,CAAC;AACnF,QAAA,EAAE,CAAC,IAAI,CAAC,GAAG,KAAK,CAAC;KAClB;AACH;;MChBa,yBAAyB,CAAA;IACpC,WAAmB,CAAA,SAAiB,EAAS,QAAgB,EAAA;QAA1C,IAAS,CAAA,SAAA,GAAT,SAAS,CAAQ;QAAS,IAAQ,CAAA,QAAA,GAAR,QAAQ,CAAQ;KAAI;AAClE,CAAA;AAED;;;AAGG;MACU,eAAe,CAAA;AAG1B,IAAA,WAAA,CAAY,GAAsB,EAAA;QAChC,IAAI,CAAC,MAAM,GAAG,GAAG,CAAC,QAAQ,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;KAChD;;AAGD;;;;;;;;;;;;;;;AAeG;AACH,IAAA,mBAAmB,CAAC,MAAW,EAAA;QAC7B,MAAM,MAAM,GAAG,MAAM,IAAI,MAAM,CAAC,QAAQ,CAAC,CAAC;QAC1C,MAAM,WAAW,GAAG,kBAAkB,CAAC;;AAEvC,QAAA,IAAI,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,UAAU,EAAE;AAC3E,YAAA,OAAO,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;SAC9B;AACD,QAAA,MAAM,KAAK,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,QAAQ,GAAG,CAAC,CAAC;AACjB,QAAA,OAAO,QAAQ,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,KAAK,IAAI,GAAG,EAAE;AACxD,YAAA,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,CAAC;AACnB,YAAA,QAAQ,EAAE,CAAC;SACZ;AACD,QAAA,MAAM,GAAG,GAAG,WAAW,CAAC,GAAG,EAAE,CAAC;AAC9B,QAAA,IAAI,MAAM,IAAI,YAAY,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,UAAU,EAAE;AACjF,YAAA,OAAO,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC;SACjC;QACD,MAAM,SAAS,GAAG,CAAC,GAAG,GAAG,KAAK,IAAI,QAAQ,CAAC;AAC3C,QAAA,OAAO,CAAC,GAAG,CAAC,OAAO,QAAQ,CAAA,wBAAA,CAA0B,CAAC,CAAC;AACvD,QAAA,OAAO,CAAC,GAAG,CAAC,CAAA,EAAG,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAe,aAAA,CAAA,CAAC,CAAC;AAEpD,QAAA,OAAO,IAAI,yBAAyB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;KAC3D;AACF;;ACrDD,MAAM,oBAAoB,GAAG,UAAU,CAAC;AAExC;;;;;;;;;;;;AAYG;AACG,SAAU,gBAAgB,CAAI,GAAoB,EAAA;IACtD,WAAW,CAAC,oBAAoB,EAAE,IAAI,eAAe,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5D,IAAA,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;;;AAIG;SACa,iBAAiB,GAAA;AAC/B,IAAA,WAAW,CAAC,oBAAoB,EAAE,IAAI,CAAC,CAAC;AAC1C;;AC1BA;;;;AAIG;MACU,EAAE,CAAA;AACb;;;;;;;AAOG;AACH,IAAA,OAAO,GAAG,GAAA;AACR,QAAA,OAAO,MAAM,IAAI,CAAC;KACnB;AAED;;;;;;;AAOG;IACH,OAAO,GAAG,CAAC,QAAgB,EAAA;QACzB,OAAO,CAAC,YAAY,KAAI;AACtB,YAAA,OAAO,YAAY,CAAC,aAAa,IAAI,IAAI;gBACrC,cAAc,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ,CAAC;AACpD,gBAAA,KAAK,CAAC;AACZ,SAAC,CAAC;KACH;AAED;;;;;;;AAOG;IACH,OAAO,SAAS,CAAC,IAAe,EAAA;AAC9B,QAAA,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC,cAAe,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;KACtE;AACF,CAAA;AAED,SAAS,cAAc,CAAC,CAAM,EAAE,QAAgB,EAAA;IAC9C,IAAIE,OAAM,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE;QAC7B,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;YACnC,CAAC,CAAC,iBAAiB,IAAI,CAAC,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YACpD,CAAC,CAAC,qBAAqB,IAAI,CAAC,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;KAClE;AAED,IAAA,OAAO,KAAK,CAAC;AACf;;ACrDA;;AAEG;AACH,MAAM,WAAW,GAAG;;AAElB,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;;AAEf,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,UAAU,EAAE,IAAI;AAChB,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,UAAU,EAAE,IAAI;;AAEhB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,SAAS,EAAE,IAAI;;AAEf,IAAA,QAAQ,EAAE,IAAI;AACd,IAAA,aAAa,EAAE,IAAI;AACnB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,cAAc,EAAE,IAAI;;AAEpB,IAAA,OAAO,EAAE,IAAI;AACb,IAAA,WAAW,EAAE,IAAI;AACjB,IAAA,YAAY,EAAE,IAAI;AAClB,IAAA,SAAS,EAAE,IAAI;AACf,IAAA,WAAW,EAAE,IAAI;;AAEjB,IAAA,KAAK,EAAE,IAAI;AACX,IAAA,WAAW,EAAE,IAAI;CAClB,CAAC;AAEF;;;;;;AAMG;MACU,qBAAqB,GAAG,IAAI,cAAc,CAAsB,qBAAqB,EAAE;AAUpG;;;;AAIG;MACU,aAAa,GAAG,IAAI,cAAc,CAAe,cAAc,EAAE;AAQ9E;;;;AAIG;MAEU,mBAAmB,CAAA;AADhC,IAAA,WAAA,GAAA;AAEE;;;;AAIG;QACH,IAAM,CAAA,MAAA,GAAa,EAAE,CAAC;AAEtB;;;;;;;;;;;;;;;AAeG;QACH,IAAS,CAAA,SAAA,GAA4B,EAAE,CAAC;AAsCzC,KAAA;AAlBC;;;;;AAKG;AACH,IAAA,WAAW,CAAC,OAAoB,EAAA;QAC9B,MAAM,EAAE,GAAG,IAAI,MAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;AAE9C,QAAA,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;AACpC,QAAA,EAAE,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,GAAG,CAAC,EAAC,MAAM,EAAE,IAAI,EAAC,CAAC,CAAC;AAErC,QAAA,KAAK,MAAM,SAAS,IAAI,IAAI,CAAC,SAAS,EAAE;AACtC,YAAA,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC,CAAC;SAClD;AAED,QAAA,OAAO,EAAE,CAAC;KACX;yHA7DU,mBAAmB,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAnB,mBAAmB,EAAA,CAAA,CAAA,EAAA;;sGAAnB,mBAAmB,EAAA,UAAA,EAAA,CAAA;kBAD/B,UAAU;;AAiEX;;;;AAIG;AAEG,MAAO,oBAAqB,SAAQ,kBAAkB,CAAA;AAG1D,IAAA,WAAA,CACsB,GAAQ,EACa,OAA4B,EAAU,OAAgB,EAClD,MAA0B,EAAA;QACvE,KAAK,CAAC,GAAG,CAAC,CAAC;QAF8B,IAAO,CAAA,OAAA,GAAP,OAAO,CAAqB;QAAU,IAAO,CAAA,OAAA,GAAP,OAAO,CAAS;QAClD,IAAM,CAAA,MAAA,GAAN,MAAM,CAAoB;QALjE,IAAc,CAAA,cAAA,GAAuB,IAAI,CAAC;KAOjD;AAEQ,IAAA,QAAQ,CAAC,SAAiB,EAAA;AACjC,QAAA,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC,EAAE;AAC1F,YAAA,OAAO,KAAK,CAAC;SACd;QAED,IAAI,CAAE,MAAc,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AAC3C,YAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,gBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CACb,CAAA,KAAA,EAAQ,SAAS,CAAmD,iDAAA,CAAA;AACpE,oBAAA,CAAA,+CAAA,CAAiD,CAAC,CAAC;aACxD;AACD,YAAA,OAAO,KAAK,CAAC;SACd;AAED,QAAA,OAAO,IAAI,CAAC;KACb;AAEQ,IAAA,gBAAgB,CAAC,OAAoB,EAAE,SAAiB,EAAE,OAAiB,EAAA;QAClF,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC;AACpC,QAAA,SAAS,GAAG,SAAS,CAAC,WAAW,EAAE,CAAC;;;QAIpC,IAAI,CAAE,MAAc,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,EAAE;AAC1C,YAAA,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,IAAI,CAAC,MAAO,EAAE,CAAC,CAAC;;;;YAI1F,IAAI,kBAAkB,GAAG,KAAK,CAAC;YAC/B,IAAI,UAAU,GAAa,MAAK;gBAC9B,kBAAkB,GAAG,IAAI,CAAC;AAC5B,aAAC,CAAC;YAEF,IAAI,CAAC,iBAAiB,CAClB,MAAM,IAAI,CAAC,cAAe;iBACf,IAAI,CAAC,MAAK;;AAET,gBAAA,IAAI,CAAE,MAAc,CAAC,MAAM,EAAE;AAC3B,oBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,wBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CACb,CAAA,iEAAA,CAAmE,CAAC,CAAC;qBAC1E;AACD,oBAAA,UAAU,GAAG,MAAK,GAAG,CAAC;oBACtB,OAAO;iBACR;gBAED,IAAI,CAAC,kBAAkB,EAAE;;;;oBAIvB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;iBACjE;AACH,aAAC,CAAC;iBACD,KAAK,CAAC,MAAK;AACV,gBAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,EAAE;AACjD,oBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CACb,CAAA,KAAA,EAAQ,SAAS,CAA6C,2CAAA,CAAA;AAC9D,wBAAA,CAAA,wBAAA,CAA0B,CAAC,CAAC;iBACjC;AACD,gBAAA,UAAU,GAAG,MAAK,GAAG,CAAC;aACvB,CAAC,CAAC,CAAC;;;;AAKlB,YAAA,OAAO,MAAK;AACV,gBAAA,UAAU,EAAE,CAAC;AACf,aAAC,CAAC;SACH;AAED,QAAA,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAK;;YAEjC,MAAM,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAC7C,MAAM,QAAQ,GAAG,UAAS,QAAqB,EAAA;gBAC7C,IAAI,CAAC,UAAU,CAAC,YAAA;oBACd,OAAO,CAAC,QAAQ,CAAC,CAAC;AACpB,iBAAC,CAAC,CAAC;AACL,aAAC,CAAC;AACF,YAAA,EAAE,CAAC,EAAE,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;AAC3B,YAAA,OAAO,MAAK;AACV,gBAAA,EAAE,CAAC,GAAG,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;;AAE5B,gBAAA,IAAI,OAAO,EAAE,CAAC,OAAO,KAAK,UAAU,EAAE;oBACpC,EAAE,CAAC,OAAO,EAAE,CAAC;iBACd;AACH,aAAC,CAAC;AACJ,SAAC,CAAC,CAAC;KACJ;AAED,IAAA,aAAa,CAAC,SAAiB,EAAA;AAC7B,QAAA,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC;KACpD;AArGU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,oBAAoB,EAInB,IAAA,EAAA,CAAA,EAAA,KAAA,EAAA,QAAQ,EACR,EAAA,EAAA,KAAA,EAAA,qBAAqB,qCACT,aAAa,EAAA,QAAA,EAAA,IAAA,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAN1B,oBAAoB,EAAA,CAAA,CAAA,EAAA;;sGAApB,oBAAoB,EAAA,UAAA,EAAA,CAAA;kBADhC,UAAU;;0BAKJ,MAAM;2BAAC,QAAQ,CAAA;;0BACf,MAAM;2BAAC,qBAAqB,CAAA;;0BAC5B,QAAQ;;0BAAI,MAAM;2BAAC,aAAa,CAAA;;AAkGvC;;;;;;;;;;AAUG;MAYU,YAAY,CAAA;yHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,QAAA,EAAA,CAAA,CAAA,EAAA;0HAAZ,YAAY,EAAA,CAAA,CAAA,EAAA;AAAZ,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,mBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,YAAY,EAVZ,SAAA,EAAA;AACT,YAAA;AACE,gBAAA,OAAO,EAAE,qBAAqB;AAC9B,gBAAA,QAAQ,EAAE,oBAAoB;AAC9B,gBAAA,KAAK,EAAE,IAAI;AACX,gBAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,qBAAqB,EAAEQ,QAAO,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;AAClF,aAAA;YACD,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,EAAC;AAC1E,SAAA,EAAA,CAAA,CAAA,EAAA;;sGAEU,YAAY,EAAA,UAAA,EAAA,CAAA;kBAXxB,QAAQ;AAAC,YAAA,IAAA,EAAA,CAAA;AACR,oBAAA,SAAS,EAAE;AACT,wBAAA;AACE,4BAAA,OAAO,EAAE,qBAAqB;AAC9B,4BAAA,QAAQ,EAAE,oBAAoB;AAC9B,4BAAA,KAAK,EAAE,IAAI;AACX,4BAAA,IAAI,EAAE,CAAC,QAAQ,EAAE,qBAAqB,EAAEA,QAAO,EAAE,CAAC,IAAI,QAAQ,EAAE,EAAE,aAAa,CAAC,CAAC;AAClF,yBAAA;wBACD,EAAC,OAAO,EAAE,qBAAqB,EAAE,QAAQ,EAAE,mBAAmB,EAAE,IAAI,EAAE,EAAE,EAAC;AAC1E,qBAAA;AACF,iBAAA,CAAA;;;ACvOD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8BG;MAEmB,YAAY,CAAA;yHAAZ,YAAY,EAAA,IAAA,EAAA,EAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;6HAAZ,YAAY,EAAA,UAAA,EADT,MAAM,EAAA,WAAA,EAAA,EAAA,CAAA,UAAA,CAAA,MAAgC,gBAAgB,CAAA,EAAA,CAAA,CAAA,EAAA;;sGACzD,YAAY,EAAA,UAAA,EAAA,CAAA;kBADjC,UAAU;AAAC,YAAA,IAAA,EAAA,CAAA,EAAC,UAAU,EAAE,MAAM,EAAE,WAAW,EAAE,UAAU,CAAC,MAAM,gBAAgB,CAAC,EAAC,CAAA;;AA2D3E,MAAO,gBAAiB,SAAQ,YAAY,CAAA;AAChD,IAAA,WAAA,CAAsC,IAAS,EAAA;AAC7C,QAAA,KAAK,EAAE,CAAC;QAD4B,IAAI,CAAA,IAAA,GAAJ,IAAI,CAAK;KAE9C;IAEQ,QAAQ,CAAC,GAAoB,EAAE,KAA4B,EAAA;QAClE,IAAI,KAAK,IAAI,IAAI;AAAE,YAAA,OAAO,IAAI,CAAC;QAC/B,QAAQ,GAAG;YACT,KAAK,eAAe,CAAC,IAAI;AACvB,gBAAA,OAAO,KAAe,CAAC;YACzB,KAAK,eAAe,CAAC,IAAI;AACvB,gBAAA,IAAIC,gCAA8B,CAAC,KAAK,EAAA,MAAA,uBAAkB,EAAE;AAC1D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC,CAAC;iBAC/B;AACD,gBAAA,OAAOC,cAAa,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC5D,KAAK,eAAe,CAAC,KAAK;AACxB,gBAAA,IAAIF,gCAA8B,CAAC,KAAK,EAAA,OAAA,wBAAmB,EAAE;AAC3D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC,CAAC;iBAC/B;AACD,gBAAA,OAAO,KAAe,CAAC;YACzB,KAAK,eAAe,CAAC,MAAM;AACzB,gBAAA,IAAID,gCAA8B,CAAC,KAAK,EAAA,QAAA,yBAAoB,EAAE;AAC5D,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC,CAAC;iBAC/B;gBACD,MAAM,IAAIX,aAAY,CAAA,IAAA,oDAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC1C,oBAAA,uCAAuC,CAAC,CAAC;YACnD,KAAK,eAAe,CAAC,GAAG;AACtB,gBAAA,IAAIU,gCAA8B,CAAC,KAAK,EAAA,KAAA,sBAAiB,EAAE;AACzD,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC,CAAC;iBAC/B;AACD,gBAAA,OAAOE,aAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;YACrC,KAAK,eAAe,CAAC,YAAY;AAC/B,gBAAA,IAAIH,gCAA8B,CAAC,KAAK,EAAA,aAAA,8BAAyB,EAAE;AACjE,oBAAA,OAAOC,gBAAe,CAAC,KAAK,CAAC,CAAC;iBAC/B;gBACD,MAAM,IAAIX,aAAY,CAAA,IAAA,0DAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;oBAC1C,CAAoD,iDAAA,EAAAc,iBAAgB,CAAG,CAAA,CAAA,CAAC,CAAC;AACnF,YAAA;gBACE,MAAM,IAAId,aAAY,CAAA,IAAA,qDAElB,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AAC1C,oBAAA,CAAA,2BAAA,EAA8B,GAAG,CAAA,MAAA,EAASc,iBAAgB,CAAA,CAAA,CAAG,CAAC,CAAC;SAC1E;KACF;AAEQ,IAAA,uBAAuB,CAAC,KAAa,EAAA;AAC5C,QAAA,OAAOC,4BAA2B,CAAC,KAAK,CAAC,CAAC;KAC3C;AACQ,IAAA,wBAAwB,CAAC,KAAa,EAAA;AAC7C,QAAA,OAAOC,6BAA4B,CAAC,KAAK,CAAC,CAAC;KAC5C;AACQ,IAAA,yBAAyB,CAAC,KAAa,EAAA;AAC9C,QAAA,OAAOC,8BAA6B,CAAC,KAAK,CAAC,CAAC;KAC7C;AACQ,IAAA,sBAAsB,CAAC,KAAa,EAAA;AAC3C,QAAA,OAAOC,2BAA0B,CAAC,KAAK,CAAC,CAAC;KAC1C;AACQ,IAAA,8BAA8B,CAAC,KAAa,EAAA;AACnD,QAAA,OAAOC,mCAAkC,CAAC,KAAK,CAAC,CAAC;KAClD;AA/DU,IAAA,SAAA,IAAA,CAAA,IAAA,GAAA,EAAA,CAAA,kBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,kBACP,QAAQ,EAAA,CAAA,EAAA,MAAA,EAAA,EAAA,CAAA,eAAA,CAAA,UAAA,EAAA,CAAA,CAAA,EAAA;AADjB,IAAA,SAAA,IAAA,CAAA,KAAA,GAAA,EAAA,CAAA,qBAAA,CAAA,EAAA,UAAA,EAAA,QAAA,EAAA,OAAA,EAAA,mBAAA,EAAA,QAAA,EAAA,EAAA,EAAA,IAAA,EAAA,gBAAgB,cADJ,MAAM,EAAA,CAAA,CAAA,EAAA;;sGAClB,gBAAgB,EAAA,UAAA,EAAA,CAAA;kBAD5B,UAAU;mBAAC,EAAC,UAAU,EAAE,MAAM,EAAC,CAAA;;0BAEjB,MAAM;2BAAC,QAAQ,CAAA;;;ACvI9B;;;;;AAKG;IACS,qBAGX;AAHD,CAAA,UAAY,oBAAoB,EAAA;AAC9B,IAAA,oBAAA,CAAA,oBAAA,CAAA,qBAAA,CAAA,GAAA,CAAA,CAAA,GAAA,qBAAmB,CAAA;AACnB,IAAA,oBAAA,CAAA,oBAAA,CAAA,0BAAA,CAAA,GAAA,CAAA,CAAA,GAAA,0BAAwB,CAAA;AAC1B,CAAC,EAHW,oBAAoB,KAApB,oBAAoB,GAG/B,EAAA,CAAA,CAAA,CAAA;AAYD;;AAEG;AACH,SAAS,gBAAgB,CACrB,KAAkB,EAAE,aAAyB,EAAE,EAC/C,WAAoB,EAAE,EAAA;AACxB,IAAA,OAAO,EAAC,KAAK,EAAE,UAAU,EAAC,CAAC;AAC7B,CAAC;AAED;;;;;AAKG;SACa,uBAAuB,GAAA;;;AAIrC,IAAA,OAAO,gBAAgB,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,CAAC;AACpE,CAAC;AAED;;;;;;;AAOG;AACG,SAAU,4BAA4B,CACxC,OAAiC,EAAA;;IAGnC,OAAO,gBAAgB,CACnB,oBAAoB,CAAC,wBAAwB,EAAE,sBAAsB,CAAC,OAAO,CAAC,CAAC,CAAC;AACtF,CAAC;AAED;;;;AAIG;AACH,SAAS,kCAAkC,GAAA;AACzC,IAAA,OAAO,CAAC;AACN,YAAA,OAAO,EAAE,uBAAuB;YAChC,QAAQ,EAAE,MAAK;AACb,gBAAA,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC;;;AAG9B,gBAAA,IAAI,MAAM,CAAC,WAAW,KAAK,MAAM,EAAE;AACjC,oBAAA,MAAM,OAAO,GAAG,MAAM,CAACV,QAAO,CAAC,CAAC;AAChC,oBAAA,MAAM,OAAO,GAAGW,mBAAkB,CAAA,CAAA,IAAA,qDAE9B,iEAAiE;wBAC7D,uDAAuD;AACvD,wBAAA,kDAAkD,CAAC,CAAC;;AAE5D,oBAAA,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBACvB;aACF;AACD,YAAA,KAAK,EAAE,IAAI;AACZ,SAAA,CAAC,CAAC;AACL,CAAC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2CG;AACa,SAAA,sBAAsB,CAAC,GAAG,QAAkD,EAAA;IAE1F,MAAM,SAAS,GAAe,EAAE,CAAC;AACjC,IAAA,MAAM,YAAY,GAAG,IAAI,GAAG,EAAwB,CAAC;IACrD,MAAM,2BAA2B,GAC7B,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;IAEpE,KAAK,MAAM,EAAC,UAAU,EAAE,KAAK,EAAC,IAAI,QAAQ,EAAE;AAC1C,QAAA,YAAY,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC;AAExB,QAAA,IAAI,UAAU,CAAC,MAAM,EAAE;AACrB,YAAA,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;SAC5B;KACF;AAED,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;QAC7C,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,IAAI,2BAA2B,EAAE;;AAE7F,QAAA,MAAM,IAAI,KAAK,CACX,sKAAsK,CAAC,CAAC;KAC7K;AAED,IAAA,OAAO,wBAAwB,CAAC;AAC9B,QAAA,CAAC,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,kCAAkC,EAAE,GAAG,EAAE;AAC3F,QAAAC,iBAAgB,EAAE;AAClB,SAAC,CAAC,YAAY,CAAC,GAAG,CAAC,oBAAoB,CAAC,mBAAmB,CAAC,IAAI,2BAA2B;AACtF,YAAA,EAAE;YACF,sBAAsB,CAAC,EAAE,CAAC;QAC/B,SAAS;AACV,KAAA,CAAC,CAAC;AACL;;ACrKA;;;;AAIG;AAIH;;AAEG;MACU,OAAO,GAAG,IAAI,OAAO,CAAC,mBAAmB;;ACXtD;AAGA;;;;;;;;;;;;;;AAcG;AACH;AACO,MAAM,YAAY,GAAGC,eAAqB;AAuBjD;AACO,MAAM,aAAa,GAAoCC;;AC3C9D;;;;AAIG;AAEH;;ACNA;;ACRA;;AAEG;;;;"}