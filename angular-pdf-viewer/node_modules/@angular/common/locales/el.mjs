/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["el", [["πμ", "μμ"], ["π.μ.", "μ.μ."], u], u, [["Κ", "Δ", "Τ", "Τ", "Π", "Π", "Σ"], ["Κυρ", "Δευ", "Τρί", "Τετ", "Πέμ", "Παρ", "Σάβ"], ["Κυριακή", "Δευτέρα", "Τρίτη", "Τετάρτη", "Πέμπτη", "Παρασκευή", "Σάββατο"], ["Κυ", "Δε", "Τρ", "Τε", "Πέ", "Πα", "Σ<PERSON>"]], u, [["Ι", "<PERSON>", "Μ", "Α", "Μ", "Ι", "Ι", "Α", "Σ", "<PERSON>", "Ν", "Δ"], ["Ιαν", "Φεβ", "Μαρ", "Απρ", "Μαΐ", "Ιουν", "Ιουλ", "Αυγ", "Σεπ", "Οκτ", "Νοε", "Δεκ"], ["Ιανουαρίου", "Φεβρουαρίου", "Μαρτίου", "Απριλίου", "Μαΐου", "Ιουνίου", "Ιουλίου", "Αυγούστου", "Σεπτεμβρίου", "Οκτωβρίου", "Νοεμβρίου", "Δεκεμβρίου"]], [["Ι", "Φ", "Μ", "Α", "Μ", "Ι", "Ι", "Α", "Σ", "Ο", "Ν", "Δ"], ["Ιαν", "Φεβ", "Μάρ", "Απρ", "Μάι", "Ιούν", "Ιούλ", "Αύγ", "Σεπ", "Οκτ", "Νοέ", "Δεκ"], ["Ιανουάριος", "Φεβρουάριος", "Μάρτιος", "Απρίλιος", "Μάιος", "Ιούνιος", "Ιούλιος", "Αύγουστος", "Σεπτέμβριος", "Οκτώβριος", "Νοέμβριος", "Δεκέμβριος"]], [["π.Χ.", "μ.Χ."], u, ["προ Χριστού", "μετά Χριστόν"]], 1, [6, 0], ["d/M/yy", "d MMM y", "d MMMM y", "EEEE d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} - {0}", u], [",", ".", ";", "%", "+", "-", "e", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "EUR", "€", "Ευρώ", { "BYN": [u, "р."], "GRD": ["Δρχ"], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "THB": ["฿"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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