/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ce", [["AM", "PM"], u, u], u, [["кӀи", "ор", "ши", "кха", "еа", "пӀе", "шуо"], u, ["кӀира", "оршот", "шинара", "кхаара", "еара", "пӀераска", "шуот"], ["кӀи", "ор", "ши", "кха", "еа", "пӀе", "шуо"]], [["кӀ", "о", "ш", "кх", "е", "пӀ", "ш"], ["кӀи", "ор", "ши", "кха", "еа", "пӀе", "шуо"], ["кӀира", "оршот", "шинара", "кхаара", "еара", "пӀераска", "шуот"], ["кӀи", "ор", "ши", "кха", "еа", "пӀе", "шуо"]], [["Я", "Ф", "М", "А", "М", "И", "И", "А", "С", "О", "Н", "Д"], ["янв", "фев", "мар", "апр", "май", "июн", "июл", "авг", "сен", "окт", "ноя", "дек"], ["январь", "февраль", "март", "апрель", "май", "июнь", "июль", "август", "сентябрь", "октябрь", "ноябрь", "декабрь"]], u, [["в. э. тӀ. я", "в. э"], u, ["Ӏийса пайхамар вина де кхачале", "Ӏийса пайхамар вина дийнахь дуьйна"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "Терхьаш дац", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "RUB", "₽", "Российн сом", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "RON": [u, "лей"], "RUB": ["₽"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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