/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ta-MY", [["மு.ப", "பி.ப"], ["முற்பகல்", "பிற்பகல்"], u], u, [["ஞா", "தி", "செ", "பு", "வி", "வெ", "ச"], ["ஞாயி.", "திங்.", "செவ்.", "புத.", "வியா.", "வெள்.", "சனி"], ["ஞாயிறு", "திங்கள்", "செவ்வாய்", "புதன்", "வியாழன்", "வெள்ளி", "சனி"], ["ஞா", "தி", "செ", "பு", "வி", "வெ", "ச"]], u, [["ஜ", "பி", "மா", "ஏ", "மே", "ஜூ", "ஜூ", "ஆ", "செ", "அ", "ந", "டி"], ["ஜன.", "பிப்.", "மார்.", "ஏப்.", "மே", "ஜூன்", "ஜூலை", "ஆக.", "செப்.", "அக்.", "நவ.", "டிச."], ["ஜனவரி", "பிப்ரவரி", "மார்ச்", "ஏப்ரல்", "மே", "ஜூன்", "ஜூலை", "ஆகஸ்ட்", "செப்டம்பர்", "அக்டோபர்", "நவம்பர்", "டிசம்பர்"]], u, [["கி.மு.", "கி.பி."], u, ["கிறிஸ்துவுக்கு முன்", "அன்னோ டோமினி"]], 1, [6, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE, d MMMM, y"], ["a h:mm", "a h:mm:ss", "a h:mm:ss z", "a h:mm:ss zzzz"], ["{1}, {0}", u, "{1} அன்று {0}", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "MYR", "RM", "மலேஷியன் ரிங்கிட்", { "BYN": [u, "р."], "MYR": ["RM"], "PHP": [u, "₱"], "SGD": ["S$", "$"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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