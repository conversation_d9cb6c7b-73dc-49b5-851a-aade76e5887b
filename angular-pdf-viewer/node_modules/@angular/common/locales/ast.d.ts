/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    DKK: never[];
    HRK: never[];
    ISK: never[];
    NOK: never[];
    PHP: (string | undefined)[];
    PLN: never[];
    SEK: never[];
    THB: string[];
    TWD: string[];
    XXX: never[];
} | undefined)[];
export default _default;
