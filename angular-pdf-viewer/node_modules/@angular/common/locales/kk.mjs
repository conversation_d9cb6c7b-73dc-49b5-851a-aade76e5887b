/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["kk", [["AM", "PM"], u, u], u, [["Ж", "Д", "С", "С", "Б", "Ж", "С"], ["жс", "дс", "сс", "ср", "бс", "жм", "сб"], ["жексенбі", "дүйсенбі", "сейсенбі", "сәрсенбі", "бейсенбі", "жұма", "сенбі"], ["жс", "дс", "сс", "ср", "бс", "жм", "сб"]], u, [["Қ", "А", "Н", "С", "М", "М", "Ш", "Т", "Қ", "Қ", "Қ", "Ж"], ["қаң.", "ақп.", "нау.", "сәу.", "мам.", "мау.", "шіл.", "там.", "қыр.", "қаз.", "қар.", "жел."], ["қаңтар", "ақпан", "наурыз", "сәуір", "мамыр", "маусым", "шілде", "тамыз", "қыркүйек", "қазан", "қараша", "желтоқсан"]], [["Қ", "А", "Н", "С", "М", "М", "Ш", "Т", "Қ", "Қ", "Қ", "Ж"], ["қаң.", "ақп.", "нау.", "сәу.", "мам.", "мау.", "шіл.", "там.", "қыр.", "қаз.", "қар.", "жел."], ["Қаңтар", "Ақпан", "Наурыз", "Сәуір", "Мамыр", "Маусым", "Шілде", "Тамыз", "Қыркүйек", "Қазан", "Қараша", "Желтоқсан"]], [["б.з.д.", "б.з."], u, ["Біздің заманымызға дейін", "біздің заманымыз"]], 1, [6, 0], ["dd.MM.yy", "y 'ж'. dd MMM", "y 'ж'. d MMMM", "y 'ж'. d MMMM, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "сан емес", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "KZT", "₸", "Қазақстан теңгесі", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "KZT": ["₸"], "LSL": ["ЛСЛ"], "PHP": [u, "₱"], "RUB": ["₽"], "THB": ["฿"], "TWD": ["NT$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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