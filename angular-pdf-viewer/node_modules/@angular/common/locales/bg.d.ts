/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
declare function plural(val: number): number;
declare const _default: (string | number | number[] | (string | undefined)[] | typeof plural | (string[] | undefined)[] | {
    AFN: (string | undefined)[];
    AMD: never[];
    ARS: never[];
    AUD: never[];
    AZN: never[];
    BBD: never[];
    BDT: never[];
    BGN: string[];
    BMD: never[];
    BND: never[];
    BRL: never[];
    BSD: never[];
    BZD: never[];
    CAD: never[];
    CLP: never[];
    CNY: never[];
    COP: never[];
    CRC: never[];
    CUP: never[];
    DOP: never[];
    FJD: never[];
    FKP: never[];
    GBP: (string | undefined)[];
    GHS: never[];
    GIP: never[];
    GYD: never[];
    HKD: never[];
    ILS: never[];
    INR: never[];
    JMD: never[];
    JPY: (string | undefined)[];
    KHR: never[];
    KRW: never[];
    KYD: never[];
    KZT: never[];
    LAK: never[];
    LRD: never[];
    MNT: never[];
    MXN: never[];
    NAD: never[];
    NGN: never[];
    NZD: never[];
    PHP: never[];
    PYG: never[];
    RON: never[];
    SBD: never[];
    SGD: never[];
    SRD: never[];
    SSP: never[];
    TRY: never[];
    TTD: never[];
    TWD: never[];
    UAH: never[];
    USD: string[];
    UYU: never[];
    VND: never[];
    XCD: (string | undefined)[];
} | undefined)[];
export default _default;
