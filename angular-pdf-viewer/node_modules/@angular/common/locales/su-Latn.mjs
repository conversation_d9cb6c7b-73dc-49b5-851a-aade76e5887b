/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    return 5;
}
export default ["su-Latn", [["AM", "PM"], u, u], u, [["M", "S", "S", "R", "K", "J", "S"], ["Mng", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sap"], ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Saptu"], ["Mng", "<PERSON>", "<PERSON>", "Reb", "Ke<PERSON>", "Ju<PERSON>", "Sap"]], u, [["J", "P", "M", "A", "M", "J", "<PERSON>", "A", "<PERSON>", "O", "<PERSON>", "<PERSON>"], ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>é<PERSON>", "<PERSON>", "<PERSON>", "Ags", "<PERSON>é<PERSON>", "Okt", "<PERSON>p", "<PERSON><PERSON>"], ["<PERSON>uari", "<PERSON><PERSON>bruari", "<PERSON>t", "April", "<PERSON>éi", "Juni", "Juli", "Agustus", "Séptémber", "Oktober", "<PERSON>pémber", "D<PERSON>émber"]], u, [["<PERSON>", "M"], u, u], 0, [6, 0], ["d/M/yy", "d M<PERSON> y", "d M<PERSON>M y", "EEEE, d MMMM y"], ["H.mm", "H.mm.ss", "H.mm.ss z", "H.mm.ss zzzz"], ["{1}, {0}", u, "{1} 'jam' {0}", u], [",", ".", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", "."], ["#,##0.###", "#,##0%", "¤#,##0.00", "#E0"], "IDR", "Rp", "Rupee Indonésia", { "IDR": ["Rp"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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