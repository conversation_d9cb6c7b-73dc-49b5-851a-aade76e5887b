/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// **Note**: Locale files are generated through Bazel and never part of the sources. This is an
// exception for backwards compatibility. With the Gulp setup we never deleted old locale files
// when updating CLDR, so older locale files which have been removed, or renamed in the CLDR
// data remained in the repository. We keep these files checked-in until the next major to avoid
// potential breaking changes. It's worth noting that the locale data for such files is outdated
// anyway. e.g. the data is missing the directionality, throwing off the indices.
const u = undefined;
export default [];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZmYtTVIuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9leHRyYS9mZi1NUi50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwrRkFBK0Y7QUFDL0YsK0ZBQStGO0FBQy9GLDRGQUE0RjtBQUM1RixnR0FBZ0c7QUFDaEcsZ0dBQWdHO0FBQ2hHLGlGQUFpRjtBQUVqRixNQUFNLENBQUMsR0FBRyxTQUFTLENBQUM7QUFFcEIsZUFBZSxFQUFFLENBQUMiLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlXG4gKiBDb3B5cmlnaHQgR29vZ2xlIExMQyBBbGwgUmlnaHRzIFJlc2VydmVkLlxuICpcbiAqIFVzZSBvZiB0aGlzIHNvdXJjZSBjb2RlIGlzIGdvdmVybmVkIGJ5IGFuIE1JVC1zdHlsZSBsaWNlbnNlIHRoYXQgY2FuIGJlXG4gKiBmb3VuZCBpbiB0aGUgTElDRU5TRSBmaWxlIGF0IGh0dHBzOi8vYW5ndWxhci5pby9saWNlbnNlXG4gKi9cblxuLy8gKipOb3RlKio6IExvY2FsZSBmaWxlcyBhcmUgZ2VuZXJhdGVkIHRocm91Z2ggQmF6ZWwgYW5kIG5ldmVyIHBhcnQgb2YgdGhlIHNvdXJjZXMuIFRoaXMgaXMgYW5cbi8vIGV4Y2VwdGlvbiBmb3IgYmFja3dhcmRzIGNvbXBhdGliaWxpdHkuIFdpdGggdGhlIEd1bHAgc2V0dXAgd2UgbmV2ZXIgZGVsZXRlZCBvbGQgbG9jYWxlIGZpbGVzXG4vLyB3aGVuIHVwZGF0aW5nIENMRFIsIHNvIG9sZGVyIGxvY2FsZSBmaWxlcyB3aGljaCBoYXZlIGJlZW4gcmVtb3ZlZCwgb3IgcmVuYW1lZCBpbiB0aGUgQ0xEUlxuLy8gZGF0YSByZW1haW5lZCBpbiB0aGUgcmVwb3NpdG9yeS4gV2Uga2VlcCB0aGVzZSBmaWxlcyBjaGVja2VkLWluIHVudGlsIHRoZSBuZXh0IG1ham9yIHRvIGF2b2lkXG4vLyBwb3RlbnRpYWwgYnJlYWtpbmcgY2hhbmdlcy4gSXQncyB3b3J0aCBub3RpbmcgdGhhdCB0aGUgbG9jYWxlIGRhdGEgZm9yIHN1Y2ggZmlsZXMgaXMgb3V0ZGF0ZWRcbi8vIGFueXdheS4gZS5nLiB0aGUgZGF0YSBpcyBtaXNzaW5nIHRoZSBkaXJlY3Rpb25hbGl0eSwgdGhyb3dpbmcgb2ZmIHRoZSBpbmRpY2VzLlxuXG5jb25zdCB1ID0gdW5kZWZpbmVkO1xuXG5leHBvcnQgZGVmYXVsdCBbXTtcbiJdfQ==