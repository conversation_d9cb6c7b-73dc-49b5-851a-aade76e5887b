/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ckb", [["ب.ن", "د.ن"], u, u], u, [["ی", "د", "س", "چ", "پ", "ھ", "ش"], ["یەکشەممە", "دووشەممە", "سێشەممە", "چوارشەممە", "پێنجشەممە", "ھەینی", "شەممە"], u, ["١ش", "٢ش", "٣ش", "٤ش", "٥ش", "ھ", "ش"]], u, [["ک", "ش", "ئ", "ن", "ئ", "ح", "ت", "ئ", "ئ", "ت", "ت", "ک"], ["کانوونی دووەم", "شوبات", "ئازار", "نیسان", "ئایار", "حوزەیران", "تەمووز", "ئاب", "ئەیلوول", "تشرینی یەکەم", "تشرینی دووەم", "کانونی یەکەم"], u], u, [["پێش زایین", "زایینی"], u, u], 6, [5, 6], ["y-MM-dd", "y MMM d", "dی MMMMی y", "y MMMM d, EEEE"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1} {0}", u, u, u], [".", ",", ";", "%", "‎+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "IQD", "د.ع.‏", "IQD", { "IQD": ["د.ع.‏"], "JPY": ["JP¥", "¥"], "USD": ["US$", "$"] }, "rtl", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY2tiLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29tbW9uL2xvY2FsZXMvY2tiLnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBOzs7Ozs7R0FNRztBQUVILDBDQUEwQztBQUMxQyxNQUFNLENBQUMsR0FBRyxTQUFTLENBQUM7QUFFcEIsU0FBUyxNQUFNLENBQUMsR0FBVztJQUMzQixNQUFNLENBQUMsR0FBRyxHQUFHLENBQUM7SUFFZCxJQUFJLENBQUMsS0FBSyxDQUFDO1FBQ1AsT0FBTyxDQUFDLENBQUM7SUFDYixPQUFPLENBQUMsQ0FBQztBQUNULENBQUM7QUFFRCxlQUFlLENBQUMsS0FBSyxFQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxDQUFDLEVBQUMsQ0FBQyxVQUFVLEVBQUMsVUFBVSxFQUFDLFNBQVMsRUFBQyxXQUFXLEVBQUMsV0FBVyxFQUFDLE9BQU8sRUFBQyxPQUFPLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLGVBQWUsRUFBQyxPQUFPLEVBQUMsT0FBTyxFQUFDLE9BQU8sRUFBQyxPQUFPLEVBQUMsVUFBVSxFQUFDLFFBQVEsRUFBQyxLQUFLLEVBQUMsU0FBUyxFQUFDLGNBQWMsRUFBQyxjQUFjLEVBQUMsY0FBYyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxXQUFXLEVBQUMsUUFBUSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFNBQVMsRUFBQyxTQUFTLEVBQUMsWUFBWSxFQUFDLGdCQUFnQixDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsV0FBVyxFQUFDLGFBQWEsRUFBQyxnQkFBZ0IsQ0FBQyxFQUFDLENBQUMsU0FBUyxFQUFDLENBQUMsRUFBQyxDQUFDLEVBQUMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsSUFBSSxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsV0FBVyxFQUFDLFFBQVEsRUFBQyxZQUFZLEVBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLE9BQU8sRUFBQyxLQUFLLEVBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxPQUFPLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLEVBQUMsR0FBRyxDQUFDLEVBQUMsS0FBSyxFQUFDLENBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLEVBQUMsS0FBSyxFQUFFLE1BQU0sQ0FBQyxDQUFDIiwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAbGljZW5zZVxuICogQ29weXJpZ2h0IEdvb2dsZSBMTEMgQWxsIFJpZ2h0cyBSZXNlcnZlZC5cbiAqXG4gKiBVc2Ugb2YgdGhpcyBzb3VyY2UgY29kZSBpcyBnb3Zlcm5lZCBieSBhbiBNSVQtc3R5bGUgbGljZW5zZSB0aGF0IGNhbiBiZVxuICogZm91bmQgaW4gdGhlIExJQ0VOU0UgZmlsZSBhdCBodHRwczovL2FuZ3VsYXIuaW8vbGljZW5zZVxuICovXG5cbi8vIFRISVMgQ09ERSBJUyBHRU5FUkFURUQgLSBETyBOT1QgTU9ESUZZLlxuY29uc3QgdSA9IHVuZGVmaW5lZDtcblxuZnVuY3Rpb24gcGx1cmFsKHZhbDogbnVtYmVyKTogbnVtYmVyIHtcbmNvbnN0IG4gPSB2YWw7XG5cbmlmIChuID09PSAxKVxuICAgIHJldHVybiAxO1xucmV0dXJuIDU7XG59XG5cbmV4cG9ydCBkZWZhdWx0IFtcImNrYlwiLFtbXCLYqC7ZhlwiLFwi2K8u2YZcIl0sdSx1XSx1LFtbXCLbjFwiLFwi2K9cIixcItizXCIsXCLahlwiLFwi2b5cIixcItq+XCIsXCLYtFwiXSxbXCLbjNuV2qnYtNuV2YXZhduVXCIsXCLYr9mI2YjYtNuV2YXZhduVXCIsXCLYs9uO2LTbldmF2YXblVwiLFwi2obZiNin2LHYtNuV2YXZhduVXCIsXCLZvtuO2YbYrNi025XZhdmF25VcIixcItq+25XbjNmG24xcIixcIti025XZhdmF25VcIl0sdSxbXCLZodi0XCIsXCLZoti0XCIsXCLZo9i0XCIsXCLZpNi0XCIsXCLZpdi0XCIsXCLavlwiLFwi2LRcIl1dLHUsW1tcItqpXCIsXCLYtFwiLFwi2KZcIixcItmGXCIsXCLYplwiLFwi2K1cIixcItiqXCIsXCLYplwiLFwi2KZcIixcItiqXCIsXCLYqlwiLFwi2qlcIl0sW1wi2qnYp9mG2YjZiNmG24wg2K/ZiNmI25XZhVwiLFwi2LTZiNio2KfYqlwiLFwi2KbYp9iy2KfYsVwiLFwi2YbbjNiz2KfZhlwiLFwi2KbYp9uM2KfYsVwiLFwi2K3ZiNiy25XbjNix2KfZhlwiLFwi2KrbldmF2YjZiNiyXCIsXCLYptin2KhcIixcItim25XbjNmE2YjZiNmEXCIsXCLYqti02LHbjNmG24wg24zbldqp25XZhVwiLFwi2KrYtNix24zZhtuMINiv2YjZiNuV2YVcIixcItqp2KfZhtmI2YbbjCDbjNuV2qnbldmFXCJdLHVdLHUsW1tcItm+247YtCDYstin24zbjNmGXCIsXCLYstin24zbjNmG24xcIl0sdSx1XSw2LFs1LDZdLFtcInktTU0tZGRcIixcInkgTU1NIGRcIixcImTbjCBNTU1N24wgeVwiLFwieSBNTU1NIGQsIEVFRUVcIl0sW1wiaDptbSBhXCIsXCJoOm1tOnNzIGFcIixcImg6bW06c3MgYSB6XCIsXCJoOm1tOnNzIGEgenp6elwiXSxbXCJ7MX0gezB9XCIsdSx1LHVdLFtcIi5cIixcIixcIixcIjtcIixcIiVcIixcIuKAjitcIixcIi1cIixcIkVcIixcIsOXXCIsXCLigLBcIixcIuKInlwiLFwiTmFOXCIsXCI6XCJdLFtcIiMsIyMwLiMjI1wiLFwiIywjIzAlXCIsXCLCpMKgIywjIzAuMDBcIixcIiNFMFwiXSxcIklRRFwiLFwi2K8u2Lku4oCPXCIsXCJJUURcIix7XCJJUURcIjpbXCLYry7YuS7igI9cIl0sXCJKUFlcIjpbXCJKUMKlXCIsXCLCpVwiXSxcIlVTRFwiOltcIlVTJFwiLFwiJFwiXX0sXCJydGxcIiwgcGx1cmFsXTtcbiJdfQ==