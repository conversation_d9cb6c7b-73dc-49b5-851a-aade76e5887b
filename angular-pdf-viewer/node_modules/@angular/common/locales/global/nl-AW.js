/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val, i = Math.floor(Math.abs(val)), v = val.toString().replace(/^[^.]*\.?/, '').length;

if (i === 1 && v === 0)
    return 1;
return 5;
}
    global.ng.common.locales['nl-aw'] = ["nl-AW",[["a.m.","p.m."],u,u],u,[["Z","M","D","W","D","V","Z"],["zo","ma","di","wo","do","vr","za"],["zondag","maandag","dinsdag","woensdag","donderdag","vrijdag","zaterdag"],["zo","ma","di","wo","do","vr","za"]],u,[["J","F","M","A","M","J","J","A","S","O","N","D"],["jan.","feb.","mrt.","apr.","mei","jun.","jul.","aug.","sep.","okt.","nov.","dec."],["januari","februari","maart","april","mei","juni","juli","augustus","september","oktober","november","december"]],u,[["v.C.","n.C."],["v.Chr.","n.Chr."],["voor Christus","na Christus"]],1,[6,0],["dd-MM-y","d MMM y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,"{1} 'om' {0}",u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤ #,##0.00;¤ -#,##0.00","#E0"],"AWG","Afl.","Arubaanse gulden",{"AUD":["AU$","$"],"AWG":["Afl."],"BYN":[u,"р."],"CAD":["C$","$"],"FJD":["FJ$","$"],"JPY":["JP¥","¥"],"PHP":[u,"₱"],"RUR":[u,"р."],"SBD":["SI$","$"],"THB":["฿"],"TWD":["NT$"],"USD":["US$","$"],"XPF":[],"XXX":[]},"ltr", plural, [[["middernacht","’s ochtends","’s middags","’s avonds","’s nachts"],u,u],[["middernacht","ochtend","middag","avond","nacht"],u,u],["00:00",["06:00","12:00"],["12:00","18:00"],["18:00","24:00"],["00:00","06:00"]]]];
  })(globalThis);
    