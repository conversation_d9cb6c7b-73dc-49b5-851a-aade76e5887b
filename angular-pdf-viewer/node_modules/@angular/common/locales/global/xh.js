/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['xh'] = ["xh",[["AM","PM"],u,u],u,[["S","M","T","W","T","F","S"],["<PERSON><PERSON>","Mvu","<PERSON>","<PERSON>ha","<PERSON>","<PERSON>la","Mgq"],["<PERSON>aw<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","Mgq"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>pr","<PERSON>y","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>","<PERSON>t","<PERSON>","<PERSON>s"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON>p<PERSON><PERSON>","<PERSON>yi","<PERSON>i","<PERSON><PERSON>i","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Okthoba","Novemba","Disemba"]],u,[["BC","AD"],u,u],0,[6,0],["y-MM-dd","y MMM d","y MMMM d","y MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],["."," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"ZAR","R","iRandi yaseMzanzi Afrika",{"JPY":["JP¥","¥"],"USD":["US$","$"],"ZAR":["R"]},"ltr", plural, []];
  })(globalThis);
    