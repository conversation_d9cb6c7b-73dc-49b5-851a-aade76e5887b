/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

return 5;
}
    global.ng.common.locales['shi-latn'] = ["shi-Latn",[["tifawt","tadggʷat"],u,u],u,[["S","M","T","W","T","F","S"],["asa","ayn","asi","akṛ","akw","asim","asiḍ"],["asamas","aynas","asinas","akṛas","akwas","asimwas","asiḍyas"],["asa","ayn","asi","akṛ","akw","asim","asiḍ"]],u,[["i","b","m","i","m","y","y","ɣ","c","k","n","d"],["inn","bṛa","maṛ","ibr","may","yun","yul","ɣuc","cut","ktu","nuw","duj"],["innayr","bṛayṛ","maṛṣ","ibrir","mayyu","yunyu","yulyuz","ɣuct","cutanbir","ktubr","nuwanbir","dujanbir"]],u,[["daɛ","dfɛ"],u,["dat n ɛisa","dffir n ɛisa"]],1,[6,0],["d/M/y","d MMM, y","d MMMM y","EEEE d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00¤","#E0"],"MAD","MAD","adrim n lmɣrib",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    