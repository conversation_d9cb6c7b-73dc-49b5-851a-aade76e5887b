/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['xog'] = ["xog",[["<PERSON><PERSON><PERSON>","Eigulo"],u,u],u,[["S","B","B","S","K","K","M"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON>wokus<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"]],u,[["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON><PERSON>","<PERSON>a","<PERSON>u","<PERSON>","<PERSON><PERSON>","<PERSON>b","<PERSON>i","<PERSON>","<PERSON>"],["<PERSON><PERSON>iyo","<PERSON>waliyo","<PERSON>i","<PERSON>pu<PERSON>","<PERSON><PERSON>i","<PERSON><PERSON>","<PERSON>aayi","Agusito","Sebuttemba","Okitobba","Novemba","Desemba"]],u,[["AZ","AF"],u,["Kulisto nga azilawo","Kulisto nga affile"]],1,[0,0],["dd/MM/y","d MMM y","d MMMM y","EEEE, d MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00 ¤","#E0"],"UGX","USh","Silingi eya Yuganda",{"JPY":["JP¥","¥"],"UGX":["USh"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    