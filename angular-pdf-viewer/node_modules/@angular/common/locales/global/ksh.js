/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 0)
    return 0;
if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['ksh'] = ["ksh",[["v.M.","n.M."],u,["<PERSON>r vörmiddaachs","Uhr nommendaachs"]],[["v.M.","n.M."],u,["Vörmeddaach","Nommendaach"]],[["<PERSON>","<PERSON>","D","M","D","F","S"],["<PERSON>.","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>"],["<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>","<PERSON>ii<PERSON><PERSON>","<PERSON><PERSON><PERSON><PERSON>"],["<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"]],u,[["<PERSON>","<PERSON>","<PERSON>","A","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>"],["<PERSON>","<PERSON><PERSON>b","<PERSON><PERSON>z","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>uj","<PERSON>äp","Okt","Nov","Dez"],["Jannewa","Fäbrowa","Määz","Aprell","Mai","Juuni","Juuli","Oujoß","Septämber","Oktohber","Novämber","Dezämber"]],[["J","F","M","A","M","J","J","O","S","O","N","D"],["Jan.","Fäb.","Mäz.","Apr.","Mai","Jun.","Jul.","Ouj.","Säp.","Okt.","Nov.","Dez."],["Jannewa","Fäbrowa","Määz","Aprell","Mai","Juuni","Juuli","Oujoß","Septämber","Oktohber","Novämber","Dezämber"]],[["vC","nC"],["v. Chr.","n. Chr."],["vür Krestos","noh Krestos"]],1,[6,0],["d. M. y","d. MMM. y","d. MMMM y","EEEE, 'dä' d. MMMM y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[","," ",";","%","+","−","×10^","×","‰","∞","NaN",":"],["#,##0.###","#,##0 %","#,##0.00 ¤","#E0"],"EUR","€","Euro",{"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    