/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['ku'] = ["ku",[["BN","PN"],u,u],u,[["Y","D","S","Ç","P","Î","Ş"],["yş","dş","sş","çş","pş","în","ş"],["yekşem","duşem","sêşem","çarşem","pêncşem","în","şemî"],["yş","dş","sş","çş","pş","în","ş"]],u,[["R","R","A","A","G","P","T","G","R","K","S","B"],["rêb","reş","ada","avr","gul","pûş","tîr","gel","rez","kew","ser","ber"],["rêbendanê","reşemiyê","adarê","avrêlê","gulanê","pûşperê","tîrmehê","gelawêjê","rezberê","kewçêrê","sermawezê","berfanbarê"]],[["R","R","A","A","G","P","T","G","R","K","S","B"],["rêb","reş","ada","avr","gul","pûş","tîr","gel","rez","kew","ser","ber"],["rêbendan","reşemî","adar","avrêl","gulan","pûşper","tîrmeh","gelawêj","rezber","kewçêr","sermawez","berfanbar"]],[["BZ","PZ"],u,["berî zayînê","piştî zayînê"]],1,[6,0],["y-MM-dd","y MMM d","y MMMM d","y MMMM d, EEEE"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","%#,##0","#,##0.00 ¤","#E0"],"TRY","₺","TRY",{"JPY":["JP¥","¥"],"TRY":["₺"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    