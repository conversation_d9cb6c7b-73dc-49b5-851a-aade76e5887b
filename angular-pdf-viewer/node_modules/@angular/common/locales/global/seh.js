/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === 1)
    return 1;
return 5;
}
    global.ng.common.locales['seh'] = ["seh",[["AM","PM"],u,u],u,[["D","P","C","T","N","S","S"],["Dim","<PERSON><PERSON>","<PERSON>r","Tat","Nai","<PERSON>ha","Sab"],["<PERSON>ming<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>"],["<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON>","<PERSON>ha","Sab"]],u,[["J","F","M","A","M","J","J","A","S","O","<PERSON>","<PERSON>"],["<PERSON>","<PERSON>v","<PERSON>","<PERSON>br","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>","<PERSON>tu","<PERSON>","<PERSON>"],["<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","<PERSON>bril","<PERSON>o","<PERSON><PERSON>","<PERSON><PERSON>","Augusto","<PERSON>em<PERSON>","<PERSON>tub<PERSON>","<PERSON>embro","<PERSON>embro"]],u,[["AC","AD"],u,["Antes de Cristo","Anno Domini"]],0,[6,0],["d/M/y","d 'de' MMM 'de' y","d 'de' MMMM 'de' y","EEEE, d 'de' MMMM 'de' y"],["HH:mm","HH:mm:ss","HH:mm:ss z","HH:mm:ss zzzz"],["{1} {0}",u,u,u],[",",".",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","#,##0.00¤","#E0"],"MZN","MTn","Metical de Moçambique",{"JPY":["JP¥","¥"],"MZN":["MTn"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    