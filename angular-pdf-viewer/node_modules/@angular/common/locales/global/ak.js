/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */

// THIS CODE IS GENERATED - DO NOT MODIFY.
  (function(global) {
    global.ng ??= {};
    global.ng.common ??= {};
    global.ng.common.locales ??= {};
    const u = undefined;
    function plural(val) {
const n = val;

if (n === Math.floor(n) && (n >= 0 && n <= 1))
    return 1;
return 5;
}
    global.ng.common.locales['ak'] = ["ak",[["AN","EW"],u,u],u,[["K","D","B","W","Y","F","M"],["Kwe","Dwo","Ben","Wuk","Yaw","Fia","Mem"],["<PERSON><PERSON><PERSON>","<PERSON>wow<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>"],["<PERSON>we","<PERSON><PERSON>","<PERSON>","<PERSON><PERSON>","Yaw","<PERSON>a","Mem"]],u,[["1","2","3","4","5","6","7","8","9","10","11","12"],["S-Ɔ","K-Ɔ","E-Ɔ","E-O","E-K","O-A","A-K","D-Ɔ","F-Ɛ","Ɔ-A","Ɔ-O","M-Ɔ"],["Sanda-Ɔpɛpɔn","Kwakwar-Ɔgyefuo","Ebɔw-Ɔbenem","Ebɔbira-Oforisuo","Esusow Aketseaba-Kɔtɔnimba","Obirade-Ayɛwohomumu","Ayɛwoho-Kitawonsa","Difuu-Ɔsandaa","Fankwa-Ɛbɔ","Ɔbɛsɛ-Ahinime","Ɔberɛfɛw-Obubuo","Mumu-Ɔpɛnimba"]],u,[["AK","KE"],u,["Ansa Kristo","Kristo Ekyiri"]],1,[6,0],["yy/MM/dd","y MMM d","y MMMM d","EEEE, y MMMM dd"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1} {0}",u,u,u],[".",",",";","%","+","-","E","×","‰","∞","NaN",":"],["#,##0.###","#,##0%","¤#,##0.00","#E0"],"GHS","GH₵","Ghana Sidi",{"GHS":["GH₵"],"JPY":["JP¥","¥"],"USD":["US$","$"]},"ltr", plural, []];
  })(globalThis);
    