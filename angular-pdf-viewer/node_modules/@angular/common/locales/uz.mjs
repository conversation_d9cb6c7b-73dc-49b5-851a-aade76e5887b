/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["uz", [["TO", "TK"], u, u], u, [["Y", "D", "S", "C", "P", "J", "S"], ["Yak", "Dush", "<PERSON><PERSON>", "Chor", "Pay", "Jum", "Shan"], ["yakshanba", "dushanba", "seshanba", "chorshanba", "payshanba", "juma", "shanba"], ["Ya", "Du", "Se", "Ch", "Pa", "Ju", "Sh"]], u, [["Y", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["yan", "fev", "mar", "apr", "may", "iyn", "iyl", "avg", "sen", "okt", "noy", "dek"], ["yanvar", "fevral", "mart", "aprel", "may", "iyun", "iyul", "avgust", "sentabr", "oktabr", "noyabr", "dekabr"]], [["Y", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["Yan", "Fev", "Mar", "Apr", "May", "Iyn", "Iyl", "Avg", "Sen", "Okt", "Noy", "Dek"], ["Yanvar", "Fevral", "Mart", "Aprel", "May", "Iyun", "Iyul", "Avgust", "Sentabr", "Oktabr", "Noyabr", "Dekabr"]], [["m.a.", "milodiy"], u, ["miloddan avvalgi", "milodiy"]], 1, [6, 0], ["dd/MM/yy", "d-MMM, y", "d-MMMM, y", "EEEE, d-MMMM, y"], ["HH:mm", "HH:mm:ss", "H:mm:ss (z)", "H:mm:ss (zzzz)"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "son emas", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "UZS", "soʻm", "O‘zbekiston so‘mi", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"], "UZS": ["soʻm"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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