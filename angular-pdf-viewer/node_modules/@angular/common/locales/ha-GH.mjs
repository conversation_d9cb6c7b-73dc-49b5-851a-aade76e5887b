/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["ha-GH", [["SF", "YM"], u, ["<PERSON><PERSON><PERSON>", "Yamma"]], [["SF", "YM"], u, u], [["L", "L", "T", "L", "A", "J", "A"], ["Lah", "<PERSON>t", "Tal", "Lar", "<PERSON>h", "Ju<PERSON>", "<PERSON>a"], ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>a", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ʼa", "<PERSON><PERSON>"], ["Lh", "<PERSON>", "<PERSON>", "<PERSON>r", "<PERSON>", "<PERSON>", "<PERSON>"]], u, [["<PERSON>", "<PERSON>", "<PERSON>", "A", "M", "Y", "Y", "A", "<PERSON>", "O", "N", "D"], ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>fi", "<PERSON>", "<PERSON>", "<PERSON>l", "<PERSON>gu", "<PERSON>t", "<PERSON>t", "<PERSON>uw", "<PERSON>s"], ["<PERSON>iru", "<PERSON>ab<PERSON>ru", "<PERSON>", "<PERSON>firilu", "<PERSON>u", "<PERSON>i", "<PERSON><PERSON>", "<PERSON><PERSON>ta", "<PERSON><PERSON><PERSON>", "<PERSON>toba", "<PERSON>uwa<PERSON>", "<PERSON><PERSON><PERSON>"]], u, [["<PERSON>.<PERSON>", "BHAI"], u, ["Kafin haihuwar annab", "Bayan haihuwar annab"]], 1, [6, 0], ["d/M/yy", "d MMM, y", "d MMMM, y", "EEEE d MMMM, y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a z", "h:mm:ss a zzzz"], ["{1}, {0}", u, "{1} 'da' {0}", "{1} {0}"], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "¤ #,##0.00", "#E0"], "GHS", "GH₵", "Sidi na Ghana", { "GHS": ["GH₵"], "NGN": ["₦"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaGEtR0guanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb21tb24vbG9jYWxlcy9oYS1HSC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCwwQ0FBMEM7QUFDMUMsTUFBTSxDQUFDLEdBQUcsU0FBUyxDQUFDO0FBRXBCLFNBQVMsTUFBTSxDQUFDLEdBQVc7SUFDM0IsTUFBTSxDQUFDLEdBQUcsR0FBRyxDQUFDO0lBRWQsSUFBSSxDQUFDLEtBQUssQ0FBQztRQUNQLE9BQU8sQ0FBQyxDQUFDO0lBQ2IsT0FBTyxDQUFDLENBQUM7QUFDVCxDQUFDO0FBRUQsZUFBZSxDQUFDLE9BQU8sRUFBQyxDQUFDLENBQUMsSUFBSSxFQUFDLElBQUksQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxPQUFPLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssQ0FBQyxFQUFDLENBQUMsUUFBUSxFQUFDLFNBQVMsRUFBQyxRQUFRLEVBQUMsUUFBUSxFQUFDLFNBQVMsRUFBQyxTQUFTLEVBQUMsUUFBUSxDQUFDLEVBQUMsQ0FBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLEVBQUMsSUFBSSxFQUFDLElBQUksRUFBQyxJQUFJLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLENBQUMsRUFBQyxDQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxLQUFLLEVBQUMsS0FBSyxDQUFDLEVBQUMsQ0FBQyxTQUFTLEVBQUMsV0FBVyxFQUFDLE9BQU8sRUFBQyxTQUFTLEVBQUMsTUFBTSxFQUFDLE1BQU0sRUFBQyxNQUFNLEVBQUMsUUFBUSxFQUFDLFNBQVMsRUFBQyxRQUFRLEVBQUMsU0FBUyxFQUFDLFNBQVMsQ0FBQyxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsQ0FBQyxLQUFLLEVBQUMsTUFBTSxDQUFDLEVBQUMsQ0FBQyxFQUFDLENBQUMsc0JBQXNCLEVBQUMsc0JBQXNCLENBQUMsQ0FBQyxFQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLENBQUMsRUFBQyxDQUFDLFFBQVEsRUFBQyxVQUFVLEVBQUMsV0FBVyxFQUFDLGdCQUFnQixDQUFDLEVBQUMsQ0FBQyxRQUFRLEVBQUMsV0FBVyxFQUFDLGFBQWEsRUFBQyxnQkFBZ0IsQ0FBQyxFQUFDLENBQUMsVUFBVSxFQUFDLENBQUMsRUFBQyxjQUFjLEVBQUMsU0FBUyxDQUFDLEVBQUMsQ0FBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsR0FBRyxFQUFDLEdBQUcsRUFBQyxHQUFHLEVBQUMsS0FBSyxFQUFDLEdBQUcsQ0FBQyxFQUFDLENBQUMsV0FBVyxFQUFDLFFBQVEsRUFBQyxZQUFZLEVBQUMsS0FBSyxDQUFDLEVBQUMsS0FBSyxFQUFDLEtBQUssRUFBQyxlQUFlLEVBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxLQUFLLENBQUMsRUFBQyxLQUFLLEVBQUMsQ0FBQyxHQUFHLENBQUMsRUFBQyxFQUFDLEtBQUssRUFBRSxNQUFNLENBQUMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBUSElTIENPREUgSVMgR0VORVJBVEVEIC0gRE8gTk9UIE1PRElGWS5cbmNvbnN0IHUgPSB1bmRlZmluZWQ7XG5cbmZ1bmN0aW9uIHBsdXJhbCh2YWw6IG51bWJlcik6IG51bWJlciB7XG5jb25zdCBuID0gdmFsO1xuXG5pZiAobiA9PT0gMSlcbiAgICByZXR1cm4gMTtcbnJldHVybiA1O1xufVxuXG5leHBvcnQgZGVmYXVsdCBbXCJoYS1HSFwiLFtbXCJTRlwiLFwiWU1cIl0sdSxbXCJTYWZpeWFcIixcIllhbW1hXCJdXSxbW1wiU0ZcIixcIllNXCJdLHUsdV0sW1tcIkxcIixcIkxcIixcIlRcIixcIkxcIixcIkFcIixcIkpcIixcIkFcIl0sW1wiTGFoXCIsXCJMaXRcIixcIlRhbFwiLFwiTGFyXCIsXCJBbGhcIixcIkp1bVwiLFwiQXNhXCJdLFtcIkxhaGFkaVwiLFwiTGl0aW5pblwiLFwiVGFsYXRhXCIsXCJMYXJhYmFcIixcIkFsaGFtaXNcIixcIkp1bW1hyrxhXCIsXCJBc2FiYXJcIl0sW1wiTGhcIixcIkxpXCIsXCJUYVwiLFwiTHJcIixcIkFsXCIsXCJKdVwiLFwiQXNcIl1dLHUsW1tcIkpcIixcIkZcIixcIk1cIixcIkFcIixcIk1cIixcIllcIixcIllcIixcIkFcIixcIlNcIixcIk9cIixcIk5cIixcIkRcIl0sW1wiSmFuXCIsXCJGYWJcIixcIk1hclwiLFwiQWZpXCIsXCJNYXlcIixcIll1blwiLFwiWXVsXCIsXCJBZ3VcIixcIlNhdFwiLFwiT2t0XCIsXCJOdXdcIixcIkRpc1wiXSxbXCJKYW5haXJ1XCIsXCJGYWJ1cmFpcnVcIixcIk1hcmlzXCIsXCJBZmlyaWx1XCIsXCJNYXl1XCIsXCJZdW5pXCIsXCJZdWxpXCIsXCJBZ3VzdGFcIixcIlNhdHVtYmFcIixcIk9rdG9iYVwiLFwiTnV3YW1iYVwiLFwiRGlzYW1iYVwiXV0sdSxbW1wiSy5IXCIsXCJCSEFJXCJdLHUsW1wiS2FmaW4gaGFpaHV3YXIgYW5uYWJcIixcIkJheWFuIGhhaWh1d2FyIGFubmFiXCJdXSwxLFs2LDBdLFtcImQvTS95eVwiLFwiZCBNTU0sIHlcIixcImQgTU1NTSwgeVwiLFwiRUVFRSBkIE1NTU0sIHlcIl0sW1wiaDptbSBhXCIsXCJoOm1tOnNzIGFcIixcImg6bW06c3MgYSB6XCIsXCJoOm1tOnNzIGEgenp6elwiXSxbXCJ7MX0sIHswfVwiLHUsXCJ7MX0gJ2RhJyB7MH1cIixcInsxfSB7MH1cIl0sW1wiLlwiLFwiLFwiLFwiO1wiLFwiJVwiLFwiK1wiLFwiLVwiLFwiRVwiLFwiw5dcIixcIuKAsFwiLFwi4oieXCIsXCJOYU5cIixcIjpcIl0sW1wiIywjIzAuIyMjXCIsXCIjLCMjMCVcIixcIsKkwqAjLCMjMC4wMFwiLFwiI0UwXCJdLFwiR0hTXCIsXCJHSOKCtVwiLFwiU2lkaSBuYSBHaGFuYVwiLHtcIkdIU1wiOltcIkdI4oK1XCJdLFwiTkdOXCI6W1wi4oKmXCJdfSxcImx0clwiLCBwbHVyYWxdO1xuIl19