/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["sq", [["p.d.", "m.d."], u, ["e paradites", "e pasdites"]], [["p.d.", "m.d."], u, ["paradite", "pasdite"]], [["d", "h", "m", "m", "e", "p", "sh"], ["Die", "Hën", "Mar", "Mër", "Enj", "Pre", "Sht"], ["e diel", "e hënë", "e martë", "e mërkurë", "e enjte", "e premte", "e shtunë"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"]], [["d", "h", "m", "m", "e", "p", "sh"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"], ["e diel", "e hënë", "e martë", "e mërkurë", "e enjte", "e premte", "e shtunë"], ["die", "hën", "mar", "mër", "enj", "pre", "sht"]], [["j", "sh", "m", "p", "m", "q", "k", "g", "sh", "t", "n", "dh"], ["jan", "shk", "mar", "pri", "maj", "qer", "korr", "gush", "sht", "tet", "nën", "dhj"], ["janar", "shkurt", "mars", "prill", "maj", "qershor", "korrik", "gusht", "shtator", "tetor", "nëntor", "dhjetor"]], u, [["p.K.", "mb.K."], u, ["para Krishtit", "mbas Krishtit"]], 1, [6, 0], ["d.M.yy", "d MMM y", "d MMMM y", "EEEE, d MMMM y"], ["h:mm a", "h:mm:ss a", "h:mm:ss a, z", "h:mm:ss a, zzzz"], ["{1}, {0}", u, "{1} 'në' {0}", u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "ALL", "Lekë", "Leku shqiptar", { "AFN": [], "ALL": ["Lekë"], "AMD": [], "AOA": [], "ARS": [], "AUD": ["A$", "AUD"], "AZN": [], "BAM": [], "BBD": [], "BDT": [], "BMD": [], "BND": [], "BOB": [], "BRL": [], "BSD": [], "BWP": [], "BZD": [], "CAD": ["CA$", "CAD"], "CLP": [], "CNY": ["CN¥", "CNY"], "COP": [], "CRC": [], "CUC": [], "CUP": [], "CZK": [], "DKK": [], "DOP": [], "EGP": [], "FJD": [], "FKP": [], "GBP": ["£", "GBP"], "GEL": [], "GIP": [], "GNF": [], "GTQ": [], "GYD": [], "HKD": ["HK$", "HKS"], "HNL": [], "HRK": [], "HUF": [], "IDR": [], "ILS": ["₪", "ILS"], "INR": ["₹", "INR"], "ISK": [], "JMD": [], "JPY": ["JP¥", "JPY"], "KHR": [], "KMF": [], "KPW": [], "KRW": ["₩", "KRW"], "KYD": [], "KZT": [], "LAK": [], "LBP": [], "LKR": [], "LRD": [], "MGA": [], "MMK": [], "MNT": [], "MUR": [], "MXN": ["MX$", "MXN"], "MYR": [], "NAD": [], "NGN": [], "NIO": [], "NOK": [], "NPR": [], "NZD": ["NZ$", "NZD"], "PHP": [], "PKR": [], "PLN": [], "PYG": [], "RON": [], "RUB": [], "RWF": [], "SBD": [], "SEK": [], "SGD": [], "SHP": [], "SRD": [], "SSP": [], "STN": [], "SYP": [], "THB": ["฿", "THB"], "TOP": [], "TRY": [], "TTD": [], "TWD": ["NT$", "TWD"], "UAH": [], "USD": ["US$", "USD"], "UYU": [], "VND": ["₫", "VND"], "XCD": ["EC$", "XCD"], "ZAR": [], "ZMW": [] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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