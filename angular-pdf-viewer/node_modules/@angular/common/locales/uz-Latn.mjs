/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["uz-Latn", [["TO", "TK"], u, u], u, [["Y", "D", "S", "C", "P", "J", "S"], ["Yak", "Dush", "Sesh", "Chor", "Pay", "Jum", "Shan"], ["yakshanba", "dushanba", "seshanba", "chorshanba", "payshanba", "juma", "shanba"], ["Ya", "Du", "Se", "Ch", "Pa", "Ju", "Sh"]], u, [["Y", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["yan", "fev", "mar", "apr", "may", "iyn", "iyl", "avg", "sen", "okt", "noy", "dek"], ["yanvar", "fevral", "mart", "aprel", "may", "iyun", "iyul", "avgust", "sentabr", "oktabr", "noyabr", "dekabr"]], [["Y", "F", "M", "A", "M", "I", "I", "A", "S", "O", "N", "D"], ["Yan", "Fev", "Mar", "Apr", "May", "Iyn", "Iyl", "Avg", "Sen", "Okt", "Noy", "Dek"], ["Yanvar", "Fevral", "Mart", "Aprel", "May", "Iyun", "Iyul", "Avgust", "Sentabr", "Oktabr", "Noyabr", "Dekabr"]], [["m.a.", "milodiy"], u, ["miloddan avvalgi", "milodiy"]], 1, [6, 0], ["dd/MM/yy", "d-MMM, y", "d-MMMM, y", "EEEE, d-MMMM, y"], ["HH:mm", "HH:mm:ss", "H:mm:ss (z)", "H:mm:ss (zzzz)"], ["{1}, {0}", u, u, u], [",", " ", ";", "%", "+", "-", "E", "×", "‰", "∞", "son emas", ":"], ["#,##0.###", "#,##0%", "#,##0.00 ¤", "#E0"], "UZS", "soʻm", "O‘zbekiston so‘mi", { "BYN": [u, "р."], "JPY": ["JP¥", "¥"], "PHP": [u, "₱"], "USD": ["US$", "$"], "UZS": ["soʻm"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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