/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    if (n === 2)
        return 2;
    return 5;
}
export default ["se", [["i.b.", "e.b."], u, ["iđitbeaivet", "eahketbeaivet"]], [["i.b.", "e.b."], u, ["iđitbeaivi", "eahketbeaivi"]], [["S", "V", "M", "G", "D", "B", "L"], ["sotn", "vuos", "maŋ", "gask", "duor", "bear", "láv"], ["sotnabeaivi", "vuossárga", "maŋŋeb<PERSON>rga", "gaska<PERSON><PERSON>k<PERSON>", "duorasdat", "bearjadat", "lávvardat"], ["sotn", "vuos", "maŋ", "gask", "duor", "bear", "láv"]], u, [["O", "G", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "S", "B", "<PERSON>", "<PERSON>", "S", "J"], ["ođđj", "guov", "njuk", "cuo", "mies", "geas", "suoi", "borg", "čakč", "golg", "sk<PERSON>b", "juov"], ["ođđajagemánnu", "guovvamánnu", "njukčamánnu", "cuoŋománnu", "miessemánnu", "geassemánnu", "suoidnemánnu", "borgemánnu", "čakčamánnu", "golggotmánnu", "skábmamánnu", "juovlamánnu"]], u, [["o.Kr.", "m.Kr."], u, ["ovdal Kristtusa", "maŋŋel Kristtusa"]], 1, [6, 0], ["y-MM-dd", "y MMM d", "y MMMM d", "y MMMM d, EEEE"], ["HH:mm", "HH:mm:ss", "HH:mm:ss z", "HH:mm:ss zzzz"], ["{1} {0}", u, u, u], [",", " ", ";", "%", "+", "−", "·10^", "·", "‰", "∞", "NaN", ":"], ["#,##0.###", "#,##0 %", "#,##0.00 ¤", "#E0"], "NOK", "kr", "norgga kruvdno", { "DKK": ["Dkr", "kr"], "JPY": ["JP¥", "¥"], "NOK": ["kr"], "SEK": ["Skr", "kr"], "THB": ["฿"], "USD": ["US$", "$"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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