/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// THIS CODE IS GENERATED - DO NOT MODIFY.
const u = undefined;
function plural(val) {
    const n = val;
    if (n === 1)
        return 1;
    return 5;
}
export default ["brx", [["फुं", "बेलासे"], u, u], u, [["र", "स", "मं", "बु", "बि", "सु", "सु"], ["रबि", "सम", "मंगल", "बुध", "बिस्थि", "सुखुर", "सुनि"], ["रबिबार", "समबार", "मंगलबार", "बुधबार", "बिस्थिबार", "सुखुरबार", "सुनिबार"], ["रबि", "सम", "मंगल", "बुध", "बिस्थि", "सुखुर", "सुनि"]], u, [["ज", "फ", "म", "ए", "म", "ज", "ज", "आ", "स", "अ", "न", "ड"], ["जान", "फेब", "मार्च", "एप्रि", "मे", "जुन", "जुल", "आग", "सेप", "अक्ट’", "नवे", "डिसे"], ["जानुवारी", "फेब्रूवारी", "मार्च", "एप्रिल", "मे", "जुन", "जुलाई", "आगष्ट", "सेप्थेम्बर", "अक्ट’बर", "नवेम्बर", "डिसेम्बर"]], u, [["बि.सि.", "ए.दि"], u, u], 0, [0, 0], ["y-MM-dd", "MMM d, y", "MMMM d, y", "y MMMM d, EEEE"], ["a नि h:mm", "a h:mm:ss", "a h:mm:ss z", "a h:mm:ss zzzz"], ["{1}, {0}", u, "{1} नि {0} याव", u], [".", ",", ";", "%", "+", "-", "E", "×", "‰", "∞", "NaN", ":"], ["#,##,##0.###", "#,##,##0%", "¤ #,##,##0.00", "#E0"], "INR", "₹", "भारतनि रुपी", { "CNY": ["सिएन¥", "¥"], "JPY": ["JP¥", "¥"], "RUB": ["रूब", "₽"] }, "ltr", plural];
//# sourceMappingURL=data:application/json;base64,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