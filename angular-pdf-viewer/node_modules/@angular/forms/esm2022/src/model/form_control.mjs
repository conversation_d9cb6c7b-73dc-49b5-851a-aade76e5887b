/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { removeListItem } from '../util';
import { AbstractControl, isOptionsObj, pickAsyncValidators, pickValidators } from './abstract_model';
function isFormControlState(formState) {
    return typeof formState === 'object' && formState !== null &&
        Object.keys(formState).length === 2 && 'value' in formState && 'disabled' in formState;
}
export const FormControl = (class FormControl extends AbstractControl {
    constructor(
    // formState and defaultValue will only be null if T is nullable
    formState = null, validatorOrOpts, asyncValidator) {
        super(pickValidators(validatorOrOpts), pickAsyncValidators(asyncValidator, validatorOrOpts));
        /** @publicApi */
        this.defaultValue = null;
        /** @internal */
        this._onChange = [];
        /** @internal */
        this._pendingChange = false;
        this._applyFormState(formState);
        this._setUpdateStrategy(validatorOrOpts);
        this._initObservables();
        this.updateValueAndValidity({
            onlySelf: true,
            // If `asyncValidator` is present, it will trigger control status change from `PENDING` to
            // `VALID` or `INVALID`.
            // The status should be broadcasted via the `statusChanges` observable, so we set
            // `emitEvent` to `true` to allow that during the control creation process.
            emitEvent: !!this.asyncValidator
        });
        if (isOptionsObj(validatorOrOpts) &&
            (validatorOrOpts.nonNullable || validatorOrOpts.initialValueIsDefault)) {
            if (isFormControlState(formState)) {
                this.defaultValue = formState.value;
            }
            else {
                this.defaultValue = formState;
            }
        }
    }
    setValue(value, options = {}) {
        this.value = this._pendingValue = value;
        if (this._onChange.length && options.emitModelToViewChange !== false) {
            this._onChange.forEach((changeFn) => changeFn(this.value, options.emitViewToModelChange !== false));
        }
        this.updateValueAndValidity(options);
    }
    patchValue(value, options = {}) {
        this.setValue(value, options);
    }
    reset(formState = this.defaultValue, options = {}) {
        this._applyFormState(formState);
        this.markAsPristine(options);
        this.markAsUntouched(options);
        this.setValue(this.value, options);
        this._pendingChange = false;
    }
    /**  @internal */
    _updateValue() { }
    /**  @internal */
    _anyControls(condition) {
        return false;
    }
    /**  @internal */
    _allControlsDisabled() {
        return this.disabled;
    }
    registerOnChange(fn) {
        this._onChange.push(fn);
    }
    /** @internal */
    _unregisterOnChange(fn) {
        removeListItem(this._onChange, fn);
    }
    registerOnDisabledChange(fn) {
        this._onDisabledChange.push(fn);
    }
    /** @internal */
    _unregisterOnDisabledChange(fn) {
        removeListItem(this._onDisabledChange, fn);
    }
    /** @internal */
    _forEachChild(cb) { }
    /** @internal */
    _syncPendingControls() {
        if (this.updateOn === 'submit') {
            if (this._pendingDirty)
                this.markAsDirty();
            if (this._pendingTouched)
                this.markAsTouched();
            if (this._pendingChange) {
                this.setValue(this._pendingValue, { onlySelf: true, emitModelToViewChange: false });
                return true;
            }
        }
        return false;
    }
    _applyFormState(formState) {
        if (isFormControlState(formState)) {
            this.value = this._pendingValue = formState.value;
            formState.disabled ? this.disable({ onlySelf: true, emitEvent: false }) :
                this.enable({ onlySelf: true, emitEvent: false });
        }
        else {
            this.value = this._pendingValue = formState;
        }
    }
});
export const UntypedFormControl = FormControl;
/**
 * @description
 * Asserts that the given control is an instance of `FormControl`
 *
 * @publicApi
 */
export const isFormControl = (control) => control instanceof FormControl;
//# sourceMappingURL=data:application/json;base64,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