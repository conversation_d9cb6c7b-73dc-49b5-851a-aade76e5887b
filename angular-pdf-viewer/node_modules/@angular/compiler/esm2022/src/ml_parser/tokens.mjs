/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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