/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../../../output/output_ast';
import * as ir from '../../ir';
/**
 * The escape sequence used indicate message param values.
 */
const ESCAPE = '\uFFFD';
/**
 * Marker used to indicate an element tag.
 */
const ELEMENT_MARKER = '#';
/**
 * Marker used to indicate a template tag.
 */
const TEMPLATE_MARKER = '*';
/**
 * Marker used to indicate closing of an element or template tag.
 */
const TAG_CLOSE_MARKER = '/';
/**
 * Marker used to indicate the sub-template context.
 */
const CONTEXT_MARKER = ':';
/**
 * Marker used to indicate the start of a list of values.
 */
const LIST_START_MARKER = '[';
/**
 * Marker used to indicate the end of a list of values.
 */
const LIST_END_MARKER = ']';
/**
 * Delimiter used to separate multiple values in a list.
 */
const LIST_DELIMITER = '|';
/**
 * Formats the param maps on extracted message ops into a maps of `Expression` objects that can be
 * used in the final output.
 */
export function extractI18nMessages(job) {
    // Create an i18n message for each context.
    // TODO: Merge the context op with the message op since they're 1:1 anyways.
    const i18nMessagesByContext = new Map();
    const i18nBlocks = new Map();
    const i18nContexts = new Map();
    for (const unit of job.units) {
        for (const op of unit.create) {
            switch (op.kind) {
                case ir.OpKind.I18nContext:
                    const i18nMessageOp = createI18nMessage(job, op);
                    unit.create.push(i18nMessageOp);
                    i18nMessagesByContext.set(op.xref, i18nMessageOp);
                    i18nContexts.set(op.xref, op);
                    break;
                case ir.OpKind.I18nStart:
                    i18nBlocks.set(op.xref, op);
                    break;
            }
        }
    }
    // Associate sub-messages for ICUs with their root message. At this point we can also remove the
    // ICU start/end ops, as they are no longer needed.
    let currentIcu = null;
    for (const unit of job.units) {
        for (const op of unit.create) {
            switch (op.kind) {
                case ir.OpKind.IcuStart:
                    currentIcu = op;
                    ir.OpList.remove(op);
                    // Skip any contexts not associated with an ICU.
                    const icuContext = i18nContexts.get(op.context);
                    if (icuContext.contextKind !== ir.I18nContextKind.Icu) {
                        continue;
                    }
                    // Skip ICUs that share a context with their i18n message. These represent root-level
                    // ICUs, not sub-messages.
                    const i18nBlock = i18nBlocks.get(icuContext.i18nBlock);
                    if (i18nBlock.context === icuContext.xref) {
                        continue;
                    }
                    // Find the root message and push this ICUs message as a sub-message.
                    const rootI18nBlock = i18nBlocks.get(i18nBlock.root);
                    const rootMessage = i18nMessagesByContext.get(rootI18nBlock.context);
                    if (rootMessage === undefined) {
                        throw Error('AssertionError: ICU sub-message should belong to a root message.');
                    }
                    const subMessage = i18nMessagesByContext.get(icuContext.xref);
                    subMessage.messagePlaceholder = op.messagePlaceholder;
                    rootMessage.subMessages.push(subMessage.xref);
                    break;
                case ir.OpKind.IcuEnd:
                    currentIcu = null;
                    ir.OpList.remove(op);
                    break;
                case ir.OpKind.IcuPlaceholder:
                    // Add ICU placeholders to the message, then remove the ICU placeholder ops.
                    if (currentIcu === null || currentIcu.context == null) {
                        throw Error('AssertionError: Unexpected ICU placeholder outside of i18n context');
                    }
                    const msg = i18nMessagesByContext.get(currentIcu.context);
                    msg.postprocessingParams.set(op.name, o.literal(formatIcuPlaceholder(op)));
                    ir.OpList.remove(op);
                    break;
            }
        }
    }
}
/**
 * Create an i18n message op from an i18n context op.
 */
function createI18nMessage(job, context, messagePlaceholder) {
    let formattedParams = formatParams(context.params);
    const formattedPostprocessingParams = formatParams(context.postprocessingParams);
    let needsPostprocessing = [...context.params.values()].some(v => v.length > 1);
    return ir.createI18nMessageOp(job.allocateXrefId(), context.xref, context.i18nBlock, context.message, messagePlaceholder ?? null, formattedParams, formattedPostprocessingParams, needsPostprocessing);
}
/**
 * Formats an ICU placeholder into a single string with expression placeholders.
 */
function formatIcuPlaceholder(op) {
    if (op.strings.length !== op.expressionPlaceholders.length + 1) {
        throw Error(`AssertionError: Invalid ICU placeholder with ${op.strings.length} strings and ${op.expressionPlaceholders.length} expressions`);
    }
    const values = op.expressionPlaceholders.map(formatValue);
    return op.strings.flatMap((str, i) => [str, values[i] || '']).join('');
}
/**
 * Formats a map of `I18nParamValue[]` values into a map of `Expression` values.
 */
function formatParams(params) {
    const formattedParams = new Map();
    for (const [placeholder, placeholderValues] of params) {
        const serializedValues = formatParamValues(placeholderValues);
        if (serializedValues !== null) {
            formattedParams.set(placeholder, o.literal(serializedValues));
        }
    }
    return formattedParams;
}
/**
 * Formats an `I18nParamValue[]` into a string (or null for empty array).
 */
function formatParamValues(values) {
    if (values.length === 0) {
        return null;
    }
    const serializedValues = values.map(value => formatValue(value));
    return serializedValues.length === 1 ?
        serializedValues[0] :
        `${LIST_START_MARKER}${serializedValues.join(LIST_DELIMITER)}${LIST_END_MARKER}`;
}
/**
 * Formats a single `I18nParamValue` into a string
 */
function formatValue(value) {
    // Element tags with a structural directive use a special form that concatenates the element and
    // template values.
    if ((value.flags & ir.I18nParamValueFlags.ElementTag) &&
        (value.flags & ir.I18nParamValueFlags.TemplateTag)) {
        if (typeof value.value !== 'object') {
            throw Error('AssertionError: Expected i18n param value to have an element and template slot');
        }
        const elementValue = formatValue({
            ...value,
            value: value.value.element,
            flags: value.flags & ~ir.I18nParamValueFlags.TemplateTag
        });
        const templateValue = formatValue({
            ...value,
            value: value.value.template,
            flags: value.flags & ~ir.I18nParamValueFlags.ElementTag
        });
        // TODO(mmalerba): This is likely a bug in TemplateDefinitionBuilder, we should not need to
        // record the template value twice. For now I'm re-implementing the behavior here to keep the
        // output consistent with TemplateDefinitionBuilder.
        if ((value.flags & ir.I18nParamValueFlags.OpenTag) &&
            (value.flags & ir.I18nParamValueFlags.CloseTag)) {
            return `${templateValue}${elementValue}${templateValue}`;
        }
        // To match the TemplateDefinitionBuilder output, flip the order depending on whether the
        // values represent a closing or opening tag (or both).
        // TODO(mmalerba): Figure out if this makes a difference in terms of either functionality,
        // or the resulting message ID. If not, we can remove the special-casing in the future.
        return value.flags & ir.I18nParamValueFlags.CloseTag ? `${elementValue}${templateValue}` :
            `${templateValue}${elementValue}`;
    }
    // Self-closing tags use a special form that concatenates the start and close tag values.
    if ((value.flags & ir.I18nParamValueFlags.OpenTag) &&
        (value.flags & ir.I18nParamValueFlags.CloseTag)) {
        return `${formatValue({ ...value, flags: value.flags & ~ir.I18nParamValueFlags.CloseTag })}${formatValue({ ...value, flags: value.flags & ~ir.I18nParamValueFlags.OpenTag })}`;
    }
    // If there are no special flags, just return the raw value.
    if (value.flags === ir.I18nParamValueFlags.None) {
        return `${value.value}`;
    }
    // Encode the remaining flags as part of the value.
    let tagMarker = '';
    let closeMarker = '';
    if (value.flags & ir.I18nParamValueFlags.ElementTag) {
        tagMarker = ELEMENT_MARKER;
    }
    else if (value.flags & ir.I18nParamValueFlags.TemplateTag) {
        tagMarker = TEMPLATE_MARKER;
    }
    if (tagMarker !== '') {
        closeMarker = value.flags & ir.I18nParamValueFlags.CloseTag ? TAG_CLOSE_MARKER : '';
    }
    const context = value.subTemplateIndex === null ? '' : `${CONTEXT_MARKER}${value.subTemplateIndex}`;
    return `${ESCAPE}${closeMarker}${tagMarker}${value.value}${context}${ESCAPE}`;
}
//# sourceMappingURL=data:application/json;base64,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