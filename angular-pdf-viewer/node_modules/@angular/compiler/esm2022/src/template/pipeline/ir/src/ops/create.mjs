/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { I18nContextKind, Namespace, OpKind } from '../enums';
import { SlotHandle } from '../handle';
import { OpList } from '../operations';
import { TRAIT_CONSUMES_SLOT, TRAIT_CONSUMES_VARS } from '../traits';
import { NEW_OP } from './shared';
/**
 * The set of OpKinds that represent the creation of an element or container
 */
const elementContainerOpKinds = new Set([
    OpKind.Element, OpKind.ElementStart, OpKind.Container, OpKind.ContainerStart, OpKind.Template,
    OpKind.RepeaterCreate
]);
/**
 * Checks whether the given operation represents the creation of an element or container.
 */
export function isElementOrContainerOp(op) {
    return elementContainerOpKinds.has(op.kind);
}
/**
 * Create an `ElementStartOp`.
 */
export function createElementStartOp(tag, xref, namespace, i18nPlaceholder, startSourceSpan, wholeSourceSpan) {
    return {
        kind: OpKind.ElementStart,
        xref,
        tag,
        handle: new SlotHandle(),
        attributes: null,
        localRefs: [],
        nonBindable: false,
        namespace,
        i18nPlaceholder,
        startSourceSpan,
        wholeSourceSpan,
        ...TRAIT_CONSUMES_SLOT,
        ...NEW_OP,
    };
}
/**
 * Create a `TemplateOp`.
 */
export function createTemplateOp(xref, templateKind, tag, functionNameSuffix, namespace, i18nPlaceholder, startSourceSpan, wholeSourceSpan) {
    return {
        kind: OpKind.Template,
        xref,
        templateKind,
        attributes: null,
        tag,
        handle: new SlotHandle(),
        functionNameSuffix,
        decls: null,
        vars: null,
        localRefs: [],
        nonBindable: false,
        namespace,
        i18nPlaceholder,
        startSourceSpan,
        wholeSourceSpan,
        ...TRAIT_CONSUMES_SLOT,
        ...NEW_OP,
    };
}
export function createRepeaterCreateOp(primaryView, emptyView, tag, track, varNames, emptyTag, i18nPlaceholder, emptyI18nPlaceholder, startSourceSpan, wholeSourceSpan) {
    return {
        kind: OpKind.RepeaterCreate,
        attributes: null,
        xref: primaryView,
        handle: new SlotHandle(),
        emptyView,
        track,
        trackByFn: null,
        tag,
        emptyTag,
        emptyAttributes: null,
        functionNameSuffix: 'For',
        namespace: Namespace.HTML,
        nonBindable: false,
        localRefs: [],
        decls: null,
        vars: null,
        varNames,
        usesComponentInstance: false,
        i18nPlaceholder,
        emptyI18nPlaceholder,
        startSourceSpan,
        wholeSourceSpan,
        ...TRAIT_CONSUMES_SLOT,
        ...NEW_OP,
        ...TRAIT_CONSUMES_VARS,
        numSlotsUsed: emptyView === null ? 2 : 3,
    };
}
/**
 * Create an `ElementEndOp`.
 */
export function createElementEndOp(xref, sourceSpan) {
    return {
        kind: OpKind.ElementEnd,
        xref,
        sourceSpan,
        ...NEW_OP,
    };
}
export function createDisableBindingsOp(xref) {
    return {
        kind: OpKind.DisableBindings,
        xref,
        ...NEW_OP,
    };
}
export function createEnableBindingsOp(xref) {
    return {
        kind: OpKind.EnableBindings,
        xref,
        ...NEW_OP,
    };
}
/**
 * Create a `TextOp`.
 */
export function createTextOp(xref, initialValue, icuPlaceholder, sourceSpan) {
    return {
        kind: OpKind.Text,
        xref,
        handle: new SlotHandle(),
        initialValue,
        icuPlaceholder,
        sourceSpan,
        ...TRAIT_CONSUMES_SLOT,
        ...NEW_OP,
    };
}
/**
 * Create a `ListenerOp`. Host bindings reuse all the listener logic.
 */
export function createListenerOp(target, targetSlot, name, tag, handlerOps, animationPhase, eventTarget, hostListener, sourceSpan) {
    const handlerList = new OpList();
    handlerList.push(handlerOps);
    return {
        kind: OpKind.Listener,
        target,
        targetSlot,
        tag,
        hostListener,
        name,
        handlerOps: handlerList,
        handlerFnName: null,
        consumesDollarEvent: false,
        isAnimationListener: animationPhase !== null,
        animationPhase,
        eventTarget,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Create a `TwoWayListenerOp`.
 */
export function createTwoWayListenerOp(target, targetSlot, name, tag, handlerOps, sourceSpan) {
    const handlerList = new OpList();
    handlerList.push(handlerOps);
    return {
        kind: OpKind.TwoWayListener,
        target,
        targetSlot,
        tag,
        name,
        handlerOps: handlerList,
        handlerFnName: null,
        sourceSpan,
        ...NEW_OP,
    };
}
export function createPipeOp(xref, slot, name) {
    return {
        kind: OpKind.Pipe,
        xref,
        handle: slot,
        name,
        ...NEW_OP,
        ...TRAIT_CONSUMES_SLOT,
    };
}
export function createNamespaceOp(namespace) {
    return {
        kind: OpKind.Namespace,
        active: namespace,
        ...NEW_OP,
    };
}
export function createProjectionDefOp(def) {
    return {
        kind: OpKind.ProjectionDef,
        def,
        ...NEW_OP,
    };
}
export function createProjectionOp(xref, selector, i18nPlaceholder, sourceSpan) {
    return {
        kind: OpKind.Projection,
        xref,
        handle: new SlotHandle(),
        selector,
        i18nPlaceholder,
        projectionSlotIndex: 0,
        attributes: null,
        localRefs: [],
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_CONSUMES_SLOT,
    };
}
/**
 * Create an `ExtractedAttributeOp`.
 */
export function createExtractedAttributeOp(target, bindingKind, namespace, name, expression, i18nContext, i18nMessage, securityContext) {
    return {
        kind: OpKind.ExtractedAttribute,
        target,
        bindingKind,
        namespace,
        name,
        expression,
        i18nContext,
        i18nMessage,
        securityContext,
        trustedValueFn: null,
        ...NEW_OP,
    };
}
export function createDeferOp(xref, main, mainSlot, metadata, resolverFn, sourceSpan) {
    return {
        kind: OpKind.Defer,
        xref,
        handle: new SlotHandle(),
        mainView: main,
        mainSlot,
        loadingView: null,
        loadingSlot: null,
        loadingConfig: null,
        loadingMinimumTime: null,
        loadingAfterTime: null,
        placeholderView: null,
        placeholderSlot: null,
        placeholderConfig: null,
        placeholderMinimumTime: null,
        errorView: null,
        errorSlot: null,
        metadata,
        resolverFn,
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_CONSUMES_SLOT,
        numSlotsUsed: 2,
    };
}
export function createDeferOnOp(defer, trigger, prefetch, sourceSpan) {
    return {
        kind: OpKind.DeferOn,
        defer,
        trigger,
        prefetch,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Create an `ExtractedMessageOp`.
 */
export function createI18nMessageOp(xref, i18nContext, i18nBlock, message, messagePlaceholder, params, postprocessingParams, needsPostprocessing) {
    return {
        kind: OpKind.I18nMessage,
        xref,
        i18nContext,
        i18nBlock,
        message,
        messagePlaceholder,
        params,
        postprocessingParams,
        needsPostprocessing,
        subMessages: [],
        ...NEW_OP,
    };
}
/**
 * Create an `I18nStartOp`.
 */
export function createI18nStartOp(xref, message, root, sourceSpan) {
    return {
        kind: OpKind.I18nStart,
        xref,
        handle: new SlotHandle(),
        root: root ?? xref,
        message,
        messageIndex: null,
        subTemplateIndex: null,
        context: null,
        sourceSpan,
        ...NEW_OP,
        ...TRAIT_CONSUMES_SLOT,
    };
}
/**
 * Create an `I18nEndOp`.
 */
export function createI18nEndOp(xref, sourceSpan) {
    return {
        kind: OpKind.I18nEnd,
        xref,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Creates an ICU start op.
 */
export function createIcuStartOp(xref, message, messagePlaceholder, sourceSpan) {
    return {
        kind: OpKind.IcuStart,
        xref,
        message,
        messagePlaceholder,
        context: null,
        sourceSpan,
        ...NEW_OP,
    };
}
/**
 * Creates an ICU end op.
 */
export function createIcuEndOp(xref) {
    return {
        kind: OpKind.IcuEnd,
        xref,
        ...NEW_OP,
    };
}
/**
 * Creates an ICU placeholder op.
 */
export function createIcuPlaceholderOp(xref, name, strings) {
    return {
        kind: OpKind.IcuPlaceholder,
        xref,
        name,
        strings,
        expressionPlaceholders: [],
        ...NEW_OP,
    };
}
export function createI18nContextOp(contextKind, xref, i18nBlock, message, sourceSpan) {
    if (i18nBlock === null && contextKind !== I18nContextKind.Attr) {
        throw new Error('AssertionError: i18nBlock must be provided for non-attribute contexts.');
    }
    return {
        kind: OpKind.I18nContext,
        contextKind,
        xref,
        i18nBlock,
        message,
        sourceSpan,
        params: new Map(),
        postprocessingParams: new Map(),
        ...NEW_OP,
    };
}
export function createI18nAttributesOp(xref, handle, target) {
    return {
        kind: OpKind.I18nAttributes,
        xref,
        handle,
        target,
        i18nAttributesConfig: null,
        ...NEW_OP,
        ...TRAIT_CONSUMES_SLOT,
    };
}
//# sourceMappingURL=data:application/json;base64,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