/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../../../output/output_ast';
import * as ir from '../../ir';
/**
 * Optimize variables declared and used in the IR.
 *
 * Variables are eagerly generated by pipeline stages for all possible values that could be
 * referenced. This stage processes the list of declared variables and all variable usages,
 * and optimizes where possible. It performs 3 main optimizations:
 *
 *   * It transforms variable declarations to side effectful expressions when the
 *     variable is not used, but its initializer has global effects which other
 *     operations rely upon.
 *   * It removes variable declarations if those variables are not referenced and
 *     either they do not have global effects, or nothing relies on them.
 *   * It inlines variable declarations when those variables are only used once
 *     and the inlining is semantically safe.
 *
 * To guarantee correctness, analysis of "fences" in the instruction lists is used to determine
 * which optimizations are safe to perform.
 */
export function optimizeVariables(job) {
    for (const unit of job.units) {
        inlineAlwaysInlineVariables(unit.create);
        inlineAlwaysInlineVariables(unit.update);
        for (const op of unit.create) {
            if (op.kind === ir.OpKind.Listener || op.kind === ir.OpKind.TwoWayListener) {
                inlineAlwaysInlineVariables(op.handlerOps);
            }
        }
        optimizeVariablesInOpList(unit.create, job.compatibility);
        optimizeVariablesInOpList(unit.update, job.compatibility);
        for (const op of unit.create) {
            if (op.kind === ir.OpKind.Listener || op.kind === ir.OpKind.TwoWayListener) {
                optimizeVariablesInOpList(op.handlerOps, job.compatibility);
            }
        }
    }
}
/**
 * A [fence](https://en.wikipedia.org/wiki/Memory_barrier) flag for an expression which indicates
 * how that expression can be optimized in relation to other expressions or instructions.
 *
 * `Fence`s are a bitfield, so multiple flags may be set on a single expression.
 */
var Fence;
(function (Fence) {
    /**
     * Empty flag (no fence exists).
     */
    Fence[Fence["None"] = 0] = "None";
    /**
     * A context read fence, meaning that the expression in question reads from the "current view"
     * context of the runtime.
     */
    Fence[Fence["ViewContextRead"] = 1] = "ViewContextRead";
    /**
     * A context write fence, meaning that the expression in question writes to the "current view"
     * context of the runtime.
     *
     * Note that all `ContextWrite` fences are implicitly `ContextRead` fences as operations which
     * change the view context do so based on the current one.
     */
    Fence[Fence["ViewContextWrite"] = 2] = "ViewContextWrite";
    /**
     * Indicates that a call is required for its side-effects, even if nothing reads its result.
     *
     * This is also true of `ViewContextWrite` operations **if** they are followed by a
     * `ViewContextRead`.
     */
    Fence[Fence["SideEffectful"] = 4] = "SideEffectful";
})(Fence || (Fence = {}));
function inlineAlwaysInlineVariables(ops) {
    const vars = new Map();
    for (const op of ops) {
        if (op.kind === ir.OpKind.Variable && op.flags & ir.VariableFlags.AlwaysInline) {
            ir.visitExpressionsInOp(op, expr => {
                if (ir.isIrExpression(expr) && fencesForIrExpression(expr) !== Fence.None) {
                    throw new Error(`AssertionError: A context-sensitive variable was marked AlwaysInline`);
                }
            });
            vars.set(op.xref, op);
        }
        ir.transformExpressionsInOp(op, expr => {
            if (expr instanceof ir.ReadVariableExpr && vars.has(expr.xref)) {
                const varOp = vars.get(expr.xref);
                // Inline by cloning, because we might inline into multiple places.
                return varOp.initializer.clone();
            }
            return expr;
        }, ir.VisitorContextFlag.None);
    }
    for (const op of vars.values()) {
        ir.OpList.remove(op);
    }
}
/**
 * Process a list of operations and optimize variables within that list.
 */
function optimizeVariablesInOpList(ops, compatibility) {
    const varDecls = new Map();
    const varUsages = new Map();
    // Track variables that are used outside of the immediate operation list. For example, within
    // `ListenerOp` handler operations of listeners in the current operation list.
    const varRemoteUsages = new Set();
    const opMap = new Map();
    // First, extract information about variables declared or used within the whole list.
    for (const op of ops) {
        if (op.kind === ir.OpKind.Variable) {
            if (varDecls.has(op.xref) || varUsages.has(op.xref)) {
                throw new Error(`Should not see two declarations of the same variable: ${op.xref}`);
            }
            varDecls.set(op.xref, op);
            varUsages.set(op.xref, 0);
        }
        opMap.set(op, collectOpInfo(op));
        countVariableUsages(op, varUsages, varRemoteUsages);
    }
    // The next step is to remove any variable declarations for variables that aren't used. The
    // variable initializer expressions may be side-effectful, so they may need to be retained as
    // expression statements.
    // Track whether we've seen an operation which reads from the view context yet. This is used to
    // determine whether a write to the view context in a variable initializer can be observed.
    let contextIsUsed = false;
    // Note that iteration through the list happens in reverse, which guarantees that we'll process
    // all reads of a variable prior to processing its declaration.
    for (const op of ops.reversed()) {
        const opInfo = opMap.get(op);
        if (op.kind === ir.OpKind.Variable && varUsages.get(op.xref) === 0) {
            // This variable is unused and can be removed. We might need to keep the initializer around,
            // though, if something depends on it running.
            if ((contextIsUsed && opInfo.fences & Fence.ViewContextWrite) ||
                (opInfo.fences & Fence.SideEffectful)) {
                // This variable initializer has a side effect which must be retained. Either:
                //  * it writes to the view context, and we know there is a future operation which depends
                //    on that write, or
                //  * it's an operation which is inherently side-effectful.
                // We can't remove the initializer, but we can remove the variable declaration itself and
                // replace it with a side-effectful statement.
                const stmtOp = ir.createStatementOp(op.initializer.toStmt());
                opMap.set(stmtOp, opInfo);
                ir.OpList.replace(op, stmtOp);
            }
            else {
                // It's safe to delete this entire variable declaration as nothing depends on it, even
                // side-effectfully. Note that doing this might make other variables unused. Since we're
                // iterating in reverse order, we should always be processing usages before declarations
                // and therefore by the time we get to a declaration, all removable usages will have been
                // removed.
                uncountVariableUsages(op, varUsages);
                ir.OpList.remove(op);
            }
            opMap.delete(op);
            varDecls.delete(op.xref);
            varUsages.delete(op.xref);
            continue;
        }
        // Does this operation depend on the view context?
        if (opInfo.fences & Fence.ViewContextRead) {
            contextIsUsed = true;
        }
    }
    // Next, inline any remaining variables with exactly one usage.
    const toInline = [];
    for (const [id, count] of varUsages) {
        const decl = varDecls.get(id);
        // We can inline variables that:
        //  - are used exactly once, and
        //  - are not used remotely
        // OR
        //  - are marked for always inlining
        const isAlwaysInline = !!(decl.flags & ir.VariableFlags.AlwaysInline);
        if (count !== 1 || isAlwaysInline) {
            // We can't inline this variable as it's used more than once.
            continue;
        }
        if (varRemoteUsages.has(id)) {
            // This variable is used once, but across an operation boundary, so it can't be inlined.
            continue;
        }
        toInline.push(id);
    }
    let candidate;
    while (candidate = toInline.pop()) {
        // We will attempt to inline this variable. If inlining fails (due to fences for example),
        // no future operation will make inlining legal.
        const decl = varDecls.get(candidate);
        const varInfo = opMap.get(decl);
        const isAlwaysInline = !!(decl.flags & ir.VariableFlags.AlwaysInline);
        if (isAlwaysInline) {
            throw new Error(`AssertionError: Found an 'AlwaysInline' variable after the always inlining pass.`);
        }
        // Scan operations following the variable declaration and look for the point where that variable
        // is used. There should only be one usage given the precondition above.
        for (let targetOp = decl.next; targetOp.kind !== ir.OpKind.ListEnd; targetOp = targetOp.next) {
            const opInfo = opMap.get(targetOp);
            // Is the variable used in this operation?
            if (opInfo.variablesUsed.has(candidate)) {
                if (compatibility === ir.CompatibilityMode.TemplateDefinitionBuilder &&
                    !allowConservativeInlining(decl, targetOp)) {
                    // We're in conservative mode, and this variable is not eligible for inlining into the
                    // target operation in this mode.
                    break;
                }
                // Yes, try to inline it. Inlining may not be successful if fences in this operation before
                // the variable's usage cannot be safely crossed.
                if (tryInlineVariableInitializer(candidate, decl.initializer, targetOp, varInfo.fences)) {
                    // Inlining was successful! Update the tracking structures to reflect the inlined
                    // variable.
                    opInfo.variablesUsed.delete(candidate);
                    // Add all variables used in the variable's initializer to its new usage site.
                    for (const id of varInfo.variablesUsed) {
                        opInfo.variablesUsed.add(id);
                    }
                    // Merge fences in the variable's initializer into its new usage site.
                    opInfo.fences |= varInfo.fences;
                    // Delete tracking info related to the declaration.
                    varDecls.delete(candidate);
                    varUsages.delete(candidate);
                    opMap.delete(decl);
                    // And finally, delete the original declaration from the operation list.
                    ir.OpList.remove(decl);
                }
                // Whether inlining succeeded or failed, we're done processing this variable.
                break;
            }
            // If the variable is not used in this operation, then we'd need to inline across it. Check if
            // that's safe to do.
            if (!safeToInlinePastFences(opInfo.fences, varInfo.fences)) {
                // We can't safely inline this variable beyond this operation, so don't proceed with
                // inlining this variable.
                break;
            }
        }
    }
}
/**
 * Given an `ir.Expression`, returns the `Fence` flags for that expression type.
 */
function fencesForIrExpression(expr) {
    switch (expr.kind) {
        case ir.ExpressionKind.NextContext:
            return Fence.ViewContextRead | Fence.ViewContextWrite;
        case ir.ExpressionKind.RestoreView:
            return Fence.ViewContextRead | Fence.ViewContextWrite | Fence.SideEffectful;
        case ir.ExpressionKind.Reference:
            return Fence.ViewContextRead;
        default:
            return Fence.None;
    }
}
/**
 * Build the `OpInfo` structure for the given `op`. This performs two operations:
 *
 *  * It tracks which variables are used in the operation's expressions.
 *  * It rolls up fence flags for expressions within the operation.
 */
function collectOpInfo(op) {
    let fences = Fence.None;
    const variablesUsed = new Set();
    ir.visitExpressionsInOp(op, expr => {
        if (!ir.isIrExpression(expr)) {
            return;
        }
        switch (expr.kind) {
            case ir.ExpressionKind.ReadVariable:
                variablesUsed.add(expr.xref);
                break;
            default:
                fences |= fencesForIrExpression(expr);
        }
    });
    return { fences, variablesUsed };
}
/**
 * Count the number of usages of each variable, being careful to track whether those usages are
 * local or remote.
 */
function countVariableUsages(op, varUsages, varRemoteUsage) {
    ir.visitExpressionsInOp(op, (expr, flags) => {
        if (!ir.isIrExpression(expr)) {
            return;
        }
        if (expr.kind !== ir.ExpressionKind.ReadVariable) {
            return;
        }
        const count = varUsages.get(expr.xref);
        if (count === undefined) {
            // This variable is declared outside the current scope of optimization.
            return;
        }
        varUsages.set(expr.xref, count + 1);
        if (flags & ir.VisitorContextFlag.InChildOperation) {
            varRemoteUsage.add(expr.xref);
        }
    });
}
/**
 * Remove usages of a variable in `op` from the `varUsages` tracking.
 */
function uncountVariableUsages(op, varUsages) {
    ir.visitExpressionsInOp(op, expr => {
        if (!ir.isIrExpression(expr)) {
            return;
        }
        if (expr.kind !== ir.ExpressionKind.ReadVariable) {
            return;
        }
        const count = varUsages.get(expr.xref);
        if (count === undefined) {
            // This variable is declared outside the current scope of optimization.
            return;
        }
        else if (count === 0) {
            throw new Error(`Inaccurate variable count: ${expr.xref} - found another read but count is already 0`);
        }
        varUsages.set(expr.xref, count - 1);
    });
}
/**
 * Checks whether it's safe to inline a variable across a particular operation.
 *
 * @param fences the fences of the operation which the inlining will cross
 * @param declFences the fences of the variable being inlined.
 */
function safeToInlinePastFences(fences, declFences) {
    if (fences & Fence.ViewContextWrite) {
        // It's not safe to inline context reads across context writes.
        if (declFences & Fence.ViewContextRead) {
            return false;
        }
    }
    else if (fences & Fence.ViewContextRead) {
        // It's not safe to inline context writes across context reads.
        if (declFences & Fence.ViewContextWrite) {
            return false;
        }
    }
    return true;
}
/**
 * Attempt to inline the initializer of a variable into a target operation's expressions.
 *
 * This may or may not be safe to do. For example, the variable could be read following the
 * execution of an expression with fences that don't permit the variable to be inlined across them.
 */
function tryInlineVariableInitializer(id, initializer, target, declFences) {
    // We use `ir.transformExpressionsInOp` to walk the expressions and inline the variable if
    // possible. Since this operation is callback-based, once inlining succeeds or fails we can't
    // "stop" the expression processing, and have to keep track of whether inlining has succeeded or
    // is no longer allowed.
    let inlined = false;
    let inliningAllowed = true;
    ir.transformExpressionsInOp(target, (expr, flags) => {
        if (!ir.isIrExpression(expr)) {
            return expr;
        }
        if (inlined || !inliningAllowed) {
            // Either the inlining has already succeeded, or we've passed a fence that disallows inlining
            // at this point, so don't try.
            return expr;
        }
        else if ((flags & ir.VisitorContextFlag.InChildOperation) && (declFences & Fence.ViewContextRead)) {
            // We cannot inline variables that are sensitive to the current context across operation
            // boundaries.
            return expr;
        }
        switch (expr.kind) {
            case ir.ExpressionKind.ReadVariable:
                if (expr.xref === id) {
                    // This is the usage site of the variable. Since nothing has disallowed inlining, it's
                    // safe to inline the initializer here.
                    inlined = true;
                    return initializer;
                }
                break;
            default:
                // For other types of `ir.Expression`s, whether inlining is allowed depends on their fences.
                const exprFences = fencesForIrExpression(expr);
                inliningAllowed = inliningAllowed && safeToInlinePastFences(exprFences, declFences);
                break;
        }
        return expr;
    }, ir.VisitorContextFlag.None);
    return inlined;
}
/**
 * Determines whether inlining of `decl` should be allowed in "conservative" mode.
 *
 * In conservative mode, inlining behavior is limited to those operations which the
 * `TemplateDefinitionBuilder` supported, with the goal of producing equivalent output.
 */
function allowConservativeInlining(decl, target) {
    // TODO(alxhub): understand exactly how TemplateDefinitionBuilder approaches inlining, and record
    // that behavior here.
    switch (decl.variable.kind) {
        case ir.SemanticVariableKind.Identifier:
            if (decl.initializer instanceof o.ReadVarExpr && decl.initializer.name === 'ctx') {
                // Although TemplateDefinitionBuilder is cautious about inlining, we still want to do so
                // when the variable is the context, to imitate its behavior with aliases in control flow
                // blocks. This quirky behavior will become dead code once compatibility mode is no longer
                // supported.
                return true;
            }
            return false;
        case ir.SemanticVariableKind.Context:
            // Context can only be inlined into other variables.
            return target.kind === ir.OpKind.Variable;
        default:
            return true;
    }
}
//# sourceMappingURL=data:application/json;base64,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