/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../../output/output_ast';
import { Identifiers as R3 } from '../r3_identifiers';
import { renderFlagCheckIfStmt } from './template';
import { CONTEXT_NAME, RENDER_FLAGS, TEMPORARY_NAME, temporaryAllocator } from './util';
/**
 * Translates query flags into `TQueryFlags` type in
 * packages/core/src/render3/interfaces/query.ts
 * @param query
 */
function toQueryFlags(query) {
    return ((query.descendants ? 1 /* QueryFlags.descendants */ : 0 /* QueryFlags.none */) |
        (query.static ? 2 /* QueryFlags.isStatic */ : 0 /* QueryFlags.none */) |
        (query.emitDistinctChangesOnly ? 4 /* QueryFlags.emitDistinctChangesOnly */ : 0 /* QueryFlags.none */));
}
export function getQueryPredicate(query, constantPool) {
    if (Array.isArray(query.predicate)) {
        let predicate = [];
        query.predicate.forEach((selector) => {
            // Each item in predicates array may contain strings with comma-separated refs
            // (for ex. 'ref, ref1, ..., refN'), thus we extract individual refs and store them
            // as separate array entities
            const selectors = selector.split(',').map((token) => o.literal(token.trim()));
            predicate.push(...selectors);
        });
        return constantPool.getConstLiteral(o.literalArr(predicate), true);
    }
    else {
        // The original predicate may have been wrapped in a `forwardRef()` call.
        switch (query.predicate.forwardRef) {
            case 0 /* ForwardRefHandling.None */:
            case 2 /* ForwardRefHandling.Unwrapped */:
                return query.predicate.expression;
            case 1 /* ForwardRefHandling.Wrapped */:
                return o.importExpr(R3.resolveForwardRef).callFn([query.predicate.expression]);
        }
    }
}
export function createQueryCreateCall(query, constantPool, queryTypeFns, prependParams) {
    const parameters = [];
    if (prependParams !== undefined) {
        parameters.push(...prependParams);
    }
    if (query.isSignal) {
        parameters.push(new o.ReadPropExpr(o.variable(CONTEXT_NAME), query.propertyName));
    }
    parameters.push(getQueryPredicate(query, constantPool), o.literal(toQueryFlags(query)));
    if (query.read) {
        parameters.push(query.read);
    }
    const queryCreateFn = query.isSignal ? queryTypeFns.signalBased : queryTypeFns.nonSignal;
    return o.importExpr(queryCreateFn).callFn(parameters);
}
const queryAdvancePlaceholder = Symbol('queryAdvancePlaceholder');
/**
 * Collapses query advance placeholders in a list of statements.
 *
 * This allows for less generated code because multiple sibling query advance
 * statements can be collapsed into a single call with the count as argument.
 *
 * e.g.
 *
 * ```ts
 *   bla();
 *   queryAdvance();
 *   queryAdvance();
 *   bla();
 * ```
 *
 *   --> will turn into
 *
 * ```
 *   bla();
 *   queryAdvance(2);
 *   bla();
 * ```
 */
function collapseAdvanceStatements(statements) {
    const result = [];
    let advanceCollapseCount = 0;
    const flushAdvanceCount = () => {
        if (advanceCollapseCount > 0) {
            result.unshift(o.importExpr(R3.queryAdvance)
                .callFn(advanceCollapseCount === 1 ? [] : [o.literal(advanceCollapseCount)])
                .toStmt());
            advanceCollapseCount = 0;
        }
    };
    // Iterate through statements in reverse and collapse advance placeholders.
    for (let i = statements.length - 1; i >= 0; i--) {
        const st = statements[i];
        if (st === queryAdvancePlaceholder) {
            advanceCollapseCount++;
        }
        else {
            flushAdvanceCount();
            result.unshift(st);
        }
    }
    flushAdvanceCount();
    return result;
}
// Define and update any view queries
export function createViewQueriesFunction(viewQueries, constantPool, name) {
    const createStatements = [];
    const updateStatements = [];
    const tempAllocator = temporaryAllocator(st => updateStatements.push(st), TEMPORARY_NAME);
    viewQueries.forEach((query) => {
        // creation call, e.g. r3.viewQuery(somePredicate, true) or
        //                r3.viewQuerySignal(ctx.prop, somePredicate, true);
        const queryDefinitionCall = createQueryCreateCall(query, constantPool, {
            signalBased: R3.viewQuerySignal,
            nonSignal: R3.viewQuery,
        });
        createStatements.push(queryDefinitionCall.toStmt());
        // Signal queries update lazily and we just advance the index.
        if (query.isSignal) {
            updateStatements.push(queryAdvancePlaceholder);
            return;
        }
        // update, e.g. (r3.queryRefresh(tmp = r3.loadQuery()) && (ctx.someDir = tmp));
        const temporary = tempAllocator();
        const getQueryList = o.importExpr(R3.loadQuery).callFn([]);
        const refresh = o.importExpr(R3.queryRefresh).callFn([temporary.set(getQueryList)]);
        const updateDirective = o.variable(CONTEXT_NAME)
            .prop(query.propertyName)
            .set(query.first ? temporary.prop('first') : temporary);
        updateStatements.push(refresh.and(updateDirective).toStmt());
    });
    const viewQueryFnName = name ? `${name}_Query` : null;
    return o.fn([new o.FnParam(RENDER_FLAGS, o.NUMBER_TYPE), new o.FnParam(CONTEXT_NAME, null)], [
        renderFlagCheckIfStmt(1 /* core.RenderFlags.Create */, createStatements),
        renderFlagCheckIfStmt(2 /* core.RenderFlags.Update */, collapseAdvanceStatements(updateStatements))
    ], o.INFERRED_TYPE, null, viewQueryFnName);
}
// Define and update any content queries
export function createContentQueriesFunction(queries, constantPool, name) {
    const createStatements = [];
    const updateStatements = [];
    const tempAllocator = temporaryAllocator(st => updateStatements.push(st), TEMPORARY_NAME);
    for (const query of queries) {
        // creation, e.g. r3.contentQuery(dirIndex, somePredicate, true, null) or
        //                r3.contentQuerySignal(dirIndex, propName, somePredicate, <flags>, <read>).
        createStatements.push(createQueryCreateCall(query, constantPool, { nonSignal: R3.contentQuery, signalBased: R3.contentQuerySignal }, 
        /* prependParams */ [o.variable('dirIndex')])
            .toStmt());
        // Signal queries update lazily and we just advance the index.
        if (query.isSignal) {
            updateStatements.push(queryAdvancePlaceholder);
            continue;
        }
        // update, e.g. (r3.queryRefresh(tmp = r3.loadQuery()) && (ctx.someDir = tmp));
        const temporary = tempAllocator();
        const getQueryList = o.importExpr(R3.loadQuery).callFn([]);
        const refresh = o.importExpr(R3.queryRefresh).callFn([temporary.set(getQueryList)]);
        const updateDirective = o.variable(CONTEXT_NAME)
            .prop(query.propertyName)
            .set(query.first ? temporary.prop('first') : temporary);
        updateStatements.push(refresh.and(updateDirective).toStmt());
    }
    const contentQueriesFnName = name ? `${name}_ContentQueries` : null;
    return o.fn([
        new o.FnParam(RENDER_FLAGS, o.NUMBER_TYPE), new o.FnParam(CONTEXT_NAME, null),
        new o.FnParam('dirIndex', null)
    ], [
        renderFlagCheckIfStmt(1 /* core.RenderFlags.Create */, createStatements),
        renderFlagCheckIfStmt(2 /* core.RenderFlags.Update */, collapseAdvanceStatements(updateStatements)),
    ], o.INFERRED_TYPE, null, contentQueriesFnName);
}
//# sourceMappingURL=data:application/json;base64,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