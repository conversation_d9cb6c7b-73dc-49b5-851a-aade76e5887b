/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export var R3TemplateDependencyKind;
(function (R3TemplateDependencyKind) {
    R3TemplateDependencyKind[R3TemplateDependencyKind["Directive"] = 0] = "Directive";
    R3TemplateDependencyKind[R3TemplateDependencyKind["Pipe"] = 1] = "Pipe";
    R3TemplateDependencyKind[R3TemplateDependencyKind["NgModule"] = 2] = "NgModule";
})(R3TemplateDependencyKind || (R3TemplateDependencyKind = {}));
//# sourceMappingURL=data:application/json;base64,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