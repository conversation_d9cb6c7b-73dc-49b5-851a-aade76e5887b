/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import * as o from '../output/output_ast';
import { Identifiers as R3 } from './r3_identifiers';
import { jitOnlyGuardedExpression, refsToArray } from './util';
import { DefinitionMap } from './view/util';
/**
 * How the selector scope of an NgModule (its declarations, imports, and exports) should be emitted
 * as a part of the NgModule definition.
 */
export var R3SelectorScopeMode;
(function (R3SelectorScopeMode) {
    /**
     * Emit the declarations inline into the module definition.
     *
     * This option is useful in certain contexts where it's known that JIT support is required. The
     * tradeoff here is that this emit style prevents directives and pipes from being tree-shaken if
     * they are unused, but the NgModule is used.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["Inline"] = 0] = "Inline";
    /**
     * Emit the declarations using a side effectful function call, `ɵɵsetNgModuleScope`, that is
     * guarded with the `ngJitMode` flag.
     *
     * This form of emit supports JIT and can be optimized away if the `ngJitMode` flag is set to
     * false, which allows unused directives and pipes to be tree-shaken.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["SideEffect"] = 1] = "SideEffect";
    /**
     * Don't generate selector scopes at all.
     *
     * This is useful for contexts where JIT support is known to be unnecessary.
     */
    R3SelectorScopeMode[R3SelectorScopeMode["Omit"] = 2] = "Omit";
})(R3SelectorScopeMode || (R3SelectorScopeMode = {}));
/**
 * The type of the NgModule meta data.
 * - Global: Used for full and partial compilation modes which mainly includes R3References.
 * - Local: Used for the local compilation mode which mainly includes the raw expressions as appears
 * in the NgModule decorator.
 */
export var R3NgModuleMetadataKind;
(function (R3NgModuleMetadataKind) {
    R3NgModuleMetadataKind[R3NgModuleMetadataKind["Global"] = 0] = "Global";
    R3NgModuleMetadataKind[R3NgModuleMetadataKind["Local"] = 1] = "Local";
})(R3NgModuleMetadataKind || (R3NgModuleMetadataKind = {}));
/**
 * Construct an `R3NgModuleDef` for the given `R3NgModuleMetadata`.
 */
export function compileNgModule(meta) {
    const statements = [];
    const definitionMap = new DefinitionMap();
    definitionMap.set('type', meta.type.value);
    // Assign bootstrap definition. In local compilation mode (i.e., for
    // `R3NgModuleMetadataKind.LOCAL`) we assign the bootstrap field using the runtime
    // `ɵɵsetNgModuleScope`.
    if (meta.kind === R3NgModuleMetadataKind.Global && meta.bootstrap.length > 0) {
        definitionMap.set('bootstrap', refsToArray(meta.bootstrap, meta.containsForwardDecls));
    }
    if (meta.selectorScopeMode === R3SelectorScopeMode.Inline) {
        // If requested to emit scope information inline, pass the `declarations`, `imports` and
        // `exports` to the `ɵɵdefineNgModule()` call directly.
        if (meta.declarations.length > 0) {
            definitionMap.set('declarations', refsToArray(meta.declarations, meta.containsForwardDecls));
        }
        if (meta.imports.length > 0) {
            definitionMap.set('imports', refsToArray(meta.imports, meta.containsForwardDecls));
        }
        if (meta.exports.length > 0) {
            definitionMap.set('exports', refsToArray(meta.exports, meta.containsForwardDecls));
        }
    }
    else if (meta.selectorScopeMode === R3SelectorScopeMode.SideEffect) {
        // In this mode, scope information is not passed into `ɵɵdefineNgModule` as it
        // would prevent tree-shaking of the declarations, imports and exports references. Instead, it's
        // patched onto the NgModule definition with a `ɵɵsetNgModuleScope` call that's guarded by the
        // `ngJitMode` flag.
        const setNgModuleScopeCall = generateSetNgModuleScopeCall(meta);
        if (setNgModuleScopeCall !== null) {
            statements.push(setNgModuleScopeCall);
        }
    }
    else {
        // Selector scope emit was not requested, so skip it.
    }
    if (meta.schemas !== null && meta.schemas.length > 0) {
        definitionMap.set('schemas', o.literalArr(meta.schemas.map(ref => ref.value)));
    }
    if (meta.id !== null) {
        definitionMap.set('id', meta.id);
        // Generate a side-effectful call to register this NgModule by its id, as per the semantics of
        // NgModule ids.
        statements.push(o.importExpr(R3.registerNgModuleType).callFn([meta.type.value, meta.id]).toStmt());
    }
    const expression = o.importExpr(R3.defineNgModule).callFn([definitionMap.toLiteralMap()], undefined, true);
    const type = createNgModuleType(meta);
    return { expression, type, statements };
}
/**
 * This function is used in JIT mode to generate the call to `ɵɵdefineNgModule()` from a call to
 * `ɵɵngDeclareNgModule()`.
 */
export function compileNgModuleDeclarationExpression(meta) {
    const definitionMap = new DefinitionMap();
    definitionMap.set('type', new o.WrappedNodeExpr(meta.type));
    if (meta.bootstrap !== undefined) {
        definitionMap.set('bootstrap', new o.WrappedNodeExpr(meta.bootstrap));
    }
    if (meta.declarations !== undefined) {
        definitionMap.set('declarations', new o.WrappedNodeExpr(meta.declarations));
    }
    if (meta.imports !== undefined) {
        definitionMap.set('imports', new o.WrappedNodeExpr(meta.imports));
    }
    if (meta.exports !== undefined) {
        definitionMap.set('exports', new o.WrappedNodeExpr(meta.exports));
    }
    if (meta.schemas !== undefined) {
        definitionMap.set('schemas', new o.WrappedNodeExpr(meta.schemas));
    }
    if (meta.id !== undefined) {
        definitionMap.set('id', new o.WrappedNodeExpr(meta.id));
    }
    return o.importExpr(R3.defineNgModule).callFn([definitionMap.toLiteralMap()]);
}
export function createNgModuleType(meta) {
    if (meta.kind === R3NgModuleMetadataKind.Local) {
        return new o.ExpressionType(meta.type.value);
    }
    const { type: moduleType, declarations, exports, imports, includeImportTypes, publicDeclarationTypes } = meta;
    return new o.ExpressionType(o.importExpr(R3.NgModuleDeclaration, [
        new o.ExpressionType(moduleType.type),
        publicDeclarationTypes === null ? tupleTypeOf(declarations) :
            tupleOfTypes(publicDeclarationTypes),
        includeImportTypes ? tupleTypeOf(imports) : o.NONE_TYPE,
        tupleTypeOf(exports),
    ]));
}
/**
 * Generates a function call to `ɵɵsetNgModuleScope` with all necessary information so that the
 * transitive module scope can be computed during runtime in JIT mode. This call is marked pure
 * such that the references to declarations, imports and exports may be elided causing these
 * symbols to become tree-shakeable.
 */
function generateSetNgModuleScopeCall(meta) {
    const scopeMap = new DefinitionMap();
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.declarations.length > 0) {
            scopeMap.set('declarations', refsToArray(meta.declarations, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.declarationsExpression) {
            scopeMap.set('declarations', meta.declarationsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.imports.length > 0) {
            scopeMap.set('imports', refsToArray(meta.imports, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.importsExpression) {
            scopeMap.set('imports', meta.importsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Global) {
        if (meta.exports.length > 0) {
            scopeMap.set('exports', refsToArray(meta.exports, meta.containsForwardDecls));
        }
    }
    else {
        if (meta.exportsExpression) {
            scopeMap.set('exports', meta.exportsExpression);
        }
    }
    if (meta.kind === R3NgModuleMetadataKind.Local && meta.bootstrapExpression) {
        scopeMap.set('bootstrap', meta.bootstrapExpression);
    }
    if (Object.keys(scopeMap.values).length === 0) {
        return null;
    }
    // setNgModuleScope(...)
    const fnCall = new o.InvokeFunctionExpr(
    /* fn */ o.importExpr(R3.setNgModuleScope), 
    /* args */ [meta.type.value, scopeMap.toLiteralMap()]);
    // (ngJitMode guard) && setNgModuleScope(...)
    const guardedCall = jitOnlyGuardedExpression(fnCall);
    // function() { (ngJitMode guard) && setNgModuleScope(...); }
    const iife = new o.FunctionExpr(
    /* params */ [], 
    /* statements */ [guardedCall.toStmt()]);
    // (function() { (ngJitMode guard) && setNgModuleScope(...); })()
    const iifeCall = new o.InvokeFunctionExpr(
    /* fn */ iife, 
    /* args */ []);
    return iifeCall.toStmt();
}
function tupleTypeOf(exp) {
    const types = exp.map(ref => o.typeofExpr(ref.type));
    return exp.length > 0 ? o.expressionType(o.literalArr(types)) : o.NONE_TYPE;
}
function tupleOfTypes(types) {
    const typeofTypes = types.map(type => o.typeofExpr(type));
    return types.length > 0 ? o.expressionType(o.literalArr(typeofTypes)) : o.NONE_TYPE;
}
//# sourceMappingURL=data:application/json;base64,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