/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export {};
//# sourceMappingURL=data:application/json;base64,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