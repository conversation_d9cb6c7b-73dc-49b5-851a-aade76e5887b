/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { inject } from '@angular/core';
/**
 * Maps an array of injectable classes with canMatch functions to an array of equivalent
 * `CanMatchFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanMatch(providers) {
    return providers.map((provider) => (...params) => inject(provider).canMatch(...params));
}
/**
 * Maps an array of injectable classes with canActivate functions to an array of equivalent
 * `CanActivateFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanActivate(providers) {
    return providers.map((provider) => (...params) => inject(provider).canActivate(...params));
}
/**
 * Maps an array of injectable classes with canActivateChild functions to an array of equivalent
 * `CanActivateChildFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanActivateChild(providers) {
    return providers.map((provider) => (...params) => inject(provider).canActivateChild(...params));
}
/**
 * Maps an array of injectable classes with canDeactivate functions to an array of equivalent
 * `CanDeactivateFn` for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='CanActivate'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToCanDeactivate(providers) {
    return providers.map((provider) => (...params) => inject(provider).canDeactivate(...params));
}
/**
 * Maps an injectable class with a resolve function to an equivalent `ResolveFn`
 * for use in a `Route` definition.
 *
 * Usage {@example router/utils/functional_guards.ts region='Resolve'}
 *
 * @publicApi
 * @see {@link Route}
 */
export function mapToResolve(provider) {
    return (...params) => inject(provider).resolve(...params);
}
//# sourceMappingURL=data:application/json;base64,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