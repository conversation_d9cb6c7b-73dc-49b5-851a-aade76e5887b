/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { map, mergeMap } from 'rxjs/operators';
import { recognize as recognizeFn } from '../recognize';
export function recognize(injector, configLoader, rootComponentType, config, serializer, paramsInheritanceStrategy) {
    return mergeMap((t) => recognizeFn(injector, configLoader, rootComponentType, config, t.extractedUrl, serializer, paramsInheritanceStrategy).pipe(map(({ state: targetSnapshot, tree: urlAfterRedirects }) => {
        return { ...t, targetSnapshot, urlAfterRedirects };
    })));
}
//# sourceMappingURL=data:application/json;base64,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