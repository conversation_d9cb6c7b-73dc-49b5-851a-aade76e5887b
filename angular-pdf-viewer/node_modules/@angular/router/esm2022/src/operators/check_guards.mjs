/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { runInInjectionContext } from '@angular/core';
import { concat, defer, from, of, pipe, } from 'rxjs';
import { concatMap, first, map, mergeMap, tap } from 'rxjs/operators';
import { ActivationStart, ChildActivationStart } from '../events';
import { redirectingNavigationError } from '../navigation_canceling_error';
import { isUrlTree } from '../url_tree';
import { wrapIntoObservable } from '../utils/collection';
import { getClosestRouteInjector } from '../utils/config';
import { getCanActivateChild, getTokenOrFunctionIdentity, } from '../utils/preactivation';
import { isBoolean, isCanActivate, isCanActivateChild, isCanDeactivate, isCanLoad, isCanMatch, } from '../utils/type_guards';
import { prioritizedGuardValue } from './prioritized_guard_value';
export function checkGuards(injector, forwardEvent) {
    return mergeMap((t) => {
        const { targetSnapshot, currentSnapshot, guards: { canActivateChecks, canDeactivateChecks }, } = t;
        if (canDeactivateChecks.length === 0 && canActivateChecks.length === 0) {
            return of({ ...t, guardsResult: true });
        }
        return runCanDeactivateChecks(canDeactivateChecks, targetSnapshot, currentSnapshot, injector).pipe(mergeMap((canDeactivate) => {
            return canDeactivate && isBoolean(canDeactivate)
                ? runCanActivateChecks(targetSnapshot, canActivateChecks, injector, forwardEvent)
                : of(canDeactivate);
        }), map((guardsResult) => ({ ...t, guardsResult })));
    });
}
function runCanDeactivateChecks(checks, futureRSS, currRSS, injector) {
    return from(checks).pipe(mergeMap((check) => runCanDeactivate(check.component, check.route, currRSS, futureRSS, injector)), first((result) => {
        return result !== true;
    }, true));
}
function runCanActivateChecks(futureSnapshot, checks, injector, forwardEvent) {
    return from(checks).pipe(concatMap((check) => {
        return concat(fireChildActivationStart(check.route.parent, forwardEvent), fireActivationStart(check.route, forwardEvent), runCanActivateChild(futureSnapshot, check.path, injector), runCanActivate(futureSnapshot, check.route, injector));
    }), first((result) => {
        return result !== true;
    }, true));
}
/**
 * This should fire off `ActivationStart` events for each route being activated at this
 * level.
 * In other words, if you're activating `a` and `b` below, `path` will contain the
 * `ActivatedRouteSnapshot`s for both and we will fire `ActivationStart` for both. Always
 * return
 * `true` so checks continue to run.
 */
function fireActivationStart(snapshot, forwardEvent) {
    if (snapshot !== null && forwardEvent) {
        forwardEvent(new ActivationStart(snapshot));
    }
    return of(true);
}
/**
 * This should fire off `ChildActivationStart` events for each route being activated at this
 * level.
 * In other words, if you're activating `a` and `b` below, `path` will contain the
 * `ActivatedRouteSnapshot`s for both and we will fire `ChildActivationStart` for both. Always
 * return
 * `true` so checks continue to run.
 */
function fireChildActivationStart(snapshot, forwardEvent) {
    if (snapshot !== null && forwardEvent) {
        forwardEvent(new ChildActivationStart(snapshot));
    }
    return of(true);
}
function runCanActivate(futureRSS, futureARS, injector) {
    const canActivate = futureARS.routeConfig ? futureARS.routeConfig.canActivate : null;
    if (!canActivate || canActivate.length === 0)
        return of(true);
    const canActivateObservables = canActivate.map((canActivate) => {
        return defer(() => {
            const closestInjector = getClosestRouteInjector(futureARS) ?? injector;
            const guard = getTokenOrFunctionIdentity(canActivate, closestInjector);
            const guardVal = isCanActivate(guard)
                ? guard.canActivate(futureARS, futureRSS)
                : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));
            return wrapIntoObservable(guardVal).pipe(first());
        });
    });
    return of(canActivateObservables).pipe(prioritizedGuardValue());
}
function runCanActivateChild(futureRSS, path, injector) {
    const futureARS = path[path.length - 1];
    const canActivateChildGuards = path
        .slice(0, path.length - 1)
        .reverse()
        .map((p) => getCanActivateChild(p))
        .filter((_) => _ !== null);
    const canActivateChildGuardsMapped = canActivateChildGuards.map((d) => {
        return defer(() => {
            const guardsMapped = d.guards.map((canActivateChild) => {
                const closestInjector = getClosestRouteInjector(d.node) ?? injector;
                const guard = getTokenOrFunctionIdentity(canActivateChild, closestInjector);
                const guardVal = isCanActivateChild(guard)
                    ? guard.canActivateChild(futureARS, futureRSS)
                    : runInInjectionContext(closestInjector, () => guard(futureARS, futureRSS));
                return wrapIntoObservable(guardVal).pipe(first());
            });
            return of(guardsMapped).pipe(prioritizedGuardValue());
        });
    });
    return of(canActivateChildGuardsMapped).pipe(prioritizedGuardValue());
}
function runCanDeactivate(component, currARS, currRSS, futureRSS, injector) {
    const canDeactivate = currARS && currARS.routeConfig ? currARS.routeConfig.canDeactivate : null;
    if (!canDeactivate || canDeactivate.length === 0)
        return of(true);
    const canDeactivateObservables = canDeactivate.map((c) => {
        const closestInjector = getClosestRouteInjector(currARS) ?? injector;
        const guard = getTokenOrFunctionIdentity(c, closestInjector);
        const guardVal = isCanDeactivate(guard)
            ? guard.canDeactivate(component, currARS, currRSS, futureRSS)
            : runInInjectionContext(closestInjector, () => guard(component, currARS, currRSS, futureRSS));
        return wrapIntoObservable(guardVal).pipe(first());
    });
    return of(canDeactivateObservables).pipe(prioritizedGuardValue());
}
export function runCanLoadGuards(injector, route, segments, urlSerializer) {
    const canLoad = route.canLoad;
    if (canLoad === undefined || canLoad.length === 0) {
        return of(true);
    }
    const canLoadObservables = canLoad.map((injectionToken) => {
        const guard = getTokenOrFunctionIdentity(injectionToken, injector);
        const guardVal = isCanLoad(guard)
            ? guard.canLoad(route, segments)
            : runInInjectionContext(injector, () => guard(route, segments));
        return wrapIntoObservable(guardVal);
    });
    return of(canLoadObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));
}
function redirectIfUrlTree(urlSerializer) {
    return pipe(tap((result) => {
        if (!isUrlTree(result))
            return;
        throw redirectingNavigationError(urlSerializer, result);
    }), map((result) => result === true));
}
export function runCanMatchGuards(injector, route, segments, urlSerializer) {
    const canMatch = route.canMatch;
    if (!canMatch || canMatch.length === 0)
        return of(true);
    const canMatchObservables = canMatch.map((injectionToken) => {
        const guard = getTokenOrFunctionIdentity(injectionToken, injector);
        const guardVal = isCanMatch(guard)
            ? guard.canMatch(route, segments)
            : runInInjectionContext(injector, () => guard(route, segments));
        return wrapIntoObservable(guardVal);
    });
    return of(canMatchObservables).pipe(prioritizedGuardValue(), redirectIfUrlTree(urlSerializer));
}
//# sourceMappingURL=data:application/json;base64,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