{"version": 3, "file": "signals.mjs", "sources": ["../../../../../../../packages/core/primitives/signals/src/equality.ts", "../../../../../../../packages/core/primitives/signals/src/graph.ts", "../../../../../../../packages/core/primitives/signals/src/computed.ts", "../../../../../../../packages/core/primitives/signals/src/errors.ts", "../../../../../../../packages/core/primitives/signals/src/signal.ts", "../../../../../../../packages/core/primitives/signals/src/watch.ts", "../../../../../../../packages/core/primitives/signals/src/weak_ref.ts"], "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n/**\n * A comparison function which can determine if two values are equal.\n */\nexport type ValueEqualityFn<T> = (a: T, b: T) => boolean;\n\n/**\n * The default equality function used for `signal` and `computed`, which uses referential equality.\n */\nexport function defaultEquals<T>(a: T, b: T) {\n  return Object.is(a, b);\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean|undefined;\n\n\n/**\n * The currently active consumer `ReactiveNode`, if running code in a reactive context.\n *\n * Change this via `setActiveConsumer`.\n */\nlet activeConsumer: ReactiveNode|null = null;\nlet inNotificationPhase = false;\n\ntype Version = number&{__brand: 'Version'};\n\n/**\n * Global epoch counter. Incremented whenever a source signal is set.\n */\nlet epoch: Version = 1 as Version;\n\n/**\n * Symbol used to tell `Signal`s apart from other functions.\n *\n * This can be used to auto-unwrap signals in various cases, or to auto-wrap non-signal values.\n */\nexport const SIGNAL = /* @__PURE__ */ Symbol('SIGNAL');\n\nexport function setActiveConsumer(consumer: ReactiveNode|null): ReactiveNode|null {\n  const prev = activeConsumer;\n  activeConsumer = consumer;\n  return prev;\n}\n\nexport function getActiveConsumer(): ReactiveNode|null {\n  return activeConsumer;\n}\n\nexport function isInNotificationPhase(): boolean {\n  return inNotificationPhase;\n}\n\nexport interface Reactive {\n  [SIGNAL]: ReactiveNode;\n}\n\nexport function isReactive(value: unknown): value is Reactive {\n  return (value as Partial<Reactive>)[SIGNAL] !== undefined;\n}\n\nexport const REACTIVE_NODE: ReactiveNode = {\n  version: 0 as Version,\n  lastCleanEpoch: 0 as Version,\n  dirty: false,\n  producerNode: undefined,\n  producerLastReadVersion: undefined,\n  producerIndexOfThis: undefined,\n  nextProducerIndex: 0,\n  liveConsumerNode: undefined,\n  liveConsumerIndexOfThis: undefined,\n  consumerAllowSignalWrites: false,\n  consumerIsAlwaysLive: false,\n  producerMustRecompute: () => false,\n  producerRecomputeValue: () => {},\n  consumerMarkedDirty: () => {},\n  consumerOnSignalRead: () => {},\n};\n\n/**\n * A producer and/or consumer which participates in the reactive graph.\n *\n * Producer `ReactiveNode`s which are accessed when a consumer `ReactiveNode` is the\n * `activeConsumer` are tracked as dependencies of that consumer.\n *\n * Certain consumers are also tracked as \"live\" consumers and create edges in the other direction,\n * from producer to consumer. These edges are used to propagate change notifications when a\n * producer's value is updated.\n *\n * A `ReactiveNode` may be both a producer and consumer.\n */\nexport interface ReactiveNode {\n  /**\n   * Version of the value that this node produces.\n   *\n   * This is incremented whenever a new value is produced by this node which is not equal to the\n   * previous value (by whatever definition of equality is in use).\n   */\n  version: Version;\n\n  /**\n   * Epoch at which this node is verified to be clean.\n   *\n   * This allows skipping of some polling operations in the case where no signals have been set\n   * since this node was last read.\n   */\n  lastCleanEpoch: Version;\n\n  /**\n   * Whether this node (in its consumer capacity) is dirty.\n   *\n   * Only live consumers become dirty, when receiving a change notification from a dependency\n   * producer.\n   */\n  dirty: boolean;\n\n  /**\n   * Producers which are dependencies of this consumer.\n   *\n   * Uses the same indices as the `producerLastReadVersion` and `producerIndexOfThis` arrays.\n   */\n  producerNode: ReactiveNode[]|undefined;\n\n  /**\n   * `Version` of the value last read by a given producer.\n   *\n   * Uses the same indices as the `producerNode` and `producerIndexOfThis` arrays.\n   */\n  producerLastReadVersion: Version[]|undefined;\n\n  /**\n   * Index of `this` (consumer) in each producer's `liveConsumers` array.\n   *\n   * This value is only meaningful if this node is live (`liveConsumers.length > 0`). Otherwise\n   * these indices are stale.\n   *\n   * Uses the same indices as the `producerNode` and `producerLastReadVersion` arrays.\n   */\n  producerIndexOfThis: number[]|undefined;\n\n  /**\n   * Index into the producer arrays that the next dependency of this node as a consumer will use.\n   *\n   * This index is zeroed before this node as a consumer begins executing. When a producer is read,\n   * it gets inserted into the producers arrays at this index. There may be an existing dependency\n   * in this location which may or may not match the incoming producer, depending on whether the\n   * same producers were read in the same order as the last computation.\n   */\n  nextProducerIndex: number;\n\n  /**\n   * Array of consumers of this producer that are \"live\" (they require push notifications).\n   *\n   * `liveConsumerNode.length` is effectively our reference count for this node.\n   */\n  liveConsumerNode: ReactiveNode[]|undefined;\n\n  /**\n   * Index of `this` (producer) in each consumer's `producerNode` array.\n   *\n   * Uses the same indices as the `liveConsumerNode` array.\n   */\n  liveConsumerIndexOfThis: number[]|undefined;\n\n  /**\n   * Whether writes to signals are allowed when this consumer is the `activeConsumer`.\n   *\n   * This is used to enforce guardrails such as preventing writes to writable signals in the\n   * computation function of computed signals, which is supposed to be pure.\n   */\n  consumerAllowSignalWrites: boolean;\n\n  readonly consumerIsAlwaysLive: boolean;\n\n  /**\n   * Tracks whether producers need to recompute their value independently of the reactive graph (for\n   * example, if no initial value has been computed).\n   */\n  producerMustRecompute(node: unknown): boolean;\n  producerRecomputeValue(node: unknown): void;\n  consumerMarkedDirty(node: unknown): void;\n\n  /**\n   * Called when a signal is read within this consumer.\n   */\n  consumerOnSignalRead(node: unknown): void;\n}\n\ninterface ConsumerNode extends ReactiveNode {\n  producerNode: NonNullable<ReactiveNode['producerNode']>;\n  producerIndexOfThis: NonNullable<ReactiveNode['producerIndexOfThis']>;\n  producerLastReadVersion: NonNullable<ReactiveNode['producerLastReadVersion']>;\n}\n\ninterface ProducerNode extends ReactiveNode {\n  liveConsumerNode: NonNullable<ReactiveNode['liveConsumerNode']>;\n  liveConsumerIndexOfThis: NonNullable<ReactiveNode['liveConsumerIndexOfThis']>;\n}\n\n/**\n * Called by implementations when a producer's signal is read.\n */\nexport function producerAccessed(node: ReactiveNode): void {\n  if (inNotificationPhase) {\n    throw new Error(\n        typeof ngDevMode !== 'undefined' && ngDevMode ?\n            `Assertion error: signal read during notification phase` :\n            '');\n  }\n\n  if (activeConsumer === null) {\n    // Accessed outside of a reactive context, so nothing to record.\n    return;\n  }\n\n  activeConsumer.consumerOnSignalRead(node);\n\n  // This producer is the `idx`th dependency of `activeConsumer`.\n  const idx = activeConsumer.nextProducerIndex++;\n\n  assertConsumerNode(activeConsumer);\n\n  if (idx < activeConsumer.producerNode.length && activeConsumer.producerNode[idx] !== node) {\n    // There's been a change in producers since the last execution of `activeConsumer`.\n    // `activeConsumer.producerNode[idx]` holds a stale dependency which will be be removed and\n    // replaced with `this`.\n    //\n    // If `activeConsumer` isn't live, then this is a no-op, since we can replace the producer in\n    // `activeConsumer.producerNode` directly. However, if `activeConsumer` is live, then we need\n    // to remove it from the stale producer's `liveConsumer`s.\n    if (consumerIsLive(activeConsumer)) {\n      const staleProducer = activeConsumer.producerNode[idx];\n      producerRemoveLiveConsumerAtIndex(staleProducer, activeConsumer.producerIndexOfThis[idx]);\n\n      // At this point, the only record of `staleProducer` is the reference at\n      // `activeConsumer.producerNode[idx]` which will be overwritten below.\n    }\n  }\n\n  if (activeConsumer.producerNode[idx] !== node) {\n    // We're a new dependency of the consumer (at `idx`).\n    activeConsumer.producerNode[idx] = node;\n\n    // If the active consumer is live, then add it as a live consumer. If not, then use 0 as a\n    // placeholder value.\n    activeConsumer.producerIndexOfThis[idx] =\n        consumerIsLive(activeConsumer) ? producerAddLiveConsumer(node, activeConsumer, idx) : 0;\n  }\n  activeConsumer.producerLastReadVersion[idx] = node.version;\n}\n\n/**\n * Increment the global epoch counter.\n *\n * Called by source producers (that is, not computeds) whenever their values change.\n */\nexport function producerIncrementEpoch(): void {\n  epoch++;\n}\n\n/**\n * Ensure this producer's `version` is up-to-date.\n */\nexport function producerUpdateValueVersion(node: ReactiveNode): void {\n  if (consumerIsLive(node) && !node.dirty) {\n    // A live consumer will be marked dirty by producers, so a clean state means that its version\n    // is guaranteed to be up-to-date.\n    return;\n  }\n\n  if (!node.dirty && node.lastCleanEpoch === epoch) {\n    // Even non-live consumers can skip polling if they previously found themselves to be clean at\n    // the current epoch, since their dependencies could not possibly have changed (such a change\n    // would've increased the epoch).\n    return;\n  }\n\n  if (!node.producerMustRecompute(node) && !consumerPollProducersForChange(node)) {\n    // None of our producers report a change since the last time they were read, so no\n    // recomputation of our value is necessary, and we can consider ourselves clean.\n    node.dirty = false;\n    node.lastCleanEpoch = epoch;\n    return;\n  }\n\n  node.producerRecomputeValue(node);\n\n  // After recomputing the value, we're no longer dirty.\n  node.dirty = false;\n  node.lastCleanEpoch = epoch;\n}\n\n/**\n * Propagate a dirty notification to live consumers of this producer.\n */\nexport function producerNotifyConsumers(node: ReactiveNode): void {\n  if (node.liveConsumerNode === undefined) {\n    return;\n  }\n\n  // Prevent signal reads when we're updating the graph\n  const prev = inNotificationPhase;\n  inNotificationPhase = true;\n  try {\n    for (const consumer of node.liveConsumerNode) {\n      if (!consumer.dirty) {\n        consumerMarkDirty(consumer);\n      }\n    }\n  } finally {\n    inNotificationPhase = prev;\n  }\n}\n\n/**\n * Whether this `ReactiveNode` in its producer capacity is currently allowed to initiate updates,\n * based on the current consumer context.\n */\nexport function producerUpdatesAllowed(): boolean {\n  return activeConsumer?.consumerAllowSignalWrites !== false;\n}\n\nexport function consumerMarkDirty(node: ReactiveNode): void {\n  node.dirty = true;\n  producerNotifyConsumers(node);\n  node.consumerMarkedDirty?.(node);\n}\n\n/**\n * Prepare this consumer to run a computation in its reactive context.\n *\n * Must be called by subclasses which represent reactive computations, before those computations\n * begin.\n */\nexport function consumerBeforeComputation(node: ReactiveNode|null): ReactiveNode|null {\n  node && (node.nextProducerIndex = 0);\n  return setActiveConsumer(node);\n}\n\n/**\n * Finalize this consumer's state after a reactive computation has run.\n *\n * Must be called by subclasses which represent reactive computations, after those computations\n * have finished.\n */\nexport function consumerAfterComputation(\n    node: ReactiveNode|null, prevConsumer: ReactiveNode|null): void {\n  setActiveConsumer(prevConsumer);\n\n  if (!node || node.producerNode === undefined || node.producerIndexOfThis === undefined ||\n      node.producerLastReadVersion === undefined) {\n    return;\n  }\n\n  if (consumerIsLive(node)) {\n    // For live consumers, we need to remove the producer -> consumer edge for any stale producers\n    // which weren't dependencies after the recomputation.\n    for (let i = node.nextProducerIndex; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Truncate the producer tracking arrays.\n  // Perf note: this is essentially truncating the length to `node.nextProducerIndex`, but\n  // benchmarking has shown that individual pop operations are faster.\n  while (node.producerNode.length > node.nextProducerIndex) {\n    node.producerNode.pop();\n    node.producerLastReadVersion.pop();\n    node.producerIndexOfThis.pop();\n  }\n}\n\n/**\n * Determine whether this consumer has any dependencies which have changed since the last time\n * they were read.\n */\nexport function consumerPollProducersForChange(node: ReactiveNode): boolean {\n  assertConsumerNode(node);\n\n  // Poll producers for change.\n  for (let i = 0; i < node.producerNode.length; i++) {\n    const producer = node.producerNode[i];\n    const seenVersion = node.producerLastReadVersion[i];\n\n    // First check the versions. A mismatch means that the producer's value is known to have\n    // changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n\n    // The producer's version is the same as the last time we read it, but it might itself be\n    // stale. Force the producer to recompute its version (calculating a new value if necessary).\n    producerUpdateValueVersion(producer);\n\n    // Now when we do this check, `producer.version` is guaranteed to be up to date, so if the\n    // versions still match then it has not changed since the last time we read it.\n    if (seenVersion !== producer.version) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Disconnect this consumer from the graph.\n */\nexport function consumerDestroy(node: ReactiveNode): void {\n  assertConsumerNode(node);\n  if (consumerIsLive(node)) {\n    // Drop all connections from the graph to this node.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Truncate all the arrays to drop all connection from this node to the graph.\n  node.producerNode.length = node.producerLastReadVersion.length = node.producerIndexOfThis.length =\n      0;\n  if (node.liveConsumerNode) {\n    node.liveConsumerNode.length = node.liveConsumerIndexOfThis!.length = 0;\n  }\n}\n\n/**\n * Add `consumer` as a live consumer of this node.\n *\n * Note that this operation is potentially transitive. If this node becomes live, then it becomes\n * a live consumer of all of its current producers.\n */\nfunction producerAddLiveConsumer(\n    node: ReactiveNode, consumer: ReactiveNode, indexOfThis: number): number {\n  assertProducerNode(node);\n  assertConsumerNode(node);\n  if (node.liveConsumerNode.length === 0) {\n    // When going from 0 to 1 live consumers, we become a live consumer to our producers.\n    for (let i = 0; i < node.producerNode.length; i++) {\n      node.producerIndexOfThis[i] = producerAddLiveConsumer(node.producerNode[i], node, i);\n    }\n  }\n  node.liveConsumerIndexOfThis.push(indexOfThis);\n  return node.liveConsumerNode.push(consumer) - 1;\n}\n\n/**\n * Remove the live consumer at `idx`.\n */\nfunction producerRemoveLiveConsumerAtIndex(node: ReactiveNode, idx: number): void {\n  assertProducerNode(node);\n  assertConsumerNode(node);\n\n  if (typeof ngDevMode !== 'undefined' && ngDevMode && idx >= node.liveConsumerNode.length) {\n    throw new Error(`Assertion error: active consumer index ${idx} is out of bounds of ${\n        node.liveConsumerNode.length} consumers)`);\n  }\n\n  if (node.liveConsumerNode.length === 1) {\n    // When removing the last live consumer, we will no longer be live. We need to remove\n    // ourselves from our producers' tracking (which may cause consumer-producers to lose\n    // liveness as well).\n    for (let i = 0; i < node.producerNode.length; i++) {\n      producerRemoveLiveConsumerAtIndex(node.producerNode[i], node.producerIndexOfThis[i]);\n    }\n  }\n\n  // Move the last value of `liveConsumers` into `idx`. Note that if there's only a single\n  // live consumer, this is a no-op.\n  const lastIdx = node.liveConsumerNode.length - 1;\n  node.liveConsumerNode[idx] = node.liveConsumerNode[lastIdx];\n  node.liveConsumerIndexOfThis[idx] = node.liveConsumerIndexOfThis[lastIdx];\n\n  // Truncate the array.\n  node.liveConsumerNode.length--;\n  node.liveConsumerIndexOfThis.length--;\n\n  // If the index is still valid, then we need to fix the index pointer from the producer to this\n  // consumer, and update it from `lastIdx` to `idx` (accounting for the move above).\n  if (idx < node.liveConsumerNode.length) {\n    const idxProducer = node.liveConsumerIndexOfThis[idx];\n    const consumer = node.liveConsumerNode[idx];\n    assertConsumerNode(consumer);\n    consumer.producerIndexOfThis[idxProducer] = idx;\n  }\n}\n\nfunction consumerIsLive(node: ReactiveNode): boolean {\n  return node.consumerIsAlwaysLive || (node?.liveConsumerNode?.length ?? 0) > 0;\n}\n\n\nfunction assertConsumerNode(node: ReactiveNode): asserts node is ConsumerNode {\n  node.producerNode ??= [];\n  node.producerIndexOfThis ??= [];\n  node.producerLastReadVersion ??= [];\n}\n\nfunction assertProducerNode(node: ReactiveNode): asserts node is ProducerNode {\n  node.liveConsumerNode ??= [];\n  node.liveConsumerIndexOfThis ??= [];\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {defaultEquals, ValueEqualityFn} from './equality';\nimport {consumerAfterComputation, consumerBeforeComputation, producerAccessed, producerUpdateValueVersion, REACTIVE_NODE, ReactiveNode, SIGNAL} from './graph';\n\n\n/**\n * A computation, which derives a value from a declarative reactive expression.\n *\n * `Computed`s are both producers and consumers of reactivity.\n */\nexport interface ComputedNode<T> extends ReactiveNode {\n  /**\n   * Current value of the computation, or one of the sentinel values above (`UNSET`, `COMPUTING`,\n   * `ERROR`).\n   */\n  value: T;\n\n  /**\n   * If `value` is `ERRORED`, the error caught from the last computation attempt which will\n   * be re-thrown.\n   */\n  error: unknown;\n\n  /**\n   * The computation function which will produce a new value.\n   */\n  computation: () => T;\n\n  equal: ValueEqualityFn<T>;\n}\n\nexport type ComputedGetter<T> = (() => T)&{\n  [SIGNAL]: ComputedNode<T>;\n};\n\n/**\n * Create a computed signal which derives a reactive value from an expression.\n */\nexport function createComputed<T>(computation: () => T): ComputedGetter<T> {\n  const node: ComputedNode<T> = Object.create(COMPUTED_NODE);\n  node.computation = computation;\n\n  const computed = () => {\n    // Check if the value needs updating before returning it.\n    producerUpdateValueVersion(node);\n\n    // Record that someone looked at this signal.\n    producerAccessed(node);\n\n    if (node.value === ERRORED) {\n      throw node.error;\n    }\n\n    return node.value;\n  };\n  (computed as ComputedGetter<T>)[SIGNAL] = node;\n  return computed as unknown as ComputedGetter<T>;\n}\n\n/**\n * A dedicated symbol used before a computed value has been calculated for the first time.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst UNSET: any = /* @__PURE__ */ Symbol('UNSET');\n\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * is in progress. Used to detect cycles in computation chains.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst COMPUTING: any = /* @__PURE__ */ Symbol('COMPUTING');\n\n/**\n * A dedicated symbol used in place of a computed signal value to indicate that a given computation\n * failed. The thrown error is cached until the computation gets dirty again.\n * Explicitly typed as `any` so we can use it as signal's value.\n */\nconst ERRORED: any = /* @__PURE__ */ Symbol('ERRORED');\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst COMPUTED_NODE = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    value: UNSET,\n    dirty: true,\n    error: null,\n    equal: defaultEquals,\n\n    producerMustRecompute(node: ComputedNode<unknown>): boolean {\n      // Force a recomputation if there's no current value, or if the current value is in the\n      // process of being calculated (which should throw an error).\n      return node.value === UNSET || node.value === COMPUTING;\n    },\n\n    producerRecomputeValue(node: ComputedNode<unknown>): void {\n      if (node.value === COMPUTING) {\n        // Our computation somehow led to a cyclic read of itself.\n        throw new Error('Detected cycle in computations.');\n      }\n\n      const oldValue = node.value;\n      node.value = COMPUTING;\n\n      const prevConsumer = consumerBeforeComputation(node);\n      let newValue: unknown;\n      try {\n        newValue = node.computation();\n      } catch (err) {\n        newValue = ERRORED;\n        node.error = err;\n      } finally {\n        consumerAfterComputation(node, prevConsumer);\n      }\n\n      if (oldValue !== UNSET && oldValue !== ERRORED && newValue !== ERRORED &&\n          node.equal(oldValue, newValue)) {\n        // No change to `valueVersion` - old and new values are\n        // semantically equivalent.\n        node.value = oldValue;\n        return;\n      }\n\n      node.value = newValue;\n      node.version++;\n    },\n  };\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nfunction defaultThrowError(): never {\n  throw new Error();\n}\n\nlet throwInvalidWriteToSignalErrorFn = defaultThrowError;\n\nexport function throwInvalidWriteToSignalError() {\n  throwInvalidWriteToSignalErrorFn();\n}\n\nexport function setThrowInvalidWriteToSignalError(fn: () => never): void {\n  throwInvalidWriteToSignalErrorFn = fn;\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {defaultEquals, ValueEqualityFn} from './equality';\nimport {throwInvalidWriteToSignalError} from './errors';\nimport {producerAccessed, producerIncrementEpoch, producerNotifyConsumers, producerUpdatesAllowed, REACTIVE_NODE, ReactiveNode, SIGNAL} from './graph';\n\n// Required as the signals library is in a separate package, so we need to explicitly ensure the\n// global `ngDevMode` type is defined.\ndeclare const ngDevMode: boolean|undefined;\n\n/**\n * If set, called after `WritableSignal`s are updated.\n *\n * This hook can be used to achieve various effects, such as running effects synchronously as part\n * of setting a signal.\n */\nlet postSignalSetFn: (() => void)|null = null;\n\nexport interface SignalNode<T> extends ReactiveNode {\n  value: T;\n  equal: ValueEqualityFn<T>;\n}\n\nexport type SignalBaseGetter<T> = (() => T)&{readonly[SIGNAL]: unknown};\n\n// Note: Closure *requires* this to be an `interface` and not a type, which is why the\n// `SignalBaseGetter` type exists to provide the correct shape.\nexport interface SignalGetter<T> extends SignalBaseGetter<T> {\n  readonly[SIGNAL]: SignalNode<T>;\n}\n\n/**\n * Create a `Signal` that can be set or updated directly.\n */\nexport function createSignal<T>(initialValue: T): SignalGetter<T> {\n  const node: SignalNode<T> = Object.create(SIGNAL_NODE);\n  node.value = initialValue;\n  const getter = (() => {\n                   producerAccessed(node);\n                   return node.value;\n                 }) as SignalGetter<T>;\n  (getter as any)[SIGNAL] = node;\n  return getter;\n}\n\nexport function setPostSignalSetFn(fn: (() => void)|null): (() => void)|null {\n  const prev = postSignalSetFn;\n  postSignalSetFn = fn;\n  return prev;\n}\n\nexport function signalGetFn<T>(this: SignalNode<T>): T {\n  producerAccessed(this);\n  return this.value;\n}\n\nexport function signalSetFn<T>(node: SignalNode<T>, newValue: T) {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n\n  if (!node.equal(node.value, newValue)) {\n    node.value = newValue;\n    signalValueChanged(node);\n  }\n}\n\nexport function signalUpdateFn<T>(node: SignalNode<T>, updater: (value: T) => T): void {\n  if (!producerUpdatesAllowed()) {\n    throwInvalidWriteToSignalError();\n  }\n\n  signalSetFn(node, updater(node.value));\n}\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nexport const SIGNAL_NODE: SignalNode<unknown> = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    equal: defaultEquals,\n    value: undefined,\n  };\n})();\n\nfunction signalValueChanged<T>(node: SignalNode<T>): void {\n  node.version++;\n  producerIncrementEpoch();\n  producerNotifyConsumers(node);\n  postSignalSetFn?.();\n}\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nimport {consumerAfterComputation, consumerBeforeComputation, consumerDestroy, consumerMarkDirty, consumerPollProducersForChange, isInNotificationPhase, REACTIVE_NODE, ReactiveNode, SIGNAL} from './graph';\n\n/**\n * A cleanup function that can be optionally registered from the watch logic. If registered, the\n * cleanup logic runs before the next watch execution.\n */\nexport type WatchCleanupFn = () => void;\n\n/**\n * A callback passed to the watch function that makes it possible to register cleanup logic.\n */\nexport type WatchCleanupRegisterFn = (cleanupFn: WatchCleanupFn) => void;\n\nexport interface Watch {\n  notify(): void;\n\n  /**\n   * Execute the reactive expression in the context of this `Watch` consumer.\n   *\n   * Should be called by the user scheduling algorithm when the provided\n   * `schedule` hook is called by `Watch`.\n   */\n  run(): void;\n\n  cleanup(): void;\n\n  /**\n   * Destroy the watcher:\n   * - disconnect it from the reactive graph;\n   * - mark it as destroyed so subsequent run and notify operations are noop.\n   */\n  destroy(): void;\n\n  [SIGNAL]: WatchNode;\n}\nexport interface WatchNode extends ReactiveNode {\n  hasRun: boolean;\n  fn: ((onCleanup: WatchCleanupRegisterFn) => void)|null;\n  schedule: ((watch: Watch) => void)|null;\n  cleanupFn: WatchCleanupFn;\n  ref: Watch;\n}\n\nexport function createWatch(\n    fn: (onCleanup: WatchCleanupRegisterFn) => void, schedule: (watch: Watch) => void,\n    allowSignalWrites: boolean): Watch {\n  const node: WatchNode = Object.create(WATCH_NODE);\n  if (allowSignalWrites) {\n    node.consumerAllowSignalWrites = true;\n  }\n\n  node.fn = fn;\n  node.schedule = schedule;\n\n  const registerOnCleanup = (cleanupFn: WatchCleanupFn) => {\n    node.cleanupFn = cleanupFn;\n  };\n\n  function isWatchNodeDestroyed(node: WatchNode) {\n    return node.fn === null && node.schedule === null;\n  }\n\n  function destroyWatchNode(node: WatchNode) {\n    if (!isWatchNodeDestroyed(node)) {\n      consumerDestroy(node);  // disconnect watcher from the reactive graph\n      node.cleanupFn();\n\n      // nullify references to the integration functions to mark node as destroyed\n      node.fn = null;\n      node.schedule = null;\n      node.cleanupFn = NOOP_CLEANUP_FN;\n    }\n  }\n\n  const run = () => {\n    if (node.fn === null) {\n      // trying to run a destroyed watch is noop\n      return;\n    }\n\n    if (isInNotificationPhase()) {\n      throw new Error(`Schedulers cannot synchronously execute watches while scheduling.`);\n    }\n\n    node.dirty = false;\n    if (node.hasRun && !consumerPollProducersForChange(node)) {\n      return;\n    }\n    node.hasRun = true;\n\n    const prevConsumer = consumerBeforeComputation(node);\n    try {\n      node.cleanupFn();\n      node.cleanupFn = NOOP_CLEANUP_FN;\n      node.fn(registerOnCleanup);\n    } finally {\n      consumerAfterComputation(node, prevConsumer);\n    }\n  };\n\n  node.ref = {\n    notify: () => consumerMarkDirty(node),\n    run,\n    cleanup: () => node.cleanupFn(),\n    destroy: () => destroyWatchNode(node),\n    [SIGNAL]: node,\n  };\n\n  return node.ref;\n}\n\nconst NOOP_CLEANUP_FN: WatchCleanupFn = () => {};\n\n// Note: Using an IIFE here to ensure that the spread assignment is not considered\n// a side-effect, ending up preserving `COMPUTED_NODE` and `REACTIVE_NODE`.\n// TODO: remove when https://github.com/evanw/esbuild/issues/3392 is resolved.\nconst WATCH_NODE: Partial<WatchNode> = /* @__PURE__ */ (() => {\n  return {\n    ...REACTIVE_NODE,\n    consumerIsAlwaysLive: true,\n    consumerAllowSignalWrites: false,\n    consumerMarkedDirty: (node: WatchNode) => {\n      if (node.schedule !== null) {\n        node.schedule(node.ref);\n      }\n    },\n    hasRun: false,\n    cleanupFn: NOOP_CLEANUP_FN,\n  };\n})();\n", "/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\n\nexport function setAlternateWeakRefImpl(impl: unknown) {\n  // TODO: remove this function\n}\n"], "names": [], "mappings": ";;;;;;AAaA;;AAEG;AACa,SAAA,aAAa,CAAI,CAAI,EAAE,CAAI,EAAA;IACzC,OAAO,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;AACzB;;ACLA;;;;AAIG;AACH,IAAI,cAAc,GAAsB,IAAI,CAAC;AAC7C,IAAI,mBAAmB,GAAG,KAAK,CAAC;AAIhC;;AAEG;AACH,IAAI,KAAK,GAAY,CAAY,CAAC;AAElC;;;;AAIG;AACU,MAAA,MAAM,mBAAmB,MAAM,CAAC,QAAQ,EAAE;AAEjD,SAAU,iBAAiB,CAAC,QAA2B,EAAA;IAC3D,MAAM,IAAI,GAAG,cAAc,CAAC;IAC5B,cAAc,GAAG,QAAQ,CAAC;AAC1B,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;SAEe,iBAAiB,GAAA;AAC/B,IAAA,OAAO,cAAc,CAAC;AACxB,CAAC;SAEe,qBAAqB,GAAA;AACnC,IAAA,OAAO,mBAAmB,CAAC;AAC7B,CAAC;AAMK,SAAU,UAAU,CAAC,KAAc,EAAA;AACvC,IAAA,OAAQ,KAA2B,CAAC,MAAM,CAAC,KAAK,SAAS,CAAC;AAC5D,CAAC;AAEY,MAAA,aAAa,GAAiB;AACzC,IAAA,OAAO,EAAE,CAAY;AACrB,IAAA,cAAc,EAAE,CAAY;AAC5B,IAAA,KAAK,EAAE,KAAK;AACZ,IAAA,YAAY,EAAE,SAAS;AACvB,IAAA,uBAAuB,EAAE,SAAS;AAClC,IAAA,mBAAmB,EAAE,SAAS;AAC9B,IAAA,iBAAiB,EAAE,CAAC;AACpB,IAAA,gBAAgB,EAAE,SAAS;AAC3B,IAAA,uBAAuB,EAAE,SAAS;AAClC,IAAA,yBAAyB,EAAE,KAAK;AAChC,IAAA,oBAAoB,EAAE,KAAK;AAC3B,IAAA,qBAAqB,EAAE,MAAM,KAAK;AAClC,IAAA,sBAAsB,EAAE,MAAK,GAAG;AAChC,IAAA,mBAAmB,EAAE,MAAK,GAAG;AAC7B,IAAA,oBAAoB,EAAE,MAAK,GAAG;EAC9B;AA0HF;;AAEG;AACG,SAAU,gBAAgB,CAAC,IAAkB,EAAA;IACjD,IAAI,mBAAmB,EAAE;QACvB,MAAM,IAAI,KAAK,CACX,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS;AACzC,YAAA,CAAA,sDAAA,CAAwD;AACxD,YAAA,EAAE,CAAC,CAAC;KACb;AAED,IAAA,IAAI,cAAc,KAAK,IAAI,EAAE;;QAE3B,OAAO;KACR;AAED,IAAA,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;;AAG1C,IAAA,MAAM,GAAG,GAAG,cAAc,CAAC,iBAAiB,EAAE,CAAC;IAE/C,kBAAkB,CAAC,cAAc,CAAC,CAAC;AAEnC,IAAA,IAAI,GAAG,GAAG,cAAc,CAAC,YAAY,CAAC,MAAM,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;;;;;;;;AAQzF,QAAA,IAAI,cAAc,CAAC,cAAc,CAAC,EAAE;YAClC,MAAM,aAAa,GAAG,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC;YACvD,iCAAiC,CAAC,aAAa,EAAE,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,CAAC;;;SAI3F;KACF;IAED,IAAI,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,KAAK,IAAI,EAAE;;AAE7C,QAAA,cAAc,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC;;;AAIxC,QAAA,cAAc,CAAC,mBAAmB,CAAC,GAAG,CAAC;AACnC,YAAA,cAAc,CAAC,cAAc,CAAC,GAAG,uBAAuB,CAAC,IAAI,EAAE,cAAc,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC;KAC7F;IACD,cAAc,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,OAAO,CAAC;AAC7D,CAAC;AAED;;;;AAIG;SACa,sBAAsB,GAAA;AACpC,IAAA,KAAK,EAAE,CAAC;AACV,CAAC;AAED;;AAEG;AACG,SAAU,0BAA0B,CAAC,IAAkB,EAAA;IAC3D,IAAI,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;;;QAGvC,OAAO;KACR;IAED,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,cAAc,KAAK,KAAK,EAAE;;;;QAIhD,OAAO;KACR;AAED,IAAA,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;;;AAG9E,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,QAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;QAC5B,OAAO;KACR;AAED,IAAA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,CAAC;;AAGlC,IAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;AACnB,IAAA,IAAI,CAAC,cAAc,GAAG,KAAK,CAAC;AAC9B,CAAC;AAED;;AAEG;AACG,SAAU,uBAAuB,CAAC,IAAkB,EAAA;AACxD,IAAA,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;QACvC,OAAO;KACR;;IAGD,MAAM,IAAI,GAAG,mBAAmB,CAAC;IACjC,mBAAmB,GAAG,IAAI,CAAC;AAC3B,IAAA,IAAI;AACF,QAAA,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,gBAAgB,EAAE;AAC5C,YAAA,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE;gBACnB,iBAAiB,CAAC,QAAQ,CAAC,CAAC;aAC7B;SACF;KACF;YAAS;QACR,mBAAmB,GAAG,IAAI,CAAC;KAC5B;AACH,CAAC;AAED;;;AAGG;SACa,sBAAsB,GAAA;AACpC,IAAA,OAAO,cAAc,EAAE,yBAAyB,KAAK,KAAK,CAAC;AAC7D,CAAC;AAEK,SAAU,iBAAiB,CAAC,IAAkB,EAAA;AAClD,IAAA,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAClB,uBAAuB,CAAC,IAAI,CAAC,CAAC;AAC9B,IAAA,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,CAAC;AACnC,CAAC;AAED;;;;;AAKG;AACG,SAAU,yBAAyB,CAAC,IAAuB,EAAA;IAC/D,IAAI,KAAK,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,CAAC;AACrC,IAAA,OAAO,iBAAiB,CAAC,IAAI,CAAC,CAAC;AACjC,CAAC;AAED;;;;;AAKG;AACa,SAAA,wBAAwB,CACpC,IAAuB,EAAE,YAA+B,EAAA;IAC1D,iBAAiB,CAAC,YAAY,CAAC,CAAC;AAEhC,IAAA,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS;AAClF,QAAA,IAAI,CAAC,uBAAuB,KAAK,SAAS,EAAE;QAC9C,OAAO;KACR;AAED,IAAA,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;;AAGxB,QAAA,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACtE,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF;KACF;;;;IAKD,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,iBAAiB,EAAE;AACxD,QAAA,IAAI,CAAC,YAAY,CAAC,GAAG,EAAE,CAAC;AACxB,QAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,EAAE,CAAC;AACnC,QAAA,IAAI,CAAC,mBAAmB,CAAC,GAAG,EAAE,CAAC;KAChC;AACH,CAAC;AAED;;;AAGG;AACG,SAAU,8BAA8B,CAAC,IAAkB,EAAA;IAC/D,kBAAkB,CAAC,IAAI,CAAC,CAAC;;AAGzB,IAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QACjD,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAC;;;AAIpD,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,OAAO,EAAE;AACpC,YAAA,OAAO,IAAI,CAAC;SACb;;;QAID,0BAA0B,CAAC,QAAQ,CAAC,CAAC;;;AAIrC,QAAA,IAAI,WAAW,KAAK,QAAQ,CAAC,OAAO,EAAE;AACpC,YAAA,OAAO,IAAI,CAAC;SACb;KACF;AAED,IAAA,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;AAEG;AACG,SAAU,eAAe,CAAC,IAAkB,EAAA;IAChD,kBAAkB,CAAC,IAAI,CAAC,CAAC;AACzB,IAAA,IAAI,cAAc,CAAC,IAAI,CAAC,EAAE;;AAExB,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF;KACF;;AAGD,IAAA,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAuB,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,CAAC,MAAM;AAC5F,QAAA,CAAC,CAAC;AACN,IAAA,IAAI,IAAI,CAAC,gBAAgB,EAAE;AACzB,QAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,IAAI,CAAC,uBAAwB,CAAC,MAAM,GAAG,CAAC,CAAC;KACzE;AACH,CAAC;AAED;;;;;AAKG;AACH,SAAS,uBAAuB,CAC5B,IAAkB,EAAE,QAAsB,EAAE,WAAmB,EAAA;IACjE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACzB,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACzB,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;;AAEtC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,uBAAuB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;SACtF;KACF;AACD,IAAA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IAC/C,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;AAClD,CAAC;AAED;;AAEG;AACH,SAAS,iCAAiC,CAAC,IAAkB,EAAE,GAAW,EAAA;IACxE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IACzB,kBAAkB,CAAC,IAAI,CAAC,CAAC;AAEzB,IAAA,IAAI,OAAO,SAAS,KAAK,WAAW,IAAI,SAAS,IAAI,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;AACxF,QAAA,MAAM,IAAI,KAAK,CAAC,CAAA,uCAAA,EAA0C,GAAG,CAAA,qBAAA,EACzD,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAA,WAAA,CAAa,CAAC,CAAC;KAChD;IAED,IAAI,IAAI,CAAC,gBAAgB,CAAC,MAAM,KAAK,CAAC,EAAE;;;;AAItC,QAAA,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACjD,YAAA,iCAAiC,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC,mBAAmB,CAAC,CAAC,CAAC,CAAC,CAAC;SACtF;KACF;;;IAID,MAAM,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,GAAG,CAAC,CAAC;AACjD,IAAA,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;AAC5D,IAAA,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC;;AAG1E,IAAA,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE,CAAC;AAC/B,IAAA,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,CAAC;;;IAItC,IAAI,GAAG,GAAG,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;QACtC,MAAM,WAAW,GAAG,IAAI,CAAC,uBAAuB,CAAC,GAAG,CAAC,CAAC;QACtD,MAAM,QAAQ,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAC5C,kBAAkB,CAAC,QAAQ,CAAC,CAAC;AAC7B,QAAA,QAAQ,CAAC,mBAAmB,CAAC,WAAW,CAAC,GAAG,GAAG,CAAC;KACjD;AACH,CAAC;AAED,SAAS,cAAc,CAAC,IAAkB,EAAA;AACxC,IAAA,OAAO,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,EAAE,gBAAgB,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;AAChF,CAAC;AAGD,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,IAAI,CAAC,YAAY,KAAK,EAAE,CAAC;AACzB,IAAA,IAAI,CAAC,mBAAmB,KAAK,EAAE,CAAC;AAChC,IAAA,IAAI,CAAC,uBAAuB,KAAK,EAAE,CAAC;AACtC,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAkB,EAAA;AAC5C,IAAA,IAAI,CAAC,gBAAgB,KAAK,EAAE,CAAC;AAC7B,IAAA,IAAI,CAAC,uBAAuB,KAAK,EAAE,CAAC;AACtC;;ACrcA;;AAEG;AACG,SAAU,cAAc,CAAI,WAAoB,EAAA;IACpD,MAAM,IAAI,GAAoB,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;AAC3D,IAAA,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;IAE/B,MAAM,QAAQ,GAAG,MAAK;;QAEpB,0BAA0B,CAAC,IAAI,CAAC,CAAC;;QAGjC,gBAAgB,CAAC,IAAI,CAAC,CAAC;AAEvB,QAAA,IAAI,IAAI,CAAC,KAAK,KAAK,OAAO,EAAE;YAC1B,MAAM,IAAI,CAAC,KAAK,CAAC;SAClB;QAED,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,KAAC,CAAC;AACD,IAAA,QAA8B,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC/C,IAAA,OAAO,QAAwC,CAAC;AAClD,CAAC;AAED;;;AAGG;AACH,MAAM,KAAK,mBAAwB,MAAM,CAAC,OAAO,CAAC,CAAC;AAEnD;;;;AAIG;AACH,MAAM,SAAS,mBAAwB,MAAM,CAAC,WAAW,CAAC,CAAC;AAE3D;;;;AAIG;AACH,MAAM,OAAO,mBAAwB,MAAM,CAAC,SAAS,CAAC,CAAC;AAEvD;AACA;AACA;AACA,MAAM,aAAa,mBAAmB,CAAC,MAAK;IAC1C,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,KAAK,EAAE,KAAK;AACZ,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,IAAI;AACX,QAAA,KAAK,EAAE,aAAa;AAEpB,QAAA,qBAAqB,CAAC,IAA2B,EAAA;;;YAG/C,OAAO,IAAI,CAAC,KAAK,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,CAAC;SACzD;AAED,QAAA,sBAAsB,CAAC,IAA2B,EAAA;AAChD,YAAA,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;;AAE5B,gBAAA,MAAM,IAAI,KAAK,CAAC,iCAAiC,CAAC,CAAC;aACpD;AAED,YAAA,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC;AAC5B,YAAA,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;AAEvB,YAAA,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;AACrD,YAAA,IAAI,QAAiB,CAAC;AACtB,YAAA,IAAI;AACF,gBAAA,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC;aAC/B;YAAC,OAAO,GAAG,EAAE;gBACZ,QAAQ,GAAG,OAAO,CAAC;AACnB,gBAAA,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC;aAClB;oBAAS;AACR,gBAAA,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;aAC9C;YAED,IAAI,QAAQ,KAAK,KAAK,IAAI,QAAQ,KAAK,OAAO,IAAI,QAAQ,KAAK,OAAO;gBAClE,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,QAAQ,CAAC,EAAE;;;AAGlC,gBAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;gBACtB,OAAO;aACR;AAED,YAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;YACtB,IAAI,CAAC,OAAO,EAAE,CAAC;SAChB;KACF,CAAC;AACJ,CAAC,GAAG;;AC/HJ,SAAS,iBAAiB,GAAA;IACxB,MAAM,IAAI,KAAK,EAAE,CAAC;AACpB,CAAC;AAED,IAAI,gCAAgC,GAAG,iBAAiB,CAAC;SAEzC,8BAA8B,GAAA;AAC5C,IAAA,gCAAgC,EAAE,CAAC;AACrC,CAAC;AAEK,SAAU,iCAAiC,CAAC,EAAe,EAAA;IAC/D,gCAAgC,GAAG,EAAE,CAAC;AACxC;;ACJA;;;;;AAKG;AACH,IAAI,eAAe,GAAsB,IAAI,CAAC;AAe9C;;AAEG;AACG,SAAU,YAAY,CAAI,YAAe,EAAA;IAC7C,MAAM,IAAI,GAAkB,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC;AACvD,IAAA,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;AAC1B,IAAA,MAAM,MAAM,IAAI,MAAK;QACJ,gBAAgB,CAAC,IAAI,CAAC,CAAC;QACvB,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,KAAC,CAAoB,CAAC;AACpC,IAAA,MAAc,CAAC,MAAM,CAAC,GAAG,IAAI,CAAC;AAC/B,IAAA,OAAO,MAAM,CAAC;AAChB,CAAC;AAEK,SAAU,kBAAkB,CAAC,EAAqB,EAAA;IACtD,MAAM,IAAI,GAAG,eAAe,CAAC;IAC7B,eAAe,GAAG,EAAE,CAAC;AACrB,IAAA,OAAO,IAAI,CAAC;AACd,CAAC;SAEe,WAAW,GAAA;IACzB,gBAAgB,CAAC,IAAI,CAAC,CAAC;IACvB,OAAO,IAAI,CAAC,KAAK,CAAC;AACpB,CAAC;AAEe,SAAA,WAAW,CAAI,IAAmB,EAAE,QAAW,EAAA;AAC7D,IAAA,IAAI,CAAC,sBAAsB,EAAE,EAAE;AAC7B,QAAA,8BAA8B,EAAE,CAAC;KAClC;AAED,IAAA,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;AACrC,QAAA,IAAI,CAAC,KAAK,GAAG,QAAQ,CAAC;QACtB,kBAAkB,CAAC,IAAI,CAAC,CAAC;KAC1B;AACH,CAAC;AAEe,SAAA,cAAc,CAAI,IAAmB,EAAE,OAAwB,EAAA;AAC7E,IAAA,IAAI,CAAC,sBAAsB,EAAE,EAAE;AAC7B,QAAA,8BAA8B,EAAE,CAAC;KAClC;IAED,WAAW,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;AACzC,CAAC;AAED;AACA;AACA;MACa,WAAW,mBAAwC,CAAC,MAAK;IACpE,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,KAAK,EAAE,aAAa;AACpB,QAAA,KAAK,EAAE,SAAS;KACjB,CAAC;AACJ,CAAC,IAAI;AAEL,SAAS,kBAAkB,CAAI,IAAmB,EAAA;IAChD,IAAI,CAAC,OAAO,EAAE,CAAC;AACf,IAAA,sBAAsB,EAAE,CAAC;IACzB,uBAAuB,CAAC,IAAI,CAAC,CAAC;IAC9B,eAAe,IAAI,CAAC;AACtB;;SC9CgB,WAAW,CACvB,EAA+C,EAAE,QAAgC,EACjF,iBAA0B,EAAA;IAC5B,MAAM,IAAI,GAAc,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC;IAClD,IAAI,iBAAiB,EAAE;AACrB,QAAA,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC;KACvC;AAED,IAAA,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC;AACb,IAAA,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAEzB,IAAA,MAAM,iBAAiB,GAAG,CAAC,SAAyB,KAAI;AACtD,QAAA,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;AAC7B,KAAC,CAAC;IAEF,SAAS,oBAAoB,CAAC,IAAe,EAAA;QAC3C,OAAO,IAAI,CAAC,EAAE,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;KACnD;IAED,SAAS,gBAAgB,CAAC,IAAe,EAAA;AACvC,QAAA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,EAAE;AAC/B,YAAA,eAAe,CAAC,IAAI,CAAC,CAAC;YACtB,IAAI,CAAC,SAAS,EAAE,CAAC;;AAGjB,YAAA,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC;AACf,YAAA,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;AACrB,YAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;SAClC;KACF;IAED,MAAM,GAAG,GAAG,MAAK;AACf,QAAA,IAAI,IAAI,CAAC,EAAE,KAAK,IAAI,EAAE;;YAEpB,OAAO;SACR;QAED,IAAI,qBAAqB,EAAE,EAAE;AAC3B,YAAA,MAAM,IAAI,KAAK,CAAC,CAAA,iEAAA,CAAmE,CAAC,CAAC;SACtF;AAED,QAAA,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,EAAE;YACxD,OAAO;SACR;AACD,QAAA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC;AAEnB,QAAA,MAAM,YAAY,GAAG,yBAAyB,CAAC,IAAI,CAAC,CAAC;AACrD,QAAA,IAAI;YACF,IAAI,CAAC,SAAS,EAAE,CAAC;AACjB,YAAA,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACjC,YAAA,IAAI,CAAC,EAAE,CAAC,iBAAiB,CAAC,CAAC;SAC5B;gBAAS;AACR,YAAA,wBAAwB,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;SAC9C;AACH,KAAC,CAAC;IAEF,IAAI,CAAC,GAAG,GAAG;AACT,QAAA,MAAM,EAAE,MAAM,iBAAiB,CAAC,IAAI,CAAC;QACrC,GAAG;AACH,QAAA,OAAO,EAAE,MAAM,IAAI,CAAC,SAAS,EAAE;AAC/B,QAAA,OAAO,EAAE,MAAM,gBAAgB,CAAC,IAAI,CAAC;QACrC,CAAC,MAAM,GAAG,IAAI;KACf,CAAC;IAEF,OAAO,IAAI,CAAC,GAAG,CAAC;AAClB,CAAC;AAED,MAAM,eAAe,GAAmB,MAAK,GAAG,CAAC;AAEjD;AACA;AACA;AACA,MAAM,UAAU,mBAAuC,CAAC,MAAK;IAC3D,OAAO;AACL,QAAA,GAAG,aAAa;AAChB,QAAA,oBAAoB,EAAE,IAAI;AAC1B,QAAA,yBAAyB,EAAE,KAAK;AAChC,QAAA,mBAAmB,EAAE,CAAC,IAAe,KAAI;AACvC,YAAA,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,EAAE;AAC1B,gBAAA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACzB;SACF;AACD,QAAA,MAAM,EAAE,KAAK;AACb,QAAA,SAAS,EAAE,eAAe;KAC3B,CAAC;AACJ,CAAC,GAAG;;ACjIE,SAAU,uBAAuB,CAAC,IAAa,EAAA;;AAErD;;;;"}