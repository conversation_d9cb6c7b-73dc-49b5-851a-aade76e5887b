/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertInInjectionContext, assertNotInReactiveContext, computed, DestroyRef, inject, signal, ɵRuntimeError } from '@angular/core';
/**
 * Get the current value of an `Observable` as a reactive `Signal`.
 *
 * `toSignal` returns a `Signal` which provides synchronous reactive access to values produced
 * by the given `Observable`, by subscribing to that `Observable`. The returned `Signal` will always
 * have the most recent value emitted by the subscription, and will throw an error if the
 * `Observable` errors.
 *
 * With `requireSync` set to `true`, `toSignal` will assert that the `Observable` produces a value
 * immediately upon subscription. No `initialValue` is needed in this case, and the returned signal
 * does not include an `undefined` type.
 *
 * By default, the subscription will be automatically cleaned up when the current [injection
 * context](/guide/dependency-injection-context) is destroyed. For example, when `toSignal` is
 * called during the construction of a component, the subscription will be cleaned up when the
 * component is destroyed. If an injection context is not available, an explicit `Injector` can be
 * passed instead.
 *
 * If the subscription should persist until the `Observable` itself completes, the `manualCleanup`
 * option can be specified instead, which disables the automatic subscription teardown. No injection
 * context is needed in this configuration as well.
 *
 * @developerPreview
 */
export function toSignal(source, options) {
    ngDevMode &&
        assertNotInReactiveContext(toSignal, 'Invoking `toSignal` causes new subscriptions every time. ' +
            'Consider moving `toSignal` outside of the reactive context and read the signal value where needed.');
    const requiresCleanup = !options?.manualCleanup;
    requiresCleanup && !options?.injector && assertInInjectionContext(toSignal);
    const cleanupRef = requiresCleanup ? options?.injector?.get(DestroyRef) ?? inject(DestroyRef) : null;
    // Note: T is the Observable value type, and U is the initial value type. They don't have to be
    // the same - the returned signal gives values of type `T`.
    let state;
    if (options?.requireSync) {
        // Initially the signal is in a `NoValue` state.
        state = signal({ kind: 0 /* StateKind.NoValue */ });
    }
    else {
        // If an initial value was passed, use it. Otherwise, use `undefined` as the initial value.
        state = signal({ kind: 1 /* StateKind.Value */, value: options?.initialValue });
    }
    // Note: This code cannot run inside a reactive context (see assertion above). If we'd support
    // this, we would subscribe to the observable outside of the current reactive context, avoiding
    // that side-effect signal reads/writes are attribute to the current consumer. The current
    // consumer only needs to be notified when the `state` signal changes through the observable
    // subscription. Additional context (related to async pipe):
    // https://github.com/angular/angular/pull/50522.
    const sub = source.subscribe({
        next: value => state.set({ kind: 1 /* StateKind.Value */, value }),
        error: error => {
            if (options?.rejectErrors) {
                // Kick the error back to RxJS. It will be caught and rethrown in a macrotask, which causes
                // the error to end up as an uncaught exception.
                throw error;
            }
            state.set({ kind: 2 /* StateKind.Error */, error });
        },
        // Completion of the Observable is meaningless to the signal. Signals don't have a concept of
        // "complete".
    });
    if (ngDevMode && options?.requireSync && state().kind === 0 /* StateKind.NoValue */) {
        throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');
    }
    // Unsubscribe when the current context is destroyed, if requested.
    cleanupRef?.onDestroy(sub.unsubscribe.bind(sub));
    // The actual returned signal is a `computed` of the `State` signal, which maps the various states
    // to either values or errors.
    return computed(() => {
        const current = state();
        switch (current.kind) {
            case 1 /* StateKind.Value */:
                return current.value;
            case 2 /* StateKind.Error */:
                throw current.error;
            case 0 /* StateKind.NoValue */:
                // This shouldn't really happen because the error is thrown on creation.
                // TODO(alxhub): use a RuntimeError when we finalize the error semantics
                throw new ɵRuntimeError(601 /* ɵRuntimeErrorCode.REQUIRE_SYNC_WITHOUT_SYNC_EMIT */, '`toSignal()` called with `requireSync` but `Observable` did not emit synchronously.');
        }
    });
}
//# sourceMappingURL=data:application/json;base64,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