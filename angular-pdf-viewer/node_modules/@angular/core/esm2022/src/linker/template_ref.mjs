/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getCurrentTNode, getLView } from '../render3/state';
import { createAndRenderEmbeddedLView } from '../render3/view_manipulation';
import { ViewRef as R3_ViewRef } from '../render3/view_ref';
import { assertDefined } from '../util/assert';
import { createElementRef } from './element_ref';
/**
 * Represents an embedded template that can be used to instantiate embedded views.
 * To instantiate embedded views based on a template, use the `ViewContainerRef`
 * method `createEmbeddedView()`.
 *
 * Access a `TemplateRef` instance by placing a directive on an `<ng-template>`
 * element (or directive prefixed with `*`). The `TemplateRef` for the embedded view
 * is injected into the constructor of the directive,
 * using the `TemplateRef` token.
 *
 * You can also use a `Query` to find a `TemplateRef` associated with
 * a component or a directive.
 *
 * @see {@link ViewContainerRef}
 * @see [Navigate the Component Tree with DI](guide/dependency-injection-navtree)
 *
 * @publicApi
 */
export class TemplateRef {
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = injectTemplateRef; }
}
const ViewEngineTemplateRef = TemplateRef;
// TODO(alxhub): combine interface and implementation. Currently this is challenging since something
// in g3 depends on them being separate.
const R3TemplateRef = class TemplateRef extends ViewEngineTemplateRef {
    constructor(_declarationLView, _declarationTContainer, elementRef) {
        super();
        this._declarationLView = _declarationLView;
        this._declarationTContainer = _declarationTContainer;
        this.elementRef = elementRef;
    }
    /**
     * Returns an `ssrId` associated with a TView, which was used to
     * create this instance of the `TemplateRef`.
     *
     * @internal
     */
    get ssrId() {
        return this._declarationTContainer.tView?.ssrId || null;
    }
    createEmbeddedView(context, injector) {
        return this.createEmbeddedViewImpl(context, injector);
    }
    /**
     * @internal
     */
    createEmbeddedViewImpl(context, injector, dehydratedView) {
        const embeddedLView = createAndRenderEmbeddedLView(this._declarationLView, this._declarationTContainer, context, { embeddedViewInjector: injector, dehydratedView });
        return new R3_ViewRef(embeddedLView);
    }
};
/**
 * Creates a TemplateRef given a node.
 *
 * @returns The TemplateRef instance to use
 */
export function injectTemplateRef() {
    return createTemplateRef(getCurrentTNode(), getLView());
}
/**
 * Creates a TemplateRef and stores it on the injector.
 *
 * @param hostTNode The node on which a TemplateRef is requested
 * @param hostLView The `LView` to which the node belongs
 * @returns The TemplateRef instance or null if we can't create a TemplateRef on a given node type
 */
export function createTemplateRef(hostTNode, hostLView) {
    if (hostTNode.type & 4 /* TNodeType.Container */) {
        ngDevMode && assertDefined(hostTNode.tView, 'TView must be allocated');
        return new R3TemplateRef(hostLView, hostTNode, createElementRef(hostTNode, hostLView));
    }
    return null;
}
//# sourceMappingURL=data:application/json;base64,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