/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export var FactoryTarget;
(function (FactoryTarget) {
    FactoryTarget[FactoryTarget["Directive"] = 0] = "Directive";
    FactoryTarget[FactoryTarget["Component"] = 1] = "Component";
    FactoryTarget[FactoryTarget["Injectable"] = 2] = "Injectable";
    FactoryTarget[FactoryTarget["Pipe"] = 3] = "Pipe";
    FactoryTarget[FactoryTarget["NgModule"] = 4] = "NgModule";
})(FactoryTarget || (FactoryTarget = {}));
export var R3TemplateDependencyKind;
(function (R3TemplateDependencyKind) {
    R3TemplateDependencyKind[R3TemplateDependencyKind["Directive"] = 0] = "Directive";
    R3TemplateDependencyKind[R3TemplateDependencyKind["Pipe"] = 1] = "Pipe";
    R3TemplateDependencyKind[R3TemplateDependencyKind["NgModule"] = 2] = "NgModule";
})(R3TemplateDependencyKind || (R3TemplateDependencyKind = {}));
export var ViewEncapsulation;
(function (ViewEncapsulation) {
    ViewEncapsulation[ViewEncapsulation["Emulated"] = 0] = "Emulated";
    // Historically the 1 value was for `Native` encapsulation which has been removed as of v11.
    ViewEncapsulation[ViewEncapsulation["None"] = 2] = "None";
    ViewEncapsulation[ViewEncapsulation["ShadowDom"] = 3] = "ShadowDom";
})(ViewEncapsulation || (ViewEncapsulation = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29tcGlsZXJfZmFjYWRlX2ludGVyZmFjZS5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2NvbXBpbGVyL2NvbXBpbGVyX2ZhY2FkZV9pbnRlcmZhY2UudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBMkVILE1BQU0sQ0FBTixJQUFZLGFBTVg7QUFORCxXQUFZLGFBQWE7SUFDdkIsMkRBQWEsQ0FBQTtJQUNiLDJEQUFhLENBQUE7SUFDYiw2REFBYyxDQUFBO0lBQ2QsaURBQVEsQ0FBQTtJQUNSLHlEQUFZLENBQUE7QUFDZCxDQUFDLEVBTlcsYUFBYSxLQUFiLGFBQWEsUUFNeEI7QUFrTEQsTUFBTSxDQUFOLElBQVksd0JBSVg7QUFKRCxXQUFZLHdCQUF3QjtJQUNsQyxpRkFBYSxDQUFBO0lBQ2IsdUVBQVEsQ0FBQTtJQUNSLCtFQUFZLENBQUE7QUFDZCxDQUFDLEVBSlcsd0JBQXdCLEtBQXhCLHdCQUF3QixRQUluQztBQThCRCxNQUFNLENBQU4sSUFBWSxpQkFLWDtBQUxELFdBQVksaUJBQWlCO0lBQzNCLGlFQUFZLENBQUE7SUFDWiw0RkFBNEY7SUFDNUYseURBQVEsQ0FBQTtJQUNSLG1FQUFhLENBQUE7QUFDZixDQUFDLEVBTFcsaUJBQWlCLEtBQWpCLGlCQUFpQixRQUs1QiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKipcbiAqIEEgc2V0IG9mIGludGVyZmFjZXMgd2hpY2ggYXJlIHNoYXJlZCBiZXR3ZWVuIGBAYW5ndWxhci9jb3JlYCBhbmQgYEBhbmd1bGFyL2NvbXBpbGVyYCB0byBhbGxvd1xuICogZm9yIGxhdGUgYmluZGluZyBvZiBgQGFuZ3VsYXIvY29tcGlsZXJgIGZvciBKSVQgcHVycG9zZXMuXG4gKlxuICogVGhpcyBmaWxlIGhhcyB0d28gY29waWVzLiBQbGVhc2UgZW5zdXJlIHRoYXQgdGhleSBhcmUgaW4gc3luYzpcbiAqICAtIHBhY2thZ2VzL2NvbXBpbGVyL3NyYy9jb21waWxlcl9mYWNhZGVfaW50ZXJmYWNlLnRzICAgICAgICAgIChtYWluKVxuICogIC0gcGFja2FnZXMvY29yZS9zcmMvY29tcGlsZXIvY29tcGlsZXJfZmFjYWRlX2ludGVyZmFjZS50cyAgICAgKHJlcGxpY2EpXG4gKlxuICogUGxlYXNlIGVuc3VyZSB0aGF0IHRoZSB0d28gZmlsZXMgYXJlIGluIHN5bmMgdXNpbmcgdGhpcyBjb21tYW5kOlxuICogYGBgXG4gKiBjcCBwYWNrYWdlcy9jb21waWxlci9zcmMvY29tcGlsZXJfZmFjYWRlX2ludGVyZmFjZS50cyBcXFxuICogICAgcGFja2FnZXMvY29yZS9zcmMvY29tcGlsZXIvY29tcGlsZXJfZmFjYWRlX2ludGVyZmFjZS50c1xuICogYGBgXG4gKi9cblxuZXhwb3J0IGludGVyZmFjZSBFeHBvcnRlZENvbXBpbGVyRmFjYWRlIHtcbiAgybVjb21waWxlckZhY2FkZTogQ29tcGlsZXJGYWNhZGU7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgQ29tcGlsZXJGYWNhZGUge1xuICBjb21waWxlUGlwZShhbmd1bGFyQ29yZUVudjogQ29yZUVudmlyb25tZW50LCBzb3VyY2VNYXBVcmw6IHN0cmluZywgbWV0YTogUjNQaXBlTWV0YWRhdGFGYWNhZGUpOlxuICAgICAgYW55O1xuICBjb21waWxlUGlwZURlY2xhcmF0aW9uKFxuICAgICAgYW5ndWxhckNvcmVFbnY6IENvcmVFbnZpcm9ubWVudCwgc291cmNlTWFwVXJsOiBzdHJpbmcsIGRlY2xhcmF0aW9uOiBSM0RlY2xhcmVQaXBlRmFjYWRlKTogYW55O1xuICBjb21waWxlSW5qZWN0YWJsZShcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLCBtZXRhOiBSM0luamVjdGFibGVNZXRhZGF0YUZhY2FkZSk6IGFueTtcbiAgY29tcGlsZUluamVjdGFibGVEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLCBtZXRhOiBSM0RlY2xhcmVJbmplY3RhYmxlRmFjYWRlKTogYW55O1xuICBjb21waWxlSW5qZWN0b3IoXG4gICAgICBhbmd1bGFyQ29yZUVudjogQ29yZUVudmlyb25tZW50LCBzb3VyY2VNYXBVcmw6IHN0cmluZywgbWV0YTogUjNJbmplY3Rvck1ldGFkYXRhRmFjYWRlKTogYW55O1xuICBjb21waWxlSW5qZWN0b3JEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLFxuICAgICAgZGVjbGFyYXRpb246IFIzRGVjbGFyZUluamVjdG9yRmFjYWRlKTogYW55O1xuICBjb21waWxlTmdNb2R1bGUoXG4gICAgICBhbmd1bGFyQ29yZUVudjogQ29yZUVudmlyb25tZW50LCBzb3VyY2VNYXBVcmw6IHN0cmluZywgbWV0YTogUjNOZ01vZHVsZU1ldGFkYXRhRmFjYWRlKTogYW55O1xuICBjb21waWxlTmdNb2R1bGVEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLFxuICAgICAgZGVjbGFyYXRpb246IFIzRGVjbGFyZU5nTW9kdWxlRmFjYWRlKTogYW55O1xuICBjb21waWxlRGlyZWN0aXZlKFxuICAgICAgYW5ndWxhckNvcmVFbnY6IENvcmVFbnZpcm9ubWVudCwgc291cmNlTWFwVXJsOiBzdHJpbmcsIG1ldGE6IFIzRGlyZWN0aXZlTWV0YWRhdGFGYWNhZGUpOiBhbnk7XG4gIGNvbXBpbGVEaXJlY3RpdmVEZWNsYXJhdGlvbihcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLFxuICAgICAgZGVjbGFyYXRpb246IFIzRGVjbGFyZURpcmVjdGl2ZUZhY2FkZSk6IGFueTtcbiAgY29tcGlsZUNvbXBvbmVudChcbiAgICAgIGFuZ3VsYXJDb3JlRW52OiBDb3JlRW52aXJvbm1lbnQsIHNvdXJjZU1hcFVybDogc3RyaW5nLCBtZXRhOiBSM0NvbXBvbmVudE1ldGFkYXRhRmFjYWRlKTogYW55O1xuICBjb21waWxlQ29tcG9uZW50RGVjbGFyYXRpb24oXG4gICAgICBhbmd1bGFyQ29yZUVudjogQ29yZUVudmlyb25tZW50LCBzb3VyY2VNYXBVcmw6IHN0cmluZyxcbiAgICAgIGRlY2xhcmF0aW9uOiBSM0RlY2xhcmVDb21wb25lbnRGYWNhZGUpOiBhbnk7XG4gIGNvbXBpbGVGYWN0b3J5KFxuICAgICAgYW5ndWxhckNvcmVFbnY6IENvcmVFbnZpcm9ubWVudCwgc291cmNlTWFwVXJsOiBzdHJpbmcsIG1ldGE6IFIzRmFjdG9yeURlZk1ldGFkYXRhRmFjYWRlKTogYW55O1xuICBjb21waWxlRmFjdG9yeURlY2xhcmF0aW9uKFxuICAgICAgYW5ndWxhckNvcmVFbnY6IENvcmVFbnZpcm9ubWVudCwgc291cmNlTWFwVXJsOiBzdHJpbmcsIG1ldGE6IFIzRGVjbGFyZUZhY3RvcnlGYWNhZGUpOiBhbnk7XG5cbiAgY3JlYXRlUGFyc2VTb3VyY2VTcGFuKGtpbmQ6IHN0cmluZywgdHlwZU5hbWU6IHN0cmluZywgc291cmNlVXJsOiBzdHJpbmcpOiBQYXJzZVNvdXJjZVNwYW47XG5cbiAgRmFjdG9yeVRhcmdldDogdHlwZW9mIEZhY3RvcnlUYXJnZXQ7XG4gIC8vIE5vdGUgdGhhdCB3ZSBkbyBub3QgdXNlIGB7bmV3KCk6IFJlc291cmNlTG9hZGVyfWAgaGVyZSBiZWNhdXNlXG4gIC8vIHRoZSByZXNvdXJjZSBsb2FkZXIgY2xhc3MgaXMgYWJzdHJhY3QgYW5kIG5vdCBjb25zdHJ1Y3RhYmxlLlxuICBSZXNvdXJjZUxvYWRlcjogRnVuY3Rpb24me3Byb3RvdHlwZTogUmVzb3VyY2VMb2FkZXJ9O1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIENvcmVFbnZpcm9ubWVudCB7XG4gIFtuYW1lOiBzdHJpbmddOiB1bmtub3duO1xufVxuXG5leHBvcnQgdHlwZSBSZXNvdXJjZUxvYWRlciA9IHtcbiAgZ2V0KHVybDogc3RyaW5nKTogUHJvbWlzZTxzdHJpbmc+fHN0cmluZztcbn07XG5cbmV4cG9ydCB0eXBlIFByb3ZpZGVyID0gdW5rbm93bjtcbmV4cG9ydCB0eXBlIFR5cGUgPSBGdW5jdGlvbjtcbmV4cG9ydCB0eXBlIE9wYXF1ZVZhbHVlID0gdW5rbm93bjtcblxuZXhwb3J0IGVudW0gRmFjdG9yeVRhcmdldCB7XG4gIERpcmVjdGl2ZSA9IDAsXG4gIENvbXBvbmVudCA9IDEsXG4gIEluamVjdGFibGUgPSAyLFxuICBQaXBlID0gMyxcbiAgTmdNb2R1bGUgPSA0LFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzRGVwZW5kZW5jeU1ldGFkYXRhRmFjYWRlIHtcbiAgdG9rZW46IE9wYXF1ZVZhbHVlO1xuICBhdHRyaWJ1dGU6IHN0cmluZ3xudWxsO1xuICBob3N0OiBib29sZWFuO1xuICBvcHRpb25hbDogYm9vbGVhbjtcbiAgc2VsZjogYm9vbGVhbjtcbiAgc2tpcFNlbGY6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlRGVwZW5kZW5jeU1ldGFkYXRhRmFjYWRlIHtcbiAgdG9rZW46IE9wYXF1ZVZhbHVlO1xuICBhdHRyaWJ1dGU/OiBib29sZWFuO1xuICBob3N0PzogYm9vbGVhbjtcbiAgb3B0aW9uYWw/OiBib29sZWFuO1xuICBzZWxmPzogYm9vbGVhbjtcbiAgc2tpcFNlbGY/OiBib29sZWFuO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzUGlwZU1ldGFkYXRhRmFjYWRlIHtcbiAgbmFtZTogc3RyaW5nO1xuICB0eXBlOiBUeXBlO1xuICBwaXBlTmFtZTogc3RyaW5nO1xuICBwdXJlOiBib29sZWFuO1xuICBpc1N0YW5kYWxvbmU6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNJbmplY3RhYmxlTWV0YWRhdGFGYWNhZGUge1xuICBuYW1lOiBzdHJpbmc7XG4gIHR5cGU6IFR5cGU7XG4gIHR5cGVBcmd1bWVudENvdW50OiBudW1iZXI7XG4gIHByb3ZpZGVkSW4/OiBUeXBlfCdyb290J3wncGxhdGZvcm0nfCdhbnknfG51bGw7XG4gIHVzZUNsYXNzPzogT3BhcXVlVmFsdWU7XG4gIHVzZUZhY3Rvcnk/OiBPcGFxdWVWYWx1ZTtcbiAgdXNlRXhpc3Rpbmc/OiBPcGFxdWVWYWx1ZTtcbiAgdXNlVmFsdWU/OiBPcGFxdWVWYWx1ZTtcbiAgZGVwcz86IFIzRGVwZW5kZW5jeU1ldGFkYXRhRmFjYWRlW107XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNOZ01vZHVsZU1ldGFkYXRhRmFjYWRlIHtcbiAgdHlwZTogVHlwZTtcbiAgYm9vdHN0cmFwOiBGdW5jdGlvbltdO1xuICBkZWNsYXJhdGlvbnM6IEZ1bmN0aW9uW107XG4gIGltcG9ydHM6IEZ1bmN0aW9uW107XG4gIGV4cG9ydHM6IEZ1bmN0aW9uW107XG4gIHNjaGVtYXM6IHtuYW1lOiBzdHJpbmd9W118bnVsbDtcbiAgaWQ6IHN0cmluZ3xudWxsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzSW5qZWN0b3JNZXRhZGF0YUZhY2FkZSB7XG4gIG5hbWU6IHN0cmluZztcbiAgdHlwZTogVHlwZTtcbiAgcHJvdmlkZXJzOiBQcm92aWRlcltdO1xuICBpbXBvcnRzOiBPcGFxdWVWYWx1ZVtdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzSG9zdERpcmVjdGl2ZU1ldGFkYXRhRmFjYWRlIHtcbiAgZGlyZWN0aXZlOiBUeXBlO1xuICBpbnB1dHM/OiBzdHJpbmdbXTtcbiAgb3V0cHV0cz86IHN0cmluZ1tdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzRGlyZWN0aXZlTWV0YWRhdGFGYWNhZGUge1xuICBuYW1lOiBzdHJpbmc7XG4gIHR5cGU6IFR5cGU7XG4gIHR5cGVTb3VyY2VTcGFuOiBQYXJzZVNvdXJjZVNwYW47XG4gIHNlbGVjdG9yOiBzdHJpbmd8bnVsbDtcbiAgcXVlcmllczogUjNRdWVyeU1ldGFkYXRhRmFjYWRlW107XG4gIGhvc3Q6IHtba2V5OiBzdHJpbmddOiBzdHJpbmd9O1xuICBwcm9wTWV0YWRhdGE6IHtba2V5OiBzdHJpbmddOiBPcGFxdWVWYWx1ZVtdfTtcbiAgbGlmZWN5Y2xlOiB7dXNlc09uQ2hhbmdlczogYm9vbGVhbjt9O1xuICBpbnB1dHM6IChzdHJpbmd8e25hbWU6IHN0cmluZywgYWxpYXM/OiBzdHJpbmcsIHJlcXVpcmVkPzogYm9vbGVhbn0pW107XG4gIG91dHB1dHM6IHN0cmluZ1tdO1xuICB1c2VzSW5oZXJpdGFuY2U6IGJvb2xlYW47XG4gIGV4cG9ydEFzOiBzdHJpbmdbXXxudWxsO1xuICBwcm92aWRlcnM6IFByb3ZpZGVyW118bnVsbDtcbiAgdmlld1F1ZXJpZXM6IFIzUXVlcnlNZXRhZGF0YUZhY2FkZVtdO1xuICBpc1N0YW5kYWxvbmU6IGJvb2xlYW47XG4gIGlzU2lnbmFsOiBib29sZWFuO1xuICBob3N0RGlyZWN0aXZlczogUjNIb3N0RGlyZWN0aXZlTWV0YWRhdGFGYWNhZGVbXXxudWxsO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzQ29tcG9uZW50TWV0YWRhdGFGYWNhZGUgZXh0ZW5kcyBSM0RpcmVjdGl2ZU1ldGFkYXRhRmFjYWRlIHtcbiAgdGVtcGxhdGU6IHN0cmluZztcbiAgcHJlc2VydmVXaGl0ZXNwYWNlczogYm9vbGVhbjtcbiAgYW5pbWF0aW9uczogT3BhcXVlVmFsdWVbXXx1bmRlZmluZWQ7XG4gIGRlY2xhcmF0aW9uczogUjNUZW1wbGF0ZURlcGVuZGVuY3lGYWNhZGVbXTtcbiAgc3R5bGVzOiBzdHJpbmdbXTtcbiAgZW5jYXBzdWxhdGlvbjogVmlld0VuY2Fwc3VsYXRpb247XG4gIHZpZXdQcm92aWRlcnM6IFByb3ZpZGVyW118bnVsbDtcbiAgaW50ZXJwb2xhdGlvbj86IFtzdHJpbmcsIHN0cmluZ107XG4gIGNoYW5nZURldGVjdGlvbj86IENoYW5nZURldGVjdGlvblN0cmF0ZWd5O1xufVxuXG4vLyBUT0RPKGxlZ2FjeS1wYXJ0aWFsLW91dHB1dC1pbnB1dHMpOiBSZW1vdmUgaW4gdjE4LlxuLy8gaHR0cHM6Ly9naXRodWIuY29tL2FuZ3VsYXIvYW5ndWxhci9ibG9iL2Q0YjQyMzY5MDIxMDg3MmI1YzMyYTMyMmE2MDkwYmVkYTMwYjA1YTMvcGFja2FnZXMvY29yZS9zcmMvY29tcGlsZXIvY29tcGlsZXJfZmFjYWRlX2ludGVyZmFjZS50cyNMMTk3LUwxOTlcbmV4cG9ydCB0eXBlIExlZ2FjeUlucHV0UGFydGlhbE1hcHBpbmcgPVxuICAgIHN0cmluZ3xbYmluZGluZ1Byb3BlcnR5TmFtZTogc3RyaW5nLCBjbGFzc1Byb3BlcnR5TmFtZTogc3RyaW5nLCB0cmFuc2Zvcm1GdW5jdGlvbj86IEZ1bmN0aW9uXTtcblxuZXhwb3J0IGludGVyZmFjZSBSM0RlY2xhcmVEaXJlY3RpdmVGYWNhZGUge1xuICBzZWxlY3Rvcj86IHN0cmluZztcbiAgdHlwZTogVHlwZTtcbiAgaW5wdXRzPzoge1xuICAgIFtmaWVsZE5hbWU6IHN0cmluZ106IHtcbiAgICAgIGNsYXNzUHJvcGVydHlOYW1lOiBzdHJpbmcsXG4gICAgICBwdWJsaWNOYW1lOiBzdHJpbmcsXG4gICAgICBpc1NpZ25hbDogYm9vbGVhbixcbiAgICAgIGlzUmVxdWlyZWQ6IGJvb2xlYW4sXG4gICAgICB0cmFuc2Zvcm1GdW5jdGlvbjogRnVuY3Rpb258bnVsbCxcbiAgICB9fExlZ2FjeUlucHV0UGFydGlhbE1hcHBpbmc7XG4gIH07XG4gIG91dHB1dHM/OiB7W2NsYXNzUHJvcGVydHlOYW1lOiBzdHJpbmddOiBzdHJpbmd9O1xuICBob3N0Pzoge1xuICAgIGF0dHJpYnV0ZXM/OiB7W2tleTogc3RyaW5nXTogT3BhcXVlVmFsdWV9O1xuICAgIGxpc3RlbmVycz86IHtba2V5OiBzdHJpbmddOiBzdHJpbmd9O1xuICAgIHByb3BlcnRpZXM/OiB7W2tleTogc3RyaW5nXTogc3RyaW5nfTtcbiAgICBjbGFzc0F0dHJpYnV0ZT86IHN0cmluZztcbiAgICBzdHlsZUF0dHJpYnV0ZT86IHN0cmluZztcbiAgfTtcbiAgcXVlcmllcz86IFIzRGVjbGFyZVF1ZXJ5TWV0YWRhdGFGYWNhZGVbXTtcbiAgdmlld1F1ZXJpZXM/OiBSM0RlY2xhcmVRdWVyeU1ldGFkYXRhRmFjYWRlW107XG4gIHByb3ZpZGVycz86IE9wYXF1ZVZhbHVlO1xuICBleHBvcnRBcz86IHN0cmluZ1tdO1xuICB1c2VzSW5oZXJpdGFuY2U/OiBib29sZWFuO1xuICB1c2VzT25DaGFuZ2VzPzogYm9vbGVhbjtcbiAgaXNTdGFuZGFsb25lPzogYm9vbGVhbjtcbiAgaG9zdERpcmVjdGl2ZXM/OiBSM0hvc3REaXJlY3RpdmVNZXRhZGF0YUZhY2FkZVtdfG51bGw7XG4gIGlzU2lnbmFsPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSM0RlY2xhcmVDb21wb25lbnRGYWNhZGUgZXh0ZW5kcyBSM0RlY2xhcmVEaXJlY3RpdmVGYWNhZGUge1xuICB0ZW1wbGF0ZTogc3RyaW5nO1xuICBpc0lubGluZT86IGJvb2xlYW47XG4gIHN0eWxlcz86IHN0cmluZ1tdO1xuXG4gIC8vIFBvc3Qtc3RhbmRhbG9uZSBsaWJyYXJpZXMgdXNlIGEgdW5pZmllZCBkZXBlbmRlbmNpZXMgZmllbGQuXG4gIGRlcGVuZGVuY2llcz86IFIzRGVjbGFyZVRlbXBsYXRlRGVwZW5kZW5jeUZhY2FkZVtdO1xuXG4gIC8vIFByZS1zdGFuZGFsb25lIGxpYnJhcmllcyBoYXZlIHNlcGFyYXRlIGNvbXBvbmVudC9kaXJlY3RpdmUvcGlwZSBmaWVsZHM6XG4gIGNvbXBvbmVudHM/OiBSM0RlY2xhcmVEaXJlY3RpdmVEZXBlbmRlbmN5RmFjYWRlW107XG4gIGRpcmVjdGl2ZXM/OiBSM0RlY2xhcmVEaXJlY3RpdmVEZXBlbmRlbmN5RmFjYWRlW107XG4gIHBpcGVzPzoge1twaXBlTmFtZTogc3RyaW5nXTogT3BhcXVlVmFsdWV8KCgpID0+IE9wYXF1ZVZhbHVlKX07XG5cblxuICB2aWV3UHJvdmlkZXJzPzogT3BhcXVlVmFsdWU7XG4gIGFuaW1hdGlvbnM/OiBPcGFxdWVWYWx1ZTtcbiAgY2hhbmdlRGV0ZWN0aW9uPzogQ2hhbmdlRGV0ZWN0aW9uU3RyYXRlZ3k7XG4gIGVuY2Fwc3VsYXRpb24/OiBWaWV3RW5jYXBzdWxhdGlvbjtcbiAgaW50ZXJwb2xhdGlvbj86IFtzdHJpbmcsIHN0cmluZ107XG4gIHByZXNlcnZlV2hpdGVzcGFjZXM/OiBib29sZWFuO1xufVxuXG5leHBvcnQgdHlwZSBSM0RlY2xhcmVUZW1wbGF0ZURlcGVuZGVuY3lGYWNhZGUgPSB7XG4gIGtpbmQ6IHN0cmluZ1xufSYoUjNEZWNsYXJlRGlyZWN0aXZlRGVwZW5kZW5jeUZhY2FkZXxSM0RlY2xhcmVQaXBlRGVwZW5kZW5jeUZhY2FkZXxcbiAgIFIzRGVjbGFyZU5nTW9kdWxlRGVwZW5kZW5jeUZhY2FkZSk7XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlRGlyZWN0aXZlRGVwZW5kZW5jeUZhY2FkZSB7XG4gIGtpbmQ/OiAnZGlyZWN0aXZlJ3wnY29tcG9uZW50JztcbiAgc2VsZWN0b3I6IHN0cmluZztcbiAgdHlwZTogT3BhcXVlVmFsdWV8KCgpID0+IE9wYXF1ZVZhbHVlKTtcbiAgaW5wdXRzPzogc3RyaW5nW107XG4gIG91dHB1dHM/OiBzdHJpbmdbXTtcbiAgZXhwb3J0QXM/OiBzdHJpbmdbXTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSM0RlY2xhcmVQaXBlRGVwZW5kZW5jeUZhY2FkZSB7XG4gIGtpbmQ/OiAncGlwZSc7XG4gIG5hbWU6IHN0cmluZztcbiAgdHlwZTogT3BhcXVlVmFsdWV8KCgpID0+IE9wYXF1ZVZhbHVlKTtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSM0RlY2xhcmVOZ01vZHVsZURlcGVuZGVuY3lGYWNhZGUge1xuICBraW5kOiAnbmdtb2R1bGUnO1xuICB0eXBlOiBPcGFxdWVWYWx1ZXwoKCkgPT4gT3BhcXVlVmFsdWUpO1xufVxuXG5leHBvcnQgZW51bSBSM1RlbXBsYXRlRGVwZW5kZW5jeUtpbmQge1xuICBEaXJlY3RpdmUgPSAwLFxuICBQaXBlID0gMSxcbiAgTmdNb2R1bGUgPSAyLFxufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzVGVtcGxhdGVEZXBlbmRlbmN5RmFjYWRlIHtcbiAga2luZDogUjNUZW1wbGF0ZURlcGVuZGVuY3lLaW5kO1xuICB0eXBlOiBPcGFxdWVWYWx1ZXwoKCkgPT4gT3BhcXVlVmFsdWUpO1xufVxuZXhwb3J0IGludGVyZmFjZSBSM0ZhY3RvcnlEZWZNZXRhZGF0YUZhY2FkZSB7XG4gIG5hbWU6IHN0cmluZztcbiAgdHlwZTogVHlwZTtcbiAgdHlwZUFyZ3VtZW50Q291bnQ6IG51bWJlcjtcbiAgZGVwczogUjNEZXBlbmRlbmN5TWV0YWRhdGFGYWNhZGVbXXxudWxsO1xuICB0YXJnZXQ6IEZhY3RvcnlUYXJnZXQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlRmFjdG9yeUZhY2FkZSB7XG4gIHR5cGU6IFR5cGU7XG4gIGRlcHM6IFIzRGVjbGFyZURlcGVuZGVuY3lNZXRhZGF0YUZhY2FkZVtdfCdpbnZhbGlkJ3xudWxsO1xuICB0YXJnZXQ6IEZhY3RvcnlUYXJnZXQ7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlSW5qZWN0YWJsZUZhY2FkZSB7XG4gIHR5cGU6IFR5cGU7XG4gIHByb3ZpZGVkSW4/OiBUeXBlfCdyb290J3wncGxhdGZvcm0nfCdhbnknfG51bGw7XG4gIHVzZUNsYXNzPzogT3BhcXVlVmFsdWU7XG4gIHVzZUZhY3Rvcnk/OiBPcGFxdWVWYWx1ZTtcbiAgdXNlRXhpc3Rpbmc/OiBPcGFxdWVWYWx1ZTtcbiAgdXNlVmFsdWU/OiBPcGFxdWVWYWx1ZTtcbiAgZGVwcz86IFIzRGVjbGFyZURlcGVuZGVuY3lNZXRhZGF0YUZhY2FkZVtdO1xufVxuXG5leHBvcnQgZW51bSBWaWV3RW5jYXBzdWxhdGlvbiB7XG4gIEVtdWxhdGVkID0gMCxcbiAgLy8gSGlzdG9yaWNhbGx5IHRoZSAxIHZhbHVlIHdhcyBmb3IgYE5hdGl2ZWAgZW5jYXBzdWxhdGlvbiB3aGljaCBoYXMgYmVlbiByZW1vdmVkIGFzIG9mIHYxMS5cbiAgTm9uZSA9IDIsXG4gIFNoYWRvd0RvbSA9IDNcbn1cblxuZXhwb3J0IHR5cGUgQ2hhbmdlRGV0ZWN0aW9uU3RyYXRlZ3kgPSBudW1iZXI7XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNRdWVyeU1ldGFkYXRhRmFjYWRlIHtcbiAgcHJvcGVydHlOYW1lOiBzdHJpbmc7XG4gIGZpcnN0OiBib29sZWFuO1xuICBwcmVkaWNhdGU6IE9wYXF1ZVZhbHVlfHN0cmluZ1tdO1xuICBkZXNjZW5kYW50czogYm9vbGVhbjtcbiAgZW1pdERpc3RpbmN0Q2hhbmdlc09ubHk6IGJvb2xlYW47XG4gIHJlYWQ6IE9wYXF1ZVZhbHVlfG51bGw7XG4gIHN0YXRpYzogYm9vbGVhbjtcbiAgaXNTaWduYWw6IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlUXVlcnlNZXRhZGF0YUZhY2FkZSB7XG4gIHByb3BlcnR5TmFtZTogc3RyaW5nO1xuICBmaXJzdD86IGJvb2xlYW47XG4gIHByZWRpY2F0ZTogT3BhcXVlVmFsdWV8c3RyaW5nW107XG4gIGRlc2NlbmRhbnRzPzogYm9vbGVhbjtcbiAgcmVhZD86IE9wYXF1ZVZhbHVlO1xuICBzdGF0aWM/OiBib29sZWFuO1xuICBlbWl0RGlzdGluY3RDaGFuZ2VzT25seT86IGJvb2xlYW47XG4gIGlzU2lnbmFsPzogYm9vbGVhbjtcbn1cblxuZXhwb3J0IGludGVyZmFjZSBSM0RlY2xhcmVJbmplY3RvckZhY2FkZSB7XG4gIHR5cGU6IFR5cGU7XG4gIGltcG9ydHM/OiBPcGFxdWVWYWx1ZVtdO1xuICBwcm92aWRlcnM/OiBPcGFxdWVWYWx1ZVtdO1xufVxuXG5leHBvcnQgaW50ZXJmYWNlIFIzRGVjbGFyZU5nTW9kdWxlRmFjYWRlIHtcbiAgdHlwZTogVHlwZTtcbiAgYm9vdHN0cmFwPzogT3BhcXVlVmFsdWVbXXwoKCkgPT4gT3BhcXVlVmFsdWVbXSk7XG4gIGRlY2xhcmF0aW9ucz86IE9wYXF1ZVZhbHVlW118KCgpID0+IE9wYXF1ZVZhbHVlW10pO1xuICBpbXBvcnRzPzogT3BhcXVlVmFsdWVbXXwoKCkgPT4gT3BhcXVlVmFsdWVbXSk7XG4gIGV4cG9ydHM/OiBPcGFxdWVWYWx1ZVtdfCgoKSA9PiBPcGFxdWVWYWx1ZVtdKTtcbiAgc2NoZW1hcz86IE9wYXF1ZVZhbHVlW107XG4gIGlkPzogT3BhcXVlVmFsdWU7XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUjNEZWNsYXJlUGlwZUZhY2FkZSB7XG4gIHR5cGU6IFR5cGU7XG4gIG5hbWU6IHN0cmluZztcbiAgcHVyZT86IGJvb2xlYW47XG4gIGlzU3RhbmRhbG9uZT86IGJvb2xlYW47XG59XG5cbmV4cG9ydCBpbnRlcmZhY2UgUGFyc2VTb3VyY2VTcGFuIHtcbiAgc3RhcnQ6IGFueTtcbiAgZW5kOiBhbnk7XG4gIGRldGFpbHM6IGFueTtcbiAgZnVsbFN0YXJ0OiBhbnk7XG59XG4iXX0=