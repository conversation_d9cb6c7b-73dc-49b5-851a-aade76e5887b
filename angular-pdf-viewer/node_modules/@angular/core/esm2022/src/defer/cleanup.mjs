/*!
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { PREFETCH_TRIGGER_CLEANUP_FNS, TRIGGER_CLEANUP_FNS } from './interfaces';
/**
 * Registers a cleanup function associated with a prefetching trigger
 * or a regular trigger of a defer block.
 */
export function storeTriggerCleanupFn(type, lDetails, cleanupFn) {
    const key = type === 1 /* TriggerType.Prefetch */ ? PREFETCH_TRIGGER_CLEANUP_FNS : TRIGGER_CLEANUP_FNS;
    if (lDetails[key] === null) {
        lDetails[key] = [];
    }
    lDetails[key].push(cleanupFn);
}
/**
 * Invokes registered cleanup functions either for prefetch or for regular triggers.
 */
export function invokeTriggerCleanupFns(type, lDetails) {
    const key = type === 1 /* TriggerType.Prefetch */ ? PREFETCH_TRIGGER_CLEANUP_FNS : TRIGGER_CLEANUP_FNS;
    const cleanupFns = lDetails[key];
    if (cleanupFns !== null) {
        for (const cleanupFn of cleanupFns) {
            cleanupFn();
        }
        lDetails[key] = null;
    }
}
/**
 * Invokes registered cleanup functions for both prefetch and regular triggers.
 */
export function invokeAllTriggerCleanupFns(lDetails) {
    invokeTriggerCleanupFns(1 /* TriggerType.Prefetch */, lDetails);
    invokeTriggerCleanupFns(0 /* TriggerType.Regular */, lDetails);
}
//# sourceMappingURL=data:application/json;base64,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