/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { producerAccessed, SIGNAL } from '@angular/core/primitives/signals';
import { RuntimeError } from '../../errors';
import { INPUT_SIGNAL_NODE, REQUIRED_UNSET_VALUE } from './input_signal_node';
export const ɵINPUT_SIGNAL_BRAND_READ_TYPE = /* @__PURE__ */ Symbol();
export const ɵINPUT_SIGNAL_BRAND_WRITE_TYPE = /* @__PURE__ */ Symbol();
/**
 * Creates an input signal.
 *
 * @param initialValue The initial value.
 *   Can be set to {@link REQUIRED_UNSET_VALUE} for required inputs.
 * @param options Additional options for the input. e.g. a transform, or an alias.
 */
export function createInputSignal(initialValue, options) {
    const node = Object.create(INPUT_SIGNAL_NODE);
    node.value = initialValue;
    // Perf note: Always set `transformFn` here to ensure that `node` always
    // has the same v8 class shape, allowing monomorphic reads on input signals.
    node.transformFn = options?.transform;
    function inputValueFn() {
        // Record that someone looked at this signal.
        producerAccessed(node);
        if (node.value === REQUIRED_UNSET_VALUE) {
            throw new RuntimeError(-950 /* RuntimeErrorCode.REQUIRED_INPUT_NO_VALUE */, ngDevMode && 'Input is required but no value is available yet.');
        }
        return node.value;
    }
    inputValueFn[SIGNAL] = node;
    if (ngDevMode) {
        inputValueFn.toString = () => `[Input Signal: ${inputValueFn()}]`;
    }
    return inputValueFn;
}
//# sourceMappingURL=data:application/json;base64,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