/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { assertInInjectionContext } from '../../di';
import { REQUIRED_UNSET_VALUE } from '../input/input_signal_node';
import { createModelSignal } from './model_signal';
export function modelFunction(initialValue) {
    ngDevMode && assertInInjectionContext(model);
    return createModelSignal(initialValue);
}
export function modelRequiredFunction() {
    ngDevMode && assertInInjectionContext(model);
    return createModelSignal(REQUIRED_UNSET_VALUE);
}
/**
 * `model` declares a writeable signal that is exposed as an input/output
 * pair on the containing directive.
 *
 * The input name is taken either from the class member or from the `alias` option.
 * The output name is generated by taking the input name and appending `Change`.
 *
 * @usageNotes
 *
 * To use `model()`, import the function from `@angular/core`.
 *
 * ```
 * import {model} from '@angular/core`;
 * ```
 *
 * Inside your component, introduce a new class member and initialize
 * it with a call to `model` or `model.required`.
 *
 * ```ts
 * @Directive({
 *   ...
 * })
 * export class MyDir {
 *   firstName = model<string>();            // ModelSignal<string|undefined>
 *   lastName  = model.required<string>();   // ModelSignal<string>
 *   age       = model(0);                   // ModelSignal<number>
 * }
 * ```
 *
 * Inside your component template, you can display the value of a `model`
 * by calling the signal.
 *
 * ```html
 * <span>{{firstName()}}</span>
 * ```
 *
 * Updating the `model` is equivalent to updating a writable signal.
 *
 * ```ts
 * updateName(newFirstName: string): void {
 *   this.firstName.set(newFirstName);
 * }
 * ```
 *
 * @developerPreview
 * @initializerApiFunction
 */
export const model = (() => {
    // Note: This may be considered a side-effect, but nothing will depend on
    // this assignment, unless this `model` constant export is accessed. It's a
    // self-contained side effect that is local to the user facing `model` export.
    modelFunction.required = modelRequiredFunction;
    return modelFunction;
})();
//# sourceMappingURL=data:application/json;base64,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