/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { ERROR_DETAILS_PAGE_BASE_URL } from './error_details_base_url';
/**
 * Class that represents a runtime error.
 * Formats and outputs the error message in a consistent way.
 *
 * Example:
 * ```
 *  throw new RuntimeError(
 *    RuntimeErrorCode.INJECTOR_ALREADY_DESTROYED,
 *    ngDevMode && 'Injector has already been destroyed.');
 * ```
 *
 * Note: the `message` argument contains a descriptive error message as a string in development
 * mode (when the `ngDevMode` is defined). In production mode (after tree-shaking pass), the
 * `message` argument becomes `false`, thus we account for it in the typings and the runtime
 * logic.
 */
export class RuntimeError extends Error {
    constructor(code, message) {
        super(formatRuntimeError(code, message));
        this.code = code;
    }
}
/**
 * Called to format a runtime error.
 * See additional info on the `message` argument type in the `RuntimeError` class description.
 */
export function formatRuntimeError(code, message) {
    // Error code might be a negative number, which is a special marker that instructs the logic to
    // generate a link to the error details page on angular.io.
    // We also prepend `0` to non-compile-time errors.
    const fullCode = `NG0${Math.abs(code)}`;
    let errorMessage = `${fullCode}${message ? ': ' + message : ''}`;
    if (ngDevMode && code < 0) {
        const addPeriodSeparator = !errorMessage.match(/[.,;!?\n]$/);
        const separator = addPeriodSeparator ? '.' : '';
        errorMessage =
            `${errorMessage}${separator} Find more at ${ERROR_DETAILS_PAGE_BASE_URL}/${fullCode}`;
    }
    return errorMessage;
}
//# sourceMappingURL=data:application/json;base64,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