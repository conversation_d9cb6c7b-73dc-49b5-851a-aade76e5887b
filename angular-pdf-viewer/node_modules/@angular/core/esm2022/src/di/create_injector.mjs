/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { EMPTY_ARRAY } from '../util/empty';
import { stringify } from '../util/stringify';
import { importProvidersFrom } from './provider_collection';
import { getNullInjector, R3Injector } from './r3_injector';
/**
 * Create a new `Injector` which is configured using a `defType` of `InjectorType<any>`s.
 */
export function createInjector(defType, parent = null, additionalProviders = null, name) {
    const injector = createInjectorWithoutInjectorInstances(defType, parent, additionalProviders, name);
    injector.resolveInjectorInitializers();
    return injector;
}
/**
 * Creates a new injector without eagerly resolving its injector types. Can be used in places
 * where resolving the injector types immediately can lead to an infinite loop. The injector types
 * should be resolved at a later point by calling `_resolveInjectorDefTypes`.
 */
export function createInjectorWithoutInjectorInstances(defType, parent = null, additionalProviders = null, name, scopes = new Set()) {
    const providers = [
        additionalProviders || EMPTY_ARRAY,
        importProvidersFrom(defType),
    ];
    name = name || (typeof defType === 'object' ? undefined : stringify(defType));
    return new R3Injector(providers, parent || getNullInjector(), name || null, scopes);
}
//# sourceMappingURL=data:application/json;base64,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