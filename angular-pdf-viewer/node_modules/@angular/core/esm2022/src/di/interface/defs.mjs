/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { getClosureSafeProperty } from '../../util/property';
/**
 * Construct an injectable definition which defines how a token will be constructed by the DI
 * system, and in which injectors (if any) it will be available.
 *
 * This should be assigned to a static `ɵprov` field on a type, which will then be an
 * `InjectableType`.
 *
 * Options:
 * * `providedIn` determines which injectors will include the injectable, by either associating it
 *   with an `@NgModule` or other `InjectorType`, or by specifying that this injectable should be
 *   provided in the `'root'` injector, which will be the application-level injector in most apps.
 * * `factory` gives the zero argument function which will create an instance of the injectable.
 *   The factory can call [`inject`](api/core/inject) to access the `Injector` and request injection
 * of dependencies.
 *
 * @codeGenApi
 * @publicApi This instruction has been emitted by ViewEngine for some time and is deployed to npm.
 */
export function ɵɵdefineInjectable(opts) {
    return {
        token: opts.token,
        providedIn: opts.providedIn || null,
        factory: opts.factory,
        value: undefined,
    };
}
/**
 * @deprecated in v8, delete after v10. This API should be used only by generated code, and that
 * code should now use ɵɵdefineInjectable instead.
 * @publicApi
 */
export const defineInjectable = ɵɵdefineInjectable;
/**
 * Construct an `InjectorDef` which configures an injector.
 *
 * This should be assigned to a static injector def (`ɵinj`) field on a type, which will then be an
 * `InjectorType`.
 *
 * Options:
 *
 * * `providers`: an optional array of providers to add to the injector. Each provider must
 *   either have a factory or point to a type which has a `ɵprov` static property (the
 *   type must be an `InjectableType`).
 * * `imports`: an optional array of imports of other `InjectorType`s or `InjectorTypeWithModule`s
 *   whose providers will also be added to the injector. Locally provided types will override
 *   providers from imports.
 *
 * @codeGenApi
 */
export function ɵɵdefineInjector(options) {
    return { providers: options.providers || [], imports: options.imports || [] };
}
/**
 * Read the injectable def (`ɵprov`) for `type` in a way which is immune to accidentally reading
 * inherited value.
 *
 * @param type A type which may have its own (non-inherited) `ɵprov`.
 */
export function getInjectableDef(type) {
    return getOwnDefinition(type, NG_PROV_DEF) || getOwnDefinition(type, NG_INJECTABLE_DEF);
}
export function isInjectable(type) {
    return getInjectableDef(type) !== null;
}
/**
 * Return definition only if it is defined directly on `type` and is not inherited from a base
 * class of `type`.
 */
function getOwnDefinition(type, field) {
    return type.hasOwnProperty(field) ? type[field] : null;
}
/**
 * Read the injectable def (`ɵprov`) for `type` or read the `ɵprov` from one of its ancestors.
 *
 * @param type A type which may have `ɵprov`, via inheritance.
 *
 * @deprecated Will be removed in a future version of Angular, where an error will occur in the
 *     scenario if we find the `ɵprov` on an ancestor only.
 */
export function getInheritedInjectableDef(type) {
    const def = type && (type[NG_PROV_DEF] || type[NG_INJECTABLE_DEF]);
    if (def) {
        ngDevMode &&
            console.warn(`DEPRECATED: DI is instantiating a token "${type.name}" that inherits its @Injectable decorator but does not provide one itself.\n` +
                `This will become an error in a future version of Angular. Please add @Injectable() to the "${type.name}" class.`);
        return def;
    }
    else {
        return null;
    }
}
/**
 * Read the injector def type in a way which is immune to accidentally reading inherited value.
 *
 * @param type type which may have an injector def (`ɵinj`)
 */
export function getInjectorDef(type) {
    return type && (type.hasOwnProperty(NG_INJ_DEF) || type.hasOwnProperty(NG_INJECTOR_DEF)) ?
        type[NG_INJ_DEF] :
        null;
}
export const NG_PROV_DEF = getClosureSafeProperty({ ɵprov: getClosureSafeProperty });
export const NG_INJ_DEF = getClosureSafeProperty({ ɵinj: getClosureSafeProperty });
// We need to keep these around so we can read off old defs if new defs are unavailable
export const NG_INJECTABLE_DEF = getClosureSafeProperty({ ngInjectableDef: getClosureSafeProperty });
export const NG_INJECTOR_DEF = getClosureSafeProperty({ ngInjectorDef: getClosureSafeProperty });
//# sourceMappingURL=data:application/json;base64,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