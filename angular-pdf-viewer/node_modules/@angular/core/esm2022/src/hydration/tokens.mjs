/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { InjectionToken } from '../di/injection_token';
/**
 * Internal token that specifies whether DOM reuse logic
 * during hydration is enabled.
 */
export const IS_HYDRATION_DOM_REUSE_ENABLED = new InjectionToken((typeof ngDevMode === 'undefined' || !!ngDevMode) ? 'IS_HYDRATION_DOM_REUSE_ENABLED' : '');
// By default (in client rendering mode), we remove all the contents
// of the host element and render an application after that.
export const PRESERVE_HOST_CONTENT_DEFAULT = false;
/**
 * Internal token that indicates whether host element content should be
 * retained during the bootstrap.
 */
export const PRESERVE_HOST_CONTENT = new InjectionToken((typeof ngDevMode === 'undefined' || !!ngDevMode) ? 'PRESERVE_HOST_CONTENT' : '', {
    providedIn: 'root',
    factory: () => PRESERVE_HOST_CONTENT_DEFAULT,
});
/**
 * Internal token that indicates whether hydration support for i18n
 * is enabled.
 */
export const IS_I18N_HYDRATION_ENABLED = new InjectionToken((typeof ngDevMode === 'undefined' || !!ngDevMode ? 'IS_I18N_HYDRATION_ENABLED' : ''));
//# sourceMappingURL=data:application/json;base64,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