/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** Encodes that the node lookup should start from the host node of this component. */
export const REFERENCE_NODE_HOST = 'h';
/** Encodes that the node lookup should start from the document body node. */
export const REFERENCE_NODE_BODY = 'b';
/**
 * Describes navigation steps that the runtime logic need to perform,
 * starting from a given (known) element.
 */
export var NodeNavigationStep;
(function (NodeNavigationStep) {
    NodeNavigationStep["FirstChild"] = "f";
    NodeNavigationStep["NextSibling"] = "n";
})(NodeNavigationStep || (NodeNavigationStep = {}));
/**
 * Keys within serialized view data structure to represent various
 * parts. See the `SerializedView` interface below for additional information.
 */
export const ELEMENT_CONTAINERS = 'e';
export const TEMPLATES = 't';
export const CONTAINERS = 'c';
export const MULTIPLIER = 'x';
export const NUM_ROOT_NODES = 'r';
export const TEMPLATE_ID = 'i'; // as it's also an "id"
export const NODES = 'n';
export const DISCONNECTED_NODES = 'd';
//# sourceMappingURL=data:application/json;base64,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