/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
/** Flags describing an input for a directive. */
export var InputFlags;
(function (InputFlags) {
    InputFlags[InputFlags["None"] = 0] = "None";
    InputFlags[InputFlags["SignalBased"] = 1] = "SignalBased";
    InputFlags[InputFlags["HasDecoratorInputTransform"] = 2] = "HasDecoratorInputTransform";
})(InputFlags || (InputFlags = {}));
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiaW5wdXRfZmxhZ3MuanMiLCJzb3VyY2VSb290IjoiIiwic291cmNlcyI6WyIuLi8uLi8uLi8uLi8uLi8uLi8uLi8uLi9wYWNrYWdlcy9jb3JlL3NyYy9yZW5kZXIzL2ludGVyZmFjZXMvaW5wdXRfZmxhZ3MudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsaURBQWlEO0FBQ2pELE1BQU0sQ0FBTixJQUFZLFVBSVg7QUFKRCxXQUFZLFVBQVU7SUFDcEIsMkNBQVEsQ0FBQTtJQUNSLHlEQUFvQixDQUFBO0lBQ3BCLHVGQUFtQyxDQUFBO0FBQ3JDLENBQUMsRUFKVyxVQUFVLEtBQVYsVUFBVSxRQUlyQiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vKiogRmxhZ3MgZGVzY3JpYmluZyBhbiBpbnB1dCBmb3IgYSBkaXJlY3RpdmUuICovXG5leHBvcnQgZW51bSBJbnB1dEZsYWdzIHtcbiAgTm9uZSA9IDAsXG4gIFNpZ25hbEJhc2VkID0gMSA8PCAwLFxuICBIYXNEZWNvcmF0b3JJbnB1dFRyYW5zZm9ybSA9IDEgPDwgMSxcbn1cbiJdfQ==