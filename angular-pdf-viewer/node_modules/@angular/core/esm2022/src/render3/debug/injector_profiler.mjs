/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { resolveForwardRef } from '../../di/forward_ref';
import { InjectionToken } from '../../di/injection_token';
import { throwError } from '../../util/assert';
let _injectorProfilerContext;
export function getInjectorProfilerContext() {
    !ngDevMode && throwError('getInjectorProfilerContext should never be called in production mode');
    return _injectorProfilerContext;
}
export function setInjectorProfilerContext(context) {
    !ngDevMode && throwError('setInjectorProfilerContext should never be called in production mode');
    const previous = _injectorProfilerContext;
    _injectorProfilerContext = context;
    return previous;
}
let injectorProfilerCallback = null;
/**
 * Sets the callback function which will be invoked during certain DI events within the
 * runtime (for example: injecting services, creating injectable instances, configuring providers)
 *
 * Warning: this function is *INTERNAL* and should not be relied upon in application's code.
 * The contract of the function might be changed in any release and/or the function can be removed
 * completely.
 *
 * @param profiler function provided by the caller or null value to disable profiling.
 */
export const setInjectorProfiler = (injectorProfiler) => {
    !ngDevMode && throwError('setInjectorProfiler should never be called in production mode');
    injectorProfilerCallback = injectorProfiler;
};
/**
 * Injector profiler function which emits on DI events executed by the runtime.
 *
 * @param event InjectorProfilerEvent corresponding to the DI event being emitted
 */
function injectorProfiler(event) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    if (injectorProfilerCallback != null /* both `null` and `undefined` */) {
        injectorProfilerCallback(event);
    }
}
/**
 * Emits an InjectorProfilerEventType.ProviderConfigured to the injector profiler. The data in the
 * emitted event includes the raw provider, as well as the token that provider is providing.
 *
 * @param eventProvider A provider object
 */
export function emitProviderConfiguredEvent(eventProvider, isViewProvider = false) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    let token;
    // if the provider is a TypeProvider (typeof provider is function) then the token is the
    // provider itself
    if (typeof eventProvider === 'function') {
        token = eventProvider;
    }
    // if the provider is an injection token, then the token is the injection token.
    else if (eventProvider instanceof InjectionToken) {
        token = eventProvider;
    }
    // in all other cases we can access the token via the `provide` property of the provider
    else {
        token = resolveForwardRef(eventProvider.provide);
    }
    let provider = eventProvider;
    // Injection tokens may define their own default provider which gets attached to the token itself
    // as `ɵprov`. In this case, we want to emit the provider that is attached to the token, not the
    // token itself.
    if (eventProvider instanceof InjectionToken) {
        provider = eventProvider.ɵprov || eventProvider;
    }
    injectorProfiler({
        type: 2 /* InjectorProfilerEventType.ProviderConfigured */,
        context: getInjectorProfilerContext(),
        providerRecord: { token, provider, isViewProvider }
    });
}
/**
 * Emits an event to the injector profiler with the instance that was created. Note that
 * the injector associated with this emission can be accessed by using getDebugInjectContext()
 *
 * @param instance an object created by an injector
 */
export function emitInstanceCreatedByInjectorEvent(instance) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    injectorProfiler({
        type: 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */,
        context: getInjectorProfilerContext(),
        instance: { value: instance }
    });
}
/**
 * @param token DI token associated with injected service
 * @param value the instance of the injected service (i.e the result of `inject(token)`)
 * @param flags the flags that the token was injected with
 */
export function emitInjectEvent(token, value, flags) {
    !ngDevMode && throwError('Injector profiler should never be called in production mode');
    injectorProfiler({
        type: 0 /* InjectorProfilerEventType.Inject */,
        context: getInjectorProfilerContext(),
        service: { token, value, flags }
    });
}
export function runInInjectorProfilerContext(injector, token, callback) {
    !ngDevMode &&
        throwError('runInInjectorProfilerContext should never be called in production mode');
    const prevInjectContext = setInjectorProfilerContext({ injector, token });
    try {
        callback();
    }
    finally {
        setInjectorProfilerContext(prevInjectContext);
    }
}
//# sourceMappingURL=data:application/json;base64,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