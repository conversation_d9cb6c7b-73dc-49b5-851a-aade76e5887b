/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { EnvironmentInjector } from '../../di/r3_injector';
import { assertDefined, throwError } from '../../util/assert';
import { assertTNodeForLView } from '../assert';
import { getComponentDef } from '../definition';
import { getNodeInjectorLView, getNodeInjectorTNode, NodeInjector } from '../di';
import { setInjectorProfiler } from './injector_profiler';
/**
 * These are the data structures that our framework injector profiler will fill with data in order
 * to support DI debugging APIs.
 *
 * resolverToTokenToDependencies: Maps an injector to a Map of tokens to an Array of
 * dependencies. Injector -> Token -> Dependencies This is used to support the
 * getDependenciesFromInjectable API, which takes in an injector and a token and returns it's
 * dependencies.
 *
 * resolverToProviders: Maps a DI resolver (an Injector or a TNode) to the providers configured
 * within it This is used to support the getInjectorProviders API, which takes in an injector and
 * returns the providers that it was configured with. Note that for the element injector case we
 * use the TNode instead of the LView as the DI resolver. This is because the registration of
 * providers happens only once per type of TNode. If an injector is created with an identical TNode,
 * the providers for that injector will not be reconfigured.
 *
 * standaloneInjectorToComponent: Maps the injector of a standalone component to the standalone
 * component that it is associated with. Used in the getInjectorProviders API, specificially in the
 * discovery of import paths for each provider. This is necessary because the imports array of a
 * standalone component is processed and configured in its standalone injector, but exists within
 * the component's definition. Because getInjectorProviders takes in an injector, if that injector
 * is the injector of a standalone component, we need to be able to discover the place where the
 * imports array is located (the component) in order to flatten the imports array within it to
 * discover all of it's providers.
 *
 *
 * All of these data structures are instantiated with WeakMaps. This will ensure that the presence
 * of any object in the keys of these maps does not prevent the garbage collector from collecting
 * those objects. Because of this property of WeakMaps, these data structures will never be the
 * source of a memory leak.
 *
 * An example of this advantage: When components are destroyed, we don't need to do
 * any additional work to remove that component from our mappings.
 *
 */
class DIDebugData {
    constructor() {
        this.resolverToTokenToDependencies = new WeakMap();
        this.resolverToProviders = new WeakMap();
        this.standaloneInjectorToComponent = new WeakMap();
    }
    reset() {
        this.resolverToTokenToDependencies =
            new WeakMap();
        this.resolverToProviders = new WeakMap();
        this.standaloneInjectorToComponent = new WeakMap();
    }
}
let frameworkDIDebugData = new DIDebugData();
export function getFrameworkDIDebugData() {
    return frameworkDIDebugData;
}
/**
 * Initalize default handling of injector events. This handling parses events
 * as they are emitted and constructs the data structures necessary to support
 * some of debug APIs.
 *
 * See handleInjectEvent, handleCreateEvent and handleProviderConfiguredEvent
 * for descriptions of each handler
 *
 * Supported APIs:
 *               - getDependenciesFromInjectable
 *               - getInjectorProviders
 */
export function setupFrameworkInjectorProfiler() {
    frameworkDIDebugData.reset();
    setInjectorProfiler((injectorProfilerEvent) => handleInjectorProfilerEvent(injectorProfilerEvent));
}
function handleInjectorProfilerEvent(injectorProfilerEvent) {
    const { context, type } = injectorProfilerEvent;
    if (type === 0 /* InjectorProfilerEventType.Inject */) {
        handleInjectEvent(context, injectorProfilerEvent.service);
    }
    else if (type === 1 /* InjectorProfilerEventType.InstanceCreatedByInjector */) {
        handleInstanceCreatedByInjectorEvent(context, injectorProfilerEvent.instance);
    }
    else if (type === 2 /* InjectorProfilerEventType.ProviderConfigured */) {
        handleProviderConfiguredEvent(context, injectorProfilerEvent.providerRecord);
    }
}
/**
 *
 * Stores the injected service in frameworkDIDebugData.resolverToTokenToDependencies
 * based on it's injector and token.
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data InjectedService the service associated with this inject event.
 *
 */
function handleInjectEvent(context, data) {
    const diResolver = getDIResolver(context.injector);
    if (diResolver === null) {
        throwError('An Inject event must be run within an injection context.');
    }
    const diResolverToInstantiatedToken = frameworkDIDebugData.resolverToTokenToDependencies;
    if (!diResolverToInstantiatedToken.has(diResolver)) {
        diResolverToInstantiatedToken.set(diResolver, new WeakMap());
    }
    // if token is a primitive type, ignore this event. We do this because we cannot keep track of
    // non-primitive tokens in WeakMaps since they are not garbage collectable.
    if (!canBeHeldWeakly(context.token)) {
        return;
    }
    const instantiatedTokenToDependencies = diResolverToInstantiatedToken.get(diResolver);
    if (!instantiatedTokenToDependencies.has(context.token)) {
        instantiatedTokenToDependencies.set(context.token, []);
    }
    const { token, value, flags } = data;
    assertDefined(context.token, 'Injector profiler context token is undefined.');
    const dependencies = instantiatedTokenToDependencies.get(context.token);
    assertDefined(dependencies, 'Could not resolve dependencies for token.');
    if (context.injector instanceof NodeInjector) {
        dependencies.push({ token, value, flags, injectedIn: getNodeInjectorContext(context.injector) });
    }
    else {
        dependencies.push({ token, value, flags });
    }
}
/**
 *
 * Returns the LView and TNode associated with a NodeInjector. Returns undefined if the injector
 * is not a NodeInjector.
 *
 * @param injector
 * @returns {lView: LView, tNode: TNode}|undefined
 */
function getNodeInjectorContext(injector) {
    if (!(injector instanceof NodeInjector)) {
        throwError('getNodeInjectorContext must be called with a NodeInjector');
    }
    const lView = getNodeInjectorLView(injector);
    const tNode = getNodeInjectorTNode(injector);
    if (tNode === null) {
        return;
    }
    assertTNodeForLView(tNode, lView);
    return { lView, tNode };
}
/**
 *
 * If the created instance is an instance of a standalone component, maps the injector to that
 * standalone component in frameworkDIDebugData.standaloneInjectorToComponent
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data InjectorCreatedInstance an object containing the instance that was just created
 *
 */
function handleInstanceCreatedByInjectorEvent(context, data) {
    const { value } = data;
    if (getDIResolver(context.injector) === null) {
        throwError('An InjectorCreatedInstance event must be run within an injection context.');
    }
    // if our value is an instance of a standalone component, map the injector of that standalone
    // component to the component class. Otherwise, this event is a noop.
    let standaloneComponent = undefined;
    if (typeof value === 'object') {
        standaloneComponent = value?.constructor;
    }
    if (standaloneComponent === undefined || !isStandaloneComponent(standaloneComponent)) {
        return;
    }
    const environmentInjector = context.injector.get(EnvironmentInjector, null, { optional: true });
    // Standalone components should have an environment injector. If one cannot be
    // found we may be in a test case for low level functionality that did not explictly
    // setup this injector. In those cases, we simply ignore this event.
    if (environmentInjector === null) {
        return;
    }
    const { standaloneInjectorToComponent } = frameworkDIDebugData;
    // If our injector has already been mapped, as is the case
    // when a standalone component imports another standalone component,
    // we consider the original component (the component doing the importing)
    // as the component connected to our injector.
    if (standaloneInjectorToComponent.has(environmentInjector)) {
        return;
    }
    // If our injector hasn't been mapped, then we map it to the standalone component
    standaloneInjectorToComponent.set(environmentInjector, standaloneComponent);
}
function isStandaloneComponent(value) {
    const def = getComponentDef(value);
    return !!def?.standalone;
}
/**
 *
 * Stores the emitted ProviderRecords from the InjectorProfilerEventType.ProviderConfigured
 * event in frameworkDIDebugData.resolverToProviders
 *
 * @param context InjectorProfilerContext the injection context that this event occurred in.
 * @param data ProviderRecord an object containing the instance that was just created
 *
 */
function handleProviderConfiguredEvent(context, data) {
    const { resolverToProviders } = frameworkDIDebugData;
    let diResolver;
    if (context?.injector instanceof NodeInjector) {
        diResolver = getNodeInjectorTNode(context.injector);
    }
    else {
        diResolver = context.injector;
    }
    if (diResolver === null) {
        throwError('A ProviderConfigured event must be run within an injection context.');
    }
    if (!resolverToProviders.has(diResolver)) {
        resolverToProviders.set(diResolver, []);
    }
    resolverToProviders.get(diResolver).push(data);
}
function getDIResolver(injector) {
    let diResolver = null;
    if (injector === undefined) {
        return diResolver;
    }
    // We use the LView as the diResolver for NodeInjectors because they
    // do not persist anywhere in the framework. They are simply wrappers around an LView and a TNode
    // that do persist. Because of this, we rely on the LView of the NodeInjector in order to use
    // as a concrete key to represent this injector. If we get the same LView back later, we know
    // we're looking at the same injector.
    if (injector instanceof NodeInjector) {
        diResolver = getNodeInjectorLView(injector);
    }
    // Other injectors can be used a keys for a map because their instances
    // persist
    else {
        diResolver = injector;
    }
    return diResolver;
}
// inspired by
// https://tc39.es/ecma262/multipage/executable-code-and-execution-contexts.html#sec-canbeheldweakly
function canBeHeldWeakly(value) {
    // we check for value !== null here because typeof null === 'object
    return value !== null &&
        (typeof value === 'object' || typeof value === 'function' || typeof value === 'symbol');
}
//# sourceMappingURL=data:application/json;base64,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