/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createSignal, SIGNAL, signalSetFn, signalUpdateFn } from '@angular/core/primitives/signals';
import { performanceMarkFeature } from '../../util/performance';
import { isSignal } from './api';
/** Symbol used distinguish `WritableSignal` from other non-writable signals and functions. */
export const ɵWRITABLE_SIGNAL = /* @__PURE__ */ Symbol('WRITABLE_SIGNAL');
/**
 * Utility function used during template type checking to extract the value from a `WritableSignal`.
 * @codeGenApi
 */
export function ɵunwrapWritableSignal(value) {
    // Note: the function uses `WRITABLE_SIGNAL` as a brand instead of `WritableSignal<T>`,
    // because the latter incorrectly unwraps non-signal getter functions.
    return null;
}
/**
 * Create a `Signal` that can be set or updated directly.
 */
export function signal(initialValue, options) {
    performanceMarkFeature('NgSignals');
    const signalFn = createSignal(initialValue);
    const node = signalFn[SIGNAL];
    if (options?.equal) {
        node.equal = options.equal;
    }
    signalFn.set = (newValue) => signalSetFn(node, newValue);
    signalFn.update = (updateFn) => signalUpdateFn(node, updateFn);
    signalFn.asReadonly = signalAsReadonlyFn.bind(signalFn);
    if (ngDevMode) {
        signalFn.toString = () => `[Signal: ${signalFn()}]`;
    }
    return signalFn;
}
export function signalAsReadonlyFn() {
    const node = this[SIGNAL];
    if (node.readonlyFn === undefined) {
        const readonlyFn = () => this();
        readonlyFn[SIGNAL] = node;
        node.readonlyFn = readonlyFn;
    }
    return node.readonlyFn;
}
/**
 * Checks if the given `value` is a writeable signal.
 */
export function isWritableSignal(value) {
    return isSignal(value) && typeof value.set === 'function';
}
//# sourceMappingURL=data:application/json;base64,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