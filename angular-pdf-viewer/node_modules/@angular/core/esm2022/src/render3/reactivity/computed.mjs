/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { createComputed, SIGNAL } from '@angular/core/primitives/signals';
import { performanceMarkFeature } from '../../util/performance';
/**
 * Create a computed `Signal` which derives a reactive value from an expression.
 */
export function computed(computation, options) {
    performanceMarkFeature('NgSignals');
    const getter = createComputed(computation);
    if (options?.equal) {
        getter[SIGNAL].equal = options.equal;
    }
    if (ngDevMode) {
        getter.toString = () => `[Computed: ${getter()}]`;
    }
    return getter;
}
//# sourceMappingURL=data:application/json;base64,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