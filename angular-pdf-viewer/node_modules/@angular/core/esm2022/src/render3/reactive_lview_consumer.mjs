/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { REACTIVE_NODE } from '@angular/core/primitives/signals';
import { REACTIVE_TEMPLATE_CONSUMER } from './interfaces/view';
import { markAncestorsForTraversal } from './util/view_utils';
let freeConsumers = [];
/**
 * Create a new template consumer pointing at the specified LView.
 * Sometimes, a previously created consumer may be reused, in order to save on allocations. In that
 * case, the LView will be updated.
 */
export function getOrBorrowReactiveLViewConsumer(lView) {
    return lView[REACTIVE_TEMPLATE_CONSUMER] ?? borrowReactiveLViewConsumer(lView);
}
function borrowReactiveLViewConsumer(lView) {
    const consumer = freeConsumers.pop() ?? Object.create(REACTIVE_LVIEW_CONSUMER_NODE);
    consumer.lView = lView;
    return consumer;
}
export function maybeReturnReactiveLViewConsumer(consumer) {
    if (consumer.lView[REACTIVE_TEMPLATE_CONSUMER] === consumer) {
        // The consumer got committed.
        return;
    }
    consumer.lView = null;
    freeConsumers.push(consumer);
}
const REACTIVE_LVIEW_CONSUMER_NODE = {
    ...REACTIVE_NODE,
    consumerIsAlwaysLive: true,
    consumerMarkedDirty: (node) => {
        markAncestorsForTraversal(node.lView);
    },
    consumerOnSignalRead() {
        this.lView[REACTIVE_TEMPLATE_CONSUMER] = this;
    },
};
//# sourceMappingURL=data:application/json;base64,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