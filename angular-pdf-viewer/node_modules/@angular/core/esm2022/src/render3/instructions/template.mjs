/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { validateMatchingNode, validateNodeExists } from '../../hydration/error_handling';
import { TEMPLATES } from '../../hydration/interfaces';
import { locateNextRNode, siblingAfter } from '../../hydration/node_lookup_utils';
import { calcSerializedContainerSize, isDisconnectedNode, markRNodeAsClaimedByHydration, setSegmentHead } from '../../hydration/utils';
import { isDetachedByI18n } from '../../i18n/utils';
import { populateDehydratedViewsInLContainer } from '../../linker/view_container_ref';
import { assertEqual } from '../../util/assert';
import { assertFirstCreatePass } from '../assert';
import { attachPatchData } from '../context_discovery';
import { registerPostOrderHooks } from '../hooks';
import { isDirectiveHost } from '../interfaces/type_checks';
import { HEADER_OFFSET, HYDRATION, RENDERER } from '../interfaces/view';
import { appendChild } from '../node_manipulation';
import { getLView, getTView, isInSkipHydrationBlock, lastNodeWasCreated, setCurrentTNode, wasLastNodeCreated } from '../state';
import { getConstant } from '../util/view_utils';
import { addToViewTree, createDirectivesInstances, createLContainer, createTView, getOrCreateTNode, resolveDirectives, saveResolvedLocalsInData } from './shared';
function templateFirstCreatePass(index, tView, lView, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex) {
    ngDevMode && assertFirstCreatePass(tView);
    ngDevMode && ngDevMode.firstCreatePass++;
    const tViewConsts = tView.consts;
    // TODO(pk): refactor getOrCreateTNode to have the "create" only version
    const tNode = getOrCreateTNode(tView, index, 4 /* TNodeType.Container */, tagName || null, getConstant(tViewConsts, attrsIndex));
    resolveDirectives(tView, lView, tNode, getConstant(tViewConsts, localRefsIndex));
    registerPostOrderHooks(tView, tNode);
    const embeddedTView = tNode.tView = createTView(2 /* TViewType.Embedded */, tNode, templateFn, decls, vars, tView.directiveRegistry, tView.pipeRegistry, null, tView.schemas, tViewConsts, null /* ssrId */);
    if (tView.queries !== null) {
        tView.queries.template(tView, tNode);
        embeddedTView.queries = tView.queries.embeddedTView(tNode);
    }
    return tNode;
}
/**
 * Creates an LContainer for an ng-template (dynamically-inserted view), e.g.
 *
 * <ng-template #foo>
 *    <div></div>
 * </ng-template>
 *
 * @param index The index of the container in the data array
 * @param templateFn Inline template
 * @param decls The number of nodes, local refs, and pipes for this template
 * @param vars The number of bindings for this template
 * @param tagName The name of the container element, if applicable
 * @param attrsIndex Index of template attributes in the `consts` array.
 * @param localRefs Index of the local references in the `consts` array.
 * @param localRefExtractor A function which extracts local-refs values from the template.
 *        Defaults to the current element associated with the local-ref.
 *
 * @codeGenApi
 */
export function ɵɵtemplate(index, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex, localRefExtractor) {
    const lView = getLView();
    const tView = getTView();
    const adjustedIndex = index + HEADER_OFFSET;
    const tNode = tView.firstCreatePass ? templateFirstCreatePass(adjustedIndex, tView, lView, templateFn, decls, vars, tagName, attrsIndex, localRefsIndex) :
        tView.data[adjustedIndex];
    setCurrentTNode(tNode, false);
    const comment = _locateOrCreateContainerAnchor(tView, lView, tNode, index);
    if (wasLastNodeCreated()) {
        appendChild(tView, lView, comment, tNode);
    }
    attachPatchData(comment, lView);
    const lContainer = createLContainer(comment, lView, comment, tNode);
    lView[adjustedIndex] = lContainer;
    addToViewTree(lView, lContainer);
    // If hydration is enabled, looks up dehydrated views in the DOM
    // using hydration annotation info and stores those views on LContainer.
    // In client-only mode, this function is a noop.
    populateDehydratedViewsInLContainer(lContainer, tNode, lView);
    if (isDirectiveHost(tNode)) {
        createDirectivesInstances(tView, lView, tNode);
    }
    if (localRefsIndex != null) {
        saveResolvedLocalsInData(lView, tNode, localRefExtractor);
    }
    return ɵɵtemplate;
}
let _locateOrCreateContainerAnchor = createContainerAnchorImpl;
/**
 * Regular creation mode for LContainers and their anchor (comment) nodes.
 */
function createContainerAnchorImpl(tView, lView, tNode, index) {
    lastNodeWasCreated(true);
    return lView[RENDERER].createComment(ngDevMode ? 'container' : '');
}
/**
 * Enables hydration code path (to lookup existing elements in DOM)
 * in addition to the regular creation mode for LContainers and their
 * anchor (comment) nodes.
 */
function locateOrCreateContainerAnchorImpl(tView, lView, tNode, index) {
    const hydrationInfo = lView[HYDRATION];
    const isNodeCreationMode = !hydrationInfo || isInSkipHydrationBlock() ||
        isDetachedByI18n(tNode) || isDisconnectedNode(hydrationInfo, index);
    lastNodeWasCreated(isNodeCreationMode);
    // Regular creation mode.
    if (isNodeCreationMode) {
        return createContainerAnchorImpl(tView, lView, tNode, index);
    }
    const ssrId = hydrationInfo.data[TEMPLATES]?.[index] ?? null;
    // Apply `ssrId` value to the underlying TView if it was not previously set.
    //
    // There might be situations when the same component is present in a template
    // multiple times and some instances are opted-out of using hydration via
    // `ngSkipHydration` attribute. In this scenario, at the time a TView is created,
    // the `ssrId` might be `null` (if the first component is opted-out of hydration).
    // The code below makes sure that the `ssrId` is applied to the TView if it's still
    // `null` and verifies we never try to override it with a different value.
    if (ssrId !== null && tNode.tView !== null) {
        if (tNode.tView.ssrId === null) {
            tNode.tView.ssrId = ssrId;
        }
        else {
            ngDevMode &&
                assertEqual(tNode.tView.ssrId, ssrId, 'Unexpected value of the `ssrId` for this TView');
        }
    }
    // Hydration mode, looking up existing elements in DOM.
    const currentRNode = locateNextRNode(hydrationInfo, tView, lView, tNode);
    ngDevMode && validateNodeExists(currentRNode, lView, tNode);
    setSegmentHead(hydrationInfo, index, currentRNode);
    const viewContainerSize = calcSerializedContainerSize(hydrationInfo, index);
    const comment = siblingAfter(viewContainerSize, currentRNode);
    if (ngDevMode) {
        validateMatchingNode(comment, Node.COMMENT_NODE, null, lView, tNode);
        markRNodeAsClaimedByHydration(comment);
    }
    return comment;
}
export function enableLocateOrCreateContainerAnchorImpl() {
    _locateOrCreateContainerAnchor = locateOrCreateContainerAnchorImpl;
}
//# sourceMappingURL=data:application/json;base64,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