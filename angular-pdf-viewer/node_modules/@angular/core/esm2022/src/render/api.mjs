/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import { isLView } from '../render3/interfaces/type_checks';
import { RENDERER } from '../render3/interfaces/view';
import { getCurrentTNode, getLView } from '../render3/state';
import { getComponentLViewByIndex } from '../render3/util/view_utils';
/**
 * Creates and initializes a custom renderer that implements the `Renderer2` base class.
 *
 * @publicApi
 */
export class RendererFactory2 {
}
/**
 * Extend this base class to implement custom rendering. By default, <PERSON><PERSON>
 * renders a template into DOM. You can use custom rendering to intercept
 * rendering calls, or to render to something other than DOM.
 *
 * Create your custom renderer using `RendererFactory2`.
 *
 * Use a custom renderer to bypass <PERSON><PERSON>'s templating and
 * make custom UI changes that can't be expressed declaratively.
 * For example if you need to set a property or an attribute whose name is
 * not statically known, use the `setProperty()` or
 * `setAttribute()` method.
 *
 * @publicApi
 */
export class Renderer2 {
    constructor() {
        /**
         * If null or undefined, the view engine won't call it.
         * This is used as a performance optimization for production mode.
         */
        this.destroyNode = null;
    }
    /**
     * @internal
     * @nocollapse
     */
    static { this.__NG_ELEMENT_ID__ = () => injectRenderer2(); }
}
/** Injects a Renderer2 for the current component. */
export function injectRenderer2() {
    // We need the Renderer to be based on the component that it's being injected into, however since
    // DI happens before we've entered its view, `getLView` will return the parent view instead.
    const lView = getLView();
    const tNode = getCurrentTNode();
    const nodeAtIndex = getComponentLViewByIndex(tNode.index, lView);
    return (isLView(nodeAtIndex) ? nodeAtIndex : lView)[RENDERER];
}
//# sourceMappingURL=data:application/json;base64,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