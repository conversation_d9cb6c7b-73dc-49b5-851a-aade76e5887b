/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
// clang-format off
export { isSignal, } from './render3/reactivity/api';
export { computed, } from './render3/reactivity/computed';
export { signal, ɵunwrapWritableSignal, } from './render3/reactivity/signal';
export { untracked, } from './render3/reactivity/untracked';
export { effect, EffectScheduler as ɵEffectScheduler, } from './render3/reactivity/effect';
export { assertNotInReactiveContext, } from './render3/reactivity/asserts';
// clang-format on
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiY29yZV9yZWFjdGl2aXR5X2V4cG9ydF9pbnRlcm5hbC5qcyIsInNvdXJjZVJvb3QiOiIiLCJzb3VyY2VzIjpbIi4uLy4uLy4uLy4uLy4uLy4uL3BhY2thZ2VzL2NvcmUvc3JjL2NvcmVfcmVhY3Rpdml0eV9leHBvcnRfaW50ZXJuYWwudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUE7Ozs7OztHQU1HO0FBRUgsbUJBQW1CO0FBQ25CLE9BQU8sRUFDTCxRQUFRLEdBR1QsTUFBTSwwQkFBMEIsQ0FBQztBQUNsQyxPQUFPLEVBQ0wsUUFBUSxHQUVULE1BQU0sK0JBQStCLENBQUM7QUFDdkMsT0FBTyxFQUVMLE1BQU0sRUFFTixxQkFBcUIsR0FDdEIsTUFBTSw2QkFBNkIsQ0FBQztBQUNyQyxPQUFPLEVBQ0wsU0FBUyxHQUNWLE1BQU0sZ0NBQWdDLENBQUM7QUFDeEMsT0FBTyxFQUVMLE1BQU0sRUFJTixlQUFlLElBQUksZ0JBQWdCLEdBQ3BDLE1BQU0sNkJBQTZCLENBQUM7QUFDckMsT0FBTyxFQUNMLDBCQUEwQixHQUMzQixNQUFNLDhCQUE4QixDQUFDO0FBQ3RDLGtCQUFrQiIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG4vLyBjbGFuZy1mb3JtYXQgb2ZmXG5leHBvcnQge1xuICBpc1NpZ25hbCxcbiAgU2lnbmFsLFxuICBWYWx1ZUVxdWFsaXR5Rm4sXG59IGZyb20gJy4vcmVuZGVyMy9yZWFjdGl2aXR5L2FwaSc7XG5leHBvcnQge1xuICBjb21wdXRlZCxcbiAgQ3JlYXRlQ29tcHV0ZWRPcHRpb25zLFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9jb21wdXRlZCc7XG5leHBvcnQge1xuICBDcmVhdGVTaWduYWxPcHRpb25zLFxuICBzaWduYWwsXG4gIFdyaXRhYmxlU2lnbmFsLFxuICDJtXVud3JhcFdyaXRhYmxlU2lnbmFsLFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9zaWduYWwnO1xuZXhwb3J0IHtcbiAgdW50cmFja2VkLFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS91bnRyYWNrZWQnO1xuZXhwb3J0IHtcbiAgQ3JlYXRlRWZmZWN0T3B0aW9ucyxcbiAgZWZmZWN0LFxuICBFZmZlY3RSZWYsXG4gIEVmZmVjdENsZWFudXBGbixcbiAgRWZmZWN0Q2xlYW51cFJlZ2lzdGVyRm4sXG4gIEVmZmVjdFNjaGVkdWxlciBhcyDJtUVmZmVjdFNjaGVkdWxlcixcbn0gZnJvbSAnLi9yZW5kZXIzL3JlYWN0aXZpdHkvZWZmZWN0JztcbmV4cG9ydCB7XG4gIGFzc2VydE5vdEluUmVhY3RpdmVDb250ZXh0LFxufSBmcm9tICcuL3JlbmRlcjMvcmVhY3Rpdml0eS9hc3NlcnRzJztcbi8vIGNsYW5nLWZvcm1hdCBvblxuIl19