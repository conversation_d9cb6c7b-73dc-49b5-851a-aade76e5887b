/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
function defaultThrowError() {
    throw new Error();
}
let throwInvalidWriteToSignalErrorFn = defaultThrowError;
export function throwInvalidWriteToSignalError() {
    throwInvalidWriteToSignalErrorFn();
}
export function setThrowInvalidWriteToSignalError(fn) {
    throwInvalidWriteToSignalErrorFn = fn;
}
//# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiZXJyb3JzLmpzIiwic291cmNlUm9vdCI6IiIsInNvdXJjZXMiOlsiLi4vLi4vLi4vLi4vLi4vLi4vLi4vLi4vcGFja2FnZXMvY29yZS9wcmltaXRpdmVzL3NpZ25hbHMvc3JjL2Vycm9ycy50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTs7Ozs7O0dBTUc7QUFFSCxTQUFTLGlCQUFpQjtJQUN4QixNQUFNLElBQUksS0FBSyxFQUFFLENBQUM7QUFDcEIsQ0FBQztBQUVELElBQUksZ0NBQWdDLEdBQUcsaUJBQWlCLENBQUM7QUFFekQsTUFBTSxVQUFVLDhCQUE4QjtJQUM1QyxnQ0FBZ0MsRUFBRSxDQUFDO0FBQ3JDLENBQUM7QUFFRCxNQUFNLFVBQVUsaUNBQWlDLENBQUMsRUFBZTtJQUMvRCxnQ0FBZ0MsR0FBRyxFQUFFLENBQUM7QUFDeEMsQ0FBQyIsInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGxpY2Vuc2VcbiAqIENvcHlyaWdodCBHb29nbGUgTExDIEFsbCBSaWdodHMgUmVzZXJ2ZWQuXG4gKlxuICogVXNlIG9mIHRoaXMgc291cmNlIGNvZGUgaXMgZ292ZXJuZWQgYnkgYW4gTUlULXN0eWxlIGxpY2Vuc2UgdGhhdCBjYW4gYmVcbiAqIGZvdW5kIGluIHRoZSBMSUNFTlNFIGZpbGUgYXQgaHR0cHM6Ly9hbmd1bGFyLmlvL2xpY2Vuc2VcbiAqL1xuXG5mdW5jdGlvbiBkZWZhdWx0VGhyb3dFcnJvcigpOiBuZXZlciB7XG4gIHRocm93IG5ldyBFcnJvcigpO1xufVxuXG5sZXQgdGhyb3dJbnZhbGlkV3JpdGVUb1NpZ25hbEVycm9yRm4gPSBkZWZhdWx0VGhyb3dFcnJvcjtcblxuZXhwb3J0IGZ1bmN0aW9uIHRocm93SW52YWxpZFdyaXRlVG9TaWduYWxFcnJvcigpIHtcbiAgdGhyb3dJbnZhbGlkV3JpdGVUb1NpZ25hbEVycm9yRm4oKTtcbn1cblxuZXhwb3J0IGZ1bmN0aW9uIHNldFRocm93SW52YWxpZFdyaXRlVG9TaWduYWxFcnJvcihmbjogKCkgPT4gbmV2ZXIpOiB2b2lkIHtcbiAgdGhyb3dJbnZhbGlkV3JpdGVUb1NpZ25hbEVycm9yRm4gPSBmbjtcbn1cbiJdfQ==