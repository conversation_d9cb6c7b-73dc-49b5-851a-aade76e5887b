{"schematics": {"block-template-entities": {"version": "17.0.0", "description": "Angular v17 introduces a new control flow syntax that uses the @ and } characters. This migration replaces the existing usages with their corresponding HTML entities.", "factory": "./migrations/block-template-entities/bundle"}, "migration-v17-compiler-options": {"version": "17.0.0", "description": "CompilerOption.useJit and CompilerOption.missingTranslation are unused under Ivy. This migration removes their usage", "factory": "./migrations/compiler-options/bundle"}, "migration-transfer-state": {"version": "17.0.0", "description": "Updates `TransferState`, `makeStateKey`, `StateKey` imports from `@angular/platform-browser` to `@angular/core`.", "factory": "./migrations/transfer-state/bundle"}, "invalid-two-way-bindings": {"version": "17.3.0", "description": "Updates two-way bindings that have an invalid expression to use the longform expression instead.", "factory": "./migrations/invalid-two-way-bindings/bundle"}}}