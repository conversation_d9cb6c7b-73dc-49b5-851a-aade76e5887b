
      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  PotentialImportKind,
  PotentialImportMode
} from "../chunk-YUMIYLNL.js";
import {
  DynamicValue,
  PartialEvaluator,
  StaticInterpreter,
  forwardRefResolver
} from "../chunk-26Z5EPVF.js";
import {
  Reference,
  TypeScriptReflectionHost,
  reflectObjectLiteral
} from "../chunk-NMMGOE7N.js";
import "../chunk-R4KQI5XI.js";
import "../chunk-75YFKYUJ.js";
import "../chunk-XI2RTGAL.js";
export {
  DynamicValue,
  PartialEvaluator,
  PotentialImportKind,
  PotentialImportMode,
  Reference,
  StaticInterpreter,
  TypeScriptReflectionHost,
  forwardRefResolver,
  reflectObjectLiteral
};
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
//# sourceMappingURL=migrations.js.map
