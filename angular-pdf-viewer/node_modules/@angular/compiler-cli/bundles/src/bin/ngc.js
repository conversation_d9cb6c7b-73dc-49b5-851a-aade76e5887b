#!/usr/bin/env node

      import {createRequire as __cjsCompatRequire} from 'module';
      const require = __cjsCompatRequire(import.meta.url);
    
import {
  main
} from "../../chunk-ZVICXMWS.js";
import "../../chunk-LV7FGTGX.js";
import "../../chunk-YUMIYLNL.js";
import "../../chunk-26Z5EPVF.js";
import "../../chunk-NMMGOE7N.js";
import "../../chunk-R4KQI5XI.js";
import {
  NodeJSFileSystem,
  setFileSystem
} from "../../chunk-75YFKYUJ.js";
import "../../chunk-XI2RTGAL.js";

// bazel-out/darwin_arm64-fastbuild/bin/packages/compiler-cli/src/bin/ngc.mjs
import "reflect-metadata";
async function runNgcComamnd() {
  process.title = "Angular Compiler (ngc)";
  const args = process.argv.slice(2);
  setFileSystem(new NodeJSFileSystem());
  process.exitCode = main(args, void 0, void 0, void 0, void 0, void 0);
}
runNgcComamnd().catch((e) => {
  console.error(e);
  process.exitCode = 1;
});
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
//# sourceMappingURL=ngc.js.map
