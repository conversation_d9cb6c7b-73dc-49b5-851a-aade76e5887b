/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { COMPILER_ERRORS_WITH_GUIDES } from './src/docs';
export { addDiagno<PERSON><PERSON>hain, FatalDiagnosticError, isFatalDiagnosticError, isLocalCompilationDiagnostics, makeDiagnostic, makeDiagnosticChain, makeRelatedInformation } from './src/error';
export { ErrorCode } from './src/error_code';
export { ERROR_DETAILS_PAGE_BASE_URL } from './src/error_details_base_url';
export { ExtendedTemplateDiagnosticName } from './src/extended_template_diagnostic_name';
export { ngErrorCode, replaceTsWithNgInErrors } from './src/util';
