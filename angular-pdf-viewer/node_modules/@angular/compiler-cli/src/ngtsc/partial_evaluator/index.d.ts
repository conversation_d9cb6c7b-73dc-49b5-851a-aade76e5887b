/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
export { describeResolvedType, traceDynamicValue } from './src/diagnostics';
export { DynamicValue } from './src/dynamic';
export { ForeignFunctionResolver, PartialEvaluator } from './src/interface';
export { StaticInterpreter } from './src/interpreter';
export { EnumValue, KnownFn, ResolvedValue, ResolvedValueArray, ResolvedValueMap } from './src/result';
export { SyntheticValue } from './src/synthetic';
