/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
import ts from 'typescript';
import { ImportedSymbolsTracker } from '../../../ngtsc/imports';
import { ClassMember, Decorator, ReflectionHost } from '../../../ngtsc/reflection';
import { ImportManager } from '../../../ngtsc/translator';
/** Function that can be used to transform class properties. */
export type PropertyTransform = (member: Pick<ClassMember, 'name' | 'accessLevel' | 'value'> & {
    node: ts.PropertyDeclaration;
}, host: ReflectionHost, factory: ts.NodeFactory, importTracker: ImportedSymbolsTracker, importManager: ImportManager, classDecorator: Decorator, isCore: boolean) => ts.PropertyDeclaration;
/**
 * Creates an import and access for a given Angular core import while
 * ensuring the decorator symbol access can be traced back to an Angular core
 * import in order to make the synthetic decorator compatible with the JIT
 * decorator downlevel transform.
 */
export declare function createSyntheticAngularCoreDecoratorAccess(factory: ts.NodeFactory, importManager: ImportManager, ngClassDecorator: Decorator, sourceFile: ts.SourceFile, decoratorName: string): ts.PropertyAccessExpression;
/** Casts the given expression as `any`. */
export declare function castAsAny(factory: ts.NodeFactory, expr: ts.Expression): ts.Expression;
