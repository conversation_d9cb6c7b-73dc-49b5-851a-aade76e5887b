{"version": 3, "names": ["_assert", "data", "require", "_fs", "_interopRequireWildcard", "_process", "_url", "_path", "_module", "_v", "_util", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "prototype", "hasOwnProperty", "call", "i", "set", "own$1", "classRegExp", "kTypes", "Set", "codes", "formatList", "array", "type", "length", "join", "slice", "messages", "Map", "nodeInternalPrefix", "userStackTraceLimit", "ERR_INVALID_ARG_TYPE", "createError", "name", "expected", "actual", "assert", "Array", "isArray", "message", "endsWith", "includes", "types", "instances", "other", "value", "push", "toLowerCase", "exec", "pos", "indexOf", "determineSpecificType", "TypeError", "ERR_INVALID_MODULE_SPECIFIER", "request", "reason", "base", "undefined", "ERR_INVALID_PACKAGE_CONFIG", "path", "Error", "ERR_INVALID_PACKAGE_TARGET", "pkgPath", "key", "target", "isImport", "rel<PERSON><PERSON><PERSON>", "startsWith", "JSON", "stringify", "ERR_MODULE_NOT_FOUND", "exactUrl", "ERR_NETWORK_IMPORT_DISALLOWED", "ERR_PACKAGE_IMPORT_NOT_DEFINED", "specifier", "packagePath", "ERR_PACKAGE_PATH_NOT_EXPORTED", "subpath", "ERR_UNSUPPORTED_DIR_IMPORT", "ERR_UNKNOWN_FILE_EXTENSION", "ext", "ERR_INVALID_ARG_VALUE", "inspected", "inspect", "sym", "def", "makeNodeErrorWithCode", "Base", "NodeError", "args", "limit", "stackTraceLimit", "isErrorStackTraceLimitWritable", "error", "getMessage", "defineProperties", "enumerable", "writable", "configurable", "toString", "captureLargerStackTrace", "code", "v8", "startupSnapshot", "isBuildingSnapshot", "_unused", "desc", "isExtensible", "hideStackFrames", "fn", "hidden", "stackTraceLimitIsWritable", "Number", "POSITIVE_INFINITY", "captureStackTrace", "self", "Reflect", "apply", "regex", "<PERSON><PERSON><PERSON><PERSON>", "unshift", "format", "String", "constructor", "depth", "colors", "hasOwnProperty$1", "ERR_INVALID_PACKAGE_CONFIG$1", "cache", "reader", "read", "packageJsonReader", "jsonPath", "existing", "string", "fs", "readFileSync", "toNamespacedPath", "exception", "result", "exists", "p<PERSON><PERSON><PERSON><PERSON>", "main", "exports", "imports", "parsed", "parse", "error_", "cause", "fileURLToPath", "getPackageScopeConfig", "resolved", "packageJSONUrl", "URL", "packageJSONPath", "pathname", "packageConfig", "lastPackageJSONUrl", "getPackageType", "url", "extensionFormatMap", "mimeToFormat", "mime", "test", "protocolHandlers", "getDataProtocolModuleFormat", "getFileProtocolModuleFormat", "getHttpProtocolModuleFormat", "node:", "extname", "index", "codePointAt", "_context", "ignoreErrors", "packageType", "filepath", "defaultGetFormatWithoutErrors", "context", "protocol", "DEFAULT_CONDITIONS", "freeze", "DEFAULT_CONDITIONS_SET", "getDefaultConditions", "getDefaultConditionsSet", "getConditionsSet", "conditions", "RegExpPrototypeSymbolReplace", "RegExp", "Symbol", "replace", "own", "invalidSegmentRegEx", "deprecatedInvalidSegmentRegEx", "invalidPackageNameRegEx", "patternRegEx", "encodedSepRegEx", "emittedPackageWarnings", "doubleSlashRegEx", "emitInvalidSegmentDeprecation", "match", "packageJsonUrl", "internal", "<PERSON><PERSON><PERSON><PERSON>", "process", "noDeprecation", "double", "emitWarning", "emitLegacyIndexDeprecation", "parentURL", "href", "url<PERSON><PERSON>", "basePath", "resolve", "tryStatSync", "statSync", "_unused2", "Stats", "fileExists", "stats", "throwIfNoEntry", "isFile", "legacyMainResolve", "guess", "tries", "finalizeResolution", "preserveSymlinks", "filePath", "isDirectory", "real", "realpathSync", "search", "hash", "pathToFileURL", "sep", "importNotDefined", "exportsNotFound", "throwInvalidSubpath", "invalidPackageTarget", "resolvePackageTargetString", "pattern", "isPathMap", "isURL", "_unused3", "exportTarget", "packageResolve", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "isArrayIndex", "keyNumber", "resolvePackageTarget", "packageSubpath", "targetList", "lastException", "targetItem", "resolveResult", "keys", "getOwnPropertyNames", "<PERSON><PERSON><PERSON><PERSON>", "isConditionalExportsMainSugar", "isConditionalSugar", "j", "curIsConditionalSugar", "emitTrailingSlashPatternDeprecation", "pjsonUrl", "add", "packageExportsResolve", "bestMatch", "bestMatchSubpath", "patternIndex", "patternTrailer", "patternKeyCompare", "lastIndexOf", "b", "aPatternIndex", "bPatternIndex", "baseLengthA", "baseLengthB", "packageImportsResolve", "parsePackageName", "separatorIndex", "validPackageName", "isScoped", "packageName", "builtinModules", "packageJsonPath", "last<PERSON><PERSON>", "stat", "isRelativeSpecifier", "shouldBeTreatedAsRelativeOrAbsolutePath", "moduleResolve", "isRemote", "_unused4", "checkIfDisallowedImport", "parsedParentURL", "parentProtocol", "parsedProtocol", "Boolean", "throwIfInvalidParentURL", "defaultResolve", "_unused5", "_unused6", "maybeReturn", "parent"], "sources": ["../../src/vendor/import-meta-resolve.js"], "sourcesContent": ["\n/****************************************************************************\\\n *                         NOTE FROM BABEL AUTHORS                          *\n * This file is inlined from https://github.com/wooorm/import-meta-resolve, *\n * because we need to compile it to CommonJS.                               *\n\\****************************************************************************/\n\n/*\n(The MIT License)\n\nCopyright (c) 2021 Titus Wormer <mailto:<EMAIL>>\n\nPermission is hereby granted, free of charge, to any person obtaining\na copy of this software and associated documentation files (the\n'Software'), to deal in the Software without restriction, including\nwithout limitation the rights to use, copy, modify, merge, publish,\ndistribute, sublicense, and/or sell copies of the Software, and to\npermit persons to whom the Software is furnished to do so, subject to\nthe following conditions:\n\nThe above copyright notice and this permission notice shall be\nincluded in all copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED 'AS IS', WITHOUT WARRANTY OF ANY KIND,\nEXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\nMERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.\nIN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY\nCLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT,\nTORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE\nSOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n---\n\nThis is a derivative work based on:\n<https://github.com/nodejs/node>.\nWhich is licensed:\n\n\"\"\"\nCopyright Node.js contributors. All rights reserved.\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n\nThis license applies to parts of Node.js originating from the\nhttps://github.com/joyent/node repository:\n\n\"\"\"\nCopyright Joyent, Inc. and other Node contributors. All rights reserved.\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to\ndeal in the Software without restriction, including without limitation the\nrights to use, copy, modify, merge, publish, distribute, sublicense, and/or\nsell copies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in\nall copies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING\nFROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS\nIN THE SOFTWARE.\n\"\"\"\n*/\n\nimport assert from 'assert';\nimport fs, { realpathSync, statSync, Stats } from 'fs';\nimport process from 'process';\nimport { fileURLToPath, URL, pathToFileURL } from 'url';\nimport path from 'path';\nimport { builtinModules } from 'module';\nimport v8 from 'v8';\nimport { format, inspect } from 'util';\n\n/**\n * @typedef ErrnoExceptionFields\n * @property {number | undefined} [errnode]\n * @property {string | undefined} [code]\n * @property {string | undefined} [path]\n * @property {string | undefined} [syscall]\n * @property {string | undefined} [url]\n *\n * @typedef {Error & ErrnoExceptionFields} ErrnoException\n */\n\n\nconst own$1 = {}.hasOwnProperty;\n\nconst classRegExp = /^([A-Z][a-z\\d]*)+$/;\n// Sorted by a rough estimate on most frequently used entries.\nconst kTypes = new Set([\n  'string',\n  'function',\n  'number',\n  'object',\n  // Accept 'Function' and 'Object' as alternative to the lower cased version.\n  'Function',\n  'Object',\n  'boolean',\n  'bigint',\n  'symbol'\n]);\n\nconst codes = {};\n\n/**\n * Create a list string in the form like 'A and B' or 'A, B, ..., and Z'.\n * We cannot use Intl.ListFormat because it's not available in\n * --without-intl builds.\n *\n * @param {Array<string>} array\n *   An array of strings.\n * @param {string} [type]\n *   The list type to be inserted before the last element.\n * @returns {string}\n */\nfunction formatList(array, type = 'and') {\n  return array.length < 3\n    ? array.join(` ${type} `)\n    : `${array.slice(0, -1).join(', ')}, ${type} ${array[array.length - 1]}`\n}\n\n/** @type {Map<string, MessageFunction | string>} */\nconst messages = new Map();\nconst nodeInternalPrefix = '__node_internal_';\n/** @type {number} */\nlet userStackTraceLimit;\n\ncodes.ERR_INVALID_ARG_TYPE = createError(\n  'ERR_INVALID_ARG_TYPE',\n  /**\n   * @param {string} name\n   * @param {Array<string> | string} expected\n   * @param {unknown} actual\n   */\n  (name, expected, actual) => {\n    assert(typeof name === 'string', \"'name' must be a string\");\n    if (!Array.isArray(expected)) {\n      expected = [expected];\n    }\n\n    let message = 'The ';\n    if (name.endsWith(' argument')) {\n      // For cases like 'first argument'\n      message += `${name} `;\n    } else {\n      const type = name.includes('.') ? 'property' : 'argument';\n      message += `\"${name}\" ${type} `;\n    }\n\n    message += 'must be ';\n\n    /** @type {Array<string>} */\n    const types = [];\n    /** @type {Array<string>} */\n    const instances = [];\n    /** @type {Array<string>} */\n    const other = [];\n\n    for (const value of expected) {\n      assert(\n        typeof value === 'string',\n        'All expected entries have to be of type string'\n      );\n\n      if (kTypes.has(value)) {\n        types.push(value.toLowerCase());\n      } else if (classRegExp.exec(value) === null) {\n        assert(\n          value !== 'object',\n          'The value \"object\" should be written as \"Object\"'\n        );\n        other.push(value);\n      } else {\n        instances.push(value);\n      }\n    }\n\n    // Special handle `object` in case other instances are allowed to outline\n    // the differences between each other.\n    if (instances.length > 0) {\n      const pos = types.indexOf('object');\n      if (pos !== -1) {\n        types.slice(pos, 1);\n        instances.push('Object');\n      }\n    }\n\n    if (types.length > 0) {\n      message += `${types.length > 1 ? 'one of type' : 'of type'} ${formatList(\n        types,\n        'or'\n      )}`;\n      if (instances.length > 0 || other.length > 0) message += ' or ';\n    }\n\n    if (instances.length > 0) {\n      message += `an instance of ${formatList(instances, 'or')}`;\n      if (other.length > 0) message += ' or ';\n    }\n\n    if (other.length > 0) {\n      if (other.length > 1) {\n        message += `one of ${formatList(other, 'or')}`;\n      } else {\n        if (other[0].toLowerCase() !== other[0]) message += 'an ';\n        message += `${other[0]}`;\n      }\n    }\n\n    message += `. Received ${determineSpecificType(actual)}`;\n\n    return message\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_MODULE_SPECIFIER = createError(\n  'ERR_INVALID_MODULE_SPECIFIER',\n  /**\n   * @param {string} request\n   * @param {string} reason\n   * @param {string} [base]\n   */\n  (request, reason, base = undefined) => {\n    return `Invalid module \"${request}\" ${reason}${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_PACKAGE_CONFIG = createError(\n  'ERR_INVALID_PACKAGE_CONFIG',\n  /**\n   * @param {string} path\n   * @param {string} [base]\n   * @param {string} [message]\n   */\n  (path, base, message) => {\n    return `Invalid package config ${path}${\n      base ? ` while importing ${base}` : ''\n    }${message ? `. ${message}` : ''}`\n  },\n  Error\n);\n\ncodes.ERR_INVALID_PACKAGE_TARGET = createError(\n  'ERR_INVALID_PACKAGE_TARGET',\n  /**\n   * @param {string} pkgPath\n   * @param {string} key\n   * @param {unknown} target\n   * @param {boolean} [isImport=false]\n   * @param {string} [base]\n   */\n  (pkgPath, key, target, isImport = false, base = undefined) => {\n    const relError =\n      typeof target === 'string' &&\n      !isImport &&\n      target.length > 0 &&\n      !target.startsWith('./');\n    if (key === '.') {\n      assert(isImport === false);\n      return (\n        `Invalid \"exports\" main target ${JSON.stringify(target)} defined ` +\n        `in the package config ${pkgPath}package.json${\n          base ? ` imported from ${base}` : ''\n        }${relError ? '; targets must start with \"./\"' : ''}`\n      )\n    }\n\n    return `Invalid \"${\n      isImport ? 'imports' : 'exports'\n    }\" target ${JSON.stringify(\n      target\n    )} defined for '${key}' in the package config ${pkgPath}package.json${\n      base ? ` imported from ${base}` : ''\n    }${relError ? '; targets must start with \"./\"' : ''}`\n  },\n  Error\n);\n\ncodes.ERR_MODULE_NOT_FOUND = createError(\n  'ERR_MODULE_NOT_FOUND',\n  /**\n   * @param {string} path\n   * @param {string} base\n   * @param {boolean} [exactUrl]\n   */\n  (path, base, exactUrl = false) => {\n    return `Cannot find ${\n      exactUrl ? 'module' : 'package'\n    } '${path}' imported from ${base}`\n  },\n  Error\n);\n\ncodes.ERR_NETWORK_IMPORT_DISALLOWED = createError(\n  'ERR_NETWORK_IMPORT_DISALLOWED',\n  \"import of '%s' by %s is not supported: %s\",\n  Error\n);\n\ncodes.ERR_PACKAGE_IMPORT_NOT_DEFINED = createError(\n  'ERR_PACKAGE_IMPORT_NOT_DEFINED',\n  /**\n   * @param {string} specifier\n   * @param {string} packagePath\n   * @param {string} base\n   */\n  (specifier, packagePath, base) => {\n    return `Package import specifier \"${specifier}\" is not defined${\n      packagePath ? ` in package ${packagePath}package.json` : ''\n    } imported from ${base}`\n  },\n  TypeError\n);\n\ncodes.ERR_PACKAGE_PATH_NOT_EXPORTED = createError(\n  'ERR_PACKAGE_PATH_NOT_EXPORTED',\n  /**\n   * @param {string} pkgPath\n   * @param {string} subpath\n   * @param {string} [base]\n   */\n  (pkgPath, subpath, base = undefined) => {\n    if (subpath === '.')\n      return `No \"exports\" main defined in ${pkgPath}package.json${\n        base ? ` imported from ${base}` : ''\n      }`\n    return `Package subpath '${subpath}' is not defined by \"exports\" in ${pkgPath}package.json${\n      base ? ` imported from ${base}` : ''\n    }`\n  },\n  Error\n);\n\ncodes.ERR_UNSUPPORTED_DIR_IMPORT = createError(\n  'ERR_UNSUPPORTED_DIR_IMPORT',\n  \"Directory import '%s' is not supported \" +\n    'resolving ES modules imported from %s',\n  Error\n);\n\ncodes.ERR_UNKNOWN_FILE_EXTENSION = createError(\n  'ERR_UNKNOWN_FILE_EXTENSION',\n  /**\n   * @param {string} ext\n   * @param {string} path\n   */\n  (ext, path) => {\n    return `Unknown file extension \"${ext}\" for ${path}`\n  },\n  TypeError\n);\n\ncodes.ERR_INVALID_ARG_VALUE = createError(\n  'ERR_INVALID_ARG_VALUE',\n  /**\n   * @param {string} name\n   * @param {unknown} value\n   * @param {string} [reason='is invalid']\n   */\n  (name, value, reason = 'is invalid') => {\n    let inspected = inspect(value);\n\n    if (inspected.length > 128) {\n      inspected = `${inspected.slice(0, 128)}...`;\n    }\n\n    const type = name.includes('.') ? 'property' : 'argument';\n\n    return `The ${type} '${name}' ${reason}. Received ${inspected}`\n  },\n  TypeError\n  // Note: extra classes have been shaken out.\n  // , RangeError\n);\n\n/**\n * Utility function for registering the error codes. Only used here. Exported\n * *only* to allow for testing.\n * @param {string} sym\n * @param {MessageFunction | string} value\n * @param {ErrorConstructor} def\n * @returns {new (...args: Array<any>) => Error}\n */\nfunction createError(sym, value, def) {\n  // Special case for SystemError that formats the error message differently\n  // The SystemErrors only have SystemError as their base classes.\n  messages.set(sym, value);\n\n  return makeNodeErrorWithCode(def, sym)\n}\n\n/**\n * @param {ErrorConstructor} Base\n * @param {string} key\n * @returns {ErrorConstructor}\n */\nfunction makeNodeErrorWithCode(Base, key) {\n  // @ts-expect-error It’s a Node error.\n  return NodeError\n  /**\n   * @param {Array<unknown>} args\n   */\n  function NodeError(...args) {\n    const limit = Error.stackTraceLimit;\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = 0;\n    const error = new Base();\n    // Reset the limit and setting the name property.\n    if (isErrorStackTraceLimitWritable()) Error.stackTraceLimit = limit;\n    const message = getMessage(key, args, error);\n    Object.defineProperties(error, {\n      // Note: no need to implement `kIsNodeError` symbol, would be hard,\n      // probably.\n      message: {\n        value: message,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      },\n      toString: {\n        /** @this {Error} */\n        value() {\n          return `${this.name} [${key}]: ${this.message}`\n        },\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n\n    captureLargerStackTrace(error);\n    // @ts-expect-error It’s a Node error.\n    error.code = key;\n    return error\n  }\n}\n\n/**\n * @returns {boolean}\n */\nfunction isErrorStackTraceLimitWritable() {\n  // Do no touch Error.stackTraceLimit as V8 would attempt to install\n  // it again during deserialization.\n  try {\n    // @ts-expect-error: not in types?\n    if (v8.startupSnapshot.isBuildingSnapshot()) {\n      return false\n    }\n  } catch {}\n\n  const desc = Object.getOwnPropertyDescriptor(Error, 'stackTraceLimit');\n  if (desc === undefined) {\n    return Object.isExtensible(Error)\n  }\n\n  return own$1.call(desc, 'writable') && desc.writable !== undefined\n    ? desc.writable\n    : desc.set !== undefined\n}\n\n/**\n * This function removes unnecessary frames from Node.js core errors.\n * @template {(...args: unknown[]) => unknown} T\n * @param {T} fn\n * @returns {T}\n */\nfunction hideStackFrames(fn) {\n  // We rename the functions that will be hidden to cut off the stacktrace\n  // at the outermost one\n  const hidden = nodeInternalPrefix + fn.name;\n  Object.defineProperty(fn, 'name', {value: hidden});\n  return fn\n}\n\nconst captureLargerStackTrace = hideStackFrames(\n  /**\n   * @param {Error} error\n   * @returns {Error}\n   */\n  // @ts-expect-error: fine\n  function (error) {\n    const stackTraceLimitIsWritable = isErrorStackTraceLimitWritable();\n    if (stackTraceLimitIsWritable) {\n      userStackTraceLimit = Error.stackTraceLimit;\n      Error.stackTraceLimit = Number.POSITIVE_INFINITY;\n    }\n\n    Error.captureStackTrace(error);\n\n    // Reset the limit\n    if (stackTraceLimitIsWritable) Error.stackTraceLimit = userStackTraceLimit;\n\n    return error\n  }\n);\n\n/**\n * @param {string} key\n * @param {Array<unknown>} args\n * @param {Error} self\n * @returns {string}\n */\nfunction getMessage(key, args, self) {\n  const message = messages.get(key);\n  assert(message !== undefined, 'expected `message` to be found');\n\n  if (typeof message === 'function') {\n    assert(\n      message.length <= args.length, // Default options do not count.\n      `Code: ${key}; The provided arguments length (${args.length}) does not ` +\n        `match the required ones (${message.length}).`\n    );\n    return Reflect.apply(message, self, args)\n  }\n\n  const regex = /%[dfijoOs]/g;\n  let expectedLength = 0;\n  while (regex.exec(message) !== null) expectedLength++;\n  assert(\n    expectedLength === args.length,\n    `Code: ${key}; The provided arguments length (${args.length}) does not ` +\n      `match the required ones (${expectedLength}).`\n  );\n  if (args.length === 0) return message\n\n  args.unshift(message);\n  return Reflect.apply(format, null, args)\n}\n\n/**\n * Determine the specific type of a value for type-mismatch errors.\n * @param {unknown} value\n * @returns {string}\n */\nfunction determineSpecificType(value) {\n  if (value === null || value === undefined) {\n    return String(value)\n  }\n\n  if (typeof value === 'function' && value.name) {\n    return `function ${value.name}`\n  }\n\n  if (typeof value === 'object') {\n    if (value.constructor && value.constructor.name) {\n      return `an instance of ${value.constructor.name}`\n    }\n\n    return `${inspect(value, {depth: -1})}`\n  }\n\n  let inspected = inspect(value, {colors: false});\n\n  if (inspected.length > 28) {\n    inspected = `${inspected.slice(0, 25)}...`;\n  }\n\n  return `type ${typeof value} (${inspected})`\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/package_json_reader.js>\n// Last checked on: Nov 2, 2023.\n// Removed the native dependency.\n// Also: no need to cache, we do that in resolve already.\n\n\nconst hasOwnProperty$1 = {}.hasOwnProperty;\n\nconst {ERR_INVALID_PACKAGE_CONFIG: ERR_INVALID_PACKAGE_CONFIG$1} = codes;\n\n/** @type {Map<string, PackageConfig>} */\nconst cache = new Map();\n\nconst reader = {read};\nvar packageJsonReader = reader;\n\n/**\n * @param {string} jsonPath\n * @param {{specifier: URL | string, base?: URL}} options\n * @returns {PackageConfig}\n */\nfunction read(jsonPath, {base, specifier}) {\n  const existing = cache.get(jsonPath);\n\n  if (existing) {\n    return existing\n  }\n\n  /** @type {string | undefined} */\n  let string;\n\n  try {\n    string = fs.readFileSync(path.toNamespacedPath(jsonPath), 'utf8');\n  } catch (error) {\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (exception.code !== 'ENOENT') {\n      throw exception\n    }\n  }\n\n  /** @type {PackageConfig} */\n  const result = {\n    exists: false,\n    pjsonPath: jsonPath,\n    main: undefined,\n    name: undefined,\n    type: 'none', // Ignore unknown types for forwards compatibility\n    exports: undefined,\n    imports: undefined\n  };\n\n  if (string !== undefined) {\n    /** @type {Record<string, unknown>} */\n    let parsed;\n\n    try {\n      parsed = JSON.parse(string);\n    } catch (error_) {\n      const cause = /** @type {ErrnoException} */ (error_);\n      const error = new ERR_INVALID_PACKAGE_CONFIG$1(\n        jsonPath,\n        (base ? `\"${specifier}\" from ` : '') + fileURLToPath(base || specifier),\n        cause.message\n      );\n      // @ts-expect-error: fine.\n      error.cause = cause;\n      throw error\n    }\n\n    result.exists = true;\n\n    if (\n      hasOwnProperty$1.call(parsed, 'name') &&\n      typeof parsed.name === 'string'\n    ) {\n      result.name = parsed.name;\n    }\n\n    if (\n      hasOwnProperty$1.call(parsed, 'main') &&\n      typeof parsed.main === 'string'\n    ) {\n      result.main = parsed.main;\n    }\n\n    if (hasOwnProperty$1.call(parsed, 'exports')) {\n      // @ts-expect-error: assume valid.\n      result.exports = parsed.exports;\n    }\n\n    if (hasOwnProperty$1.call(parsed, 'imports')) {\n      // @ts-expect-error: assume valid.\n      result.imports = parsed.imports;\n    }\n\n    // Ignore unknown types for forwards compatibility\n    if (\n      hasOwnProperty$1.call(parsed, 'type') &&\n      (parsed.type === 'commonjs' || parsed.type === 'module')\n    ) {\n      result.type = parsed.type;\n    }\n  }\n\n  cache.set(jsonPath, result);\n\n  return result\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/package_config.js>\n// Last checked on: Nov 2, 2023.\n\n\n/**\n * @param {URL | string} resolved\n * @returns {PackageConfig}\n */\nfunction getPackageScopeConfig(resolved) {\n  let packageJSONUrl = new URL('package.json', resolved);\n\n  while (true) {\n    const packageJSONPath = packageJSONUrl.pathname;\n    if (packageJSONPath.endsWith('node_modules/package.json')) {\n      break\n    }\n\n    const packageConfig = packageJsonReader.read(\n      fileURLToPath(packageJSONUrl),\n      {specifier: resolved}\n    );\n\n    if (packageConfig.exists) {\n      return packageConfig\n    }\n\n    const lastPackageJSONUrl = packageJSONUrl;\n    packageJSONUrl = new URL('../package.json', packageJSONUrl);\n\n    // Terminates at root where ../package.json equals ../../package.json\n    // (can't just check \"/package.json\" for Windows support).\n    if (packageJSONUrl.pathname === lastPackageJSONUrl.pathname) {\n      break\n    }\n  }\n\n  const packageJSONPath = fileURLToPath(packageJSONUrl);\n\n  return {\n    pjsonPath: packageJSONPath,\n    exists: false,\n    main: undefined,\n    name: undefined,\n    type: 'none',\n    exports: undefined,\n    imports: undefined\n  }\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/resolve.js>\n// Last checked on: Nov 2, 2023.\n//\n// This file solves a circular dependency.\n// In Node.js, `getPackageType` is in `resolve.js`.\n// `resolve.js` imports `get-format.js`, which needs `getPackageType`.\n// We split that up so that bundlers don’t fail.\n\n\n/**\n * @param {URL} url\n * @returns {PackageType}\n */\nfunction getPackageType(url) {\n  const packageConfig = getPackageScopeConfig(url);\n  return packageConfig.type\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/get_format.js>\n// Last checked on: Nov 2, 2023.\n\n\nconst {ERR_UNKNOWN_FILE_EXTENSION} = codes;\n\nconst hasOwnProperty = {}.hasOwnProperty;\n\n/** @type {Record<string, string>} */\nconst extensionFormatMap = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  '.cjs': 'commonjs',\n  '.js': 'module',\n  '.json': 'json',\n  '.mjs': 'module'\n};\n\n/**\n * @param {string | null} mime\n * @returns {string | null}\n */\nfunction mimeToFormat(mime) {\n  if (\n    mime &&\n    /\\s*(text|application)\\/javascript\\s*(;\\s*charset=utf-?8\\s*)?/i.test(mime)\n  )\n    return 'module'\n  if (mime === 'application/json') return 'json'\n  return null\n}\n\n/**\n * @callback ProtocolHandler\n * @param {URL} parsed\n * @param {{parentURL: string, source?: Buffer}} context\n * @param {boolean} ignoreErrors\n * @returns {string | null | void}\n */\n\n/**\n * @type {Record<string, ProtocolHandler>}\n */\nconst protocolHandlers = {\n  // @ts-expect-error: hush.\n  __proto__: null,\n  'data:': getDataProtocolModuleFormat,\n  'file:': getFileProtocolModuleFormat,\n  'http:': getHttpProtocolModuleFormat,\n  'https:': getHttpProtocolModuleFormat,\n  'node:'() {\n    return 'builtin'\n  }\n};\n\n/**\n * @param {URL} parsed\n */\nfunction getDataProtocolModuleFormat(parsed) {\n  const {1: mime} = /^([^/]+\\/[^;,]+)[^,]*?(;base64)?,/.exec(\n    parsed.pathname\n  ) || [null, null, null];\n  return mimeToFormat(mime)\n}\n\n/**\n * Returns the file extension from a URL.\n *\n * Should give similar result to\n * `require('node:path').extname(require('node:url').fileURLToPath(url))`\n * when used with a `file:` URL.\n *\n * @param {URL} url\n * @returns {string}\n */\nfunction extname(url) {\n  const pathname = url.pathname;\n  let index = pathname.length;\n\n  while (index--) {\n    const code = pathname.codePointAt(index);\n\n    if (code === 47 /* `/` */) {\n      return ''\n    }\n\n    if (code === 46 /* `.` */) {\n      return pathname.codePointAt(index - 1) === 47 /* `/` */\n        ? ''\n        : pathname.slice(index)\n    }\n  }\n\n  return ''\n}\n\n/**\n * @type {ProtocolHandler}\n */\nfunction getFileProtocolModuleFormat(url, _context, ignoreErrors) {\n  const ext = extname(url);\n\n  if (ext === '.js') {\n    const packageType = getPackageType(url);\n\n    if (packageType !== 'none') {\n      return packageType\n    }\n\n    return 'commonjs'\n  }\n\n  if (ext === '') {\n    const packageType = getPackageType(url);\n\n    // Legacy behavior\n    if (packageType === 'none' || packageType === 'commonjs') {\n      return 'commonjs'\n    }\n\n    // Note: we don’t implement WASM, so we don’t need\n    // `getFormatOfExtensionlessFile` from `formats`.\n    return 'module'\n  }\n\n  const format = extensionFormatMap[ext];\n  if (format) return format\n\n  // Explicit undefined return indicates load hook should rerun format check\n  if (ignoreErrors) {\n    return undefined\n  }\n\n  const filepath = fileURLToPath(url);\n  throw new ERR_UNKNOWN_FILE_EXTENSION(ext, filepath)\n}\n\nfunction getHttpProtocolModuleFormat() {\n  // To do: HTTPS imports.\n}\n\n/**\n * @param {URL} url\n * @param {{parentURL: string}} context\n * @returns {string | null}\n */\nfunction defaultGetFormatWithoutErrors(url, context) {\n  const protocol = url.protocol;\n\n  if (!hasOwnProperty.call(protocolHandlers, protocol)) {\n    return null\n  }\n\n  return protocolHandlers[protocol](url, context, true) || null\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/utils.js>\n// Last checked on: Nov 2, 2023.\n\n\nconst {ERR_INVALID_ARG_VALUE} = codes;\n\n// In Node itself these values are populated from CLI arguments, before any\n// user code runs.\n// Here we just define the defaults.\nconst DEFAULT_CONDITIONS = Object.freeze(['node', 'import']);\nconst DEFAULT_CONDITIONS_SET = new Set(DEFAULT_CONDITIONS);\n\n/**\n * Returns the default conditions for ES module loading.\n */\nfunction getDefaultConditions() {\n  return DEFAULT_CONDITIONS\n}\n\n/**\n * Returns the default conditions for ES module loading, as a Set.\n */\nfunction getDefaultConditionsSet() {\n  return DEFAULT_CONDITIONS_SET\n}\n\n/**\n * @param {Array<string>} [conditions]\n * @returns {Set<string>}\n */\nfunction getConditionsSet(conditions) {\n  if (conditions !== undefined && conditions !== getDefaultConditions()) {\n    if (!Array.isArray(conditions)) {\n      throw new ERR_INVALID_ARG_VALUE(\n        'conditions',\n        conditions,\n        'expected an array'\n      )\n    }\n\n    return new Set(conditions)\n  }\n\n  return getDefaultConditionsSet()\n}\n\n// Manually “tree shaken” from:\n// <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/resolve.js>\n// Last checked on: Nov 2, 2023.\n\n\nconst RegExpPrototypeSymbolReplace = RegExp.prototype[Symbol.replace];\n\nconst {\n  ERR_NETWORK_IMPORT_DISALLOWED,\n  ERR_INVALID_MODULE_SPECIFIER,\n  ERR_INVALID_PACKAGE_CONFIG,\n  ERR_INVALID_PACKAGE_TARGET,\n  ERR_MODULE_NOT_FOUND,\n  ERR_PACKAGE_IMPORT_NOT_DEFINED,\n  ERR_PACKAGE_PATH_NOT_EXPORTED,\n  ERR_UNSUPPORTED_DIR_IMPORT\n} = codes;\n\nconst own = {}.hasOwnProperty;\n\nconst invalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))?(\\\\|\\/|$)/i;\nconst deprecatedInvalidSegmentRegEx =\n  /(^|\\\\|\\/)((\\.|%2e)(\\.|%2e)?|(n|%6e|%4e)(o|%6f|%4f)(d|%64|%44)(e|%65|%45)(_|%5f)(m|%6d|%4d)(o|%6f|%4f)(d|%64|%44)(u|%75|%55)(l|%6c|%4c)(e|%65|%45)(s|%73|%53))(\\\\|\\/|$)/i;\nconst invalidPackageNameRegEx = /^\\.|%|\\\\/;\nconst patternRegEx = /\\*/g;\nconst encodedSepRegEx = /%2f|%5c/i;\n/** @type {Set<string>} */\nconst emittedPackageWarnings = new Set();\n\nconst doubleSlashRegEx = /[/\\\\]{2}/;\n\n/**\n *\n * @param {string} target\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} base\n * @param {boolean} isTarget\n */\nfunction emitInvalidSegmentDeprecation(\n  target,\n  request,\n  match,\n  packageJsonUrl,\n  internal,\n  base,\n  isTarget\n) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const pjsonPath = fileURLToPath(packageJsonUrl);\n  const double = doubleSlashRegEx.exec(isTarget ? target : request) !== null;\n  process.emitWarning(\n    `Use of deprecated ${\n      double ? 'double slash' : 'leading or trailing slash matching'\n    } resolving \"${target}\" for module ` +\n      `request \"${request}\" ${\n        request === match ? '' : `matched to \"${match}\" `\n      }in the \"${\n        internal ? 'imports' : 'exports'\n      }\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }.`,\n    'DeprecationWarning',\n    'DEP0166'\n  );\n}\n\n/**\n * @param {URL} url\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {string} [main]\n * @returns {void}\n */\nfunction emitLegacyIndexDeprecation(url, packageJsonUrl, base, main) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const format = defaultGetFormatWithoutErrors(url, {parentURL: base.href});\n  if (format !== 'module') return\n  const urlPath = fileURLToPath(url.href);\n  const pkgPath = fileURLToPath(new URL('.', packageJsonUrl));\n  const basePath = fileURLToPath(base);\n  if (!main) {\n    process.emitWarning(\n      `No \"main\" or \"exports\" field defined in the package.json for ${pkgPath} resolving the main entry point \"${urlPath.slice(\n        pkgPath.length\n      )}\", imported from ${basePath}.\\nDefault \"index\" lookups for the main are deprecated for ES modules.`,\n      'DeprecationWarning',\n      'DEP0151'\n    );\n  } else if (path.resolve(pkgPath, main) !== urlPath) {\n    process.emitWarning(\n      `Package ${pkgPath} has a \"main\" field set to \"${main}\", ` +\n        `excluding the full filename and extension to the resolved file at \"${urlPath.slice(\n          pkgPath.length\n        )}\", imported from ${basePath}.\\n Automatic extension resolution of the \"main\" field is ` +\n        'deprecated for ES modules.',\n      'DeprecationWarning',\n      'DEP0151'\n    );\n  }\n}\n\n/**\n * @param {string} path\n * @returns {Stats}\n */\nfunction tryStatSync(path) {\n  // Note: from Node 15 onwards we can use `throwIfNoEntry: false` instead.\n  try {\n    return statSync(path)\n  } catch {\n    return new Stats()\n  }\n}\n\n/**\n * Legacy CommonJS main resolution:\n * 1. let M = pkg_url + (json main field)\n * 2. TRY(M, M.js, M.json, M.node)\n * 3. TRY(M/index.js, M/index.json, M/index.node)\n * 4. TRY(pkg_url/index.js, pkg_url/index.json, pkg_url/index.node)\n * 5. NOT_FOUND\n *\n * @param {URL} url\n * @returns {boolean}\n */\nfunction fileExists(url) {\n  const stats = statSync(url, {throwIfNoEntry: false});\n  const isFile = stats ? stats.isFile() : undefined;\n  return isFile === null || isFile === undefined ? false : isFile\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {PackageConfig} packageConfig\n * @param {URL} base\n * @returns {URL}\n */\nfunction legacyMainResolve(packageJsonUrl, packageConfig, base) {\n  /** @type {URL | undefined} */\n  let guess;\n  if (packageConfig.main !== undefined) {\n    guess = new URL(packageConfig.main, packageJsonUrl);\n    // Note: fs check redundances will be handled by Descriptor cache here.\n    if (fileExists(guess)) return guess\n\n    const tries = [\n      `./${packageConfig.main}.js`,\n      `./${packageConfig.main}.json`,\n      `./${packageConfig.main}.node`,\n      `./${packageConfig.main}/index.js`,\n      `./${packageConfig.main}/index.json`,\n      `./${packageConfig.main}/index.node`\n    ];\n    let i = -1;\n\n    while (++i < tries.length) {\n      guess = new URL(tries[i], packageJsonUrl);\n      if (fileExists(guess)) break\n      guess = undefined;\n    }\n\n    if (guess) {\n      emitLegacyIndexDeprecation(\n        guess,\n        packageJsonUrl,\n        base,\n        packageConfig.main\n      );\n      return guess\n    }\n    // Fallthrough.\n  }\n\n  const tries = ['./index.js', './index.json', './index.node'];\n  let i = -1;\n\n  while (++i < tries.length) {\n    guess = new URL(tries[i], packageJsonUrl);\n    if (fileExists(guess)) break\n    guess = undefined;\n  }\n\n  if (guess) {\n    emitLegacyIndexDeprecation(guess, packageJsonUrl, base, packageConfig.main);\n    return guess\n  }\n\n  // Not found.\n  throw new ERR_MODULE_NOT_FOUND(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {URL} resolved\n * @param {URL} base\n * @param {boolean} [preserveSymlinks]\n * @returns {URL}\n */\nfunction finalizeResolution(resolved, base, preserveSymlinks) {\n  if (encodedSepRegEx.exec(resolved.pathname) !== null) {\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      resolved.pathname,\n      'must not include encoded \"/\" or \"\\\\\" characters',\n      fileURLToPath(base)\n    )\n  }\n\n  /** @type {string} */\n  let filePath;\n\n  try {\n    filePath = fileURLToPath(resolved);\n  } catch (error) {\n    const cause = /** @type {ErrnoException} */ (error);\n    Object.defineProperty(cause, 'input', {value: String(resolved)});\n    Object.defineProperty(cause, 'module', {value: String(base)});\n    throw cause\n  }\n\n  const stats = tryStatSync(\n    filePath.endsWith('/') ? filePath.slice(-1) : filePath\n  );\n\n  if (stats.isDirectory()) {\n    const error = new ERR_UNSUPPORTED_DIR_IMPORT(filePath, fileURLToPath(base));\n    // @ts-expect-error Add this for `import.meta.resolve`.\n    error.url = String(resolved);\n    throw error\n  }\n\n  if (!stats.isFile()) {\n    const error = new ERR_MODULE_NOT_FOUND(\n      filePath || resolved.pathname,\n      base && fileURLToPath(base),\n      true\n    );\n    // @ts-expect-error Add this for `import.meta.resolve`.\n    error.url = String(resolved);\n    throw error\n  }\n\n  if (!preserveSymlinks) {\n    const real = realpathSync(filePath);\n    const {search, hash} = resolved;\n    resolved = pathToFileURL(real + (filePath.endsWith(path.sep) ? '/' : ''));\n    resolved.search = search;\n    resolved.hash = hash;\n  }\n\n  return resolved\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction importNotDefined(specifier, packageJsonUrl, base) {\n  return new ERR_PACKAGE_IMPORT_NOT_DEFINED(\n    specifier,\n    packageJsonUrl && fileURLToPath(new URL('.', packageJsonUrl)),\n    fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {Error}\n */\nfunction exportsNotFound(subpath, packageJsonUrl, base) {\n  return new ERR_PACKAGE_PATH_NOT_EXPORTED(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    subpath,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} request\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {never}\n */\nfunction throwInvalidSubpath(request, match, packageJsonUrl, internal, base) {\n  const reason = `request is not a valid match in pattern \"${match}\" for the \"${\n    internal ? 'imports' : 'exports'\n  }\" resolution of ${fileURLToPath(packageJsonUrl)}`;\n  throw new ERR_INVALID_MODULE_SPECIFIER(\n    request,\n    reason,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} subpath\n * @param {unknown} target\n * @param {URL} packageJsonUrl\n * @param {boolean} internal\n * @param {URL} [base]\n * @returns {Error}\n */\nfunction invalidPackageTarget(subpath, target, packageJsonUrl, internal, base) {\n  target =\n    typeof target === 'object' && target !== null\n      ? JSON.stringify(target, null, '')\n      : `${target}`;\n\n  return new ERR_INVALID_PACKAGE_TARGET(\n    fileURLToPath(new URL('.', packageJsonUrl)),\n    subpath,\n    target,\n    internal,\n    base && fileURLToPath(base)\n  )\n}\n\n/**\n * @param {string} target\n * @param {string} subpath\n * @param {string} match\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction resolvePackageTargetString(\n  target,\n  subpath,\n  match,\n  packageJsonUrl,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (subpath !== '' && !pattern && target[target.length - 1] !== '/')\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (!target.startsWith('./')) {\n    if (internal && !target.startsWith('../') && !target.startsWith('/')) {\n      let isURL = false;\n\n      try {\n        new URL(target);\n        isURL = true;\n      } catch {\n        // Continue regardless of error.\n      }\n\n      if (!isURL) {\n        const exportTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target + subpath;\n\n        return packageResolve(exportTarget, packageJsonUrl, conditions)\n      }\n    }\n\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n  }\n\n  if (invalidSegmentRegEx.exec(target.slice(2)) !== null) {\n    if (deprecatedInvalidSegmentRegEx.exec(target.slice(2)) === null) {\n      if (!isPathMap) {\n        const request = pattern\n          ? match.replace('*', () => subpath)\n          : match + subpath;\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          true\n        );\n      }\n    } else {\n      throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n    }\n  }\n\n  const resolved = new URL(target, packageJsonUrl);\n  const resolvedPath = resolved.pathname;\n  const packagePath = new URL('.', packageJsonUrl).pathname;\n\n  if (!resolvedPath.startsWith(packagePath))\n    throw invalidPackageTarget(match, target, packageJsonUrl, internal, base)\n\n  if (subpath === '') return resolved\n\n  if (invalidSegmentRegEx.exec(subpath) !== null) {\n    const request = pattern\n      ? match.replace('*', () => subpath)\n      : match + subpath;\n    if (deprecatedInvalidSegmentRegEx.exec(subpath) === null) {\n      if (!isPathMap) {\n        const resolvedTarget = pattern\n          ? RegExpPrototypeSymbolReplace.call(\n              patternRegEx,\n              target,\n              () => subpath\n            )\n          : target;\n        emitInvalidSegmentDeprecation(\n          resolvedTarget,\n          request,\n          match,\n          packageJsonUrl,\n          internal,\n          base,\n          false\n        );\n      }\n    } else {\n      throwInvalidSubpath(request, match, packageJsonUrl, internal, base);\n    }\n  }\n\n  if (pattern) {\n    return new URL(\n      RegExpPrototypeSymbolReplace.call(\n        patternRegEx,\n        resolved.href,\n        () => subpath\n      )\n    )\n  }\n\n  return new URL(subpath, resolved)\n}\n\n/**\n * @param {string} key\n * @returns {boolean}\n */\nfunction isArrayIndex(key) {\n  const keyNumber = Number(key);\n  if (`${keyNumber}` !== key) return false\n  return keyNumber >= 0 && keyNumber < 0xff_ff_ff_ff\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {unknown} target\n * @param {string} subpath\n * @param {string} packageSubpath\n * @param {URL} base\n * @param {boolean} pattern\n * @param {boolean} internal\n * @param {boolean} isPathMap\n * @param {Set<string> | undefined} conditions\n * @returns {URL | null}\n */\nfunction resolvePackageTarget(\n  packageJsonUrl,\n  target,\n  subpath,\n  packageSubpath,\n  base,\n  pattern,\n  internal,\n  isPathMap,\n  conditions\n) {\n  if (typeof target === 'string') {\n    return resolvePackageTargetString(\n      target,\n      subpath,\n      packageSubpath,\n      packageJsonUrl,\n      base,\n      pattern,\n      internal,\n      isPathMap,\n      conditions\n    )\n  }\n\n  if (Array.isArray(target)) {\n    /** @type {Array<unknown>} */\n    const targetList = target;\n    if (targetList.length === 0) return null\n\n    /** @type {ErrnoException | null | undefined} */\n    let lastException;\n    let i = -1;\n\n    while (++i < targetList.length) {\n      const targetItem = targetList[i];\n      /** @type {URL | null} */\n      let resolveResult;\n      try {\n        resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          targetItem,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n      } catch (error) {\n        const exception = /** @type {ErrnoException} */ (error);\n        lastException = exception;\n        if (exception.code === 'ERR_INVALID_PACKAGE_TARGET') continue\n        throw error\n      }\n\n      if (resolveResult === undefined) continue\n\n      if (resolveResult === null) {\n        lastException = null;\n        continue\n      }\n\n      return resolveResult\n    }\n\n    if (lastException === undefined || lastException === null) {\n      return null\n    }\n\n    throw lastException\n  }\n\n  if (typeof target === 'object' && target !== null) {\n    const keys = Object.getOwnPropertyNames(target);\n    let i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (isArrayIndex(key)) {\n        throw new ERR_INVALID_PACKAGE_CONFIG(\n          fileURLToPath(packageJsonUrl),\n          base,\n          '\"exports\" cannot contain numeric property keys.'\n        )\n      }\n    }\n\n    i = -1;\n\n    while (++i < keys.length) {\n      const key = keys[i];\n      if (key === 'default' || (conditions && conditions.has(key))) {\n        // @ts-expect-error: indexable.\n        const conditionalTarget = /** @type {unknown} */ (target[key]);\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          conditionalTarget,\n          subpath,\n          packageSubpath,\n          base,\n          pattern,\n          internal,\n          isPathMap,\n          conditions\n        );\n        if (resolveResult === undefined) continue\n        return resolveResult\n      }\n    }\n\n    return null\n  }\n\n  if (target === null) {\n    return null\n  }\n\n  throw invalidPackageTarget(\n    packageSubpath,\n    target,\n    packageJsonUrl,\n    internal,\n    base\n  )\n}\n\n/**\n * @param {unknown} exports\n * @param {URL} packageJsonUrl\n * @param {URL} base\n * @returns {boolean}\n */\nfunction isConditionalExportsMainSugar(exports, packageJsonUrl, base) {\n  if (typeof exports === 'string' || Array.isArray(exports)) return true\n  if (typeof exports !== 'object' || exports === null) return false\n\n  const keys = Object.getOwnPropertyNames(exports);\n  let isConditionalSugar = false;\n  let i = 0;\n  let j = -1;\n  while (++j < keys.length) {\n    const key = keys[j];\n    const curIsConditionalSugar = key === '' || key[0] !== '.';\n    if (i++ === 0) {\n      isConditionalSugar = curIsConditionalSugar;\n    } else if (isConditionalSugar !== curIsConditionalSugar) {\n      throw new ERR_INVALID_PACKAGE_CONFIG(\n        fileURLToPath(packageJsonUrl),\n        base,\n        '\"exports\" cannot contain some keys starting with \\'.\\' and some not.' +\n          ' The exports object must either be an object of package subpath keys' +\n          ' or an object of main entry condition name keys only.'\n      )\n    }\n  }\n\n  return isConditionalSugar\n}\n\n/**\n * @param {string} match\n * @param {URL} pjsonUrl\n * @param {URL} base\n */\nfunction emitTrailingSlashPatternDeprecation(match, pjsonUrl, base) {\n  // @ts-expect-error: apparently it does exist, TS.\n  if (process.noDeprecation) {\n    return\n  }\n\n  const pjsonPath = fileURLToPath(pjsonUrl);\n  if (emittedPackageWarnings.has(pjsonPath + '|' + match)) return\n  emittedPackageWarnings.add(pjsonPath + '|' + match);\n  process.emitWarning(\n    `Use of deprecated trailing slash pattern mapping \"${match}\" in the ` +\n      `\"exports\" field module resolution of the package at ${pjsonPath}${\n        base ? ` imported from ${fileURLToPath(base)}` : ''\n      }. Mapping specifiers ending in \"/\" is no longer supported.`,\n    'DeprecationWarning',\n    'DEP0155'\n  );\n}\n\n/**\n * @param {URL} packageJsonUrl\n * @param {string} packageSubpath\n * @param {Record<string, unknown>} packageConfig\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageExportsResolve(\n  packageJsonUrl,\n  packageSubpath,\n  packageConfig,\n  base,\n  conditions\n) {\n  let exports = packageConfig.exports;\n\n  if (isConditionalExportsMainSugar(exports, packageJsonUrl, base)) {\n    exports = {'.': exports};\n  }\n\n  if (\n    own.call(exports, packageSubpath) &&\n    !packageSubpath.includes('*') &&\n    !packageSubpath.endsWith('/')\n  ) {\n    // @ts-expect-error: indexable.\n    const target = exports[packageSubpath];\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      '',\n      packageSubpath,\n      base,\n      false,\n      false,\n      false,\n      conditions\n    );\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  let bestMatch = '';\n  let bestMatchSubpath = '';\n  const keys = Object.getOwnPropertyNames(exports);\n  let i = -1;\n\n  while (++i < keys.length) {\n    const key = keys[i];\n    const patternIndex = key.indexOf('*');\n\n    if (\n      patternIndex !== -1 &&\n      packageSubpath.startsWith(key.slice(0, patternIndex))\n    ) {\n      // When this reaches EOL, this can throw at the top of the whole function:\n      //\n      // if (StringPrototypeEndsWith(packageSubpath, '/'))\n      //   throwInvalidSubpath(packageSubpath)\n      //\n      // To match \"imports\" and the spec.\n      if (packageSubpath.endsWith('/')) {\n        emitTrailingSlashPatternDeprecation(\n          packageSubpath,\n          packageJsonUrl,\n          base\n        );\n      }\n\n      const patternTrailer = key.slice(patternIndex + 1);\n\n      if (\n        packageSubpath.length >= key.length &&\n        packageSubpath.endsWith(patternTrailer) &&\n        patternKeyCompare(bestMatch, key) === 1 &&\n        key.lastIndexOf('*') === patternIndex\n      ) {\n        bestMatch = key;\n        bestMatchSubpath = packageSubpath.slice(\n          patternIndex,\n          packageSubpath.length - patternTrailer.length\n        );\n      }\n    }\n  }\n\n  if (bestMatch) {\n    // @ts-expect-error: indexable.\n    const target = /** @type {unknown} */ (exports[bestMatch]);\n    const resolveResult = resolvePackageTarget(\n      packageJsonUrl,\n      target,\n      bestMatchSubpath,\n      bestMatch,\n      base,\n      true,\n      false,\n      packageSubpath.endsWith('/'),\n      conditions\n    );\n\n    if (resolveResult === null || resolveResult === undefined) {\n      throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n    }\n\n    return resolveResult\n  }\n\n  throw exportsNotFound(packageSubpath, packageJsonUrl, base)\n}\n\n/**\n * @param {string} a\n * @param {string} b\n */\nfunction patternKeyCompare(a, b) {\n  const aPatternIndex = a.indexOf('*');\n  const bPatternIndex = b.indexOf('*');\n  const baseLengthA = aPatternIndex === -1 ? a.length : aPatternIndex + 1;\n  const baseLengthB = bPatternIndex === -1 ? b.length : bPatternIndex + 1;\n  if (baseLengthA > baseLengthB) return -1\n  if (baseLengthB > baseLengthA) return 1\n  if (aPatternIndex === -1) return 1\n  if (bPatternIndex === -1) return -1\n  if (a.length > b.length) return -1\n  if (b.length > a.length) return 1\n  return 0\n}\n\n/**\n * @param {string} name\n * @param {URL} base\n * @param {Set<string>} [conditions]\n * @returns {URL}\n */\nfunction packageImportsResolve(name, base, conditions) {\n  if (name === '#' || name.startsWith('#/') || name.endsWith('/')) {\n    const reason = 'is not a valid internal imports specifier name';\n    throw new ERR_INVALID_MODULE_SPECIFIER(name, reason, fileURLToPath(base))\n  }\n\n  /** @type {URL | undefined} */\n  let packageJsonUrl;\n\n  const packageConfig = getPackageScopeConfig(base);\n\n  if (packageConfig.exists) {\n    packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    const imports = packageConfig.imports;\n    if (imports) {\n      if (own.call(imports, name) && !name.includes('*')) {\n        const resolveResult = resolvePackageTarget(\n          packageJsonUrl,\n          imports[name],\n          '',\n          name,\n          base,\n          false,\n          true,\n          false,\n          conditions\n        );\n        if (resolveResult !== null && resolveResult !== undefined) {\n          return resolveResult\n        }\n      } else {\n        let bestMatch = '';\n        let bestMatchSubpath = '';\n        const keys = Object.getOwnPropertyNames(imports);\n        let i = -1;\n\n        while (++i < keys.length) {\n          const key = keys[i];\n          const patternIndex = key.indexOf('*');\n\n          if (patternIndex !== -1 && name.startsWith(key.slice(0, -1))) {\n            const patternTrailer = key.slice(patternIndex + 1);\n            if (\n              name.length >= key.length &&\n              name.endsWith(patternTrailer) &&\n              patternKeyCompare(bestMatch, key) === 1 &&\n              key.lastIndexOf('*') === patternIndex\n            ) {\n              bestMatch = key;\n              bestMatchSubpath = name.slice(\n                patternIndex,\n                name.length - patternTrailer.length\n              );\n            }\n          }\n        }\n\n        if (bestMatch) {\n          const target = imports[bestMatch];\n          const resolveResult = resolvePackageTarget(\n            packageJsonUrl,\n            target,\n            bestMatchSubpath,\n            bestMatch,\n            base,\n            true,\n            true,\n            false,\n            conditions\n          );\n\n          if (resolveResult !== null && resolveResult !== undefined) {\n            return resolveResult\n          }\n        }\n      }\n    }\n  }\n\n  throw importNotDefined(name, packageJsonUrl, base)\n}\n\n// Note: In Node.js, `getPackageType` is here.\n// To prevent a circular dependency, we move it to\n// `resolve-get-package-type.js`.\n\n/**\n * @param {string} specifier\n * @param {URL} base\n */\nfunction parsePackageName(specifier, base) {\n  let separatorIndex = specifier.indexOf('/');\n  let validPackageName = true;\n  let isScoped = false;\n  if (specifier[0] === '@') {\n    isScoped = true;\n    if (separatorIndex === -1 || specifier.length === 0) {\n      validPackageName = false;\n    } else {\n      separatorIndex = specifier.indexOf('/', separatorIndex + 1);\n    }\n  }\n\n  const packageName =\n    separatorIndex === -1 ? specifier : specifier.slice(0, separatorIndex);\n\n  // Package name cannot have leading . and cannot have percent-encoding or\n  // \\\\ separators.\n  if (invalidPackageNameRegEx.exec(packageName) !== null) {\n    validPackageName = false;\n  }\n\n  if (!validPackageName) {\n    throw new ERR_INVALID_MODULE_SPECIFIER(\n      specifier,\n      'is not a valid package name',\n      fileURLToPath(base)\n    )\n  }\n\n  const packageSubpath =\n    '.' + (separatorIndex === -1 ? '' : specifier.slice(separatorIndex));\n\n  return {packageName, packageSubpath, isScoped}\n}\n\n/**\n * @param {string} specifier\n * @param {URL} base\n * @param {Set<string> | undefined} conditions\n * @returns {URL}\n */\nfunction packageResolve(specifier, base, conditions) {\n  if (builtinModules.includes(specifier)) {\n    return new URL('node:' + specifier)\n  }\n\n  const {packageName, packageSubpath, isScoped} = parsePackageName(\n    specifier,\n    base\n  );\n\n  // ResolveSelf\n  const packageConfig = getPackageScopeConfig(base);\n\n  // Can’t test.\n  /* c8 ignore next 16 */\n  if (packageConfig.exists) {\n    const packageJsonUrl = pathToFileURL(packageConfig.pjsonPath);\n    if (\n      packageConfig.name === packageName &&\n      packageConfig.exports !== undefined &&\n      packageConfig.exports !== null\n    ) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n  }\n\n  let packageJsonUrl = new URL(\n    './node_modules/' + packageName + '/package.json',\n    base\n  );\n  let packageJsonPath = fileURLToPath(packageJsonUrl);\n  /** @type {string} */\n  let lastPath;\n  do {\n    const stat = tryStatSync(packageJsonPath.slice(0, -13));\n    if (!stat.isDirectory()) {\n      lastPath = packageJsonPath;\n      packageJsonUrl = new URL(\n        (isScoped ? '../../../../node_modules/' : '../../../node_modules/') +\n          packageName +\n          '/package.json',\n        packageJsonUrl\n      );\n      packageJsonPath = fileURLToPath(packageJsonUrl);\n      continue\n    }\n\n    // Package match.\n    const packageConfig = packageJsonReader.read(packageJsonPath, {\n      base,\n      specifier\n    });\n    if (packageConfig.exports !== undefined && packageConfig.exports !== null) {\n      return packageExportsResolve(\n        packageJsonUrl,\n        packageSubpath,\n        packageConfig,\n        base,\n        conditions\n      )\n    }\n\n    if (packageSubpath === '.') {\n      return legacyMainResolve(packageJsonUrl, packageConfig, base)\n    }\n\n    return new URL(packageSubpath, packageJsonUrl)\n    // Cross-platform root check.\n  } while (packageJsonPath.length !== lastPath.length)\n\n  throw new ERR_MODULE_NOT_FOUND(packageName, fileURLToPath(base), false)\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction isRelativeSpecifier(specifier) {\n  if (specifier[0] === '.') {\n    if (specifier.length === 1 || specifier[1] === '/') return true\n    if (\n      specifier[1] === '.' &&\n      (specifier.length === 2 || specifier[2] === '/')\n    ) {\n      return true\n    }\n  }\n\n  return false\n}\n\n/**\n * @param {string} specifier\n * @returns {boolean}\n */\nfunction shouldBeTreatedAsRelativeOrAbsolutePath(specifier) {\n  if (specifier === '') return false\n  if (specifier[0] === '/') return true\n  return isRelativeSpecifier(specifier)\n}\n\n/**\n * The “Resolver Algorithm Specification” as detailed in the Node docs (which is\n * sync and slightly lower-level than `resolve`).\n *\n * @param {string} specifier\n *   `/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`, etc.\n * @param {URL} base\n *   Full URL (to a file) that `specifier` is resolved relative from.\n * @param {Set<string>} [conditions]\n *   Conditions.\n * @param {boolean} [preserveSymlinks]\n *   Keep symlinks instead of resolving them.\n * @returns {URL}\n *   A URL object to the found thing.\n */\nfunction moduleResolve(specifier, base, conditions, preserveSymlinks) {\n  const protocol = base.protocol;\n  const isRemote = protocol === 'http:' || protocol === 'https:';\n  // Order swapped from spec for minor perf gain.\n  // Ok since relative URLs cannot parse as URLs.\n  /** @type {URL | undefined} */\n  let resolved;\n\n  if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n    resolved = new URL(specifier, base);\n  } else if (!isRemote && specifier[0] === '#') {\n    resolved = packageImportsResolve(specifier, base, conditions);\n  } else {\n    try {\n      resolved = new URL(specifier);\n    } catch {\n      if (!isRemote) {\n        resolved = packageResolve(specifier, base, conditions);\n      }\n    }\n  }\n\n  assert(resolved !== undefined, 'expected to be defined');\n\n  if (resolved.protocol !== 'file:') {\n    return resolved\n  }\n\n  return finalizeResolution(resolved, base, preserveSymlinks)\n}\n\n/**\n * @param {string} specifier\n * @param {URL | undefined} parsed\n * @param {URL | undefined} parsedParentURL\n */\nfunction checkIfDisallowedImport(specifier, parsed, parsedParentURL) {\n  if (parsedParentURL) {\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    const parentProtocol = parsedParentURL.protocol;\n\n    if (parentProtocol === 'http:' || parentProtocol === 'https:') {\n      if (shouldBeTreatedAsRelativeOrAbsolutePath(specifier)) {\n        // Avoid accessing the `protocol` property due to the lazy getters.\n        const parsedProtocol = parsed?.protocol;\n\n        // `data:` and `blob:` disallowed due to allowing file: access via\n        // indirection\n        if (\n          parsedProtocol &&\n          parsedProtocol !== 'https:' &&\n          parsedProtocol !== 'http:'\n        ) {\n          throw new ERR_NETWORK_IMPORT_DISALLOWED(\n            specifier,\n            parsedParentURL,\n            'remote imports cannot import from a local location.'\n          )\n        }\n\n        return {url: parsed?.href || ''}\n      }\n\n      if (builtinModules.includes(specifier)) {\n        throw new ERR_NETWORK_IMPORT_DISALLOWED(\n          specifier,\n          parsedParentURL,\n          'remote imports cannot import from a local location.'\n        )\n      }\n\n      throw new ERR_NETWORK_IMPORT_DISALLOWED(\n        specifier,\n        parsedParentURL,\n        'only relative and absolute specifiers are supported.'\n      )\n    }\n  }\n}\n\n// Note: this is from:\n// <https://github.com/nodejs/node/blob/3e74590/lib/internal/url.js#L687>\n/**\n * Checks if a value has the shape of a WHATWG URL object.\n *\n * Using a symbol or instanceof would not be able to recognize URL objects\n * coming from other implementations (e.g. in Electron), so instead we are\n * checking some well known properties for a lack of a better test.\n *\n * We use `href` and `protocol` as they are the only properties that are\n * easy to retrieve and calculate due to the lazy nature of the getters.\n *\n * @template {unknown} Value\n * @param {Value} self\n * @returns {Value is URL}\n */\nfunction isURL(self) {\n  return Boolean(\n    self &&\n      typeof self === 'object' &&\n      'href' in self &&\n      typeof self.href === 'string' &&\n      'protocol' in self &&\n      typeof self.protocol === 'string' &&\n      self.href &&\n      self.protocol\n  )\n}\n\n/**\n * Validate user-input in `context` supplied by a custom loader.\n *\n * @param {unknown} parentURL\n * @returns {asserts parentURL is URL | string | undefined}\n */\nfunction throwIfInvalidParentURL(parentURL) {\n  if (parentURL === undefined) {\n    return // Main entry point, so no parent\n  }\n\n  if (typeof parentURL !== 'string' && !isURL(parentURL)) {\n    throw new codes.ERR_INVALID_ARG_TYPE(\n      'parentURL',\n      ['string', 'URL'],\n      parentURL\n    )\n  }\n}\n\n/**\n * @param {string} specifier\n * @param {{parentURL?: string, conditions?: Array<string>}} context\n * @returns {{url: string, format?: string | null}}\n */\nfunction defaultResolve(specifier, context = {}) {\n  const {parentURL} = context;\n  assert(parentURL !== undefined, 'expected `parentURL` to be defined');\n  throwIfInvalidParentURL(parentURL);\n\n  /** @type {URL | undefined} */\n  let parsedParentURL;\n  if (parentURL) {\n    try {\n      parsedParentURL = new URL(parentURL);\n    } catch {\n      // Ignore exception\n    }\n  }\n\n  /** @type {URL | undefined} */\n  let parsed;\n  try {\n    parsed = shouldBeTreatedAsRelativeOrAbsolutePath(specifier)\n      ? new URL(specifier, parsedParentURL)\n      : new URL(specifier);\n\n    // Avoid accessing the `protocol` property due to the lazy getters.\n    const protocol = parsed.protocol;\n\n    if (protocol === 'data:') {\n      return {url: parsed.href, format: null}\n    }\n  } catch {\n    // Ignore exception\n  }\n\n  // There are multiple deep branches that can either throw or return; instead\n  // of duplicating that deeply nested logic for the possible returns, DRY and\n  // check for a return. This seems the least gnarly.\n  const maybeReturn = checkIfDisallowedImport(\n    specifier,\n    parsed,\n    parsedParentURL\n  );\n\n  if (maybeReturn) return maybeReturn\n\n  // This must come after checkIfDisallowedImport\n  if (parsed && parsed.protocol === 'node:') return {url: specifier}\n\n  const conditions = getConditionsSet(context.conditions);\n\n  const url = moduleResolve(specifier, new URL(parentURL), conditions, false);\n\n  return {\n    // Do NOT cast `url` to a string: that will work even when there are real\n    // problems, silencing them\n    url: url.href,\n    format: defaultGetFormatWithoutErrors(url, {parentURL})\n  }\n}\n\n/**\n * @typedef {import('./lib/errors.js').ErrnoException} ErrnoException\n */\n\n\n/**\n * Match `import.meta.resolve` except that `parent` is required (you can pass\n * `import.meta.url`).\n *\n * @param {string} specifier\n *   The module specifier to resolve relative to parent\n *   (`/example.js`, `./example.js`, `../example.js`, `some-package`, `fs`,\n *   etc).\n * @param {string} parent\n *   The absolute parent module URL to resolve from.\n *   You must pass `import.meta.url` or something else.\n * @returns {string}\n *   Returns a string to a full `file:`, `data:`, or `node:` URL\n *   to the found thing.\n */\nfunction resolve(specifier, parent) {\n  if (!parent) {\n    throw new Error(\n      'Please pass `parent`: `import-meta-resolve` cannot ponyfill that'\n    )\n  }\n\n  try {\n    return defaultResolve(specifier, {parentURL: parent}).url\n  } catch (error) {\n    // See: <https://github.com/nodejs/node/blob/45f5c9b/lib/internal/modules/esm/initialize_import_meta.js#L34>\n    const exception = /** @type {ErrnoException} */ (error);\n\n    if (\n      (exception.code === 'ERR_UNSUPPORTED_DIR_IMPORT' ||\n        exception.code === 'ERR_MODULE_NOT_FOUND') &&\n      typeof exception.url === 'string'\n    ) {\n      return exception.url\n    }\n\n    throw error\n  }\n}\n\nexport { moduleResolve, resolve };\n"], "mappings": ";;;;;;;AAoFA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,IAAA;EAAA,MAAAF,IAAA,GAAAG,uBAAA,CAAAF,OAAA;EAAAC,GAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,SAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,QAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,KAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,IAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAM,MAAA;EAAA,MAAAN,IAAA,GAAAC,OAAA;EAAAK,KAAA,YAAAA,CAAA;IAAA,OAAAN,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAO,QAAA;EAAA,MAAAP,IAAA,GAAAC,OAAA;EAAAM,OAAA,YAAAA,CAAA;IAAA,OAAAP,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAQ,GAAA;EAAA,MAAAR,IAAA,GAAAC,OAAA;EAAAO,EAAA,YAAAA,CAAA;IAAA,OAAAR,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAS,MAAA;EAAA,MAAAT,IAAA,GAAAC,OAAA;EAAAQ,KAAA,YAAAA,CAAA;IAAA,OAAAT,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuC,SAAAU,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAjB,CAAA,EAAAc,CAAA,SAAAI,CAAA,GAAAR,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAI,CAAA,KAAAA,CAAA,CAAAX,GAAA,IAAAW,CAAA,CAAAC,GAAA,IAAAR,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAI,CAAA,IAAAV,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAgB,GAAA,CAAAnB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAcvC,MAAMY,KAAK,GAAG,CAAC,CAAC,CAACJ,cAAc;AAE/B,MAAMK,WAAW,GAAG,oBAAoB;AAExC,MAAMC,MAAM,GAAG,IAAIC,GAAG,CAAC,CACrB,QAAQ,EACR,UAAU,EACV,QAAQ,EACR,QAAQ,EAER,UAAU,EACV,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,QAAQ,CACT,CAAC;AAEF,MAAMC,KAAK,GAAG,CAAC,CAAC;AAahB,SAASC,UAAUA,CAACC,KAAK,EAAEC,IAAI,GAAG,KAAK,EAAE;EACvC,OAAOD,KAAK,CAACE,MAAM,GAAG,CAAC,GACnBF,KAAK,CAACG,IAAI,CAAE,IAAGF,IAAK,GAAE,CAAC,GACtB,GAAED,KAAK,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACD,IAAI,CAAC,IAAI,CAAE,KAAIF,IAAK,IAAGD,KAAK,CAACA,KAAK,CAACE,MAAM,GAAG,CAAC,CAAE,EAAC;AAC5E;AAGA,MAAMG,QAAQ,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC1B,MAAMC,kBAAkB,GAAG,kBAAkB;AAE7C,IAAIC,mBAAmB;AAEvBV,KAAK,CAACW,oBAAoB,GAAGC,WAAW,CACtC,sBAAsB,EAMtB,CAACC,IAAI,EAAEC,QAAQ,EAAEC,MAAM,KAAK;EAC1BC,QAAKA,CAAC,CAAC,OAAOH,IAAI,KAAK,QAAQ,EAAE,yBAAyB,CAAC;EAC3D,IAAI,CAACI,KAAK,CAACC,OAAO,CAACJ,QAAQ,CAAC,EAAE;IAC5BA,QAAQ,GAAG,CAACA,QAAQ,CAAC;EACvB;EAEA,IAAIK,OAAO,GAAG,MAAM;EACpB,IAAIN,IAAI,CAACO,QAAQ,CAAC,WAAW,CAAC,EAAE;IAE9BD,OAAO,IAAK,GAAEN,IAAK,GAAE;EACvB,CAAC,MAAM;IACL,MAAMV,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;IACzDF,OAAO,IAAK,IAAGN,IAAK,KAAIV,IAAK,GAAE;EACjC;EAEAgB,OAAO,IAAI,UAAU;EAGrB,MAAMG,KAAK,GAAG,EAAE;EAEhB,MAAMC,SAAS,GAAG,EAAE;EAEpB,MAAMC,KAAK,GAAG,EAAE;EAEhB,KAAK,MAAMC,KAAK,IAAIX,QAAQ,EAAE;IAC5BE,QAAKA,CAAC,CACJ,OAAOS,KAAK,KAAK,QAAQ,EACzB,gDACF,CAAC;IAED,IAAI3B,MAAM,CAAChB,GAAG,CAAC2C,KAAK,CAAC,EAAE;MACrBH,KAAK,CAACI,IAAI,CAACD,KAAK,CAACE,WAAW,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI9B,WAAW,CAAC+B,IAAI,CAACH,KAAK,CAAC,KAAK,IAAI,EAAE;MAC3CT,QAAKA,CAAC,CACJS,KAAK,KAAK,QAAQ,EAClB,kDACF,CAAC;MACDD,KAAK,CAACE,IAAI,CAACD,KAAK,CAAC;IACnB,CAAC,MAAM;MACLF,SAAS,CAACG,IAAI,CAACD,KAAK,CAAC;IACvB;EACF;EAIA,IAAIF,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxB,MAAMyB,GAAG,GAAGP,KAAK,CAACQ,OAAO,CAAC,QAAQ,CAAC;IACnC,IAAID,GAAG,KAAK,CAAC,CAAC,EAAE;MACdP,KAAK,CAAChB,KAAK,CAACuB,GAAG,EAAE,CAAC,CAAC;MACnBN,SAAS,CAACG,IAAI,CAAC,QAAQ,CAAC;IAC1B;EACF;EAEA,IAAIJ,KAAK,CAAClB,MAAM,GAAG,CAAC,EAAE;IACpBe,OAAO,IAAK,GAAEG,KAAK,CAAClB,MAAM,GAAG,CAAC,GAAG,aAAa,GAAG,SAAU,IAAGH,UAAU,CACtEqB,KAAK,EACL,IACF,CAAE,EAAC;IACH,IAAIC,SAAS,CAACnB,MAAM,GAAG,CAAC,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACjE;EAEA,IAAII,SAAS,CAACnB,MAAM,GAAG,CAAC,EAAE;IACxBe,OAAO,IAAK,kBAAiBlB,UAAU,CAACsB,SAAS,EAAE,IAAI,CAAE,EAAC;IAC1D,IAAIC,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAEe,OAAO,IAAI,MAAM;EACzC;EAEA,IAAIK,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;IACpB,IAAIoB,KAAK,CAACpB,MAAM,GAAG,CAAC,EAAE;MACpBe,OAAO,IAAK,UAASlB,UAAU,CAACuB,KAAK,EAAE,IAAI,CAAE,EAAC;IAChD,CAAC,MAAM;MACL,IAAIA,KAAK,CAAC,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC,KAAKH,KAAK,CAAC,CAAC,CAAC,EAAEL,OAAO,IAAI,KAAK;MACzDA,OAAO,IAAK,GAAEK,KAAK,CAAC,CAAC,CAAE,EAAC;IAC1B;EACF;EAEAL,OAAO,IAAK,cAAaY,qBAAqB,CAAChB,MAAM,CAAE,EAAC;EAExD,OAAOI,OAAO;AAChB,CAAC,EACDa,SACF,CAAC;AAEDhC,KAAK,CAACiC,4BAA4B,GAAGrB,WAAW,CAC9C,8BAA8B,EAM9B,CAACsB,OAAO,EAAEC,MAAM,EAAEC,IAAI,GAAGC,SAAS,KAAK;EACrC,OAAQ,mBAAkBH,OAAQ,KAAIC,MAAO,GAC3CC,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;AACJ,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACsC,0BAA0B,GAAG1B,WAAW,CAC5C,4BAA4B,EAM5B,CAAC2B,IAAI,EAAEH,IAAI,EAAEjB,OAAO,KAAK;EACvB,OAAQ,0BAAyBoB,IAAK,GACpCH,IAAI,GAAI,oBAAmBA,IAAK,EAAC,GAAG,EACrC,GAAEjB,OAAO,GAAI,KAAIA,OAAQ,EAAC,GAAG,EAAG,EAAC;AACpC,CAAC,EACDqB,KACF,CAAC;AAEDxC,KAAK,CAACyC,0BAA0B,GAAG7B,WAAW,CAC5C,4BAA4B,EAQ5B,CAAC8B,OAAO,EAAEC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,GAAG,KAAK,EAAET,IAAI,GAAGC,SAAS,KAAK;EAC5D,MAAMS,QAAQ,GACZ,OAAOF,MAAM,KAAK,QAAQ,IAC1B,CAACC,QAAQ,IACTD,MAAM,CAACxC,MAAM,GAAG,CAAC,IACjB,CAACwC,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;EAC1B,IAAIJ,GAAG,KAAK,GAAG,EAAE;IACf3B,QAAKA,CAAC,CAAC6B,QAAQ,KAAK,KAAK,CAAC;IAC1B,OACG,iCAAgCG,IAAI,CAACC,SAAS,CAACL,MAAM,CAAE,WAAU,GACjE,yBAAwBF,OAAQ,eAC/BN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,GAAEU,QAAQ,GAAG,gCAAgC,GAAG,EAAG,EAAC;EAEzD;EAEA,OAAQ,YACND,QAAQ,GAAG,SAAS,GAAG,SACxB,YAAWG,IAAI,CAACC,SAAS,CACxBL,MACF,CAAE,iBAAgBD,GAAI,2BAA0BD,OAAQ,eACtDN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,GAAEU,QAAQ,GAAG,gCAAgC,GAAG,EAAG,EAAC;AACvD,CAAC,EACDN,KACF,CAAC;AAEDxC,KAAK,CAACkD,oBAAoB,GAAGtC,WAAW,CACtC,sBAAsB,EAMtB,CAAC2B,IAAI,EAAEH,IAAI,EAAEe,QAAQ,GAAG,KAAK,KAAK;EAChC,OAAQ,eACNA,QAAQ,GAAG,QAAQ,GAAG,SACvB,KAAIZ,IAAK,mBAAkBH,IAAK,EAAC;AACpC,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAACoD,6BAA6B,GAAGxC,WAAW,CAC/C,+BAA+B,EAC/B,2CAA2C,EAC3C4B,KACF,CAAC;AAEDxC,KAAK,CAACqD,8BAA8B,GAAGzC,WAAW,CAChD,gCAAgC,EAMhC,CAAC0C,SAAS,EAAEC,WAAW,EAAEnB,IAAI,KAAK;EAChC,OAAQ,6BAA4BkB,SAAU,mBAC5CC,WAAW,GAAI,eAAcA,WAAY,cAAa,GAAG,EAC1D,kBAAiBnB,IAAK,EAAC;AAC1B,CAAC,EACDJ,SACF,CAAC;AAEDhC,KAAK,CAACwD,6BAA6B,GAAG5C,WAAW,CAC/C,+BAA+B,EAM/B,CAAC8B,OAAO,EAAEe,OAAO,EAAErB,IAAI,GAAGC,SAAS,KAAK;EACtC,IAAIoB,OAAO,KAAK,GAAG,EACjB,OAAQ,gCAA+Bf,OAAQ,eAC7CN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;EACJ,OAAQ,oBAAmBqB,OAAQ,oCAAmCf,OAAQ,eAC5EN,IAAI,GAAI,kBAAiBA,IAAK,EAAC,GAAG,EACnC,EAAC;AACJ,CAAC,EACDI,KACF,CAAC;AAEDxC,KAAK,CAAC0D,0BAA0B,GAAG9C,WAAW,CAC5C,4BAA4B,EAC5B,yCAAyC,GACvC,uCAAuC,EACzC4B,KACF,CAAC;AAEDxC,KAAK,CAAC2D,0BAA0B,GAAG/C,WAAW,CAC5C,4BAA4B,EAK5B,CAACgD,GAAG,EAAErB,IAAI,KAAK;EACb,OAAQ,2BAA0BqB,GAAI,SAAQrB,IAAK,EAAC;AACtD,CAAC,EACDP,SACF,CAAC;AAEDhC,KAAK,CAAC6D,qBAAqB,GAAGjD,WAAW,CACvC,uBAAuB,EAMvB,CAACC,IAAI,EAAEY,KAAK,EAAEU,MAAM,GAAG,YAAY,KAAK;EACtC,IAAI2B,SAAS,GAAG,IAAAC,eAAO,EAACtC,KAAK,CAAC;EAE9B,IAAIqC,SAAS,CAAC1D,MAAM,GAAG,GAAG,EAAE;IAC1B0D,SAAS,GAAI,GAAEA,SAAS,CAACxD,KAAK,CAAC,CAAC,EAAE,GAAG,CAAE,KAAI;EAC7C;EAEA,MAAMH,IAAI,GAAGU,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,GAAG,UAAU,GAAG,UAAU;EAEzD,OAAQ,OAAMlB,IAAK,KAAIU,IAAK,KAAIsB,MAAO,cAAa2B,SAAU,EAAC;AACjE,CAAC,EACD9B,SAGF,CAAC;AAUD,SAASpB,WAAWA,CAACoD,GAAG,EAAEvC,KAAK,EAAEwC,GAAG,EAAE;EAGpC1D,QAAQ,CAACZ,GAAG,CAACqE,GAAG,EAAEvC,KAAK,CAAC;EAExB,OAAOyC,qBAAqB,CAACD,GAAG,EAAED,GAAG,CAAC;AACxC;AAOA,SAASE,qBAAqBA,CAACC,IAAI,EAAExB,GAAG,EAAE;EAExC,OAAOyB,SAAS;EAIhB,SAASA,SAASA,CAAC,GAAGC,IAAI,EAAE;IAC1B,MAAMC,KAAK,GAAG9B,KAAK,CAAC+B,eAAe;IACnC,IAAIC,8BAA8B,CAAC,CAAC,EAAEhC,KAAK,CAAC+B,eAAe,GAAG,CAAC;IAC/D,MAAME,KAAK,GAAG,IAAIN,IAAI,CAAC,CAAC;IAExB,IAAIK,8BAA8B,CAAC,CAAC,EAAEhC,KAAK,CAAC+B,eAAe,GAAGD,KAAK;IACnE,MAAMnD,OAAO,GAAGuD,UAAU,CAAC/B,GAAG,EAAE0B,IAAI,EAAEI,KAAK,CAAC;IAC5CtF,MAAM,CAACwF,gBAAgB,CAACF,KAAK,EAAE;MAG7BtD,OAAO,EAAE;QACPM,KAAK,EAAEN,OAAO;QACdyD,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB,CAAC;MACDC,QAAQ,EAAE;QAERtD,KAAKA,CAAA,EAAG;UACN,OAAQ,GAAE,IAAI,CAACZ,IAAK,KAAI8B,GAAI,MAAK,IAAI,CAACxB,OAAQ,EAAC;QACjD,CAAC;QACDyD,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;IAEFE,uBAAuB,CAACP,KAAK,CAAC;IAE9BA,KAAK,CAACQ,IAAI,GAAGtC,GAAG;IAChB,OAAO8B,KAAK;EACd;AACF;AAKA,SAASD,8BAA8BA,CAAA,EAAG;EAGxC,IAAI;IAEF,IAAIU,GAACA,CAAC,CAACC,eAAe,CAACC,kBAAkB,CAAC,CAAC,EAAE;MAC3C,OAAO,KAAK;IACd;EACF,CAAC,CAAC,OAAAC,OAAA,EAAM,CAAC;EAET,MAAMC,IAAI,GAAGnG,MAAM,CAACE,wBAAwB,CAACmD,KAAK,EAAE,iBAAiB,CAAC;EACtE,IAAI8C,IAAI,KAAKjD,SAAS,EAAE;IACtB,OAAOlD,MAAM,CAACoG,YAAY,CAAC/C,KAAK,CAAC;EACnC;EAEA,OAAO5C,KAAK,CAACH,IAAI,CAAC6F,IAAI,EAAE,UAAU,CAAC,IAAIA,IAAI,CAACT,QAAQ,KAAKxC,SAAS,GAC9DiD,IAAI,CAACT,QAAQ,GACbS,IAAI,CAAC3F,GAAG,KAAK0C,SAAS;AAC5B;AAQA,SAASmD,eAAeA,CAACC,EAAE,EAAE;EAG3B,MAAMC,MAAM,GAAGjF,kBAAkB,GAAGgF,EAAE,CAAC5E,IAAI;EAC3C1B,MAAM,CAACC,cAAc,CAACqG,EAAE,EAAE,MAAM,EAAE;IAAChE,KAAK,EAAEiE;EAAM,CAAC,CAAC;EAClD,OAAOD,EAAE;AACX;AAEA,MAAMT,uBAAuB,GAAGQ,eAAe,CAM7C,UAAUf,KAAK,EAAE;EACf,MAAMkB,yBAAyB,GAAGnB,8BAA8B,CAAC,CAAC;EAClE,IAAImB,yBAAyB,EAAE;IAC7BjF,mBAAmB,GAAG8B,KAAK,CAAC+B,eAAe;IAC3C/B,KAAK,CAAC+B,eAAe,GAAGqB,MAAM,CAACC,iBAAiB;EAClD;EAEArD,KAAK,CAACsD,iBAAiB,CAACrB,KAAK,CAAC;EAG9B,IAAIkB,yBAAyB,EAAEnD,KAAK,CAAC+B,eAAe,GAAG7D,mBAAmB;EAE1E,OAAO+D,KAAK;AACd,CACF,CAAC;AAQD,SAASC,UAAUA,CAAC/B,GAAG,EAAE0B,IAAI,EAAE0B,IAAI,EAAE;EACnC,MAAM5E,OAAO,GAAGZ,QAAQ,CAACxB,GAAG,CAAC4D,GAAG,CAAC;EACjC3B,QAAKA,CAAC,CAACG,OAAO,KAAKkB,SAAS,EAAE,gCAAgC,CAAC;EAE/D,IAAI,OAAOlB,OAAO,KAAK,UAAU,EAAE;IACjCH,QAAKA,CAAC,CACJG,OAAO,CAACf,MAAM,IAAIiE,IAAI,CAACjE,MAAM,EAC5B,SAAQuC,GAAI,oCAAmC0B,IAAI,CAACjE,MAAO,aAAY,GACrE,4BAA2Be,OAAO,CAACf,MAAO,IAC/C,CAAC;IACD,OAAO4F,OAAO,CAACC,KAAK,CAAC9E,OAAO,EAAE4E,IAAI,EAAE1B,IAAI,CAAC;EAC3C;EAEA,MAAM6B,KAAK,GAAG,aAAa;EAC3B,IAAIC,cAAc,GAAG,CAAC;EACtB,OAAOD,KAAK,CAACtE,IAAI,CAACT,OAAO,CAAC,KAAK,IAAI,EAAEgF,cAAc,EAAE;EACrDnF,QAAKA,CAAC,CACJmF,cAAc,KAAK9B,IAAI,CAACjE,MAAM,EAC7B,SAAQuC,GAAI,oCAAmC0B,IAAI,CAACjE,MAAO,aAAY,GACrE,4BAA2B+F,cAAe,IAC/C,CAAC;EACD,IAAI9B,IAAI,CAACjE,MAAM,KAAK,CAAC,EAAE,OAAOe,OAAO;EAErCkD,IAAI,CAAC+B,OAAO,CAACjF,OAAO,CAAC;EACrB,OAAO6E,OAAO,CAACC,KAAK,CAACI,cAAM,EAAE,IAAI,EAAEhC,IAAI,CAAC;AAC1C;AAOA,SAAStC,qBAAqBA,CAACN,KAAK,EAAE;EACpC,IAAIA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKY,SAAS,EAAE;IACzC,OAAOiE,MAAM,CAAC7E,KAAK,CAAC;EACtB;EAEA,IAAI,OAAOA,KAAK,KAAK,UAAU,IAAIA,KAAK,CAACZ,IAAI,EAAE;IAC7C,OAAQ,YAAWY,KAAK,CAACZ,IAAK,EAAC;EACjC;EAEA,IAAI,OAAOY,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIA,KAAK,CAAC8E,WAAW,IAAI9E,KAAK,CAAC8E,WAAW,CAAC1F,IAAI,EAAE;MAC/C,OAAQ,kBAAiBY,KAAK,CAAC8E,WAAW,CAAC1F,IAAK,EAAC;IACnD;IAEA,OAAQ,GAAE,IAAAkD,eAAO,EAACtC,KAAK,EAAE;MAAC+E,KAAK,EAAE,CAAC;IAAC,CAAC,CAAE,EAAC;EACzC;EAEA,IAAI1C,SAAS,GAAG,IAAAC,eAAO,EAACtC,KAAK,EAAE;IAACgF,MAAM,EAAE;EAAK,CAAC,CAAC;EAE/C,IAAI3C,SAAS,CAAC1D,MAAM,GAAG,EAAE,EAAE;IACzB0D,SAAS,GAAI,GAAEA,SAAS,CAACxD,KAAK,CAAC,CAAC,EAAE,EAAE,CAAE,KAAI;EAC5C;EAEA,OAAQ,QAAO,OAAOmB,KAAM,KAAIqC,SAAU,GAAE;AAC9C;AASA,MAAM4C,gBAAgB,GAAG,CAAC,CAAC,CAAClH,cAAc;AAE1C,MAAM;EAAC8C,0BAA0B,EAAEqE;AAA4B,CAAC,GAAG3G,KAAK;AAGxE,MAAM4G,KAAK,GAAG,IAAIpG,GAAG,CAAC,CAAC;AAEvB,MAAMqG,MAAM,GAAG;EAACC;AAAI,CAAC;AACrB,IAAIC,iBAAiB,GAAGF,MAAM;AAO9B,SAASC,IAAIA,CAACE,QAAQ,EAAE;EAAC5E,IAAI;EAAEkB;AAAS,CAAC,EAAE;EACzC,MAAM2D,QAAQ,GAAGL,KAAK,CAAC7H,GAAG,CAACiI,QAAQ,CAAC;EAEpC,IAAIC,QAAQ,EAAE;IACZ,OAAOA,QAAQ;EACjB;EAGA,IAAIC,MAAM;EAEV,IAAI;IACFA,MAAM,GAAGC,aAAE,CAACC,YAAY,CAAC7E,MAAGA,CAAC,CAAC8E,gBAAgB,CAACL,QAAQ,CAAC,EAAE,MAAM,CAAC;EACnE,CAAC,CAAC,OAAOvC,KAAK,EAAE;IACd,MAAM6C,SAAS,GAAkC7C,KAAM;IAEvD,IAAI6C,SAAS,CAACrC,IAAI,KAAK,QAAQ,EAAE;MAC/B,MAAMqC,SAAS;IACjB;EACF;EAGA,MAAMC,MAAM,GAAG;IACbC,MAAM,EAAE,KAAK;IACbC,SAAS,EAAET,QAAQ;IACnBU,IAAI,EAAErF,SAAS;IACfxB,IAAI,EAAEwB,SAAS;IACflC,IAAI,EAAE,MAAM;IACZwH,OAAO,EAAEtF,SAAS;IAClBuF,OAAO,EAAEvF;EACX,CAAC;EAED,IAAI6E,MAAM,KAAK7E,SAAS,EAAE;IAExB,IAAIwF,MAAM;IAEV,IAAI;MACFA,MAAM,GAAG7E,IAAI,CAAC8E,KAAK,CAACZ,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOa,MAAM,EAAE;MACf,MAAMC,KAAK,GAAkCD,MAAO;MACpD,MAAMtD,KAAK,GAAG,IAAIkC,4BAA4B,CAC5CK,QAAQ,EACR,CAAC5E,IAAI,GAAI,IAAGkB,SAAU,SAAQ,GAAG,EAAE,IAAI,IAAA2E,oBAAa,EAAC7F,IAAI,IAAIkB,SAAS,CAAC,EACvE0E,KAAK,CAAC7G,OACR,CAAC;MAEDsD,KAAK,CAACuD,KAAK,GAAGA,KAAK;MACnB,MAAMvD,KAAK;IACb;IAEA8C,MAAM,CAACC,MAAM,GAAG,IAAI;IAEpB,IACEd,gBAAgB,CAACjH,IAAI,CAACoI,MAAM,EAAE,MAAM,CAAC,IACrC,OAAOA,MAAM,CAAChH,IAAI,KAAK,QAAQ,EAC/B;MACA0G,MAAM,CAAC1G,IAAI,GAAGgH,MAAM,CAAChH,IAAI;IAC3B;IAEA,IACE6F,gBAAgB,CAACjH,IAAI,CAACoI,MAAM,EAAE,MAAM,CAAC,IACrC,OAAOA,MAAM,CAACH,IAAI,KAAK,QAAQ,EAC/B;MACAH,MAAM,CAACG,IAAI,GAAGG,MAAM,CAACH,IAAI;IAC3B;IAEA,IAAIhB,gBAAgB,CAACjH,IAAI,CAACoI,MAAM,EAAE,SAAS,CAAC,EAAE;MAE5CN,MAAM,CAACI,OAAO,GAAGE,MAAM,CAACF,OAAO;IACjC;IAEA,IAAIjB,gBAAgB,CAACjH,IAAI,CAACoI,MAAM,EAAE,SAAS,CAAC,EAAE;MAE5CN,MAAM,CAACK,OAAO,GAAGC,MAAM,CAACD,OAAO;IACjC;IAGA,IACElB,gBAAgB,CAACjH,IAAI,CAACoI,MAAM,EAAE,MAAM,CAAC,KACpCA,MAAM,CAAC1H,IAAI,KAAK,UAAU,IAAI0H,MAAM,CAAC1H,IAAI,KAAK,QAAQ,CAAC,EACxD;MACAoH,MAAM,CAACpH,IAAI,GAAG0H,MAAM,CAAC1H,IAAI;IAC3B;EACF;EAEAyG,KAAK,CAACjH,GAAG,CAACqH,QAAQ,EAAEO,MAAM,CAAC;EAE3B,OAAOA,MAAM;AACf;AAWA,SAASW,qBAAqBA,CAACC,QAAQ,EAAE;EACvC,IAAIC,cAAc,GAAG,KAAIC,UAAG,EAAC,cAAc,EAAEF,QAAQ,CAAC;EAEtD,OAAO,IAAI,EAAE;IACX,MAAMG,eAAe,GAAGF,cAAc,CAACG,QAAQ;IAC/C,IAAID,eAAe,CAAClH,QAAQ,CAAC,2BAA2B,CAAC,EAAE;MACzD;IACF;IAEA,MAAMoH,aAAa,GAAGzB,iBAAiB,CAACD,IAAI,CAC1C,IAAAmB,oBAAa,EAACG,cAAc,CAAC,EAC7B;MAAC9E,SAAS,EAAE6E;IAAQ,CACtB,CAAC;IAED,IAAIK,aAAa,CAAChB,MAAM,EAAE;MACxB,OAAOgB,aAAa;IACtB;IAEA,MAAMC,kBAAkB,GAAGL,cAAc;IACzCA,cAAc,GAAG,KAAIC,UAAG,EAAC,iBAAiB,EAAED,cAAc,CAAC;IAI3D,IAAIA,cAAc,CAACG,QAAQ,KAAKE,kBAAkB,CAACF,QAAQ,EAAE;MAC3D;IACF;EACF;EAEA,MAAMD,eAAe,GAAG,IAAAL,oBAAa,EAACG,cAAc,CAAC;EAErD,OAAO;IACLX,SAAS,EAAEa,eAAe;IAC1Bd,MAAM,EAAE,KAAK;IACbE,IAAI,EAAErF,SAAS;IACfxB,IAAI,EAAEwB,SAAS;IACflC,IAAI,EAAE,MAAM;IACZwH,OAAO,EAAEtF,SAAS;IAClBuF,OAAO,EAAEvF;EACX,CAAC;AACH;AAgBA,SAASqG,cAAcA,CAACC,GAAG,EAAE;EAC3B,MAAMH,aAAa,GAAGN,qBAAqB,CAACS,GAAG,CAAC;EAChD,OAAOH,aAAa,CAACrI,IAAI;AAC3B;AAOA,MAAM;EAACwD;AAA0B,CAAC,GAAG3D,KAAK;AAE1C,MAAMR,cAAc,GAAG,CAAC,CAAC,CAACA,cAAc;AAGxC,MAAMoJ,kBAAkB,GAAG;EAEzB3J,SAAS,EAAE,IAAI;EACf,MAAM,EAAE,UAAU;EAClB,KAAK,EAAE,QAAQ;EACf,OAAO,EAAE,MAAM;EACf,MAAM,EAAE;AACV,CAAC;AAMD,SAAS4J,YAAYA,CAACC,IAAI,EAAE;EAC1B,IACEA,IAAI,IACJ,+DAA+D,CAACC,IAAI,CAACD,IAAI,CAAC,EAE1E,OAAO,QAAQ;EACjB,IAAIA,IAAI,KAAK,kBAAkB,EAAE,OAAO,MAAM;EAC9C,OAAO,IAAI;AACb;AAaA,MAAME,gBAAgB,GAAG;EAEvB/J,SAAS,EAAE,IAAI;EACf,OAAO,EAAEgK,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,OAAO,EAAEC,2BAA2B;EACpC,QAAQ,EAAEA,2BAA2B;EACrC,OAAOC,CAAA,EAAG;IACR,OAAO,SAAS;EAClB;AACF,CAAC;AAKD,SAASH,2BAA2BA,CAACpB,MAAM,EAAE;EAC3C,MAAM;IAAC,CAAC,EAAEiB;EAAI,CAAC,GAAG,mCAAmC,CAAClH,IAAI,CACxDiG,MAAM,CAACU,QACT,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACvB,OAAOM,YAAY,CAACC,IAAI,CAAC;AAC3B;AAYA,SAASO,OAAOA,CAACV,GAAG,EAAE;EACpB,MAAMJ,QAAQ,GAAGI,GAAG,CAACJ,QAAQ;EAC7B,IAAIe,KAAK,GAAGf,QAAQ,CAACnI,MAAM;EAE3B,OAAOkJ,KAAK,EAAE,EAAE;IACd,MAAMrE,IAAI,GAAGsD,QAAQ,CAACgB,WAAW,CAACD,KAAK,CAAC;IAExC,IAAIrE,IAAI,KAAK,EAAE,EAAY;MACzB,OAAO,EAAE;IACX;IAEA,IAAIA,IAAI,KAAK,EAAE,EAAY;MACzB,OAAOsD,QAAQ,CAACgB,WAAW,CAACD,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE,GACzC,EAAE,GACFf,QAAQ,CAACjI,KAAK,CAACgJ,KAAK,CAAC;IAC3B;EACF;EAEA,OAAO,EAAE;AACX;AAKA,SAASJ,2BAA2BA,CAACP,GAAG,EAAEa,QAAQ,EAAEC,YAAY,EAAE;EAChE,MAAM7F,GAAG,GAAGyF,OAAO,CAACV,GAAG,CAAC;EAExB,IAAI/E,GAAG,KAAK,KAAK,EAAE;IACjB,MAAM8F,WAAW,GAAGhB,cAAc,CAACC,GAAG,CAAC;IAEvC,IAAIe,WAAW,KAAK,MAAM,EAAE;MAC1B,OAAOA,WAAW;IACpB;IAEA,OAAO,UAAU;EACnB;EAEA,IAAI9F,GAAG,KAAK,EAAE,EAAE;IACd,MAAM8F,WAAW,GAAGhB,cAAc,CAACC,GAAG,CAAC;IAGvC,IAAIe,WAAW,KAAK,MAAM,IAAIA,WAAW,KAAK,UAAU,EAAE;MACxD,OAAO,UAAU;IACnB;IAIA,OAAO,QAAQ;EACjB;EAEA,MAAMrD,MAAM,GAAGuC,kBAAkB,CAAChF,GAAG,CAAC;EACtC,IAAIyC,MAAM,EAAE,OAAOA,MAAM;EAGzB,IAAIoD,YAAY,EAAE;IAChB,OAAOpH,SAAS;EAClB;EAEA,MAAMsH,QAAQ,GAAG,IAAA1B,oBAAa,EAACU,GAAG,CAAC;EACnC,MAAM,IAAIhF,0BAA0B,CAACC,GAAG,EAAE+F,QAAQ,CAAC;AACrD;AAEA,SAASR,2BAA2BA,CAAA,EAAG,CAEvC;AAOA,SAASS,6BAA6BA,CAACjB,GAAG,EAAEkB,OAAO,EAAE;EACnD,MAAMC,QAAQ,GAAGnB,GAAG,CAACmB,QAAQ;EAE7B,IAAI,CAACtK,cAAc,CAACC,IAAI,CAACuJ,gBAAgB,EAAEc,QAAQ,CAAC,EAAE;IACpD,OAAO,IAAI;EACb;EAEA,OAAOd,gBAAgB,CAACc,QAAQ,CAAC,CAACnB,GAAG,EAAEkB,OAAO,EAAE,IAAI,CAAC,IAAI,IAAI;AAC/D;AAOA,MAAM;EAAChG;AAAqB,CAAC,GAAG7D,KAAK;AAKrC,MAAM+J,kBAAkB,GAAG5K,MAAM,CAAC6K,MAAM,CAAC,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;AAC5D,MAAMC,sBAAsB,GAAG,IAAIlK,GAAG,CAACgK,kBAAkB,CAAC;AAK1D,SAASG,oBAAoBA,CAAA,EAAG;EAC9B,OAAOH,kBAAkB;AAC3B;AAKA,SAASI,uBAAuBA,CAAA,EAAG;EACjC,OAAOF,sBAAsB;AAC/B;AAMA,SAASG,gBAAgBA,CAACC,UAAU,EAAE;EACpC,IAAIA,UAAU,KAAKhI,SAAS,IAAIgI,UAAU,KAAKH,oBAAoB,CAAC,CAAC,EAAE;IACrE,IAAI,CAACjJ,KAAK,CAACC,OAAO,CAACmJ,UAAU,CAAC,EAAE;MAC9B,MAAM,IAAIxG,qBAAqB,CAC7B,YAAY,EACZwG,UAAU,EACV,mBACF,CAAC;IACH;IAEA,OAAO,IAAItK,GAAG,CAACsK,UAAU,CAAC;EAC5B;EAEA,OAAOF,uBAAuB,CAAC,CAAC;AAClC;AAOA,MAAMG,4BAA4B,GAAGC,MAAM,CAAChL,SAAS,CAACiL,MAAM,CAACC,OAAO,CAAC;AAErE,MAAM;EACJrH,6BAA6B;EAC7BnB,4BAA4B;EAC5BK,0BAA0B;EAC1BG,0BAA0B;EAC1BS,oBAAoB;EACpBG,8BAA8B;EAC9BG,6BAA6B;EAC7BE;AACF,CAAC,GAAG1D,KAAK;AAET,MAAM0K,GAAG,GAAG,CAAC,CAAC,CAAClL,cAAc;AAE7B,MAAMmL,mBAAmB,GACvB,0KAA0K;AAC5K,MAAMC,6BAA6B,GACjC,yKAAyK;AAC3K,MAAMC,uBAAuB,GAAG,UAAU;AAC1C,MAAMC,YAAY,GAAG,KAAK;AAC1B,MAAMC,eAAe,GAAG,UAAU;AAElC,MAAMC,sBAAsB,GAAG,IAAIjL,GAAG,CAAC,CAAC;AAExC,MAAMkL,gBAAgB,GAAG,UAAU;AAYnC,SAASC,6BAA6BA,CACpCtI,MAAM,EACNV,OAAO,EACPiJ,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRjJ,IAAI,EACJkJ,QAAQ,EACR;EAEA,IAAIC,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAM/D,SAAS,GAAG,IAAAQ,oBAAa,EAACmD,cAAc,CAAC;EAC/C,MAAMK,MAAM,GAAGR,gBAAgB,CAACrJ,IAAI,CAAC0J,QAAQ,GAAG1I,MAAM,GAAGV,OAAO,CAAC,KAAK,IAAI;EAC1EqJ,SAAMA,CAAC,CAACG,WAAW,CAChB,qBACCD,MAAM,GAAG,cAAc,GAAG,oCAC3B,eAAc7I,MAAO,eAAc,GACjC,YAAWV,OAAQ,KAClBA,OAAO,KAAKiJ,KAAK,GAAG,EAAE,GAAI,eAAcA,KAAM,IAC/C,WACCE,QAAQ,GAAG,SAAS,GAAG,SACxB,+CAA8C5D,SAAU,GACvDrF,IAAI,GAAI,kBAAiB,IAAA6F,oBAAa,EAAC7F,IAAI,CAAE,EAAC,GAAG,EAClD,GAAE,EACL,oBAAoB,EACpB,SACF,CAAC;AACH;AASA,SAASuJ,0BAA0BA,CAAChD,GAAG,EAAEyC,cAAc,EAAEhJ,IAAI,EAAEsF,IAAI,EAAE;EAEnE,IAAI6D,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAMnF,MAAM,GAAGuD,6BAA6B,CAACjB,GAAG,EAAE;IAACiD,SAAS,EAAExJ,IAAI,CAACyJ;EAAI,CAAC,CAAC;EACzE,IAAIxF,MAAM,KAAK,QAAQ,EAAE;EACzB,MAAMyF,OAAO,GAAG,IAAA7D,oBAAa,EAACU,GAAG,CAACkD,IAAI,CAAC;EACvC,MAAMnJ,OAAO,GAAG,IAAAuF,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC;EAC3D,MAAMW,QAAQ,GAAG,IAAA9D,oBAAa,EAAC7F,IAAI,CAAC;EACpC,IAAI,CAACsF,IAAI,EAAE;IACT6D,SAAMA,CAAC,CAACG,WAAW,CAChB,gEAA+DhJ,OAAQ,oCAAmCoJ,OAAO,CAACxL,KAAK,CACtHoC,OAAO,CAACtC,MACV,CAAE,oBAAmB2L,QAAS,wEAAuE,EACrG,oBAAoB,EACpB,SACF,CAAC;EACH,CAAC,MAAM,IAAIxJ,MAAGA,CAAC,CAACyJ,OAAO,CAACtJ,OAAO,EAAEgF,IAAI,CAAC,KAAKoE,OAAO,EAAE;IAClDP,SAAMA,CAAC,CAACG,WAAW,CAChB,WAAUhJ,OAAQ,+BAA8BgF,IAAK,KAAI,GACvD,sEAAqEoE,OAAO,CAACxL,KAAK,CACjFoC,OAAO,CAACtC,MACV,CAAE,oBAAmB2L,QAAS,4DAA2D,GACzF,4BAA4B,EAC9B,oBAAoB,EACpB,SACF,CAAC;EACH;AACF;AAMA,SAASE,WAAWA,CAAC1J,IAAI,EAAE;EAEzB,IAAI;IACF,OAAO,IAAA2J,cAAQ,EAAC3J,IAAI,CAAC;EACvB,CAAC,CAAC,OAAA4J,QAAA,EAAM;IACN,OAAO,KAAIC,WAAK,EAAC,CAAC;EACpB;AACF;AAaA,SAASC,UAAUA,CAAC1D,GAAG,EAAE;EACvB,MAAM2D,KAAK,GAAG,IAAAJ,cAAQ,EAACvD,GAAG,EAAE;IAAC4D,cAAc,EAAE;EAAK,CAAC,CAAC;EACpD,MAAMC,MAAM,GAAGF,KAAK,GAAGA,KAAK,CAACE,MAAM,CAAC,CAAC,GAAGnK,SAAS;EACjD,OAAOmK,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAKnK,SAAS,GAAG,KAAK,GAAGmK,MAAM;AACjE;AAQA,SAASC,iBAAiBA,CAACrB,cAAc,EAAE5C,aAAa,EAAEpG,IAAI,EAAE;EAE9D,IAAIsK,KAAK;EACT,IAAIlE,aAAa,CAACd,IAAI,KAAKrF,SAAS,EAAE;IACpCqK,KAAK,GAAG,KAAIrE,UAAG,EAACG,aAAa,CAACd,IAAI,EAAE0D,cAAc,CAAC;IAEnD,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE,OAAOA,KAAK;IAEnC,MAAMC,KAAK,GAAG,CACX,KAAInE,aAAa,CAACd,IAAK,KAAI,EAC3B,KAAIc,aAAa,CAACd,IAAK,OAAM,EAC7B,KAAIc,aAAa,CAACd,IAAK,OAAM,EAC7B,KAAIc,aAAa,CAACd,IAAK,WAAU,EACjC,KAAIc,aAAa,CAACd,IAAK,aAAY,EACnC,KAAIc,aAAa,CAACd,IAAK,aAAY,CACrC;IACD,IAAIhI,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGiN,KAAK,CAACvM,MAAM,EAAE;MACzBsM,KAAK,GAAG,KAAIrE,UAAG,EAACsE,KAAK,CAACjN,CAAC,CAAC,EAAE0L,cAAc,CAAC;MACzC,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE;MACvBA,KAAK,GAAGrK,SAAS;IACnB;IAEA,IAAIqK,KAAK,EAAE;MACTf,0BAA0B,CACxBe,KAAK,EACLtB,cAAc,EACdhJ,IAAI,EACJoG,aAAa,CAACd,IAChB,CAAC;MACD,OAAOgF,KAAK;IACd;EAEF;EAEA,MAAMC,KAAK,GAAG,CAAC,YAAY,EAAE,cAAc,EAAE,cAAc,CAAC;EAC5D,IAAIjN,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGiN,KAAK,CAACvM,MAAM,EAAE;IACzBsM,KAAK,GAAG,KAAIrE,UAAG,EAACsE,KAAK,CAACjN,CAAC,CAAC,EAAE0L,cAAc,CAAC;IACzC,IAAIiB,UAAU,CAACK,KAAK,CAAC,EAAE;IACvBA,KAAK,GAAGrK,SAAS;EACnB;EAEA,IAAIqK,KAAK,EAAE;IACTf,0BAA0B,CAACe,KAAK,EAAEtB,cAAc,EAAEhJ,IAAI,EAAEoG,aAAa,CAACd,IAAI,CAAC;IAC3E,OAAOgF,KAAK;EACd;EAGA,MAAM,IAAIxJ,oBAAoB,CAC5B,IAAA+E,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC,EAC3C,IAAAnD,oBAAa,EAAC7F,IAAI,CACpB,CAAC;AACH;AAQA,SAASwK,kBAAkBA,CAACzE,QAAQ,EAAE/F,IAAI,EAAEyK,gBAAgB,EAAE;EAC5D,IAAI9B,eAAe,CAACnJ,IAAI,CAACuG,QAAQ,CAACI,QAAQ,CAAC,KAAK,IAAI,EAAE;IACpD,MAAM,IAAItG,4BAA4B,CACpCkG,QAAQ,CAACI,QAAQ,EACjB,iDAAiD,EACjD,IAAAN,oBAAa,EAAC7F,IAAI,CACpB,CAAC;EACH;EAGA,IAAI0K,QAAQ;EAEZ,IAAI;IACFA,QAAQ,GAAG,IAAA7E,oBAAa,EAACE,QAAQ,CAAC;EACpC,CAAC,CAAC,OAAO1D,KAAK,EAAE;IACd,MAAMuD,KAAK,GAAkCvD,KAAM;IACnDtF,MAAM,CAACC,cAAc,CAAC4I,KAAK,EAAE,OAAO,EAAE;MAACvG,KAAK,EAAE6E,MAAM,CAAC6B,QAAQ;IAAC,CAAC,CAAC;IAChEhJ,MAAM,CAACC,cAAc,CAAC4I,KAAK,EAAE,QAAQ,EAAE;MAACvG,KAAK,EAAE6E,MAAM,CAAClE,IAAI;IAAC,CAAC,CAAC;IAC7D,MAAM4F,KAAK;EACb;EAEA,MAAMsE,KAAK,GAAGL,WAAW,CACvBa,QAAQ,CAAC1L,QAAQ,CAAC,GAAG,CAAC,GAAG0L,QAAQ,CAACxM,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGwM,QAChD,CAAC;EAED,IAAIR,KAAK,CAACS,WAAW,CAAC,CAAC,EAAE;IACvB,MAAMtI,KAAK,GAAG,IAAIf,0BAA0B,CAACoJ,QAAQ,EAAE,IAAA7E,oBAAa,EAAC7F,IAAI,CAAC,CAAC;IAE3EqC,KAAK,CAACkE,GAAG,GAAGrC,MAAM,CAAC6B,QAAQ,CAAC;IAC5B,MAAM1D,KAAK;EACb;EAEA,IAAI,CAAC6H,KAAK,CAACE,MAAM,CAAC,CAAC,EAAE;IACnB,MAAM/H,KAAK,GAAG,IAAIvB,oBAAoB,CACpC4J,QAAQ,IAAI3E,QAAQ,CAACI,QAAQ,EAC7BnG,IAAI,IAAI,IAAA6F,oBAAa,EAAC7F,IAAI,CAAC,EAC3B,IACF,CAAC;IAEDqC,KAAK,CAACkE,GAAG,GAAGrC,MAAM,CAAC6B,QAAQ,CAAC;IAC5B,MAAM1D,KAAK;EACb;EAEA,IAAI,CAACoI,gBAAgB,EAAE;IACrB,MAAMG,IAAI,GAAG,IAAAC,kBAAY,EAACH,QAAQ,CAAC;IACnC,MAAM;MAACI,MAAM;MAAEC;IAAI,CAAC,GAAGhF,QAAQ;IAC/BA,QAAQ,GAAG,IAAAiF,oBAAa,EAACJ,IAAI,IAAIF,QAAQ,CAAC1L,QAAQ,CAACmB,MAAGA,CAAC,CAAC8K,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,CAAC,CAAC;IACzElF,QAAQ,CAAC+E,MAAM,GAAGA,MAAM;IACxB/E,QAAQ,CAACgF,IAAI,GAAGA,IAAI;EACtB;EAEA,OAAOhF,QAAQ;AACjB;AAQA,SAASmF,gBAAgBA,CAAChK,SAAS,EAAE8H,cAAc,EAAEhJ,IAAI,EAAE;EACzD,OAAO,IAAIiB,8BAA8B,CACvCC,SAAS,EACT8H,cAAc,IAAI,IAAAnD,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC,EAC7D,IAAAnD,oBAAa,EAAC7F,IAAI,CACpB,CAAC;AACH;AAQA,SAASmL,eAAeA,CAAC9J,OAAO,EAAE2H,cAAc,EAAEhJ,IAAI,EAAE;EACtD,OAAO,IAAIoB,6BAA6B,CACtC,IAAAyE,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC,EAC3C3H,OAAO,EACPrB,IAAI,IAAI,IAAA6F,oBAAa,EAAC7F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASoL,mBAAmBA,CAACtL,OAAO,EAAEiJ,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,EAAE;EAC3E,MAAMD,MAAM,GAAI,4CAA2CgJ,KAAM,cAC/DE,QAAQ,GAAG,SAAS,GAAG,SACxB,mBAAkB,IAAApD,oBAAa,EAACmD,cAAc,CAAE,EAAC;EAClD,MAAM,IAAInJ,4BAA4B,CACpCC,OAAO,EACPC,MAAM,EACNC,IAAI,IAAI,IAAA6F,oBAAa,EAAC7F,IAAI,CAC5B,CAAC;AACH;AAUA,SAASqL,oBAAoBA,CAAChK,OAAO,EAAEb,MAAM,EAAEwI,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,EAAE;EAC7EQ,MAAM,GACJ,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,GACzCI,IAAI,CAACC,SAAS,CAACL,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,GAC/B,GAAEA,MAAO,EAAC;EAEjB,OAAO,IAAIH,0BAA0B,CACnC,IAAAwF,oBAAa,EAAC,KAAII,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC,EAC3C3H,OAAO,EACPb,MAAM,EACNyI,QAAQ,EACRjJ,IAAI,IAAI,IAAA6F,oBAAa,EAAC7F,IAAI,CAC5B,CAAC;AACH;AAcA,SAASsL,0BAA0BA,CACjC9K,MAAM,EACNa,OAAO,EACP0H,KAAK,EACLC,cAAc,EACdhJ,IAAI,EACJuL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTvD,UAAU,EACV;EACA,IAAI5G,OAAO,KAAK,EAAE,IAAI,CAACkK,OAAO,IAAI/K,MAAM,CAACA,MAAM,CAACxC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EACjE,MAAMqN,oBAAoB,CAACtC,KAAK,EAAEvI,MAAM,EAAEwI,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,CAAC;EAE3E,IAAI,CAACQ,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;IAC5B,IAAIsI,QAAQ,IAAI,CAACzI,MAAM,CAACG,UAAU,CAAC,KAAK,CAAC,IAAI,CAACH,MAAM,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;MACpE,IAAI8K,KAAK,GAAG,KAAK;MAEjB,IAAI;QACF,KAAIxF,UAAG,EAACzF,MAAM,CAAC;QACfiL,KAAK,GAAG,IAAI;MACd,CAAC,CAAC,OAAAC,QAAA,EAAM,CAER;MAEA,IAAI,CAACD,KAAK,EAAE;QACV,MAAME,YAAY,GAAGJ,OAAO,GACxBrD,4BAA4B,CAAC7K,IAAI,CAC/BqL,YAAY,EACZlI,MAAM,EACN,MAAMa,OACR,CAAC,GACDb,MAAM,GAAGa,OAAO;QAEpB,OAAOuK,cAAc,CAACD,YAAY,EAAE3C,cAAc,EAAEf,UAAU,CAAC;MACjE;IACF;IAEA,MAAMoD,oBAAoB,CAACtC,KAAK,EAAEvI,MAAM,EAAEwI,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,CAAC;EAC3E;EAEA,IAAIuI,mBAAmB,CAAC/I,IAAI,CAACgB,MAAM,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;IACtD,IAAIsK,6BAA6B,CAAChJ,IAAI,CAACgB,MAAM,CAACtC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;MAChE,IAAI,CAACsN,SAAS,EAAE;QACd,MAAM1L,OAAO,GAAGyL,OAAO,GACnBxC,KAAK,CAACV,OAAO,CAAC,GAAG,EAAE,MAAMhH,OAAO,CAAC,GACjC0H,KAAK,GAAG1H,OAAO;QACnB,MAAMwK,cAAc,GAAGN,OAAO,GAC1BrD,4BAA4B,CAAC7K,IAAI,CAC/BqL,YAAY,EACZlI,MAAM,EACN,MAAMa,OACR,CAAC,GACDb,MAAM;QACVsI,6BAA6B,CAC3B+C,cAAc,EACd/L,OAAO,EACPiJ,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRjJ,IAAI,EACJ,IACF,CAAC;MACH;IACF,CAAC,MAAM;MACL,MAAMqL,oBAAoB,CAACtC,KAAK,EAAEvI,MAAM,EAAEwI,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,CAAC;IAC3E;EACF;EAEA,MAAM+F,QAAQ,GAAG,KAAIE,UAAG,EAACzF,MAAM,EAAEwI,cAAc,CAAC;EAChD,MAAM8C,YAAY,GAAG/F,QAAQ,CAACI,QAAQ;EACtC,MAAMhF,WAAW,GAAG,KAAI8E,UAAG,EAAC,GAAG,EAAE+C,cAAc,CAAC,CAAC7C,QAAQ;EAEzD,IAAI,CAAC2F,YAAY,CAACnL,UAAU,CAACQ,WAAW,CAAC,EACvC,MAAMkK,oBAAoB,CAACtC,KAAK,EAAEvI,MAAM,EAAEwI,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,CAAC;EAE3E,IAAIqB,OAAO,KAAK,EAAE,EAAE,OAAO0E,QAAQ;EAEnC,IAAIwC,mBAAmB,CAAC/I,IAAI,CAAC6B,OAAO,CAAC,KAAK,IAAI,EAAE;IAC9C,MAAMvB,OAAO,GAAGyL,OAAO,GACnBxC,KAAK,CAACV,OAAO,CAAC,GAAG,EAAE,MAAMhH,OAAO,CAAC,GACjC0H,KAAK,GAAG1H,OAAO;IACnB,IAAImH,6BAA6B,CAAChJ,IAAI,CAAC6B,OAAO,CAAC,KAAK,IAAI,EAAE;MACxD,IAAI,CAACmK,SAAS,EAAE;QACd,MAAMK,cAAc,GAAGN,OAAO,GAC1BrD,4BAA4B,CAAC7K,IAAI,CAC/BqL,YAAY,EACZlI,MAAM,EACN,MAAMa,OACR,CAAC,GACDb,MAAM;QACVsI,6BAA6B,CAC3B+C,cAAc,EACd/L,OAAO,EACPiJ,KAAK,EACLC,cAAc,EACdC,QAAQ,EACRjJ,IAAI,EACJ,KACF,CAAC;MACH;IACF,CAAC,MAAM;MACLoL,mBAAmB,CAACtL,OAAO,EAAEiJ,KAAK,EAAEC,cAAc,EAAEC,QAAQ,EAAEjJ,IAAI,CAAC;IACrE;EACF;EAEA,IAAIuL,OAAO,EAAE;IACX,OAAO,KAAItF,UAAG,EACZiC,4BAA4B,CAAC7K,IAAI,CAC/BqL,YAAY,EACZ3C,QAAQ,CAAC0D,IAAI,EACb,MAAMpI,OACR,CACF,CAAC;EACH;EAEA,OAAO,KAAI4E,UAAG,EAAC5E,OAAO,EAAE0E,QAAQ,CAAC;AACnC;AAMA,SAASgG,YAAYA,CAACxL,GAAG,EAAE;EACzB,MAAMyL,SAAS,GAAGxI,MAAM,CAACjD,GAAG,CAAC;EAC7B,IAAK,GAAEyL,SAAU,EAAC,KAAKzL,GAAG,EAAE,OAAO,KAAK;EACxC,OAAOyL,SAAS,IAAI,CAAC,IAAIA,SAAS,GAAG,UAAa;AACpD;AAcA,SAASC,oBAAoBA,CAC3BjD,cAAc,EACdxI,MAAM,EACNa,OAAO,EACP6K,cAAc,EACdlM,IAAI,EACJuL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTvD,UAAU,EACV;EACA,IAAI,OAAOzH,MAAM,KAAK,QAAQ,EAAE;IAC9B,OAAO8K,0BAA0B,CAC/B9K,MAAM,EACNa,OAAO,EACP6K,cAAc,EACdlD,cAAc,EACdhJ,IAAI,EACJuL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTvD,UACF,CAAC;EACH;EAEA,IAAIpJ,KAAK,CAACC,OAAO,CAAC0B,MAAM,CAAC,EAAE;IAEzB,MAAM2L,UAAU,GAAG3L,MAAM;IACzB,IAAI2L,UAAU,CAACnO,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAGxC,IAAIoO,aAAa;IACjB,IAAI9O,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAG6O,UAAU,CAACnO,MAAM,EAAE;MAC9B,MAAMqO,UAAU,GAAGF,UAAU,CAAC7O,CAAC,CAAC;MAEhC,IAAIgP,aAAa;MACjB,IAAI;QACFA,aAAa,GAAGL,oBAAoB,CAClCjD,cAAc,EACdqD,UAAU,EACVhL,OAAO,EACP6K,cAAc,EACdlM,IAAI,EACJuL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTvD,UACF,CAAC;MACH,CAAC,CAAC,OAAO5F,KAAK,EAAE;QACd,MAAM6C,SAAS,GAAkC7C,KAAM;QACvD+J,aAAa,GAAGlH,SAAS;QACzB,IAAIA,SAAS,CAACrC,IAAI,KAAK,4BAA4B,EAAE;QACrD,MAAMR,KAAK;MACb;MAEA,IAAIiK,aAAa,KAAKrM,SAAS,EAAE;MAEjC,IAAIqM,aAAa,KAAK,IAAI,EAAE;QAC1BF,aAAa,GAAG,IAAI;QACpB;MACF;MAEA,OAAOE,aAAa;IACtB;IAEA,IAAIF,aAAa,KAAKnM,SAAS,IAAImM,aAAa,KAAK,IAAI,EAAE;MACzD,OAAO,IAAI;IACb;IAEA,MAAMA,aAAa;EACrB;EAEA,IAAI,OAAO5L,MAAM,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACjD,MAAM+L,IAAI,GAAGxP,MAAM,CAACyP,mBAAmB,CAAChM,MAAM,CAAC;IAC/C,IAAIlD,CAAC,GAAG,CAAC,CAAC;IAEV,OAAO,EAAEA,CAAC,GAAGiP,IAAI,CAACvO,MAAM,EAAE;MACxB,MAAMuC,GAAG,GAAGgM,IAAI,CAACjP,CAAC,CAAC;MACnB,IAAIyO,YAAY,CAACxL,GAAG,CAAC,EAAE;QACrB,MAAM,IAAIL,0BAA0B,CAClC,IAAA2F,oBAAa,EAACmD,cAAc,CAAC,EAC7BhJ,IAAI,EACJ,iDACF,CAAC;MACH;IACF;IAEA1C,CAAC,GAAG,CAAC,CAAC;IAEN,OAAO,EAAEA,CAAC,GAAGiP,IAAI,CAACvO,MAAM,EAAE;MACxB,MAAMuC,GAAG,GAAGgM,IAAI,CAACjP,CAAC,CAAC;MACnB,IAAIiD,GAAG,KAAK,SAAS,IAAK0H,UAAU,IAAIA,UAAU,CAACvL,GAAG,CAAC6D,GAAG,CAAE,EAAE;QAE5D,MAAMkM,iBAAiB,GAA2BjM,MAAM,CAACD,GAAG,CAAE;QAC9D,MAAM+L,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdyD,iBAAiB,EACjBpL,OAAO,EACP6K,cAAc,EACdlM,IAAI,EACJuL,OAAO,EACPtC,QAAQ,EACRuC,SAAS,EACTvD,UACF,CAAC;QACD,IAAIqE,aAAa,KAAKrM,SAAS,EAAE;QACjC,OAAOqM,aAAa;MACtB;IACF;IAEA,OAAO,IAAI;EACb;EAEA,IAAI9L,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO,IAAI;EACb;EAEA,MAAM6K,oBAAoB,CACxBa,cAAc,EACd1L,MAAM,EACNwI,cAAc,EACdC,QAAQ,EACRjJ,IACF,CAAC;AACH;AAQA,SAAS0M,6BAA6BA,CAACnH,OAAO,EAAEyD,cAAc,EAAEhJ,IAAI,EAAE;EACpE,IAAI,OAAOuF,OAAO,KAAK,QAAQ,IAAI1G,KAAK,CAACC,OAAO,CAACyG,OAAO,CAAC,EAAE,OAAO,IAAI;EACtE,IAAI,OAAOA,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,IAAI,EAAE,OAAO,KAAK;EAEjE,MAAMgH,IAAI,GAAGxP,MAAM,CAACyP,mBAAmB,CAACjH,OAAO,CAAC;EAChD,IAAIoH,kBAAkB,GAAG,KAAK;EAC9B,IAAIrP,CAAC,GAAG,CAAC;EACT,IAAIsP,CAAC,GAAG,CAAC,CAAC;EACV,OAAO,EAAEA,CAAC,GAAGL,IAAI,CAACvO,MAAM,EAAE;IACxB,MAAMuC,GAAG,GAAGgM,IAAI,CAACK,CAAC,CAAC;IACnB,MAAMC,qBAAqB,GAAGtM,GAAG,KAAK,EAAE,IAAIA,GAAG,CAAC,CAAC,CAAC,KAAK,GAAG;IAC1D,IAAIjD,CAAC,EAAE,KAAK,CAAC,EAAE;MACbqP,kBAAkB,GAAGE,qBAAqB;IAC5C,CAAC,MAAM,IAAIF,kBAAkB,KAAKE,qBAAqB,EAAE;MACvD,MAAM,IAAI3M,0BAA0B,CAClC,IAAA2F,oBAAa,EAACmD,cAAc,CAAC,EAC7BhJ,IAAI,EACJ,sEAAsE,GACpE,sEAAsE,GACtE,uDACJ,CAAC;IACH;EACF;EAEA,OAAO2M,kBAAkB;AAC3B;AAOA,SAASG,mCAAmCA,CAAC/D,KAAK,EAAEgE,QAAQ,EAAE/M,IAAI,EAAE;EAElE,IAAImJ,SAAMA,CAAC,CAACC,aAAa,EAAE;IACzB;EACF;EAEA,MAAM/D,SAAS,GAAG,IAAAQ,oBAAa,EAACkH,QAAQ,CAAC;EACzC,IAAInE,sBAAsB,CAAClM,GAAG,CAAC2I,SAAS,GAAG,GAAG,GAAG0D,KAAK,CAAC,EAAE;EACzDH,sBAAsB,CAACoE,GAAG,CAAC3H,SAAS,GAAG,GAAG,GAAG0D,KAAK,CAAC;EACnDI,SAAMA,CAAC,CAACG,WAAW,CAChB,qDAAoDP,KAAM,WAAU,GAClE,uDAAsD1D,SAAU,GAC/DrF,IAAI,GAAI,kBAAiB,IAAA6F,oBAAa,EAAC7F,IAAI,CAAE,EAAC,GAAG,EAClD,4DAA2D,EAC9D,oBAAoB,EACpB,SACF,CAAC;AACH;AAUA,SAASiN,qBAAqBA,CAC5BjE,cAAc,EACdkD,cAAc,EACd9F,aAAa,EACbpG,IAAI,EACJiI,UAAU,EACV;EACA,IAAI1C,OAAO,GAAGa,aAAa,CAACb,OAAO;EAEnC,IAAImH,6BAA6B,CAACnH,OAAO,EAAEyD,cAAc,EAAEhJ,IAAI,CAAC,EAAE;IAChEuF,OAAO,GAAG;MAAC,GAAG,EAAEA;IAAO,CAAC;EAC1B;EAEA,IACE+C,GAAG,CAACjL,IAAI,CAACkI,OAAO,EAAE2G,cAAc,CAAC,IACjC,CAACA,cAAc,CAACjN,QAAQ,CAAC,GAAG,CAAC,IAC7B,CAACiN,cAAc,CAAClN,QAAQ,CAAC,GAAG,CAAC,EAC7B;IAEA,MAAMwB,MAAM,GAAG+E,OAAO,CAAC2G,cAAc,CAAC;IACtC,MAAMI,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdxI,MAAM,EACN,EAAE,EACF0L,cAAc,EACdlM,IAAI,EACJ,KAAK,EACL,KAAK,EACL,KAAK,EACLiI,UACF,CAAC;IACD,IAAIqE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKrM,SAAS,EAAE;MACzD,MAAMkL,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAEhJ,IAAI,CAAC;IAC7D;IAEA,OAAOsM,aAAa;EACtB;EAEA,IAAIY,SAAS,GAAG,EAAE;EAClB,IAAIC,gBAAgB,GAAG,EAAE;EACzB,MAAMZ,IAAI,GAAGxP,MAAM,CAACyP,mBAAmB,CAACjH,OAAO,CAAC;EAChD,IAAIjI,CAAC,GAAG,CAAC,CAAC;EAEV,OAAO,EAAEA,CAAC,GAAGiP,IAAI,CAACvO,MAAM,EAAE;IACxB,MAAMuC,GAAG,GAAGgM,IAAI,CAACjP,CAAC,CAAC;IACnB,MAAM8P,YAAY,GAAG7M,GAAG,CAACb,OAAO,CAAC,GAAG,CAAC;IAErC,IACE0N,YAAY,KAAK,CAAC,CAAC,IACnBlB,cAAc,CAACvL,UAAU,CAACJ,GAAG,CAACrC,KAAK,CAAC,CAAC,EAAEkP,YAAY,CAAC,CAAC,EACrD;MAOA,IAAIlB,cAAc,CAAClN,QAAQ,CAAC,GAAG,CAAC,EAAE;QAChC8N,mCAAmC,CACjCZ,cAAc,EACdlD,cAAc,EACdhJ,IACF,CAAC;MACH;MAEA,MAAMqN,cAAc,GAAG9M,GAAG,CAACrC,KAAK,CAACkP,YAAY,GAAG,CAAC,CAAC;MAElD,IACElB,cAAc,CAAClO,MAAM,IAAIuC,GAAG,CAACvC,MAAM,IACnCkO,cAAc,CAAClN,QAAQ,CAACqO,cAAc,CAAC,IACvCC,iBAAiB,CAACJ,SAAS,EAAE3M,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAACgN,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;QACAF,SAAS,GAAG3M,GAAG;QACf4M,gBAAgB,GAAGjB,cAAc,CAAChO,KAAK,CACrCkP,YAAY,EACZlB,cAAc,CAAClO,MAAM,GAAGqP,cAAc,CAACrP,MACzC,CAAC;MACH;IACF;EACF;EAEA,IAAIkP,SAAS,EAAE;IAEb,MAAM1M,MAAM,GAA2B+E,OAAO,CAAC2H,SAAS,CAAE;IAC1D,MAAMZ,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdxI,MAAM,EACN2M,gBAAgB,EAChBD,SAAS,EACTlN,IAAI,EACJ,IAAI,EACJ,KAAK,EACLkM,cAAc,CAAClN,QAAQ,CAAC,GAAG,CAAC,EAC5BiJ,UACF,CAAC;IAED,IAAIqE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKrM,SAAS,EAAE;MACzD,MAAMkL,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAEhJ,IAAI,CAAC;IAC7D;IAEA,OAAOsM,aAAa;EACtB;EAEA,MAAMnB,eAAe,CAACe,cAAc,EAAElD,cAAc,EAAEhJ,IAAI,CAAC;AAC7D;AAMA,SAASsN,iBAAiBA,CAACxQ,CAAC,EAAE0Q,CAAC,EAAE;EAC/B,MAAMC,aAAa,GAAG3Q,CAAC,CAAC4C,OAAO,CAAC,GAAG,CAAC;EACpC,MAAMgO,aAAa,GAAGF,CAAC,CAAC9N,OAAO,CAAC,GAAG,CAAC;EACpC,MAAMiO,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAG3Q,CAAC,CAACkB,MAAM,GAAGyP,aAAa,GAAG,CAAC;EACvE,MAAMG,WAAW,GAAGF,aAAa,KAAK,CAAC,CAAC,GAAGF,CAAC,CAACxP,MAAM,GAAG0P,aAAa,GAAG,CAAC;EACvE,IAAIC,WAAW,GAAGC,WAAW,EAAE,OAAO,CAAC,CAAC;EACxC,IAAIA,WAAW,GAAGD,WAAW,EAAE,OAAO,CAAC;EACvC,IAAIF,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC;EAClC,IAAIC,aAAa,KAAK,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;EACnC,IAAI5Q,CAAC,CAACkB,MAAM,GAAGwP,CAAC,CAACxP,MAAM,EAAE,OAAO,CAAC,CAAC;EAClC,IAAIwP,CAAC,CAACxP,MAAM,GAAGlB,CAAC,CAACkB,MAAM,EAAE,OAAO,CAAC;EACjC,OAAO,CAAC;AACV;AAQA,SAAS6P,qBAAqBA,CAACpP,IAAI,EAAEuB,IAAI,EAAEiI,UAAU,EAAE;EACrD,IAAIxJ,IAAI,KAAK,GAAG,IAAIA,IAAI,CAACkC,UAAU,CAAC,IAAI,CAAC,IAAIlC,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,EAAE;IAC/D,MAAMe,MAAM,GAAG,gDAAgD;IAC/D,MAAM,IAAIF,4BAA4B,CAACpB,IAAI,EAAEsB,MAAM,EAAE,IAAA8F,oBAAa,EAAC7F,IAAI,CAAC,CAAC;EAC3E;EAGA,IAAIgJ,cAAc;EAElB,MAAM5C,aAAa,GAAGN,qBAAqB,CAAC9F,IAAI,CAAC;EAEjD,IAAIoG,aAAa,CAAChB,MAAM,EAAE;IACxB4D,cAAc,GAAG,IAAAgC,oBAAa,EAAC5E,aAAa,CAACf,SAAS,CAAC;IACvD,MAAMG,OAAO,GAAGY,aAAa,CAACZ,OAAO;IACrC,IAAIA,OAAO,EAAE;MACX,IAAI8C,GAAG,CAACjL,IAAI,CAACmI,OAAO,EAAE/G,IAAI,CAAC,IAAI,CAACA,IAAI,CAACQ,QAAQ,CAAC,GAAG,CAAC,EAAE;QAClD,MAAMqN,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdxD,OAAO,CAAC/G,IAAI,CAAC,EACb,EAAE,EACFA,IAAI,EACJuB,IAAI,EACJ,KAAK,EACL,IAAI,EACJ,KAAK,EACLiI,UACF,CAAC;QACD,IAAIqE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKrM,SAAS,EAAE;UACzD,OAAOqM,aAAa;QACtB;MACF,CAAC,MAAM;QACL,IAAIY,SAAS,GAAG,EAAE;QAClB,IAAIC,gBAAgB,GAAG,EAAE;QACzB,MAAMZ,IAAI,GAAGxP,MAAM,CAACyP,mBAAmB,CAAChH,OAAO,CAAC;QAChD,IAAIlI,CAAC,GAAG,CAAC,CAAC;QAEV,OAAO,EAAEA,CAAC,GAAGiP,IAAI,CAACvO,MAAM,EAAE;UACxB,MAAMuC,GAAG,GAAGgM,IAAI,CAACjP,CAAC,CAAC;UACnB,MAAM8P,YAAY,GAAG7M,GAAG,CAACb,OAAO,CAAC,GAAG,CAAC;UAErC,IAAI0N,YAAY,KAAK,CAAC,CAAC,IAAI3O,IAAI,CAACkC,UAAU,CAACJ,GAAG,CAACrC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAAE;YAC5D,MAAMmP,cAAc,GAAG9M,GAAG,CAACrC,KAAK,CAACkP,YAAY,GAAG,CAAC,CAAC;YAClD,IACE3O,IAAI,CAACT,MAAM,IAAIuC,GAAG,CAACvC,MAAM,IACzBS,IAAI,CAACO,QAAQ,CAACqO,cAAc,CAAC,IAC7BC,iBAAiB,CAACJ,SAAS,EAAE3M,GAAG,CAAC,KAAK,CAAC,IACvCA,GAAG,CAACgN,WAAW,CAAC,GAAG,CAAC,KAAKH,YAAY,EACrC;cACAF,SAAS,GAAG3M,GAAG;cACf4M,gBAAgB,GAAG1O,IAAI,CAACP,KAAK,CAC3BkP,YAAY,EACZ3O,IAAI,CAACT,MAAM,GAAGqP,cAAc,CAACrP,MAC/B,CAAC;YACH;UACF;QACF;QAEA,IAAIkP,SAAS,EAAE;UACb,MAAM1M,MAAM,GAAGgF,OAAO,CAAC0H,SAAS,CAAC;UACjC,MAAMZ,aAAa,GAAGL,oBAAoB,CACxCjD,cAAc,EACdxI,MAAM,EACN2M,gBAAgB,EAChBD,SAAS,EACTlN,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,KAAK,EACLiI,UACF,CAAC;UAED,IAAIqE,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAKrM,SAAS,EAAE;YACzD,OAAOqM,aAAa;UACtB;QACF;MACF;IACF;EACF;EAEA,MAAMpB,gBAAgB,CAACzM,IAAI,EAAEuK,cAAc,EAAEhJ,IAAI,CAAC;AACpD;AAUA,SAAS8N,gBAAgBA,CAAC5M,SAAS,EAAElB,IAAI,EAAE;EACzC,IAAI+N,cAAc,GAAG7M,SAAS,CAACxB,OAAO,CAAC,GAAG,CAAC;EAC3C,IAAIsO,gBAAgB,GAAG,IAAI;EAC3B,IAAIC,QAAQ,GAAG,KAAK;EACpB,IAAI/M,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB+M,QAAQ,GAAG,IAAI;IACf,IAAIF,cAAc,KAAK,CAAC,CAAC,IAAI7M,SAAS,CAAClD,MAAM,KAAK,CAAC,EAAE;MACnDgQ,gBAAgB,GAAG,KAAK;IAC1B,CAAC,MAAM;MACLD,cAAc,GAAG7M,SAAS,CAACxB,OAAO,CAAC,GAAG,EAAEqO,cAAc,GAAG,CAAC,CAAC;IAC7D;EACF;EAEA,MAAMG,WAAW,GACfH,cAAc,KAAK,CAAC,CAAC,GAAG7M,SAAS,GAAGA,SAAS,CAAChD,KAAK,CAAC,CAAC,EAAE6P,cAAc,CAAC;EAIxE,IAAItF,uBAAuB,CAACjJ,IAAI,CAAC0O,WAAW,CAAC,KAAK,IAAI,EAAE;IACtDF,gBAAgB,GAAG,KAAK;EAC1B;EAEA,IAAI,CAACA,gBAAgB,EAAE;IACrB,MAAM,IAAInO,4BAA4B,CACpCqB,SAAS,EACT,6BAA6B,EAC7B,IAAA2E,oBAAa,EAAC7F,IAAI,CACpB,CAAC;EACH;EAEA,MAAMkM,cAAc,GAClB,GAAG,IAAI6B,cAAc,KAAK,CAAC,CAAC,GAAG,EAAE,GAAG7M,SAAS,CAAChD,KAAK,CAAC6P,cAAc,CAAC,CAAC;EAEtE,OAAO;IAACG,WAAW;IAAEhC,cAAc;IAAE+B;EAAQ,CAAC;AAChD;AAQA,SAASrC,cAAcA,CAAC1K,SAAS,EAAElB,IAAI,EAAEiI,UAAU,EAAE;EACnD,IAAIkG,wBAAc,CAAClP,QAAQ,CAACiC,SAAS,CAAC,EAAE;IACtC,OAAO,KAAI+E,UAAG,EAAC,OAAO,GAAG/E,SAAS,CAAC;EACrC;EAEA,MAAM;IAACgN,WAAW;IAAEhC,cAAc;IAAE+B;EAAQ,CAAC,GAAGH,gBAAgB,CAC9D5M,SAAS,EACTlB,IACF,CAAC;EAGD,MAAMoG,aAAa,GAAGN,qBAAqB,CAAC9F,IAAI,CAAC;EAIjD,IAAIoG,aAAa,CAAChB,MAAM,EAAE;IACxB,MAAM4D,cAAc,GAAG,IAAAgC,oBAAa,EAAC5E,aAAa,CAACf,SAAS,CAAC;IAC7D,IACEe,aAAa,CAAC3H,IAAI,KAAKyP,WAAW,IAClC9H,aAAa,CAACb,OAAO,KAAKtF,SAAS,IACnCmG,aAAa,CAACb,OAAO,KAAK,IAAI,EAC9B;MACA,OAAO0H,qBAAqB,CAC1BjE,cAAc,EACdkD,cAAc,EACd9F,aAAa,EACbpG,IAAI,EACJiI,UACF,CAAC;IACH;EACF;EAEA,IAAIe,cAAc,GAAG,KAAI/C,UAAG,EAC1B,iBAAiB,GAAGiI,WAAW,GAAG,eAAe,EACjDlO,IACF,CAAC;EACD,IAAIoO,eAAe,GAAG,IAAAvI,oBAAa,EAACmD,cAAc,CAAC;EAEnD,IAAIqF,QAAQ;EACZ,GAAG;IACD,MAAMC,IAAI,GAAGzE,WAAW,CAACuE,eAAe,CAAClQ,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IACvD,IAAI,CAACoQ,IAAI,CAAC3D,WAAW,CAAC,CAAC,EAAE;MACvB0D,QAAQ,GAAGD,eAAe;MAC1BpF,cAAc,GAAG,KAAI/C,UAAG,EACtB,CAACgI,QAAQ,GAAG,2BAA2B,GAAG,wBAAwB,IAChEC,WAAW,GACX,eAAe,EACjBlF,cACF,CAAC;MACDoF,eAAe,GAAG,IAAAvI,oBAAa,EAACmD,cAAc,CAAC;MAC/C;IACF;IAGA,MAAM5C,aAAa,GAAGzB,iBAAiB,CAACD,IAAI,CAAC0J,eAAe,EAAE;MAC5DpO,IAAI;MACJkB;IACF,CAAC,CAAC;IACF,IAAIkF,aAAa,CAACb,OAAO,KAAKtF,SAAS,IAAImG,aAAa,CAACb,OAAO,KAAK,IAAI,EAAE;MACzE,OAAO0H,qBAAqB,CAC1BjE,cAAc,EACdkD,cAAc,EACd9F,aAAa,EACbpG,IAAI,EACJiI,UACF,CAAC;IACH;IAEA,IAAIiE,cAAc,KAAK,GAAG,EAAE;MAC1B,OAAO7B,iBAAiB,CAACrB,cAAc,EAAE5C,aAAa,EAAEpG,IAAI,CAAC;IAC/D;IAEA,OAAO,KAAIiG,UAAG,EAACiG,cAAc,EAAElD,cAAc,CAAC;EAEhD,CAAC,QAAQoF,eAAe,CAACpQ,MAAM,KAAKqQ,QAAQ,CAACrQ,MAAM;EAEnD,MAAM,IAAI8C,oBAAoB,CAACoN,WAAW,EAAE,IAAArI,oBAAa,EAAC7F,IAAI,CAAC,EAAE,KAAK,CAAC;AACzE;AAMA,SAASuO,mBAAmBA,CAACrN,SAAS,EAAE;EACtC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACxB,IAAIA,SAAS,CAAClD,MAAM,KAAK,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;IAC/D,IACEA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,KACnBA,SAAS,CAAClD,MAAM,KAAK,CAAC,IAAIkD,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,EAChD;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAMA,SAASsN,uCAAuCA,CAACtN,SAAS,EAAE;EAC1D,IAAIA,SAAS,KAAK,EAAE,EAAE,OAAO,KAAK;EAClC,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE,OAAO,IAAI;EACrC,OAAOqN,mBAAmB,CAACrN,SAAS,CAAC;AACvC;AAiBA,SAASuN,aAAaA,CAACvN,SAAS,EAAElB,IAAI,EAAEiI,UAAU,EAAEwC,gBAAgB,EAAE;EACpE,MAAM/C,QAAQ,GAAG1H,IAAI,CAAC0H,QAAQ;EAC9B,MAAMgH,QAAQ,GAAGhH,QAAQ,KAAK,OAAO,IAAIA,QAAQ,KAAK,QAAQ;EAI9D,IAAI3B,QAAQ;EAEZ,IAAIyI,uCAAuC,CAACtN,SAAS,CAAC,EAAE;IACtD6E,QAAQ,GAAG,KAAIE,UAAG,EAAC/E,SAAS,EAAElB,IAAI,CAAC;EACrC,CAAC,MAAM,IAAI,CAAC0O,QAAQ,IAAIxN,SAAS,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IAC5C6E,QAAQ,GAAG8H,qBAAqB,CAAC3M,SAAS,EAAElB,IAAI,EAAEiI,UAAU,CAAC;EAC/D,CAAC,MAAM;IACL,IAAI;MACFlC,QAAQ,GAAG,KAAIE,UAAG,EAAC/E,SAAS,CAAC;IAC/B,CAAC,CAAC,OAAAyN,QAAA,EAAM;MACN,IAAI,CAACD,QAAQ,EAAE;QACb3I,QAAQ,GAAG6F,cAAc,CAAC1K,SAAS,EAAElB,IAAI,EAAEiI,UAAU,CAAC;MACxD;IACF;EACF;EAEArJ,QAAKA,CAAC,CAACmH,QAAQ,KAAK9F,SAAS,EAAE,wBAAwB,CAAC;EAExD,IAAI8F,QAAQ,CAAC2B,QAAQ,KAAK,OAAO,EAAE;IACjC,OAAO3B,QAAQ;EACjB;EAEA,OAAOyE,kBAAkB,CAACzE,QAAQ,EAAE/F,IAAI,EAAEyK,gBAAgB,CAAC;AAC7D;AAOA,SAASmE,uBAAuBA,CAAC1N,SAAS,EAAEuE,MAAM,EAAEoJ,eAAe,EAAE;EACnE,IAAIA,eAAe,EAAE;IAEnB,MAAMC,cAAc,GAAGD,eAAe,CAACnH,QAAQ;IAE/C,IAAIoH,cAAc,KAAK,OAAO,IAAIA,cAAc,KAAK,QAAQ,EAAE;MAC7D,IAAIN,uCAAuC,CAACtN,SAAS,CAAC,EAAE;QAEtD,MAAM6N,cAAc,GAAGtJ,MAAM,oBAANA,MAAM,CAAEiC,QAAQ;QAIvC,IACEqH,cAAc,IACdA,cAAc,KAAK,QAAQ,IAC3BA,cAAc,KAAK,OAAO,EAC1B;UACA,MAAM,IAAI/N,6BAA6B,CACrCE,SAAS,EACT2N,eAAe,EACf,qDACF,CAAC;QACH;QAEA,OAAO;UAACtI,GAAG,EAAE,CAAAd,MAAM,oBAANA,MAAM,CAAEgE,IAAI,KAAI;QAAE,CAAC;MAClC;MAEA,IAAI0E,wBAAc,CAAClP,QAAQ,CAACiC,SAAS,CAAC,EAAE;QACtC,MAAM,IAAIF,6BAA6B,CACrCE,SAAS,EACT2N,eAAe,EACf,qDACF,CAAC;MACH;MAEA,MAAM,IAAI7N,6BAA6B,CACrCE,SAAS,EACT2N,eAAe,EACf,sDACF,CAAC;IACH;EACF;AACF;AAkBA,SAASpD,KAAKA,CAAC9H,IAAI,EAAE;EACnB,OAAOqL,OAAO,CACZrL,IAAI,IACF,OAAOA,IAAI,KAAK,QAAQ,IACxB,MAAM,IAAIA,IAAI,IACd,OAAOA,IAAI,CAAC8F,IAAI,KAAK,QAAQ,IAC7B,UAAU,IAAI9F,IAAI,IAClB,OAAOA,IAAI,CAAC+D,QAAQ,KAAK,QAAQ,IACjC/D,IAAI,CAAC8F,IAAI,IACT9F,IAAI,CAAC+D,QACT,CAAC;AACH;AAQA,SAASuH,uBAAuBA,CAACzF,SAAS,EAAE;EAC1C,IAAIA,SAAS,KAAKvJ,SAAS,EAAE;IAC3B;EACF;EAEA,IAAI,OAAOuJ,SAAS,KAAK,QAAQ,IAAI,CAACiC,KAAK,CAACjC,SAAS,CAAC,EAAE;IACtD,MAAM,IAAI5L,KAAK,CAACW,oBAAoB,CAClC,WAAW,EACX,CAAC,QAAQ,EAAE,KAAK,CAAC,EACjBiL,SACF,CAAC;EACH;AACF;AAOA,SAAS0F,cAAcA,CAAChO,SAAS,EAAEuG,OAAO,GAAG,CAAC,CAAC,EAAE;EAC/C,MAAM;IAAC+B;EAAS,CAAC,GAAG/B,OAAO;EAC3B7I,QAAKA,CAAC,CAAC4K,SAAS,KAAKvJ,SAAS,EAAE,oCAAoC,CAAC;EACrEgP,uBAAuB,CAACzF,SAAS,CAAC;EAGlC,IAAIqF,eAAe;EACnB,IAAIrF,SAAS,EAAE;IACb,IAAI;MACFqF,eAAe,GAAG,KAAI5I,UAAG,EAACuD,SAAS,CAAC;IACtC,CAAC,CAAC,OAAA2F,QAAA,EAAM,CAER;EACF;EAGA,IAAI1J,MAAM;EACV,IAAI;IACFA,MAAM,GAAG+I,uCAAuC,CAACtN,SAAS,CAAC,GACvD,KAAI+E,UAAG,EAAC/E,SAAS,EAAE2N,eAAe,CAAC,GACnC,KAAI5I,UAAG,EAAC/E,SAAS,CAAC;IAGtB,MAAMwG,QAAQ,GAAGjC,MAAM,CAACiC,QAAQ;IAEhC,IAAIA,QAAQ,KAAK,OAAO,EAAE;MACxB,OAAO;QAACnB,GAAG,EAAEd,MAAM,CAACgE,IAAI;QAAExF,MAAM,EAAE;MAAI,CAAC;IACzC;EACF,CAAC,CAAC,OAAAmL,QAAA,EAAM,CAER;EAKA,MAAMC,WAAW,GAAGT,uBAAuB,CACzC1N,SAAS,EACTuE,MAAM,EACNoJ,eACF,CAAC;EAED,IAAIQ,WAAW,EAAE,OAAOA,WAAW;EAGnC,IAAI5J,MAAM,IAAIA,MAAM,CAACiC,QAAQ,KAAK,OAAO,EAAE,OAAO;IAACnB,GAAG,EAAErF;EAAS,CAAC;EAElE,MAAM+G,UAAU,GAAGD,gBAAgB,CAACP,OAAO,CAACQ,UAAU,CAAC;EAEvD,MAAM1B,GAAG,GAAGkI,aAAa,CAACvN,SAAS,EAAE,KAAI+E,UAAG,EAACuD,SAAS,CAAC,EAAEvB,UAAU,EAAE,KAAK,CAAC;EAE3E,OAAO;IAGL1B,GAAG,EAAEA,GAAG,CAACkD,IAAI;IACbxF,MAAM,EAAEuD,6BAA6B,CAACjB,GAAG,EAAE;MAACiD;IAAS,CAAC;EACxD,CAAC;AACH;AAsBA,SAASI,OAAOA,CAAC1I,SAAS,EAAEoO,MAAM,EAAE;EAClC,IAAI,CAACA,MAAM,EAAE;IACX,MAAM,IAAIlP,KAAK,CACb,kEACF,CAAC;EACH;EAEA,IAAI;IACF,OAAO8O,cAAc,CAAChO,SAAS,EAAE;MAACsI,SAAS,EAAE8F;IAAM,CAAC,CAAC,CAAC/I,GAAG;EAC3D,CAAC,CAAC,OAAOlE,KAAK,EAAE;IAEd,MAAM6C,SAAS,GAAkC7C,KAAM;IAEvD,IACE,CAAC6C,SAAS,CAACrC,IAAI,KAAK,4BAA4B,IAC9CqC,SAAS,CAACrC,IAAI,KAAK,sBAAsB,KAC3C,OAAOqC,SAAS,CAACqB,GAAG,KAAK,QAAQ,EACjC;MACA,OAAOrB,SAAS,CAACqB,GAAG;IACtB;IAEA,MAAMlE,KAAK;EACb;AACF;AAAC"}