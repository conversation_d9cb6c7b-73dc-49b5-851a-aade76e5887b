export declare const BITS = 128;
export declare const GROUPS = 8;
/**
 * Represents IPv6 address scopes
 * @memberof Address6
 * @static
 */
export declare const SCOPES: {
    [key: number]: string | undefined;
};
/**
 * Represents IPv6 address types
 * @memberof Address6
 * @static
 */
export declare const TYPES: {
    [key: string]: string | undefined;
};
/**
 * A regular expression that matches bad characters in an IPv6 address
 * @memberof Address6
 * @static
 */
export declare const RE_BAD_CHARACTERS: RegExp;
/**
 * A regular expression that matches an incorrect IPv6 address
 * @memberof Address6
 * @static
 */
export declare const RE_BAD_ADDRESS: RegExp;
/**
 * A regular expression that matches an IPv6 subnet
 * @memberof Address6
 * @static
 */
export declare const RE_SUBNET_STRING: RegExp;
/**
 * A regular expression that matches an IPv6 zone
 * @memberof Address6
 * @static
 */
export declare const RE_ZONE_STRING: RegExp;
export declare const RE_URL: RegExp;
export declare const RE_URL_WITH_PORT: RegExp;
//# sourceMappingURL=constants.d.ts.map