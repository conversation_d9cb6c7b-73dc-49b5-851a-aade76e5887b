{"name": "is-what", "sideEffects": false, "version": "3.14.1", "description": "JS type check (TypeScript supported) functions like `isPlainObject() isArray()` etc. A simple & small integration.", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "types/index.d.ts", "scripts": {"test": "ava", "jest": "jest", "jest-w": "jest --watchAll", "lint": "tsc --noEmit src/index.ts && eslint . --ext .js,.jsx,.ts,.tsx", "rollup": "rollup -c ./build.js", "build": "rimraf types && rimraf dist && npm run lint && npm run rollup && npm run test && npm run jest", "release": "npm run build && np"}, "repository": {"type": "git", "url": "git+https://github.com/mesqueeb/is-what.git"}, "keywords": ["javascript", "typescript", "typechecker", "check-type", "javascript-type", "primitive-types", "plain-object", "plain-objects", "class-instance", "class-identifier", "type-checking", "type-checker", "type-check", "define-type", "get-type", "what-type", "is-object", "is-plain-obj", "is-plain-object"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/mesqueeb/is-what/issues"}, "homepage": "https://github.com/mesqueeb/is-what#readme", "devDependencies": {"@babel/core": "^7.12.17", "@types/babel-core": "^6.25.6", "@types/jest": "^26.0.20", "@typescript-eslint/eslint-plugin": "^4.15.1", "@typescript-eslint/parser": "^4.15.1", "ava": "^3.15.0", "babel-core": "^7.0.0-bridge.0", "babel-jest": "^26.6.3", "babel-preset-env": "^1.7.0", "eslint": "^7.20.0", "eslint-config-prettier": "^7.2.0", "eslint-plugin-tree-shaking": "^1.8.0", "jest": "^26.6.3", "np": "^7.4.0", "prettier": "^2.2.1", "regenerator-runtime": "^0.13.7", "rimraf": "^3.0.2", "rollup": "^2.39.0", "rollup-plugin-typescript2": "^0.30.0", "ts-node": "^9.1.1", "tsconfig-paths": "^3.9.0", "typescript": "^4.1.5"}, "ava": {"extensions": ["ts"], "require": ["tsconfig-paths/register", "ts-node/register"]}, "np": {"yarn": false, "branch": "production"}}