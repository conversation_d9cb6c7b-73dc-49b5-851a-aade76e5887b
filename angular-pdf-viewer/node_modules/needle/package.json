{"name": "needle", "version": "3.3.1", "description": "The leanest and most handsome HTTP client in the Nodelands.", "keywords": ["http", "https", "simple", "request", "client", "multipart", "upload", "proxy", "deflate", "timeout", "charset", "iconv", "cookie", "redirect"], "tags": ["http", "https", "simple", "request", "client", "multipart", "upload", "proxy", "deflate", "timeout", "charset", "iconv", "cookie", "redirect"], "author": "<PERSON><PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "https://github.com/tomas/needle.git"}, "dependencies": {"iconv-lite": "^0.6.3", "sax": "^1.2.4"}, "devDependencies": {"JSONStream": "^1.3.5", "jschardet": "^1.6.0", "mocha": "^5.2.0", "pump": "^3.0.0", "q": "^1.5.1", "should": "^13.2.3", "sinon": "^2.3.0", "xml2js": "^0.4.19"}, "scripts": {"test": "mocha test"}, "directories": {"lib": "./lib"}, "main": "./lib/needle", "bin": {"needle": "./bin/needle"}, "license": "MIT", "engines": {"node": ">= 4.4.x"}}