/// <reference types="node" />
import { ASN1Obj } from '../asn1';
import { X509AuthorityKeyIDExtension, X509BasicConstraintsExtension, X509Extension, X509KeyUsageExtension, X509SCTExtension, X509SubjectAlternativeNameExtension, X509SubjectKeyIDExtension } from './ext';
export declare const EXTENSION_OID_SCT = "1.3.6.1.4.1.11129.2.4.2";
export declare class X509Certificate {
    root: ASN1Obj;
    constructor(asn1: ASN1Obj);
    static parse(cert: Buffer | string): X509Certificate;
    get tbsCertificate(): ASN1Obj;
    get version(): string;
    get serialNumber(): Buffer;
    get notBefore(): Date;
    get notAfter(): Date;
    get issuer(): Buffer;
    get subject(): Buffer;
    get publicKey(): Buffer;
    get signatureAlgorithm(): string;
    get signatureValue(): Buffer;
    get subjectAltName(): string | undefined;
    get extensions(): ASN1Obj[];
    get extKeyUsage(): X509KeyUsageExtension | undefined;
    get extBasicConstraints(): X509BasicConstraintsExtension | undefined;
    get extSubjectAltName(): X509SubjectAlternativeNameExtension | undefined;
    get extAuthorityKeyID(): X509AuthorityKeyIDExtension | undefined;
    get extSubjectKeyID(): X509SubjectKeyIDExtension | undefined;
    get extSCT(): X509SCTExtension | undefined;
    get isCA(): boolean;
    extension(oid: string): X509Extension | undefined;
    verify(issuerCertificate?: X509Certificate): boolean;
    validForDate(date: Date): boolean;
    equals(other: X509Certificate): boolean;
    clone(): X509Certificate;
    private findExtension;
    private get tbsCertificateObj();
    private get signatureAlgorithmObj();
    private get signatureValueObj();
    private get versionObj();
    private get serialNumberObj();
    private get issuerObj();
    private get validityObj();
    private get subjectObj();
    private get subjectPublicKeyInfoObj();
    private get extensionsObj();
}
