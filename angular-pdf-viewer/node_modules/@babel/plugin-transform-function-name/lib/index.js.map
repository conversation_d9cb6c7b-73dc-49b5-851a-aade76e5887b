{"version": 3, "names": ["_helperCompilationTargets", "require", "_helper<PERSON>lugin<PERSON><PERSON>s", "_default", "exports", "default", "declare", "api", "assertVersion", "supportUnicodeId", "isRequired", "targets", "name", "visitor", "FunctionExpression", "exit", "path", "key", "parentPath", "isObjectProperty", "_path$ensureFunctionN", "ensureFunctionName", "NodePath", "prototype", "ObjectProperty", "value", "get", "isFunction", "_value$ensureFunction"], "sources": ["../src/index.ts"], "sourcesContent": ["import { isRequired } from \"@babel/helper-compilation-targets\";\nimport { declare } from \"@babel/helper-plugin-utils\";\n\nexport default declare(api => {\n  api.assertVersion(REQUIRED_VERSION(7));\n  const supportUnicodeId = !isRequired(\n    \"transform-unicode-escapes\",\n    api.targets(),\n  );\n\n  return {\n    name: \"transform-function-name\",\n\n    visitor: {\n      FunctionExpression: {\n        exit(path) {\n          if (path.key !== \"value\" && !path.parentPath.isObjectProperty()) {\n            if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n              // polyfill when being run by an older Babel version\n              path.ensureFunctionName ??=\n                // eslint-disable-next-line no-restricted-globals\n                require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n            }\n            path.ensureFunctionName(supportUnicodeId);\n          }\n        },\n      },\n\n      ObjectProperty(path) {\n        const value = path.get(\"value\");\n        if (value.isFunction()) {\n          if (!process.env.BABEL_8_BREAKING && !USE_ESM && !IS_STANDALONE) {\n            // polyfill when being run by an older Babel version\n            value.ensureFunctionName ??=\n              // eslint-disable-next-line no-restricted-globals\n              require(\"@babel/traverse\").NodePath.prototype.ensureFunctionName;\n          }\n          // @ts-expect-error Fixme: should check ArrowFunctionExpression\n          value.ensureFunctionName(supportUnicodeId);\n        }\n      },\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,yBAAA,GAAAC,OAAA;AACA,IAAAC,kBAAA,GAAAD,OAAA;AAAqD,IAAAE,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAEtC,IAAAC,0BAAO,EAACC,GAAG,IAAI;EAC5BA,GAAG,CAACC,aAAa,CAAkB,CAAE,CAAC;EACtC,MAAMC,gBAAgB,GAAG,CAAC,IAAAC,oCAAU,EAClC,2BAA2B,EAC3BH,GAAG,CAACI,OAAO,CAAC,CACd,CAAC;EAED,OAAO;IACLC,IAAI,EAAE,yBAAyB;IAE/BC,OAAO,EAAE;MACPC,kBAAkB,EAAE;QAClBC,IAAIA,CAACC,IAAI,EAAE;UACT,IAAIA,IAAI,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,IAAI,CAACE,UAAU,CAACC,gBAAgB,CAAC,CAAC,EAAE;YACE;cAAA,IAAAC,qBAAA;cAE/D,CAAAA,qBAAA,GAAAJ,IAAI,CAACK,kBAAkB,YAAAD,qBAAA,GAAvBJ,IAAI,CAACK,kBAAkB,GAErBpB,OAAO,CAAC,iBAAiB,CAAC,CAACqB,QAAQ,CAACC,SAAS,CAACF,kBAAkB;YACpE;YACAL,IAAI,CAACK,kBAAkB,CAACZ,gBAAgB,CAAC;UAC3C;QACF;MACF,CAAC;MAEDe,cAAcA,CAACR,IAAI,EAAE;QACnB,MAAMS,KAAK,GAAGT,IAAI,CAACU,GAAG,CAAC,OAAO,CAAC;QAC/B,IAAID,KAAK,CAACE,UAAU,CAAC,CAAC,EAAE;UAC2C;YAAA,IAAAC,qBAAA;YAE/D,CAAAA,qBAAA,GAAAH,KAAK,CAACJ,kBAAkB,YAAAO,qBAAA,GAAxBH,KAAK,CAACJ,kBAAkB,GAEtBpB,OAAO,CAAC,iBAAiB,CAAC,CAACqB,QAAQ,CAACC,SAAS,CAACF,kBAAkB;UACpE;UAEAI,KAAK,CAACJ,kBAAkB,CAACZ,gBAAgB,CAAC;QAC5C;MACF;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}