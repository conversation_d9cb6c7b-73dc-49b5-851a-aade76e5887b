{"version": 3, "names": ["currentTypes", "wrapWithTypes", "types", "fn", "args", "oldTypes", "apply", "getTypes", "runtimeProperty", "name", "t", "memberExpression", "identifier", "isReference", "path", "isReferenced", "parentPath", "isAssignmentExpression", "left", "node", "replaceWithOrRemove", "replacement", "replaceWith", "remove"], "sources": ["../../src/regenerator/util.ts"], "sourcesContent": ["let currentTypes: any = null;\n\nexport function wrapWithTypes(types: any, fn: any) {\n  return function (this: any, ...args: any[]) {\n    const oldTypes = currentTypes;\n    currentTypes = types;\n    try {\n      return fn.apply(this, args);\n    } finally {\n      currentTypes = oldTypes;\n    }\n  };\n}\n\nexport function getTypes() {\n  return currentTypes;\n}\n\nexport function runtimeProperty(name: any) {\n  const t = getTypes();\n  return t.memberExpression(\n    t.identifier(\"regeneratorRuntime\"),\n    t.identifier(name),\n    false,\n  );\n}\n\nexport function isReference(path: any) {\n  return (\n    path.isReferenced() ||\n    path.parentPath.isAssignmentExpression({ left: path.node })\n  );\n}\n\nexport function replaceWithOrRemove(path: any, replacement: any) {\n  if (replacement) {\n    path.replaceWith(replacement);\n  } else {\n    path.remove();\n  }\n}\n"], "mappings": ";;;;;;;;;;AAAA,IAAIA,YAAiB,GAAG,IAAI;AAErB,SAASC,aAAaA,CAACC,KAAU,EAAEC,EAAO,EAAE;EACjD,OAAO,UAAqB,GAAGC,IAAW,EAAE;IAC1C,MAAMC,QAAQ,GAAGL,YAAY;IAC7BA,YAAY,GAAGE,KAAK;IACpB,IAAI;MACF,OAAOC,EAAE,CAACG,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IAC7B,CAAC,SAAS;MACRJ,YAAY,GAAGK,QAAQ;IACzB;EACF,CAAC;AACH;AAEO,SAASE,QAAQA,CAAA,EAAG;EACzB,OAAOP,YAAY;AACrB;AAEO,SAASQ,eAAeA,CAACC,IAAS,EAAE;EACzC,MAAMC,CAAC,GAAGH,QAAQ,CAAC,CAAC;EACpB,OAAOG,CAAC,CAACC,gBAAgB,CACvBD,CAAC,CAACE,UAAU,CAAC,oBAAoB,CAAC,EAClCF,CAAC,CAACE,UAAU,CAACH,IAAI,CAAC,EAClB,KACF,CAAC;AACH;AAEO,SAASI,WAAWA,CAACC,IAAS,EAAE;EACrC,OACEA,IAAI,CAACC,YAAY,CAAC,CAAC,IACnBD,IAAI,CAACE,UAAU,CAACC,sBAAsB,CAAC;IAAEC,IAAI,EAAEJ,IAAI,CAACK;EAAK,CAAC,CAAC;AAE/D;AAEO,SAASC,mBAAmBA,CAACN,IAAS,EAAEO,WAAgB,EAAE;EAC/D,IAAIA,WAAW,EAAE;IACfP,IAAI,CAACQ,WAAW,CAACD,WAAW,CAAC;EAC/B,CAAC,MAAM;IACLP,IAAI,CAACS,MAAM,CAAC,CAAC;EACf;AACF", "ignoreList": []}