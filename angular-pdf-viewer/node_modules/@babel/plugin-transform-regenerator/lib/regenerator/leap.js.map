{"version": 3, "names": ["_assert", "require", "Entry", "exports", "FunctionEntry", "constructor", "returnLoc", "LoopEntry", "breakLoc", "continueLoc", "label", "SwitchEntry", "TryEntry", "firstLoc", "catchEntry", "finallyEntry", "assert", "ok", "CatchEntry", "paramId", "FinallyEntry", "afterLoc", "LabeledEntry", "LeapManager", "emitter", "entryStack", "finalLoc", "with<PERSON><PERSON>ry", "entry", "callback", "push", "call", "popped", "pop", "strictEqual", "_findLeapLocation", "property", "i", "length", "loc", "name", "getBreakLoc", "getContinueLoc"], "sources": ["../../src/regenerator/leap.ts"], "sourcesContent": ["import assert from \"node:assert\";\nimport type { Emitter } from \"./emit.ts\";\nimport type { types as t } from \"@babel/core\";\n\nexport class Entry {}\n\nexport class FunctionEntry extends Entry {\n  returnLoc: t.NumericLiteral;\n\n  constructor(returnLoc: t.NumericLiteral) {\n    super();\n    this.returnLoc = returnLoc;\n  }\n}\n\nexport class LoopEntry extends Entry {\n  breakLoc: t.NumericLiteral;\n  continueLoc: t.NumericLiteral;\n  label: t.Identifier;\n\n  constructor(\n    breakLoc: t.NumericLiteral,\n    continueLoc: t.NumericLiteral,\n    label: t.Identifier = null,\n  ) {\n    super();\n    this.breakLoc = breakLoc;\n    this.continueLoc = continueLoc;\n    this.label = label;\n  }\n}\n\nexport class SwitchEntry extends Entry {\n  breakLoc: t.NumericLiteral;\n\n  constructor(breakLoc: t.NumericLiteral) {\n    super();\n    this.breakLoc = breakLoc;\n  }\n}\n\nexport class TryEntry extends Entry {\n  firstLoc: t.NumericLiteral;\n  catchEntry: CatchEntry;\n  finallyEntry: FinallyEntry;\n\n  constructor(\n    firstLoc: t.NumericLiteral,\n    catchEntry: CatchEntry | null = null,\n    finallyEntry: FinallyEntry | null = null,\n  ) {\n    super();\n    assert.ok(catchEntry || finallyEntry);\n    this.firstLoc = firstLoc;\n    this.catchEntry = catchEntry;\n    this.finallyEntry = finallyEntry;\n  }\n}\n\nexport class CatchEntry extends Entry {\n  firstLoc: t.NumericLiteral;\n  paramId: t.Identifier;\n\n  constructor(firstLoc: t.NumericLiteral, paramId: t.Identifier) {\n    super();\n    this.firstLoc = firstLoc;\n    this.paramId = paramId;\n  }\n}\n\nexport class FinallyEntry extends Entry {\n  firstLoc: t.NumericLiteral;\n  afterLoc: t.NumericLiteral;\n\n  constructor(firstLoc: t.NumericLiteral, afterLoc: t.NumericLiteral) {\n    super();\n    this.firstLoc = firstLoc;\n    this.afterLoc = afterLoc;\n  }\n}\n\nexport class LabeledEntry extends Entry {\n  breakLoc: t.NumericLiteral;\n  label: t.Identifier;\n\n  constructor(breakLoc: t.NumericLiteral, label: t.Identifier) {\n    super();\n    this.breakLoc = breakLoc;\n    this.label = label;\n  }\n}\n\nexport class LeapManager {\n  emitter: Emitter;\n  entryStack: Entry[];\n\n  constructor(emitter: Emitter) {\n    this.emitter = emitter;\n    this.entryStack = [new FunctionEntry(emitter.finalLoc)];\n  }\n\n  withEntry(entry: Entry, callback: any) {\n    this.entryStack.push(entry);\n    try {\n      callback.call(this.emitter);\n    } finally {\n      const popped = this.entryStack.pop();\n      assert.strictEqual(popped, entry);\n    }\n  }\n\n  _findLeapLocation(property: \"breakLoc\" | \"continueLoc\", label: t.Identifier) {\n    for (let i = this.entryStack.length - 1; i >= 0; --i) {\n      const entry = this.entryStack[i];\n      // @ts-expect-error Element implicitly has an 'any' type\n      const loc = entry[property] as t.NumericLiteral;\n      if (loc) {\n        if (label) {\n          // @ts-expect-error entry.label may not exist\n          if (entry.label && entry.label.name === label.name) {\n            return loc;\n          }\n        } else if (entry instanceof LabeledEntry) {\n          // Ignore LabeledEntry entries unless we are actually breaking to\n          // a label.\n        } else {\n          return loc;\n        }\n      }\n    }\n    return null;\n  }\n\n  getBreakLoc(label: t.Identifier) {\n    return this._findLeapLocation(\"breakLoc\", label);\n  }\n\n  getContinueLoc(label: t.Identifier) {\n    return this._findLeapLocation(\"continueLoc\", label);\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,OAAA,GAAAC,OAAA;AAIO,MAAMC,KAAK,CAAC;AAAEC,OAAA,CAAAD,KAAA,GAAAA,KAAA;AAEd,MAAME,aAAa,SAASF,KAAK,CAAC;EAGvCG,WAAWA,CAACC,SAA2B,EAAE;IACvC,KAAK,CAAC,CAAC;IAAC,KAHVA,SAAS;IAIP,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC5B;AACF;AAACH,OAAA,CAAAC,aAAA,GAAAA,aAAA;AAEM,MAAMG,SAAS,SAASL,KAAK,CAAC;EAKnCG,WAAWA,CACTG,QAA0B,EAC1BC,WAA6B,EAC7BC,KAAmB,GAAG,IAAI,EAC1B;IACA,KAAK,CAAC,CAAC;IAAC,KATVF,QAAQ;IAAA,KACRC,WAAW;IAAA,KACXC,KAAK;IAQH,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;AACF;AAACP,OAAA,CAAAI,SAAA,GAAAA,SAAA;AAEM,MAAMI,WAAW,SAAST,KAAK,CAAC;EAGrCG,WAAWA,CAACG,QAA0B,EAAE;IACtC,KAAK,CAAC,CAAC;IAAC,KAHVA,QAAQ;IAIN,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAACL,OAAA,CAAAQ,WAAA,GAAAA,WAAA;AAEM,MAAMC,QAAQ,SAASV,KAAK,CAAC;EAKlCG,WAAWA,CACTQ,QAA0B,EAC1BC,UAA6B,GAAG,IAAI,EACpCC,YAAiC,GAAG,IAAI,EACxC;IACA,KAAK,CAAC,CAAC;IAAC,KATVF,QAAQ;IAAA,KACRC,UAAU;IAAA,KACVC,YAAY;IAQVC,OAAM,CAACC,EAAE,CAACH,UAAU,IAAIC,YAAY,CAAC;IACrC,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,YAAY,GAAGA,YAAY;EAClC;AACF;AAACZ,OAAA,CAAAS,QAAA,GAAAA,QAAA;AAEM,MAAMM,UAAU,SAAShB,KAAK,CAAC;EAIpCG,WAAWA,CAACQ,QAA0B,EAAEM,OAAqB,EAAE;IAC7D,KAAK,CAAC,CAAC;IAAC,KAJVN,QAAQ;IAAA,KACRM,OAAO;IAIL,IAAI,CAACN,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACM,OAAO,GAAGA,OAAO;EACxB;AACF;AAAChB,OAAA,CAAAe,UAAA,GAAAA,UAAA;AAEM,MAAME,YAAY,SAASlB,KAAK,CAAC;EAItCG,WAAWA,CAACQ,QAA0B,EAAEQ,QAA0B,EAAE;IAClE,KAAK,CAAC,CAAC;IAAC,KAJVR,QAAQ;IAAA,KACRQ,QAAQ;IAIN,IAAI,CAACR,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACQ,QAAQ,GAAGA,QAAQ;EAC1B;AACF;AAAClB,OAAA,CAAAiB,YAAA,GAAAA,YAAA;AAEM,MAAME,YAAY,SAASpB,KAAK,CAAC;EAItCG,WAAWA,CAACG,QAA0B,EAAEE,KAAmB,EAAE;IAC3D,KAAK,CAAC,CAAC;IAAC,KAJVF,QAAQ;IAAA,KACRE,KAAK;IAIH,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,KAAK,GAAGA,KAAK;EACpB;AACF;AAACP,OAAA,CAAAmB,YAAA,GAAAA,YAAA;AAEM,MAAMC,WAAW,CAAC;EAIvBlB,WAAWA,CAACmB,OAAgB,EAAE;IAAA,KAH9BA,OAAO;IAAA,KACPC,UAAU;IAGR,IAAI,CAACD,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,UAAU,GAAG,CAAC,IAAIrB,aAAa,CAACoB,OAAO,CAACE,QAAQ,CAAC,CAAC;EACzD;EAEAC,SAASA,CAACC,KAAY,EAAEC,QAAa,EAAE;IACrC,IAAI,CAACJ,UAAU,CAACK,IAAI,CAACF,KAAK,CAAC;IAC3B,IAAI;MACFC,QAAQ,CAACE,IAAI,CAAC,IAAI,CAACP,OAAO,CAAC;IAC7B,CAAC,SAAS;MACR,MAAMQ,MAAM,GAAG,IAAI,CAACP,UAAU,CAACQ,GAAG,CAAC,CAAC;MACpCjB,OAAM,CAACkB,WAAW,CAACF,MAAM,EAAEJ,KAAK,CAAC;IACnC;EACF;EAEAO,iBAAiBA,CAACC,QAAoC,EAAE1B,KAAmB,EAAE;IAC3E,KAAK,IAAI2B,CAAC,GAAG,IAAI,CAACZ,UAAU,CAACa,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;MACpD,MAAMT,KAAK,GAAG,IAAI,CAACH,UAAU,CAACY,CAAC,CAAC;MAEhC,MAAME,GAAG,GAAGX,KAAK,CAACQ,QAAQ,CAAqB;MAC/C,IAAIG,GAAG,EAAE;QACP,IAAI7B,KAAK,EAAE;UAET,IAAIkB,KAAK,CAAClB,KAAK,IAAIkB,KAAK,CAAClB,KAAK,CAAC8B,IAAI,KAAK9B,KAAK,CAAC8B,IAAI,EAAE;YAClD,OAAOD,GAAG;UACZ;QACF,CAAC,MAAM,IAAIX,KAAK,YAAYN,YAAY,EAAE,CAG1C,CAAC,MAAM;UACL,OAAOiB,GAAG;QACZ;MACF;IACF;IACA,OAAO,IAAI;EACb;EAEAE,WAAWA,CAAC/B,KAAmB,EAAE;IAC/B,OAAO,IAAI,CAACyB,iBAAiB,CAAC,UAAU,EAAEzB,KAAK,CAAC;EAClD;EAEAgC,cAAcA,CAAChC,KAAmB,EAAE;IAClC,OAAO,IAAI,CAACyB,iBAAiB,CAAC,aAAa,EAAEzB,KAAK,CAAC;EACrD;AACF;AAACP,OAAA,CAAAoB,WAAA,GAAAA,WAAA", "ignoreList": []}