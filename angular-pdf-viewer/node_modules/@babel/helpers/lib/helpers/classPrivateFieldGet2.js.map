{"version": 3, "names": ["_assert<PERSON>lassBrand", "require", "_classPrivateFieldGet2", "privateMap", "receiver", "get", "assertClassBrand"], "sources": ["../../src/helpers/classPrivateFieldGet2.ts"], "sourcesContent": ["/* @minVersion 7.24.0 */\n\nimport assertClassBrand from \"./assertClassBrand.ts\";\n\nexport default function _classPrivateFieldGet2(\n  privateMap: WeakMap<any, any>,\n  receiver: any,\n) {\n  return privateMap.get(assertClassBrand(privateMap, receiver));\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,iBAAA,GAAAC,OAAA;AAEe,SAASC,sBAAsBA,CAC5CC,UAA6B,EAC7BC,QAAa,EACb;EACA,OAAOD,UAAU,CAACE,GAAG,CAAC,IAAAC,yBAAgB,EAACH,UAAU,EAAEC,QAAQ,CAAC,CAAC;AAC/D", "ignoreList": []}