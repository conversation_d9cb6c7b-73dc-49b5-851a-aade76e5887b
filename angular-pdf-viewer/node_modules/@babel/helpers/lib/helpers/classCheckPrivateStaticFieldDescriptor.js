"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _classCheckPrivateStaticFieldDescriptor;
function _classCheckPrivateStaticFieldDescriptor(descriptor, action) {
  if (descriptor === undefined) {
    throw new TypeError("attempted to " + action + " private static field before its declaration");
  }
}

//# sourceMappingURL=classCheckPrivateStaticFieldDescriptor.js.map
