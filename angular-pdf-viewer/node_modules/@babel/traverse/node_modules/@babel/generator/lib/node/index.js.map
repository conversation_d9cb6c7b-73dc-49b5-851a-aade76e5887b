{"version": 3, "names": ["whitespace", "require", "parens", "_t", "FLIPPED_ALIAS_KEYS", "VISITOR_KEYS", "isCallExpression", "isDecorator", "isExpressionStatement", "isMemberExpression", "isNewExpression", "isParenthesizedExpression", "TokenContext", "exports", "expressionStatement", "arrowBody", "exportDefault", "forHead", "forInHead", "forOfHead", "arrowFlowReturnType", "expandAliases", "obj", "map", "Map", "add", "type", "func", "fn", "get", "set", "node", "parent", "stack", "inForInit", "getRawIdentifier", "_fn", "Object", "keys", "aliases", "alias", "expandedParens", "expandedWhitespaceNodes", "nodes", "isOrHasCallExpression", "object", "needsWhitespace", "_expandedWhitespaceNo", "expression", "flag", "needsWhitespaceBefore", "needsWhitespaceAfter", "needsParens", "tokenContext", "_expandedParens$get", "callee", "isDecoratorMemberExpression", "computed", "property", "isLastChild", "child", "visitorKeys", "i", "length", "val", "Array", "isArray", "j"], "sources": ["../../src/node/index.ts"], "sourcesContent": ["import * as whitespace from \"./whitespace.ts\";\nimport * as parens from \"./parentheses.ts\";\nimport {\n  FLIPPED_ALIAS_KEYS,\n  VISITOR_KEYS,\n  isCallExpression,\n  isDecorator,\n  isExpressionStatement,\n  isMemberExpression,\n  isNewExpression,\n  isParenthesizedExpression,\n} from \"@babel/types\";\nimport type * as t from \"@babel/types\";\n\nimport type { WhitespaceFlag } from \"./whitespace.ts\";\n\nexport const enum TokenContext {\n  expressionStatement = 1 << 0,\n  arrowBody = 1 << 1,\n  exportDefault = 1 << 2,\n  forHead = 1 << 3,\n  forInHead = 1 << 4,\n  forOfHead = 1 << 5,\n  arrowFlowReturnType = 1 << 6,\n}\n\ntype NodeHandler<R> = (\n  node: t.Node,\n  // todo:\n  // node: K extends keyof typeof t\n  //   ? Extract<typeof t[K], { type: \"string\" }>\n  //   : t.Node,\n  parent: t.Node,\n  tokenContext?: number,\n  inForStatementInit?: boolean,\n  getRawIdentifier?: (node: t.Identifier) => string,\n) => R;\n\nexport type NodeHandlers<R> = {\n  [K in string]?: NodeHandler<R>;\n};\n\nfunction expandAliases<R>(obj: NodeHandlers<R>) {\n  const map = new Map<string, NodeHandler<R>>();\n\n  function add(type: string, func: NodeHandler<R>) {\n    const fn = map.get(type);\n    map.set(\n      type,\n      fn\n        ? function (node, parent, stack, inForInit, getRawIdentifier) {\n            return (\n              fn(node, parent, stack, inForInit, getRawIdentifier) ??\n              func(node, parent, stack, inForInit, getRawIdentifier)\n            );\n          }\n        : func,\n    );\n  }\n\n  for (const type of Object.keys(obj)) {\n    const aliases = FLIPPED_ALIAS_KEYS[type];\n    if (aliases) {\n      for (const alias of aliases) {\n        add(alias, obj[type]);\n      }\n    } else {\n      add(type, obj[type]);\n    }\n  }\n\n  return map;\n}\n\n// Rather than using `t.is` on each object property, we pre-expand any type aliases\n// into concrete types so that the 'find' call below can be as fast as possible.\nconst expandedParens = expandAliases(parens);\nconst expandedWhitespaceNodes = expandAliases(whitespace.nodes);\n\nfunction isOrHasCallExpression(node: t.Node): boolean {\n  if (isCallExpression(node)) {\n    return true;\n  }\n\n  return isMemberExpression(node) && isOrHasCallExpression(node.object);\n}\n\nexport function needsWhitespace(\n  node: t.Node,\n  parent: t.Node,\n  type: WhitespaceFlag,\n): boolean {\n  if (!node) return false;\n\n  if (isExpressionStatement(node)) {\n    node = node.expression;\n  }\n\n  const flag = expandedWhitespaceNodes.get(node.type)?.(node, parent);\n\n  if (typeof flag === \"number\") {\n    return (flag & type) !== 0;\n  }\n\n  return false;\n}\n\nexport function needsWhitespaceBefore(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 1);\n}\n\nexport function needsWhitespaceAfter(node: t.Node, parent: t.Node) {\n  return needsWhitespace(node, parent, 2);\n}\n\nexport function needsParens(\n  node: t.Node,\n  parent: t.Node,\n  tokenContext?: number,\n  inForInit?: boolean,\n  getRawIdentifier?: (node: t.Identifier) => string,\n) {\n  if (!parent) return false;\n\n  if (isNewExpression(parent) && parent.callee === node) {\n    if (isOrHasCallExpression(node)) return true;\n  }\n\n  if (isDecorator(parent)) {\n    return (\n      !isDecoratorMemberExpression(node) &&\n      !(isCallExpression(node) && isDecoratorMemberExpression(node.callee)) &&\n      !isParenthesizedExpression(node)\n    );\n  }\n\n  return expandedParens.get(node.type)?.(\n    node,\n    parent,\n    tokenContext,\n    inForInit,\n    getRawIdentifier,\n  );\n}\n\nfunction isDecoratorMemberExpression(node: t.Node): boolean {\n  switch (node.type) {\n    case \"Identifier\":\n      return true;\n    case \"MemberExpression\":\n      return (\n        !node.computed &&\n        node.property.type === \"Identifier\" &&\n        isDecoratorMemberExpression(node.object)\n      );\n    default:\n      return false;\n  }\n}\n\nexport function isLastChild(parent: t.Node, child: t.Node) {\n  const visitorKeys = VISITOR_KEYS[parent.type];\n  for (let i = visitorKeys.length - 1; i >= 0; i--) {\n    const val = (parent as any)[visitorKeys[i]] as t.Node | t.Node[] | null;\n    if (val === child) {\n      return true;\n    } else if (Array.isArray(val)) {\n      let j = val.length - 1;\n      while (j >= 0 && val[j] === null) j--;\n      return j >= 0 && val[j] === child;\n    } else if (val) {\n      return false;\n    }\n  }\n  return false;\n}\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,MAAA,GAAAD,OAAA;AACA,IAAAE,EAAA,GAAAF,OAAA;AASsB;EARpBG,kBAAkB;EAClBC,YAAY;EACZC,gBAAgB;EAChBC,WAAW;EACXC,qBAAqB;EACrBC,kBAAkB;EAClBC,eAAe;EACfC;AAAyB,IAAAR,EAAA;AAAA,MAMTS,YAAY,GAAAC,OAAA,CAAAD,YAAA;EAAAE,mBAAA;EAAAC,SAAA;EAAAC,aAAA;EAAAC,OAAA;EAAAC,SAAA;EAAAC,SAAA;EAAAC,mBAAA;AAAA;AA0B9B,SAASC,aAAaA,CAAIC,GAAoB,EAAE;EAC9C,MAAMC,GAAG,GAAG,IAAIC,GAAG,CAAyB,CAAC;EAE7C,SAASC,GAAGA,CAACC,IAAY,EAAEC,IAAoB,EAAE;IAC/C,MAAMC,EAAE,GAAGL,GAAG,CAACM,GAAG,CAACH,IAAI,CAAC;IACxBH,GAAG,CAACO,GAAG,CACLJ,IAAI,EACJE,EAAE,GACE,UAAUG,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,EAAE;MAAA,IAAAC,GAAA;MAC1D,QAAAA,GAAA,GACER,EAAE,CAACG,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC,YAAAC,GAAA,GACpDT,IAAI,CAACI,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,SAAS,EAAEC,gBAAgB,CAAC;IAE1D,CAAC,GACDR,IACN,CAAC;EACH;EAEA,KAAK,MAAMD,IAAI,IAAIW,MAAM,CAACC,IAAI,CAAChB,GAAG,CAAC,EAAE;IACnC,MAAMiB,OAAO,GAAGnC,kBAAkB,CAACsB,IAAI,CAAC;IACxC,IAAIa,OAAO,EAAE;MACX,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;QAC3Bd,GAAG,CAACe,KAAK,EAAElB,GAAG,CAACI,IAAI,CAAC,CAAC;MACvB;IACF,CAAC,MAAM;MACLD,GAAG,CAACC,IAAI,EAAEJ,GAAG,CAACI,IAAI,CAAC,CAAC;IACtB;EACF;EAEA,OAAOH,GAAG;AACZ;AAIA,MAAMkB,cAAc,GAAGpB,aAAa,CAACnB,MAAM,CAAC;AAC5C,MAAMwC,uBAAuB,GAAGrB,aAAa,CAACrB,UAAU,CAAC2C,KAAK,CAAC;AAE/D,SAASC,qBAAqBA,CAACb,IAAY,EAAW;EACpD,IAAIzB,gBAAgB,CAACyB,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAOtB,kBAAkB,CAACsB,IAAI,CAAC,IAAIa,qBAAqB,CAACb,IAAI,CAACc,MAAM,CAAC;AACvE;AAEO,SAASC,eAAeA,CAC7Bf,IAAY,EACZC,MAAc,EACdN,IAAoB,EACX;EAAA,IAAAqB,qBAAA;EACT,IAAI,CAAChB,IAAI,EAAE,OAAO,KAAK;EAEvB,IAAIvB,qBAAqB,CAACuB,IAAI,CAAC,EAAE;IAC/BA,IAAI,GAAGA,IAAI,CAACiB,UAAU;EACxB;EAEA,MAAMC,IAAI,IAAAF,qBAAA,GAAGL,uBAAuB,CAACb,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAAtCqB,qBAAA,CAAyChB,IAAI,EAAEC,MAAM,CAAC;EAEnE,IAAI,OAAOiB,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAO,CAACA,IAAI,GAAGvB,IAAI,MAAM,CAAC;EAC5B;EAEA,OAAO,KAAK;AACd;AAEO,SAASwB,qBAAqBA,CAACnB,IAAY,EAAEC,MAAc,EAAE;EAClE,OAAOc,eAAe,CAACf,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASmB,oBAAoBA,CAACpB,IAAY,EAAEC,MAAc,EAAE;EACjE,OAAOc,eAAe,CAACf,IAAI,EAAEC,MAAM,EAAE,CAAC,CAAC;AACzC;AAEO,SAASoB,WAAWA,CACzBrB,IAAY,EACZC,MAAc,EACdqB,YAAqB,EACrBnB,SAAmB,EACnBC,gBAAiD,EACjD;EAAA,IAAAmB,mBAAA;EACA,IAAI,CAACtB,MAAM,EAAE,OAAO,KAAK;EAEzB,IAAItB,eAAe,CAACsB,MAAM,CAAC,IAAIA,MAAM,CAACuB,MAAM,KAAKxB,IAAI,EAAE;IACrD,IAAIa,qBAAqB,CAACb,IAAI,CAAC,EAAE,OAAO,IAAI;EAC9C;EAEA,IAAIxB,WAAW,CAACyB,MAAM,CAAC,EAAE;IACvB,OACE,CAACwB,2BAA2B,CAACzB,IAAI,CAAC,IAClC,EAAEzB,gBAAgB,CAACyB,IAAI,CAAC,IAAIyB,2BAA2B,CAACzB,IAAI,CAACwB,MAAM,CAAC,CAAC,IACrE,CAAC5C,yBAAyB,CAACoB,IAAI,CAAC;EAEpC;EAEA,QAAAuB,mBAAA,GAAOb,cAAc,CAACZ,GAAG,CAACE,IAAI,CAACL,IAAI,CAAC,qBAA7B4B,mBAAA,CACLvB,IAAI,EACJC,MAAM,EACNqB,YAAY,EACZnB,SAAS,EACTC,gBACF,CAAC;AACH;AAEA,SAASqB,2BAA2BA,CAACzB,IAAY,EAAW;EAC1D,QAAQA,IAAI,CAACL,IAAI;IACf,KAAK,YAAY;MACf,OAAO,IAAI;IACb,KAAK,kBAAkB;MACrB,OACE,CAACK,IAAI,CAAC0B,QAAQ,IACd1B,IAAI,CAAC2B,QAAQ,CAAChC,IAAI,KAAK,YAAY,IACnC8B,2BAA2B,CAACzB,IAAI,CAACc,MAAM,CAAC;IAE5C;MACE,OAAO,KAAK;EAChB;AACF;AAEO,SAASc,WAAWA,CAAC3B,MAAc,EAAE4B,KAAa,EAAE;EACzD,MAAMC,WAAW,GAAGxD,YAAY,CAAC2B,MAAM,CAACN,IAAI,CAAC;EAC7C,KAAK,IAAIoC,CAAC,GAAGD,WAAW,CAACE,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAChD,MAAME,GAAG,GAAIhC,MAAM,CAAS6B,WAAW,CAACC,CAAC,CAAC,CAA6B;IACvE,IAAIE,GAAG,KAAKJ,KAAK,EAAE;MACjB,OAAO,IAAI;IACb,CAAC,MAAM,IAAIK,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,EAAE;MAC7B,IAAIG,CAAC,GAAGH,GAAG,CAACD,MAAM,GAAG,CAAC;MACtB,OAAOI,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACG,CAAC,CAAC,KAAK,IAAI,EAAEA,CAAC,EAAE;MACrC,OAAOA,CAAC,IAAI,CAAC,IAAIH,GAAG,CAACG,CAAC,CAAC,KAAKP,KAAK;IACnC,CAAC,MAAM,IAAII,GAAG,EAAE;MACd,OAAO,KAAK;IACd;EACF;EACA,OAAO,KAAK;AACd", "ignoreList": []}