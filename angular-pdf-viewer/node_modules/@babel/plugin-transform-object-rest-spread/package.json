{"name": "@babel/plugin-transform-object-rest-spread", "version": "7.27.2", "description": "Compile object rest and spread to ES5", "repository": {"type": "git", "url": "https://github.com/babel/babel.git", "directory": "packages/babel-plugin-transform-object-rest-spread"}, "homepage": "https://babel.dev/docs/en/next/babel-plugin-transform-object-rest-spread", "license": "MIT", "publishConfig": {"access": "public"}, "main": "./lib/index.js", "keywords": ["babel-plugin"], "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.1"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}, "devDependencies": {"@babel/core": "^7.27.1", "@babel/helper-plugin-test-runner": "^7.27.1", "@babel/parser": "^7.27.2"}, "engines": {"node": ">=6.9.0"}, "author": "The Babel Team (https://babel.dev/team)", "type": "commonjs"}