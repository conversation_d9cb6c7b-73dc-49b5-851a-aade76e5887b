{
	"root": true,

	"extends": "@ljharb/eslint-config/node/0.4",

	"rules": {
		"consistent-return": "warn",
		"func-style": "warn",
		"max-len": "off",
		"max-lines-per-function": "off",
		"max-statements-per-line": "warn",
		"no-invalid-this": "warn",
		"no-param-reassign": "warn",
		"no-underscore-dangle": "warn",
	},

	"overrides": [
		{
			"files": "test/**",
			"rules": {
				"no-plusplus": "warn",
			},
		},
	],
}
