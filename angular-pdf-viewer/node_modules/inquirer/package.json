{"name": "inquirer", "type": "module", "version": "9.2.15", "description": "A collection of common interactive command line user interfaces.", "author": "<PERSON> <<EMAIL>>", "files": ["lib"], "main": "lib/inquirer.js", "keywords": ["answer", "answers", "ask", "base", "cli", "command", "command-line", "confirm", "enquirer", "generate", "generator", "hyper", "input", "inquire", "inquirer", "interface", "iterm", "javascript", "menu", "node", "nodejs", "prompt", "promptly", "prompts", "question", "readline", "scaffold", "scaffolder", "scaffolding", "stdin", "stdout", "terminal", "tty", "ui", "yeoman", "yo", "zsh"], "engines": {"node": ">=18"}, "devDependencies": {"terminal-link": "^3.0.0"}, "repository": {"type": "git", "url": "https://github.com/SBoudrias/Inquirer.js.git"}, "license": "MIT", "dependencies": {"@ljharb/through": "^2.3.12", "ansi-escapes": "^4.3.2", "chalk": "^5.3.0", "cli-cursor": "^3.1.0", "cli-width": "^4.1.0", "external-editor": "^3.1.0", "figures": "^3.2.0", "lodash": "^4.17.21", "mute-stream": "1.0.0", "ora": "^5.4.1", "run-async": "^3.0.0", "rxjs": "^7.8.1", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wrap-ansi": "^6.2.0"}, "homepage": "https://github.com/SBoudrias/Inquirer.js/blob/master/packages/inquirer/README.md", "gitHead": "0dfa6bd174d0891c07dcb6a97d04db29b9fc6f34"}