{"version": 3, "file": "mergeInternals.js", "sourceRoot": "", "sources": ["../../../../src/internal/operators/mergeInternals.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAC;AAGpD,OAAO,EAAE,eAAe,EAAE,MAAM,yBAAyB,CAAC;AAC1D,OAAO,EAAE,wBAAwB,EAAE,MAAM,sBAAsB,CAAC;AAehE,MAAM,UAAU,cAAc,CAC5B,MAAqB,EACrB,UAAyB,EACzB,OAAwD,EACxD,UAAkB,EAClB,YAAsC,EACtC,MAAgB,EAChB,iBAAiC,EACjC,mBAAgC;IAGhC,MAAM,MAAM,GAAQ,EAAE,CAAC;IAEvB,IAAI,MAAM,GAAG,CAAC,CAAC;IAEf,IAAI,KAAK,GAAG,CAAC,CAAC;IAEd,IAAI,UAAU,GAAG,KAAK,CAAC;IAKvB,MAAM,aAAa,GAAG,GAAG,EAAE;QAIzB,IAAI,UAAU,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,EAAE;YAC3C,UAAU,CAAC,QAAQ,EAAE,CAAC;SACvB;IACH,CAAC,CAAC;IAGF,MAAM,SAAS,GAAG,CAAC,KAAQ,EAAE,EAAE,CAAC,CAAC,MAAM,GAAG,UAAU,CAAC,CAAC,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAE/F,MAAM,UAAU,GAAG,CAAC,KAAQ,EAAE,EAAE;QAI9B,MAAM,IAAI,UAAU,CAAC,IAAI,CAAC,KAAY,CAAC,CAAC;QAIxC,MAAM,EAAE,CAAC;QAKT,IAAI,aAAa,GAAG,KAAK,CAAC;QAG1B,SAAS,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAC1C,wBAAwB,CACtB,UAAU,EACV,CAAC,UAAU,EAAE,EAAE;YAGb,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAG,UAAU,CAAC,CAAC;YAE3B,IAAI,MAAM,EAAE;gBAGV,SAAS,CAAC,UAAiB,CAAC,CAAC;aAC9B;iBAAM;gBAEL,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAC7B;QACH,CAAC,EACD,GAAG,EAAE;YAGH,aAAa,GAAG,IAAI,CAAC;QACvB,CAAC,EAED,SAAS,EACT,GAAG,EAAE;YAIH,IAAI,aAAa,EAAE;gBAKjB,IAAI;oBAIF,MAAM,EAAE,CAAC;oBAKT,OAAO,MAAM,CAAC,MAAM,IAAI,MAAM,GAAG,UAAU,EAAE;wBAC3C,MAAM,aAAa,GAAG,MAAM,CAAC,KAAK,EAAG,CAAC;wBAItC,IAAI,iBAAiB,EAAE;4BACrB,eAAe,CAAC,UAAU,EAAE,iBAAiB,EAAE,GAAG,EAAE,CAAC,UAAU,CAAC,aAAa,CAAC,CAAC,CAAC;yBACjF;6BAAM;4BACL,UAAU,CAAC,aAAa,CAAC,CAAC;yBAC3B;qBACF;oBAED,aAAa,EAAE,CAAC;iBACjB;gBAAC,OAAO,GAAG,EAAE;oBACZ,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;iBACvB;aACF;QACH,CAAC,CACF,CACF,CAAC;IACJ,CAAC,CAAC;IAGF,MAAM,CAAC,SAAS,CACd,wBAAwB,CAAC,UAAU,EAAE,SAAS,EAAE,GAAG,EAAE;QAEnD,UAAU,GAAG,IAAI,CAAC;QAClB,aAAa,EAAE,CAAC;IAClB,CAAC,CAAC,CACH,CAAC;IAIF,OAAO,GAAG,EAAE;QACV,mBAAmB,aAAnB,mBAAmB,uBAAnB,mBAAmB,EAAI,CAAC;IAC1B,CAAC,CAAC;AACJ,CAAC"}