{"version": 3, "file": "VirtualTimeScheduler.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/VirtualTimeScheduler.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,6CAA4C;AAC5C,gDAA+C;AAC/C,mDAAkD;AAIlD;IAA0C,wCAAc;IAyBtD,8BAAY,mBAA8D,EAAS,SAA4B;QAAnG,oCAAA,EAAA,sBAA0C,aAAoB;QAAS,0BAAA,EAAA,oBAA4B;QAA/G,YACE,kBAAM,mBAAmB,EAAE,cAAM,OAAA,KAAI,CAAC,KAAK,EAAV,CAAU,CAAC,SAC7C;QAFkF,eAAS,GAAT,SAAS,CAAmB;QAfxG,WAAK,GAAW,CAAC,CAAC;QAMlB,WAAK,GAAW,CAAC,CAAC,CAAC;;IAW1B,CAAC;IAOM,oCAAK,GAAZ;QACQ,IAAA,KAAyB,IAAI,EAA3B,OAAO,aAAA,EAAE,SAAS,eAAS,CAAC;QACpC,IAAI,KAAU,CAAC;QACf,IAAI,MAAoC,CAAC;QAEzC,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,IAAI,SAAS,EAAE;YACzD,OAAO,CAAC,KAAK,EAAE,CAAC;YAChB,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,KAAK,CAAC;YAE1B,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;gBACxD,MAAM;aACP;SACF;QAED,IAAI,KAAK,EAAE;YACT,OAAO,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,EAAE,CAAC,EAAE;gBACjC,MAAM,CAAC,WAAW,EAAE,CAAC;aACtB;YACD,MAAM,KAAK,CAAC;SACb;IACH,CAAC;IApDM,oCAAe,GAAG,EAAE,CAAC;IAqD9B,2BAAC;CAAA,AAvDD,CAA0C,+BAAc,GAuDvD;AAvDY,oDAAoB;AAyDjC;IAAsC,iCAAc;IAGlD,uBACY,SAA+B,EAC/B,IAAmD,EACnD,KAAsC;QAAtC,sBAAA,EAAA,SAAiB,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC;QAHlD,YAKE,kBAAM,SAAS,EAAE,IAAI,CAAC,SAEvB;QANW,eAAS,GAAT,SAAS,CAAsB;QAC/B,UAAI,GAAJ,IAAI,CAA+C;QACnD,WAAK,GAAL,KAAK,CAAiC;QALxC,YAAM,GAAY,IAAI,CAAC;QAQ/B,KAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;;IACvC,CAAC;IAEM,gCAAQ,GAAf,UAAgB,KAAS,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QAC1C,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;gBACZ,OAAO,iBAAM,QAAQ,YAAC,KAAK,EAAE,KAAK,CAAC,CAAC;aACrC;YACD,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;YAKpB,IAAM,MAAM,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAC5D,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YACjB,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACtC;aAAM;YAGL,OAAO,2BAAY,CAAC,KAAK,CAAC;SAC3B;IACH,CAAC;IAES,sCAAc,GAAxB,UAAyB,SAA+B,EAAE,EAAQ,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACnF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK,GAAG,KAAK,CAAC;QAC7B,IAAA,OAAO,GAAK,SAAS,QAAd,CAAe;QAC9B,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QAClB,OAAmC,CAAC,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QACrE,OAAO,CAAC,CAAC;IACX,CAAC;IAES,sCAAc,GAAxB,UAAyB,SAA+B,EAAE,EAAQ,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QACnF,OAAO,SAAS,CAAC;IACnB,CAAC;IAES,gCAAQ,GAAlB,UAAmB,KAAQ,EAAE,KAAa;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE;YACxB,OAAO,iBAAM,QAAQ,YAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;IACH,CAAC;IAEc,yBAAW,GAA1B,UAA8B,CAAmB,EAAE,CAAmB;QACpE,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE;gBACvB,OAAO,CAAC,CAAC;aACV;iBAAM,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;gBAC5B,OAAO,CAAC,CAAC;aACV;iBAAM;gBACL,OAAO,CAAC,CAAC,CAAC;aACX;SACF;aAAM,IAAI,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,EAAE;YAC5B,OAAO,CAAC,CAAC;SACV;aAAM;YACL,OAAO,CAAC,CAAC,CAAC;SACX;IACH,CAAC;IACH,oBAAC;AAAD,CAAC,AAjED,CAAsC,yBAAW,GAiEhD;AAjEY,sCAAa"}