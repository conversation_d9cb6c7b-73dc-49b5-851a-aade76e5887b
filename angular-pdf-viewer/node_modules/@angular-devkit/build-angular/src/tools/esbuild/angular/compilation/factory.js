"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || function (mod) {
    if (mod && mod.__esModule) return mod;
    var result = {};
    if (mod != null) for (var k in mod) if (k !== "default" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);
    __setModuleDefault(result, mod);
    return result;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAngularCompilation = void 0;
const environment_options_1 = require("../../../../utils/environment-options");
/**
 * Creates an Angular compilation object that can be used to perform Angular application
 * compilation either for AOT or JIT mode. By default a parallel compilation is created
 * that uses a Node.js worker thread.
 * @param jit True, for Angular JIT compilation; False, for Angular AOT compilation.
 * @returns An instance of an Angular compilation object.
 */
async function createAngularCompilation(jit) {
    if (environment_options_1.useParallelTs) {
        const { ParallelCompilation } = await Promise.resolve().then(() => __importStar(require('./parallel-compilation')));
        return new ParallelCompilation(jit);
    }
    if (jit) {
        const { JitCompilation } = await Promise.resolve().then(() => __importStar(require('./jit-compilation')));
        return new JitCompilation();
    }
    else {
        const { AotCompilation } = await Promise.resolve().then(() => __importStar(require('./aot-compilation')));
        return new AotCompilation();
    }
}
exports.createAngularCompilation = createAngularCompilation;
