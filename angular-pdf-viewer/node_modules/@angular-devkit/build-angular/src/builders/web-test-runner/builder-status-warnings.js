"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.logBuilderStatusWarnings = void 0;
const UNSUPPORTED_OPTIONS = [
    'main',
    'assets',
    'scripts',
    'styles',
    'inlineStyleLanguage',
    'stylePreprocessorOptions',
    'sourceMap',
    'progress',
    'poll',
    'preserveSymlinks',
    'browsers',
    'codeCoverage',
    'codeCoverageExclude',
    'fileReplacements',
    'webWorkerTsConfig',
    'watch',
];
/** Logs a warning for any unsupported options specified. */
function logBuilderStatusWarnings(options, ctx) {
    // Validate supported options
    for (const unsupportedOption of UNSUPPORTED_OPTIONS) {
        const value = options[unsupportedOption];
        if (value === undefined || value === false) {
            continue;
        }
        if (Array.isArray(value) && value.length === 0) {
            continue;
        }
        if (typeof value === 'object' && Object.keys(value).length === 0) {
            continue;
        }
        ctx.logger.warn(`The '${unsupportedOption}' option is not yet supported by this builder.`);
    }
}
exports.logBuilderStatusWarnings = logBuilderStatusWarnings;
