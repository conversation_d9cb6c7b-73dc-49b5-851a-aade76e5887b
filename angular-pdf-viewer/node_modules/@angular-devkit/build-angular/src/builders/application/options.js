"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.normalizeOptions = void 0;
const node_fs_1 = require("node:fs");
const promises_1 = require("node:fs/promises");
const node_module_1 = require("node:module");
const node_path_1 = __importDefault(require("node:path"));
const utils_1 = require("../../utils");
const color_1 = require("../../utils/color");
const environment_options_1 = require("../../utils/environment-options");
const i18n_options_1 = require("../../utils/i18n-options");
const normalize_cache_1 = require("../../utils/normalize-cache");
const postcss_configuration_1 = require("../../utils/postcss-configuration");
const tailwind_1 = require("../../utils/tailwind");
const schema_1 = require("./schema");
/**
 * Normalize the user provided options by creating full paths for all path based options
 * and converting multi-form options into a single form that can be directly used
 * by the build process.
 *
 * @param context The context for current builder execution.
 * @param projectName The name of the project for the current execution.
 * @param options An object containing the options to use for the build.
 * @param plugins An optional array of programmatically supplied build plugins.
 * @returns An object containing normalized options required to perform the build.
 */
// eslint-disable-next-line max-lines-per-function
async function normalizeOptions(context, projectName, options, extensions) {
    // If not explicitly set, default to the Node.js process argument
    const preserveSymlinks = options.preserveSymlinks ?? process.execArgv.includes('--preserve-symlinks');
    // Setup base paths based on workspace root and project information
    const workspaceRoot = preserveSymlinks
        ? context.workspaceRoot
        : // NOTE: promises.realpath should not be used here since it uses realpath.native which
            // can cause case conversion and other undesirable behavior on Windows systems.
            // ref: https://github.com/nodejs/node/issues/7726
            (0, node_fs_1.realpathSync)(context.workspaceRoot);
    const projectMetadata = await context.getProjectMetadata(projectName);
    const projectRoot = normalizeDirectoryPath(node_path_1.default.join(workspaceRoot, projectMetadata.root ?? ''));
    const projectSourceRoot = normalizeDirectoryPath(node_path_1.default.join(workspaceRoot, projectMetadata.sourceRoot ?? 'src'));
    // Gather persistent caching option and provide a project specific cache location
    const cacheOptions = (0, normalize_cache_1.normalizeCacheOptions)(projectMetadata, workspaceRoot);
    cacheOptions.path = node_path_1.default.join(cacheOptions.path, projectName);
    const i18nOptions = (0, i18n_options_1.createI18nOptions)(projectMetadata, options.localize);
    i18nOptions.duplicateTranslationBehavior = options.i18nDuplicateTranslation;
    i18nOptions.missingTranslationBehavior = options.i18nMissingTranslation;
    if (options.forceI18nFlatOutput) {
        i18nOptions.flatOutput = true;
    }
    const entryPoints = normalizeEntryPoints(workspaceRoot, options.browser, options.entryPoints);
    const tsconfig = node_path_1.default.join(workspaceRoot, options.tsConfig);
    const optimizationOptions = (0, utils_1.normalizeOptimization)(options.optimization);
    const sourcemapOptions = (0, utils_1.normalizeSourceMaps)(options.sourceMap ?? false);
    const assets = options.assets?.length
        ? (0, utils_1.normalizeAssetPatterns)(options.assets, workspaceRoot, projectRoot, projectSourceRoot)
        : undefined;
    const outputPath = options.outputPath;
    const outputOptions = {
        browser: 'browser',
        server: 'server',
        media: 'media',
        ...(typeof outputPath === 'string' ? undefined : outputPath),
        base: normalizeDirectoryPath(node_path_1.default.resolve(workspaceRoot, typeof outputPath === 'string' ? outputPath : outputPath.base)),
    };
    const outputNames = {
        bundles: options.outputHashing === schema_1.OutputHashing.All || options.outputHashing === schema_1.OutputHashing.Bundles
            ? '[name]-[hash]'
            : '[name]',
        media: outputOptions.media +
            (options.outputHashing === schema_1.OutputHashing.All || options.outputHashing === schema_1.OutputHashing.Media
                ? '/[name]-[hash]'
                : '/[name]'),
    };
    let fileReplacements;
    if (options.fileReplacements) {
        for (const replacement of options.fileReplacements) {
            const fileReplaceWith = node_path_1.default.join(workspaceRoot, replacement.with);
            try {
                await (0, promises_1.access)(fileReplaceWith, promises_1.constants.F_OK);
            }
            catch {
                throw new Error(`The ${fileReplaceWith} path in file replacements does not exist.`);
            }
            fileReplacements ??= {};
            fileReplacements[node_path_1.default.join(workspaceRoot, replacement.replace)] = fileReplaceWith;
        }
    }
    let loaderExtensions;
    if (options.loader) {
        for (const [extension, value] of Object.entries(options.loader)) {
            if (extension[0] !== '.' || /\.[cm]?[jt]sx?$/.test(extension)) {
                continue;
            }
            if (value !== 'text' && value !== 'binary' && value !== 'file' && value !== 'empty') {
                continue;
            }
            loaderExtensions ??= {};
            loaderExtensions[extension] = value;
        }
    }
    const postcssConfiguration = await (0, postcss_configuration_1.loadPostcssConfiguration)(workspaceRoot, projectRoot);
    // Skip tailwind configuration if postcss is customized
    const tailwindConfiguration = postcssConfiguration
        ? undefined
        : await getTailwindConfig(workspaceRoot, projectRoot, context);
    const globalStyles = normalizeGlobalEntries(options.styles, 'styles');
    const globalScripts = normalizeGlobalEntries(options.scripts, 'scripts');
    let indexHtmlOptions;
    // index can never have a value of `true` but in the schema it's of type `boolean`.
    if (typeof options.index !== 'boolean') {
        indexHtmlOptions = {
            input: node_path_1.default.join(workspaceRoot, typeof options.index === 'string' ? options.index : options.index.input),
            // The output file will be created within the configured output path
            output: typeof options.index === 'string'
                ? node_path_1.default.basename(options.index)
                : options.index.output || 'index.html',
            insertionOrder: [
                ['polyfills', true],
                ...globalStyles.filter((s) => s.initial).map((s) => [s.name, false]),
                ...globalScripts.filter((s) => s.initial).map((s) => [s.name, false]),
                ['main', true],
                // [name, esm]
            ],
            transformer: extensions?.indexHtmlTransformer,
            // Preload initial defaults to true
            preloadInitial: typeof options.index !== 'object' || (options.index.preloadInitial ?? true),
        };
    }
    let serverEntryPoint;
    if (options.server) {
        serverEntryPoint = node_path_1.default.join(workspaceRoot, options.server);
    }
    else if (options.server === '') {
        throw new Error('The "server" option cannot be an empty string.');
    }
    let prerenderOptions;
    if (options.prerender) {
        const { discoverRoutes = true, routesFile = undefined } = options.prerender === true ? {} : options.prerender;
        prerenderOptions = {
            discoverRoutes,
            routesFile: routesFile && node_path_1.default.join(workspaceRoot, routesFile),
        };
    }
    let ssrOptions;
    if (options.ssr === true) {
        ssrOptions = {};
    }
    else if (typeof options.ssr === 'object') {
        const { entry } = options.ssr;
        ssrOptions = {
            entry: entry && node_path_1.default.join(workspaceRoot, entry),
        };
    }
    let appShellOptions;
    if (options.appShell) {
        appShellOptions = {
            route: 'shell',
        };
    }
    if ((appShellOptions || ssrOptions || prerenderOptions) && !serverEntryPoint) {
        throw new Error('The "server" option is required when enabling "ssr", "prerender" or "app-shell".');
    }
    // Initial options to keep
    const { allowedCommonJsDependencies, aot, baseHref, crossOrigin, externalDependencies, extractLicenses, inlineStyleLanguage = 'css', outExtension, serviceWorker, poll, polyfills, statsJson, stylePreprocessorOptions, subresourceIntegrity, verbose, watch, progress = true, externalPackages, deleteOutputPath, namedChunks, budgets, deployUrl, clearScreen, define, } = options;
    // Return all the normalized options
    return {
        advancedOptimizations: !!aot && optimizationOptions.scripts,
        allowedCommonJsDependencies,
        baseHref,
        cacheOptions,
        crossOrigin,
        deleteOutputPath,
        externalDependencies,
        extractLicenses,
        inlineStyleLanguage,
        jit: !aot,
        stats: !!statsJson,
        polyfills: polyfills === undefined || Array.isArray(polyfills) ? polyfills : [polyfills],
        poll,
        progress,
        externalPackages,
        preserveSymlinks,
        stylePreprocessorOptions,
        subresourceIntegrity,
        serverEntryPoint,
        prerenderOptions,
        appShellOptions,
        ssrOptions,
        verbose,
        watch,
        workspaceRoot,
        entryPoints,
        optimizationOptions,
        outputOptions,
        outExtension,
        sourcemapOptions,
        tsconfig,
        projectRoot,
        assets,
        outputNames,
        fileReplacements,
        globalStyles,
        globalScripts,
        serviceWorker: serviceWorker
            ? node_path_1.default.join(workspaceRoot, typeof serviceWorker === 'string' ? serviceWorker : 'src/ngsw-config.json')
            : undefined,
        indexHtmlOptions,
        tailwindConfiguration,
        postcssConfiguration,
        i18nOptions,
        namedChunks,
        budgets: budgets?.length ? budgets : undefined,
        publicPath: deployUrl,
        plugins: extensions?.codePlugins?.length ? extensions?.codePlugins : undefined,
        loaderExtensions,
        jsonLogs: environment_options_1.useJSONBuildLogs,
        colors: color_1.colors.enabled,
        clearScreen,
        define,
    };
}
exports.normalizeOptions = normalizeOptions;
async function getTailwindConfig(workspaceRoot, projectRoot, context) {
    const tailwindConfigurationPath = await (0, tailwind_1.findTailwindConfigurationFile)(workspaceRoot, projectRoot);
    if (!tailwindConfigurationPath) {
        return undefined;
    }
    // Create a node resolver at the project root as a directory
    const resolver = (0, node_module_1.createRequire)(projectRoot + '/');
    try {
        return {
            file: tailwindConfigurationPath,
            package: resolver.resolve('tailwindcss'),
        };
    }
    catch {
        const relativeTailwindConfigPath = node_path_1.default.relative(workspaceRoot, tailwindConfigurationPath);
        context.logger.warn(`Tailwind CSS configuration file found (${relativeTailwindConfigPath})` +
            ` but the 'tailwindcss' package is not installed.` +
            ` To enable Tailwind CSS, please install the 'tailwindcss' package.`);
    }
    return undefined;
}
/**
 * Normalize entry point options. To maintain compatibility with the legacy browser builder, we need a single `browser`
 * option which defines a single entry point. However, we also want to support multiple entry points as an internal option.
 * The two options are mutually exclusive and if `browser` is provided it will be used as the sole entry point.
 * If `entryPoints` are provided, they will be used as the set of entry points.
 *
 * @param workspaceRoot Path to the root of the Angular workspace.
 * @param browser The `browser` option pointing at the application entry point. While required per the schema file, it may be omitted by
 *     programmatic usages of `browser-esbuild`.
 * @param entryPoints Set of entry points to use if provided.
 * @returns An object mapping entry point names to their file paths.
 */
function normalizeEntryPoints(workspaceRoot, browser, entryPoints = new Set()) {
    if (browser === '') {
        throw new Error('`browser` option cannot be an empty string.');
    }
    // `browser` and `entryPoints` are mutually exclusive.
    if (browser && entryPoints.size > 0) {
        throw new Error('Only one of `browser` or `entryPoints` may be provided.');
    }
    if (!browser && entryPoints.size === 0) {
        // Schema should normally reject this case, but programmatic usages of the builder might make this mistake.
        throw new Error('Either `browser` or at least one `entryPoints` value must be provided.');
    }
    // Schema types force `browser` to always be provided, but it may be omitted when the builder is invoked programmatically.
    if (browser) {
        // Use `browser` alone.
        return { 'main': node_path_1.default.join(workspaceRoot, browser) };
    }
    else {
        // Use `entryPoints` alone.
        const entryPointPaths = {};
        for (const entryPoint of entryPoints) {
            const parsedEntryPoint = node_path_1.default.parse(entryPoint);
            // Use the input file path without an extension as the "name" of the entry point dictating its output location.
            // Relative entry points are generated at the same relative path in the output directory.
            // Absolute entry points are always generated with the same file name in the root of the output directory. This includes absolute
            // paths pointing at files actually within the workspace root.
            const entryPointName = node_path_1.default.isAbsolute(entryPoint)
                ? parsedEntryPoint.name
                : node_path_1.default.join(parsedEntryPoint.dir, parsedEntryPoint.name);
            // Get the full file path to a relative entry point input. Leave bare specifiers alone so they are resolved as modules.
            const isRelativePath = entryPoint.startsWith('.');
            const entryPointPath = isRelativePath ? node_path_1.default.join(workspaceRoot, entryPoint) : entryPoint;
            // Check for conflicts with previous entry points.
            const existingEntryPointPath = entryPointPaths[entryPointName];
            if (existingEntryPointPath) {
                throw new Error(`\`${existingEntryPointPath}\` and \`${entryPointPath}\` both output to the same location \`${entryPointName}\`.` +
                    ' Rename or move one of the files to fix the conflict.');
            }
            entryPointPaths[entryPointName] = entryPointPath;
        }
        return entryPointPaths;
    }
}
/**
 * Normalize a directory path string.
 * Currently only removes a trailing slash if present.
 * @param path A path string.
 * @returns A normalized path string.
 */
function normalizeDirectoryPath(path) {
    const last = path[path.length - 1];
    if (last === '/' || last === '\\') {
        return path.slice(0, -1);
    }
    return path;
}
function normalizeGlobalEntries(rawEntries, defaultName) {
    if (!rawEntries?.length) {
        return [];
    }
    const bundles = new Map();
    for (const rawEntry of rawEntries) {
        let entry;
        if (typeof rawEntry === 'string') {
            // string entries use default bundle name and inject values
            entry = { input: rawEntry };
        }
        else {
            entry = rawEntry;
        }
        const { bundleName, input, inject = true } = entry;
        // Non-injected entries default to the file name
        const name = bundleName || (inject ? defaultName : node_path_1.default.basename(input, node_path_1.default.extname(input)));
        const existing = bundles.get(name);
        if (!existing) {
            bundles.set(name, { name, files: [input], initial: inject });
            continue;
        }
        if (existing.initial !== inject) {
            throw new Error(`The "${name}" bundle is mixing injected and non-injected entries. ` +
                'Verify that the project options are correct.');
        }
        existing.files.push(input);
    }
    return [...bundles.values()];
}
