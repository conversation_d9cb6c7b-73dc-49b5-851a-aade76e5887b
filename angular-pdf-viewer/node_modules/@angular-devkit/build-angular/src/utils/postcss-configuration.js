"use strict";
/**
 * @license
 * Copyright Google LLC All Rights Reserved.
 *
 * Use of this source code is governed by an MIT-style license that can be
 * found in the LICENSE file at https://angular.io/license
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.loadPostcssConfiguration = void 0;
const promises_1 = require("node:fs/promises");
const node_path_1 = require("node:path");
const postcssConfigurationFiles = ['postcss.config.json', '.postcssrc.json'];
async function generateSearchDirectories(roots) {
    return await Promise.all(roots.map((root) => (0, promises_1.readdir)(root, { withFileTypes: true }).then((entries) => ({
        root,
        files: new Set(entries.filter((entry) => entry.isFile()).map((entry) => entry.name)),
    }))));
}
function findFile(searchDirectories, potentialFiles) {
    for (const { root, files } of searchDirectories) {
        for (const potential of potentialFiles) {
            if (files.has(potential)) {
                return (0, node_path_1.join)(root, potential);
            }
        }
    }
    return undefined;
}
async function readPostcssConfiguration(configurationFile) {
    const data = await (0, promises_1.readFile)(configurationFile, 'utf-8');
    const config = JSON.parse(data);
    return config;
}
async function loadPostcssConfiguration(workspaceRoot, projectRoot) {
    // A configuration file can exist in the project or workspace root
    const searchDirectories = await generateSearchDirectories([projectRoot, workspaceRoot]);
    const configPath = findFile(searchDirectories, postcssConfigurationFiles);
    if (!configPath) {
        return undefined;
    }
    const raw = await readPostcssConfiguration(configPath);
    // If no plugins are defined, consider it equivalent to no configuration
    if (!raw.plugins || typeof raw.plugins !== 'object') {
        return undefined;
    }
    // Normalize plugin array form
    if (Array.isArray(raw.plugins)) {
        if (raw.plugins.length < 1) {
            return undefined;
        }
        const config = { plugins: [] };
        for (const element of raw.plugins) {
            if (typeof element === 'string') {
                config.plugins.push([element]);
            }
            else {
                config.plugins.push(element);
            }
        }
        return config;
    }
    // Normalize plugin object map form
    const entries = Object.entries(raw.plugins);
    if (entries.length < 1) {
        return undefined;
    }
    const config = { plugins: [] };
    for (const [name, options] of entries) {
        if (!options || (typeof options !== 'object' && typeof options !== 'string')) {
            continue;
        }
        config.plugins.push([name, options]);
    }
    return config;
}
exports.loadPostcssConfiguration = loadPostcssConfiguration;
