import { __extends } from "tslib";
import { Subscription } from '../Subscription';
var Action = (function (_super) {
    __extends(Action, _super);
    function Action(scheduler, work) {
        return _super.call(this) || this;
    }
    Action.prototype.schedule = function (state, delay) {
        if (delay === void 0) { delay = 0; }
        return this;
    };
    return Action;
}(Subscription));
export { Action };
//# sourceMappingURL=Action.js.map