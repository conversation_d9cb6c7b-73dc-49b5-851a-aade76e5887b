{"version": 3, "file": "getDirectory.js", "names": ["getDirectory", "filepath", "filePathIsDirectory", "isDirectory", "directory", "path", "dirname", "getDirectorySync", "isDirectorySync"], "sources": ["../src/getDirectory.ts"], "sourcesContent": ["import path from 'path';\nimport { isDirectory, isDirectorySync } from 'path-type';\n\nasync function getDirectory(filepath: string): Promise<string> {\n  const filePathIsDirectory = await isDirectory(filepath);\n\n  if (filePathIsDirectory === true) {\n    return filepath;\n  }\n\n  const directory = path.dirname(filepath);\n\n  return directory;\n}\n\nfunction getDirectorySync(filepath: string): string {\n  const filePathIsDirectory = isDirectorySync(filepath);\n\n  if (filePathIsDirectory === true) {\n    return filepath;\n  }\n\n  const directory = path.dirname(filepath);\n\n  return directory;\n}\n\nexport { getDirectory, getDirectorySync };\n"], "mappings": ";;;;;;;;AAAA;;AACA;;;;AAEA,eAAeA,YAAf,CAA4BC,QAA5B,EAA+D;EAC7D,MAAMC,mBAAmB,GAAG,MAAM,IAAAC,qBAAA,EAAYF,QAAZ,CAAlC;;EAEA,IAAIC,mBAAmB,KAAK,IAA5B,EAAkC;IAChC,OAAOD,QAAP;EACD;;EAED,MAAMG,SAAS,GAAGC,aAAA,CAAKC,OAAL,CAAaL,QAAb,CAAlB;;EAEA,OAAOG,SAAP;AACD;;AAED,SAASG,gBAAT,CAA0BN,QAA1B,EAAoD;EAClD,MAAMC,mBAAmB,GAAG,IAAAM,yBAAA,EAAgBP,QAAhB,CAA5B;;EAEA,IAAIC,mBAAmB,KAAK,IAA5B,EAAkC;IAChC,OAAOD,QAAP;EACD;;EAED,MAAMG,SAAS,GAAGC,aAAA,CAAKC,OAAL,CAAaL,QAAb,CAAlB;;EAEA,OAAOG,SAAP;AACD"}