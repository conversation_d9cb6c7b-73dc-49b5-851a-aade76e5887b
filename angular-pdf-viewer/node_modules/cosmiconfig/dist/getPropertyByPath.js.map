{"version": 3, "file": "getPropertyByPath.js", "names": ["getPropertyByPath", "source", "path", "Object", "prototype", "hasOwnProperty", "call", "parsed<PERSON><PERSON>", "split", "reduce", "previous", "key", "undefined"], "sources": ["../src/getPropertyByPath.ts"], "sourcesContent": ["// Resolves property names or property paths defined with period-delimited\n// strings or arrays of strings. Property names that are found on the source\n// object are used directly (even if they include a period).\n// Nested property names that include periods, within a path, are only\n// understood in array paths.\nfunction getPropertyByPath(\n  source: { [key: string]: unknown },\n  path: string | Array<string>,\n): unknown {\n  if (\n    typeof path === 'string' &&\n    Object.prototype.hasOwnProperty.call(source, path)\n  ) {\n    return source[path];\n  }\n\n  const parsedPath = typeof path === 'string' ? path.split('.') : path;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  return parsedPath.reduce((previous: any, key): unknown => {\n    if (previous === undefined) {\n      return previous;\n    }\n    return previous[key];\n  }, source);\n}\n\nexport { getPropertyByPath };\n"], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA,SAASA,iBAAT,CACEC,MADF,EAEEC,IAFF,EAGW;EACT,IACE,OAAOA,IAAP,KAAgB,QAAhB,IACAC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCL,MAArC,EAA6CC,IAA7C,CAFF,EAGE;IACA,OAAOD,MAAM,CAACC,IAAD,CAAb;EACD;;EAED,MAAMK,UAAU,GAAG,OAAOL,IAAP,KAAgB,QAAhB,GAA2BA,IAAI,CAACM,KAAL,CAAW,GAAX,CAA3B,GAA6CN,IAAhE,CARS,CAST;;EACA,OAAOK,UAAU,CAACE,MAAX,CAAkB,CAACC,QAAD,EAAgBC,GAAhB,KAAiC;IACxD,IAAID,QAAQ,KAAKE,SAAjB,EAA4B;MAC1B,OAAOF,QAAP;IACD;;IACD,OAAOA,QAAQ,CAACC,GAAD,CAAf;EACD,CALM,EAKJV,MALI,CAAP;AAMD"}