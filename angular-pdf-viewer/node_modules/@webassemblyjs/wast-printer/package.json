{"name": "@webassemblyjs/wast-printer", "version": "1.14.1", "description": "WebAssembly text format printer", "main": "lib/index.js", "module": "esm/index.js", "keywords": ["webassembly", "javascript", "ast", "compiler", "printer", "wast"], "scripts": {"test": "mocha"}, "author": "<PERSON>", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}, "devDependencies": {"@webassemblyjs/helper-test-framework": "1.14.1", "@webassemblyjs/wast-parser": "1.14.1"}, "repository": {"type": "git", "url": "https://github.com/xtuc/webassemblyjs.git"}, "publishConfig": {"access": "public"}, "gitHead": "25d52b1296e151ac56244a7c3886661e6b4a69ea"}