/**
 * This is the Windows implementation of isexe, which uses the file
 * extension and PATHEXT setting.
 *
 * @module
 */
import { IsexeOptions } from './options';
/**
 * Determine whether a path is executable based on the file extension
 * and PATHEXT environment variable (or specified pathExt option)
 */
export declare const isexe: (path: string, options?: IsexeOptions) => Promise<boolean>;
/**
 * Synchronously determine whether a path is executable based on the file
 * extension and PATHEXT environment variable (or specified pathExt option)
 */
export declare const sync: (path: string, options?: IsexeOptions) => boolean;
//# sourceMappingURL=win32.d.ts.map