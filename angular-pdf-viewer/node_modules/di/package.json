{"name": "di", "version": "0.0.1", "description": "Dependency Injection for Node.js. Heavily inspired by AngularJS.", "main": "lib/index.js", "scripts": {"test": "mocha --compilers coffee:coffee-script test/*"}, "repository": {"type": "git", "url": "git://github.com/vojtajina/node-di.git"}, "keywords": ["di", "dependency", "injection", "injector"], "devDependencies": {"grunt": "~0.4.0rc5", "grunt-simple-mocha": "~0.3.2", "grunt-contrib-jshint": "~0.1.1rc5", "mocha": "1.8.1", "chai": "1.4.2", "coffee-script": "1.4.0"}, "author": "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT"}