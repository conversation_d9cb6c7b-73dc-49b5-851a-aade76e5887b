{"name": "sass-loader", "version": "14.1.1", "description": "Sass loader for webpack", "license": "MIT", "repository": "webpack-contrib/sass-loader", "author": "<PERSON><PERSON>", "homepage": "https://github.com/webpack-contrib/sass-loader", "bugs": "https://github.com/webpack-contrib/sass-loader/issues", "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "main": "dist/cjs.js", "engines": {"node": ">= 18.12.0"}, "scripts": {"start": "npm run build -- -w", "clean": "del-cli dist", "prebuild": "npm run clean", "build": "cross-env NODE_ENV=production babel src -d dist --copy-files", "commitlint": "commitlint --from=master", "security": "npm audit --production", "lint:prettier": "prettier --cache --list-different .", "lint:js": "eslint --cache .", "lint:spelling": "cspell --cache --no-must-find-files --quiet \"**/*.*\"", "lint": "npm-run-all -l -p \"lint:**\"", "test:only": "cross-env NODE_ENV=test jest", "fix:js": "npm run lint:js -- --fix", "fix:prettier": "npm run lint:prettier -- --write", "fix": "npm-run-all -l fix:js fix:prettier", "test:watch": "npm run test:only -- --watch", "test:manual": "npm run build && webpack-dev-server test/manual/src/index.js --open --config test/manual/webpack.config.js", "test:coverage": "npm run test:only -- --collectCoverageFrom=\"src/**/*.js\" --coverage", "pretest": "npm run lint", "test": "npm run test:coverage", "prepare": "husky && npm run build", "release": "standard-version"}, "files": ["dist"], "peerDependencies": {"@rspack/core": "0.x || 1.x", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "webpack": {"optional": true}}, "dependencies": {"neo-async": "^2.6.2"}, "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.7", "@babel/preset-env": "^7.23.8", "@commitlint/cli": "^18.4.4", "@commitlint/config-conventional": "^18.4.4", "@webpack-contrib/eslint-config-webpack": "^3.0.0", "babel-jest": "^29.6.2", "bootstrap-sass": "^3.4.1", "bootstrap-v4": "npm:bootstrap@^4.5.3", "bootstrap-v5": "npm:bootstrap@^5.0.1", "cross-env": "^7.0.3", "cspell": "^8.3.2", "css-loader": "^6.9.0", "del": "^6.1.1", "del-cli": "^5.1.0", "enhanced-resolve": "^5.15.0", "eslint": "^8.46.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.28.0", "file-loader": "^6.2.0", "foundation-sites": "^6.7.5", "husky": "^9.0.11", "jest": "^29.6.2", "jest-environment-node-single-context": "^29.1.0", "lint-staged": "^15.2.0", "material-components-web": "^9.0.0", "memfs": "^4.6.0", "node-sass": "^9.0.0", "node-sass-glob-importer": "^5.3.2", "npm-run-all": "^4.1.5", "prettier": "^3.2.2", "sass": "^1.69.7", "sass-embedded": "^1.69.7", "semver": "^7.5.4", "standard-version": "^9.3.1", "style-loader": "^3.3.4", "webpack": "^5.88.2"}, "keywords": ["sass", "libsass", "webpack", "loader"]}