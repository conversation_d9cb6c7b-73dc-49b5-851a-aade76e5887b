{"version": 3, "file": "plugin-loader.js", "sourceRoot": "", "sources": ["../../src/less-browser/plugin-loader.js"], "names": [], "mappings": ";;;AAAA;;GAEG;AACH,oHAAiF;AAEjF;;GAEG;AACH,IAAM,YAAY,GAAG,UAAS,IAAI;IAC9B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,yDAAyD;AAC7D,CAAC,CAAC;AAEF,YAAY,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,mCAAoB,EAAE,EAAE;IAC/D,UAAU,YAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,WAAW;QAC5D,OAAO,IAAI,OAAO,CAAC,UAAC,OAAO,EAAE,MAAM;YAC/B,WAAW,CAAC,QAAQ,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,CAAC;iBACzD,IAAI,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;QACrC,CAAC,CAAC,CAAC;IACP,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,YAAY,CAAC", "sourcesContent": ["/**\n * @todo Add tests for browser `@plugin`\n */\nimport AbstractPluginLoader from '../less/environment/abstract-plugin-loader.js';\n\n/**\n * Browser Plugin Loader\n */\nconst PluginLoader = function(less) {\n    this.less = less;\n    // Should we shim this.require for browser? Probably not?\n};\n\nPluginLoader.prototype = Object.assign(new AbstractPluginLoader(), {\n    loadPlugin(filename, basePath, context, environment, fileManager) {\n        return new Promise((fulfill, reject) => {\n            fileManager.loadFile(filename, basePath, context, environment)\n                .then(fulfill).catch(reject);\n        });\n    }\n});\n\nexport default PluginLoader;\n\n"]}