{"version": 3, "file": "log-listener.js", "sourceRoot": "", "sources": ["../../src/less-browser/log-listener.js"], "names": [], "mappings": ";;AAAA,mBAAe,UAAC,IAAI,EAAE,OAAO;IACzB,IAAM,cAAc,GAAG,CAAC,CAAC;IACzB,IAAM,aAAa,GAAG,CAAC,CAAC;IACxB,IAAM,aAAa,GAAG,CAAC,CAAC;IACxB,IAAM,cAAc,GAAG,CAAC,CAAC;IAEzB,mDAAmD;IACnD,oCAAoC;IACpC,6BAA6B;IAC7B,aAAa;IACb,WAAW;IACX,gBAAgB;IAChB,OAAO,CAAC,QAAQ,GAAG,OAAO,OAAO,CAAC,QAAQ,KAAK,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,GAAG,KAAK,aAAa,CAAC,CAAC,CAAE,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,CAAC;IAElJ,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAClB,OAAO,CAAC,OAAO,GAAG,CAAC;gBACf,KAAK,EAAE,UAAS,GAAG;oBACf,IAAI,OAAO,CAAC,QAAQ,IAAI,cAAc,EAAE;wBACpC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;qBACpB;gBACL,CAAC;gBACD,IAAI,EAAE,UAAS,GAAG;oBACd,IAAI,OAAO,CAAC,QAAQ,IAAI,aAAa,EAAE;wBACnC,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;qBACpB;gBACL,CAAC;gBACD,IAAI,EAAE,UAAS,GAAG;oBACd,IAAI,OAAO,CAAC,QAAQ,IAAI,aAAa,EAAE;wBACnC,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;qBACrB;gBACL,CAAC;gBACD,KAAK,EAAE,UAAS,GAAG;oBACf,IAAI,OAAO,CAAC,QAAQ,IAAI,cAAc,EAAE;wBACpC,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;qBACtB;gBACL,CAAC;aACJ,CAAC,CAAC;KACN;IACD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAC7C,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;KAC/C;AACL,CAAC,EAAC", "sourcesContent": ["export default (less, options) => {\n    const logLevel_debug = 4;\n    const logLevel_info = 3;\n    const logLevel_warn = 2;\n    const logLevel_error = 1;\n\n    // The amount of logging in the javascript console.\n    // 3 - Debug, information and errors\n    // 2 - Information and errors\n    // 1 - Errors\n    // 0 - None\n    // Defaults to 2\n    options.logLevel = typeof options.logLevel !== 'undefined' ? options.logLevel : (options.env === 'development' ?  logLevel_info : logLevel_error);\n\n    if (!options.loggers) {\n        options.loggers = [{\n            debug: function(msg) {\n                if (options.logLevel >= logLevel_debug) {\n                    console.log(msg);\n                }\n            },\n            info: function(msg) {\n                if (options.logLevel >= logLevel_info) {\n                    console.log(msg);\n                }\n            },\n            warn: function(msg) {\n                if (options.logLevel >= logLevel_warn) {\n                    console.warn(msg);\n                }\n            },\n            error: function(msg) {\n                if (options.logLevel >= logLevel_error) {\n                    console.error(msg);\n                }\n            }\n        }];\n    }\n    for (let i = 0; i < options.loggers.length; i++) {\n        less.logger.addListener(options.loggers[i]);\n    }\n};\n"]}