{"version": 3, "file": "source-map-output.js", "sourceRoot": "", "sources": ["../../src/less/source-map-output.js"], "names": [], "mappings": ";;AAAA,mBAAyB,WAAW;IAChC;QACI,yBAAY,OAAO;YACf,IAAI,CAAC,IAAI,GAAG,EAAE,CAAC;YACf,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,QAAQ,CAAC;YAClC,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,WAAW,CAAC;YACxC,IAAI,CAAC,wBAAwB,GAAG,OAAO,CAAC,uBAAuB,CAAC;YAChE,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC3E;YACD,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,cAAc,CAAC;YAC9C,IAAI,CAAC,YAAY,GAAG,OAAO,CAAC,YAAY,CAAC;YACzC,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;aAC3E;YACD,IAAI,OAAO,CAAC,iBAAiB,EAAE;gBAC3B,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;gBACxE,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;oBAC5E,IAAI,CAAC,kBAAkB,IAAI,GAAG,CAAC;iBAClC;aACJ;iBAAM;gBACH,IAAI,CAAC,kBAAkB,GAAG,EAAE,CAAC;aAChC;YACD,IAAI,CAAC,kBAAkB,GAAG,OAAO,CAAC,iBAAiB,CAAC;YACpD,IAAI,CAAC,8BAA8B,GAAG,WAAW,CAAC,qBAAqB,EAAE,CAAC;YAE1E,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,CAAC,CAAC;QACrB,CAAC;QAED,wCAAc,GAAd,UAAe,IAAI;YACf,IAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,EAAE;gBACxE,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;gBACtD,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;oBACnD,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;iBAC5B;aACJ;YAED,OAAO,IAAI,CAAC;QAChB,CAAC;QAED,2CAAiB,GAAjB,UAAkB,QAAQ;YACtB,QAAQ,GAAG,QAAQ,CAAC,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YACxC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;YACzC,OAAO,CAAC,IAAI,CAAC,kBAAkB,IAAI,EAAE,CAAC,GAAG,QAAQ,CAAC;QACtD,CAAC;QAED,6BAAG,GAAH,UAAI,KAAK,EAAE,QAAQ,EAAE,KAAK,EAAE,QAAQ;YAEhC,8BAA8B;YAC9B,IAAI,CAAC,KAAK,EAAE;gBACR,OAAO;aACV;YAED,IAAI,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE,aAAa,EAAE,CAAC,CAAC;YAElD,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBAC/B,IAAI,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;gBAEvD,kDAAkD;gBAClD,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;oBAClD,mBAAmB;oBACnB,KAAK,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;oBAC1D,IAAI,KAAK,GAAG,CAAC,EAAE;wBAAE,KAAK,GAAG,CAAC,CAAC;qBAAE;oBAC7B,oBAAoB;oBACpB,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC;iBACrF;gBAED;;;mBAGG;gBACH,IAAI,WAAW,KAAK,SAAS,EAAE;oBAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACtB,OAAO;iBACV;gBAED,WAAW,GAAG,WAAW,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;gBAC9C,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;gBACtC,aAAa,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;aACvD;YAED,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC1B,OAAO,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;YAElC,IAAI,QAAQ,IAAI,QAAQ,CAAC,QAAQ,EAAE;gBAC/B,IAAI,CAAC,QAAQ,EAAE;oBACX,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,OAAO,EAAC;wBAChG,QAAQ,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,CAAC,MAAM,EAAC;wBACnE,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC;iBAC3D;qBAAM;oBACH,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;wBAC/B,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,EAAE,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,WAAW,GAAG,CAAC,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,EAAC;4BAClH,QAAQ,EAAE,EAAE,IAAI,EAAE,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAC;4BACrF,MAAM,EAAE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAC,CAAC,CAAC;qBAC3D;iBACJ;aACJ;YAED,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,EAAE;gBACpB,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,CAAC;aAClC;iBAAM;gBACH,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACrC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,MAAM,CAAC;aACjC;YAED,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAC1B,CAAC;QAED,iCAAO,GAAP;YACI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QAClC,CAAC;QAED,+BAAK,GAAL,UAAM,OAAO;YACT,IAAI,CAAC,mBAAmB,GAAG,IAAI,IAAI,CAAC,8BAA8B,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,eAAe,EAAE,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;YAErH,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,KAAK,IAAM,QAAQ,IAAI,IAAI,CAAC,YAAY,EAAE;oBACtC,iDAAiD;oBACjD,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;wBAC5C,IAAI,MAAM,GAAG,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;wBACzC,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,EAAE;4BACzC,MAAM,GAAG,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC,CAAC;yBAClE;wBACD,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;qBACvF;iBACJ;aACJ;YAED,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;YAErC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;gBACtB,IAAI,YAAY,SAAA,CAAC;gBACjB,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,mBAAmB,CAAC,MAAM,EAAE,CAAC,CAAC;gBAE3E,IAAI,IAAI,CAAC,YAAY,EAAE;oBACnB,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC;iBACpC;qBAAM,IAAI,IAAI,CAAC,kBAAkB,EAAE;oBAChC,YAAY,GAAG,IAAI,CAAC,kBAAkB,CAAC;iBAC1C;gBACD,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC;gBAEjC,IAAI,CAAC,SAAS,GAAG,gBAAgB,CAAC;aACrC;YAED,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAC9B,CAAC;QACL,sBAAC;IAAD,CAAC,AAlJD,IAkJC;IAED,OAAO,eAAe,CAAC;AAC3B,CAAC;AAtJD,4BAsJC", "sourcesContent": ["export default function (environment) {\n    class SourceMapOutput {\n        constructor(options) {\n            this._css = [];\n            this._rootNode = options.rootNode;\n            this._contentsMap = options.contentsMap;\n            this._contentsIgnoredCharsMap = options.contentsIgnoredCharsMap;\n            if (options.sourceMapFilename) {\n                this._sourceMapFilename = options.sourceMapFilename.replace(/\\\\/g, '/');\n            }\n            this._outputFilename = options.outputFilename;\n            this.sourceMapURL = options.sourceMapURL;\n            if (options.sourceMapBasepath) {\n                this._sourceMapBasepath = options.sourceMapBasepath.replace(/\\\\/g, '/');\n            }\n            if (options.sourceMapRootpath) {\n                this._sourceMapRootpath = options.sourceMapRootpath.replace(/\\\\/g, '/');\n                if (this._sourceMapRootpath.charAt(this._sourceMapRootpath.length - 1) !== '/') {\n                    this._sourceMapRootpath += '/';\n                }\n            } else {\n                this._sourceMapRootpath = '';\n            }\n            this._outputSourceFiles = options.outputSourceFiles;\n            this._sourceMapGeneratorConstructor = environment.getSourceMapGenerator();\n\n            this._lineNumber = 0;\n            this._column = 0;\n        }\n\n        removeBasepath(path) {\n            if (this._sourceMapBasepath && path.indexOf(this._sourceMapBasepath) === 0) {\n                path = path.substring(this._sourceMapBasepath.length);\n                if (path.charAt(0) === '\\\\' || path.charAt(0) === '/') {\n                    path = path.substring(1);\n                }\n            }\n\n            return path;\n        }\n\n        normalizeFilename(filename) {\n            filename = filename.replace(/\\\\/g, '/');\n            filename = this.removeBasepath(filename);\n            return (this._sourceMapRootpath || '') + filename;\n        }\n\n        add(chunk, fileInfo, index, mapLines) {\n\n            // ignore adding empty strings\n            if (!chunk) {\n                return;\n            }\n\n            let lines, sourceLines, columns, sourceColumns, i;\n\n            if (fileInfo && fileInfo.filename) {\n                let inputSource = this._contentsMap[fileInfo.filename];\n\n                // remove vars/banner added to the top of the file\n                if (this._contentsIgnoredCharsMap[fileInfo.filename]) {\n                    // adjust the index\n                    index -= this._contentsIgnoredCharsMap[fileInfo.filename];\n                    if (index < 0) { index = 0; }\n                    // adjust the source\n                    inputSource = inputSource.slice(this._contentsIgnoredCharsMap[fileInfo.filename]);\n                }\n\n                /** \n                 * ignore empty content, or failsafe\n                 * if contents map is incorrect\n                 */\n                if (inputSource === undefined) {\n                    this._css.push(chunk);\n                    return;\n                }\n\n                inputSource = inputSource.substring(0, index);\n                sourceLines = inputSource.split('\\n');\n                sourceColumns = sourceLines[sourceLines.length - 1];\n            }\n\n            lines = chunk.split('\\n');\n            columns = lines[lines.length - 1];\n\n            if (fileInfo && fileInfo.filename) {\n                if (!mapLines) {\n                    this._sourceMapGenerator.addMapping({ generated: { line: this._lineNumber + 1, column: this._column},\n                        original: { line: sourceLines.length, column: sourceColumns.length},\n                        source: this.normalizeFilename(fileInfo.filename)});\n                } else {\n                    for (i = 0; i < lines.length; i++) {\n                        this._sourceMapGenerator.addMapping({ generated: { line: this._lineNumber + i + 1, column: i === 0 ? this._column : 0},\n                            original: { line: sourceLines.length + i, column: i === 0 ? sourceColumns.length : 0},\n                            source: this.normalizeFilename(fileInfo.filename)});\n                    }\n                }\n            }\n\n            if (lines.length === 1) {\n                this._column += columns.length;\n            } else {\n                this._lineNumber += lines.length - 1;\n                this._column = columns.length;\n            }\n\n            this._css.push(chunk);\n        }\n\n        isEmpty() {\n            return this._css.length === 0;\n        }\n\n        toCSS(context) {\n            this._sourceMapGenerator = new this._sourceMapGeneratorConstructor({ file: this._outputFilename, sourceRoot: null });\n\n            if (this._outputSourceFiles) {\n                for (const filename in this._contentsMap) {\n                    // eslint-disable-next-line no-prototype-builtins\n                    if (this._contentsMap.hasOwnProperty(filename)) {\n                        let source = this._contentsMap[filename];\n                        if (this._contentsIgnoredCharsMap[filename]) {\n                            source = source.slice(this._contentsIgnoredCharsMap[filename]);\n                        }\n                        this._sourceMapGenerator.setSourceContent(this.normalizeFilename(filename), source);\n                    }\n                }\n            }\n\n            this._rootNode.genCSS(context, this);\n\n            if (this._css.length > 0) {\n                let sourceMapURL;\n                const sourceMapContent = JSON.stringify(this._sourceMapGenerator.toJSON());\n\n                if (this.sourceMapURL) {\n                    sourceMapURL = this.sourceMapURL;\n                } else if (this._sourceMapFilename) {\n                    sourceMapURL = this._sourceMapFilename;\n                }\n                this.sourceMapURL = sourceMapURL;\n\n                this.sourceMap = sourceMapContent;\n            }\n\n            return this._css.join('');\n        }\n    }\n\n    return SourceMapOutput;\n}\n"]}