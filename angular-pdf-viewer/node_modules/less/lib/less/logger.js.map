{"version": 3, "file": "logger.js", "sourceRoot": "", "sources": ["../../src/less/logger.js"], "names": [], "mappings": ";;AAAA,kBAAe;IACX,KAAK,EAAE,UAAS,GAAG;QACf,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IACD,IAAI,EAAE,UAAS,GAAG;QACd,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,IAAI,EAAE,UAAS,GAAG;QACd,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,CAAC,CAAC;IACjC,CAAC;IACD,KAAK,EAAE,UAAS,GAAG;QACf,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;IAClC,CAAC;IACD,WAAW,EAAE,UAAS,QAAQ;QAC1B,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACnC,CAAC;IACD,cAAc,EAAE,UAAS,QAAQ;QAC7B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAI,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;gBACjC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBAC7B,OAAO;aACV;SACJ;IACL,CAAC;IACD,UAAU,EAAE,UAAS,IAAI,EAAE,GAAG;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC7C,IAAM,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;YAC7C,IAAI,WAAW,EAAE;gBACb,WAAW,CAAC,GAAG,CAAC,CAAC;aACpB;SACJ;IACL,CAAC;IACD,UAAU,EAAE,EAAE;CACjB,CAAC", "sourcesContent": ["export default {\n    error: function(msg) {\n        this._fireEvent('error', msg);\n    },\n    warn: function(msg) {\n        this._fireEvent('warn', msg);\n    },\n    info: function(msg) {\n        this._fireEvent('info', msg);\n    },\n    debug: function(msg) {\n        this._fireEvent('debug', msg);\n    },\n    addListener: function(listener) {\n        this._listeners.push(listener);\n    },\n    removeListener: function(listener) {\n        for (let i = 0; i < this._listeners.length; i++) {\n            if (this._listeners[i] === listener) {\n                this._listeners.splice(i, 1);\n                return;\n            }\n        }\n    },\n    _fireEvent: function(type, msg) {\n        for (let i = 0; i < this._listeners.length; i++) {\n            const logFunction = this._listeners[i][type];\n            if (logFunction) {\n                logFunction(msg);\n            }\n        }\n    },\n    _listeners: []\n};\n"]}