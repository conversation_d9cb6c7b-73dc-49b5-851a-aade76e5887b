{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../src/less/utils.js"], "names": [], "mappings": ";;;;AAAA,wBAAwB;AACxB,6DAAyC;AACzC,+CAAqC;AAErC,SAAgB,WAAW,CAAC,KAAK,EAAE,WAAW;IAC1C,IAAI,CAAC,GAAG,KAAK,GAAG,CAAC,CAAC;IAClB,IAAI,IAAI,GAAG,IAAI,CAAC;IAChB,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC;IAEhB,OAAO,EAAE,CAAC,IAAI,CAAC,IAAI,WAAW,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,IAAI,EAAE;QAC/C,MAAM,EAAE,CAAC;KACZ;IAED,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE;QAC3B,IAAI,GAAG,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC;KAClE;IAED,OAAO;QACH,IAAI,MAAA;QACJ,MAAM,QAAA;KACT,CAAC;AACN,CAAC;AAjBD,kCAiBC;AAED,SAAgB,SAAS,CAAC,GAAG;IACzB,IAAI,CAAC,CAAC;IACN,IAAM,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC;IAC1B,IAAM,IAAI,GAAG,IAAI,KAAK,CAAC,MAAM,CAAC,CAAC;IAE/B,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE;QACzB,IAAI,CAAC,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;KACpB;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AATD,8BASC;AAED,SAAgB,KAAK,CAAC,GAAG;IACrB,IAAM,MAAM,GAAG,EAAE,CAAC;IAClB,KAAK,IAAM,IAAI,IAAI,GAAG,EAAE;QACpB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,EAAE;YACjD,MAAM,CAAC,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,CAAC;SAC5B;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AARD,sBAQC;AAED,SAAgB,QAAQ,CAAC,IAAI,EAAE,IAAI;IAC/B,IAAI,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;QACjB,MAAM,GAAG,EAAE,CAAC;QACZ,IAAM,UAAQ,GAAG,oBAAI,CAAC,IAAI,CAAC,CAAC;QAC5B,MAAM,CAAC,SAAS,GAAG,UAAQ,CAAC;QAC5B,IAAM,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,oBAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;QACtC,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,UAAQ,EAAE,MAAM,CAAC,CAAC;KAC3C;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAVD,4BAUC;AAED,SAAgB,WAAW,CAAC,IAAI,EAAE,IAAI;IAClC,IAAI,IAAI,IAAI,IAAI,CAAC,SAAS,EAAE;QACxB,OAAO,IAAI,CAAC;KACf;IACD,IAAM,IAAI,GAAG,QAAQ,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAClC,IAAI,IAAI,CAAC,UAAU,EAAE;QACjB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;KACrC;IACD,+CAA+C;IAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;KAChD;IACD,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,EAAE;QAC/B,QAAQ,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE;YAC7B,KAAK,QAAQ;gBACT,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;gBAClC,MAAM;YACV,KAAK,iBAAiB;gBAClB,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC3C,MAAM;YACV,KAAK,QAAQ,CAAC;YACd,KAAK,QAAQ;gBACT,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;gBAClC,MAAM;YACV;gBACI,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC;SACzC;KACJ;IACD,IAAI,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ,EAAE;QACtC,QAAQ,IAAI,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE;YACpC,KAAK,KAAK;gBACN,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;gBAC7C,MAAM;YACV,KAAK,OAAO;gBACR,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,KAAK,CAAC;gBAC/C,MAAM;YACV,KAAK,KAAK;gBACN,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC;gBAC7C,MAAM;SACb;KACJ;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AA1CD,kCA0CC;AAED,SAAgB,KAAK,CAAC,IAAI,EAAE,IAAI;IAC5B,KAAK,IAAM,IAAI,IAAI,IAAI,EAAE;QACrB,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;YAClD,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;SAC3B;KACJ;IACD,OAAO,IAAI,CAAC;AAChB,CAAC;AAPD,sBAOC;AAED,SAAgB,YAAY,CAAC,GAAG,EAAE,MAAW;IAAX,uBAAA,EAAA,WAAW;IACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,QAAM,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,QAAM,EAAE,CAAC,EAAE,EAAE;QAClD,IAAM,KAAK,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACrB,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACtB,YAAY,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;SAC/B;aAAM;YACH,IAAI,KAAK,KAAK,SAAS,EAAE;gBACrB,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aACtB;SACJ;KACJ;IACD,OAAO,MAAM,CAAC;AAClB,CAAC;AAZD,oCAYC;AAED,SAAgB,iBAAiB,CAAC,GAAG;IACjC,OAAO,GAAG,KAAK,IAAI,IAAI,GAAG,KAAK,SAAS,CAAA;AAC5C,CAAC;AAFD,8CAEC", "sourcesContent": ["/* jshint proto: true */\nimport * as Constants from './constants';\nimport { copy } from 'copy-anything';\n\nexport function getLocation(index, inputStream) {\n    let n = index + 1;\n    let line = null;\n    let column = -1;\n\n    while (--n >= 0 && inputStream.charAt(n) !== '\\n') {\n        column++;\n    }\n\n    if (typeof index === 'number') {\n        line = (inputStream.slice(0, index).match(/\\n/g) || '').length;\n    }\n\n    return {\n        line,\n        column\n    };\n}\n\nexport function copyArray(arr) {\n    let i;\n    const length = arr.length;\n    const copy = new Array(length);\n\n    for (i = 0; i < length; i++) {\n        copy[i] = arr[i];\n    }\n    return copy;\n}\n\nexport function clone(obj) {\n    const cloned = {};\n    for (const prop in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, prop)) {\n            cloned[prop] = obj[prop];\n        }\n    }\n    return cloned;\n}\n\nexport function defaults(obj1, obj2) {\n    let newObj = obj2 || {};\n    if (!obj2._defaults) {\n        newObj = {};\n        const defaults = copy(obj1);\n        newObj._defaults = defaults;\n        const cloned = obj2 ? copy(obj2) : {};\n        Object.assign(newObj, defaults, cloned);\n    }\n    return newObj;\n}\n\nexport function copyOptions(obj1, obj2) {\n    if (obj2 && obj2._defaults) {\n        return obj2;\n    }\n    const opts = defaults(obj1, obj2);\n    if (opts.strictMath) {\n        opts.math = Constants.Math.PARENS;\n    }\n    // Back compat with changed relativeUrls option\n    if (opts.relativeUrls) {\n        opts.rewriteUrls = Constants.RewriteUrls.ALL;\n    }\n    if (typeof opts.math === 'string') {\n        switch (opts.math.toLowerCase()) {\n            case 'always':\n                opts.math = Constants.Math.ALWAYS;\n                break;\n            case 'parens-division':\n                opts.math = Constants.Math.PARENS_DIVISION;\n                break;\n            case 'strict':\n            case 'parens':\n                opts.math = Constants.Math.PARENS;\n                break;\n            default:\n                opts.math = Constants.Math.PARENS;\n        }\n    }\n    if (typeof opts.rewriteUrls === 'string') {\n        switch (opts.rewriteUrls.toLowerCase()) {\n            case 'off':\n                opts.rewriteUrls = Constants.RewriteUrls.OFF;\n                break;\n            case 'local':\n                opts.rewriteUrls = Constants.RewriteUrls.LOCAL;\n                break;\n            case 'all':\n                opts.rewriteUrls = Constants.RewriteUrls.ALL;\n                break;\n        }\n    }\n    return opts;\n}\n\nexport function merge(obj1, obj2) {\n    for (const prop in obj2) {\n        if (Object.prototype.hasOwnProperty.call(obj2, prop)) {\n            obj1[prop] = obj2[prop];\n        }\n    }\n    return obj1;\n}\n\nexport function flattenArray(arr, result = []) {\n    for (let i = 0, length = arr.length; i < length; i++) {\n        const value = arr[i];\n        if (Array.isArray(value)) {\n            flattenArray(value, result);\n        } else {\n            if (value !== undefined) {\n                result.push(value);\n            }\n        }\n    }\n    return result;\n}\n\nexport function isNullOrUndefined(val) {\n    return val === null || val === undefined\n}"]}