{"version": 3, "file": "svg.js", "sourceRoot": "", "sources": ["../../../src/less/functions/svg.js"], "names": [], "mappings": ";;;AAAA,wEAA0C;AAC1C,gEAAkC;AAClC,0EAA4C;AAC5C,kEAAoC;AACpC,4DAA8B;AAE9B,mBAAe;IACX,OAAO,EAAE,cAAc,EAAE,UAAS,SAAS;YACvC,IAAI,KAAK,CAAC;YACV,IAAI,oBAAoB,CAAC;YACzB,IAAI,YAAY,GAAG,QAAQ,CAAC;YAC5B,IAAI,kBAAkB,GAAG,kCAAkC,CAAC;YAC5D,IAAM,SAAS,GAAG,EAAC,QAAQ,EAAE,KAAK,EAAC,CAAC;YACpC,IAAI,QAAQ,CAAC;YACb,IAAM,cAAc,GAAG,SAAS,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAClD,IAAI,CAAC,CAAC;YACN,IAAI,KAAK,CAAC;YACV,IAAI,QAAQ,CAAC;YACb,IAAI,aAAa,CAAC;YAClB,IAAI,KAAK,CAAC;YAEV,SAAS,uBAAuB;gBAC5B,MAAM,EAAE,IAAI,EAAE,UAAU;oBACpB,OAAO,EAAE,qFAAqF;wBAClF,oDAAoD,EAAE,CAAC;YAC3E,CAAC;YAED,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,EAAE;gBACvB,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE;oBAC/B,uBAAuB,EAAE,CAAC;iBAC7B;gBACD,KAAK,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC;aAC9B;iBAAM,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC7B,uBAAuB,EAAE,CAAC;aAC7B;iBAAM;gBACH,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC;aACpD;YAED,QAAQ,cAAc,EAAE;gBACpB,KAAK,WAAW;oBACZ,oBAAoB,GAAG,mCAAmC,CAAC;oBAC3D,MAAM;gBACV,KAAK,UAAU;oBACX,oBAAoB,GAAG,mCAAmC,CAAC;oBAC3D,MAAM;gBACV,KAAK,iBAAiB;oBAClB,oBAAoB,GAAG,qCAAqC,CAAC;oBAC7D,MAAM;gBACV,KAAK,cAAc;oBACf,oBAAoB,GAAG,qCAAqC,CAAC;oBAC7D,MAAM;gBACV,KAAK,SAAS,CAAC;gBACf,KAAK,mBAAmB;oBACpB,YAAY,GAAG,QAAQ,CAAC;oBACxB,oBAAoB,GAAG,2BAA2B,CAAC;oBACnD,kBAAkB,GAAG,0CAA0C,CAAC;oBAChE,MAAM;gBACV;oBACI,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,6DAA6D;4BAC5F,iEAAiE,EAAE,CAAC;aAC/E;YACD,QAAQ,GAAG,oEAA8D,YAAY,0BAAmB,oBAAoB,MAAG,CAAC;YAEhI,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE;gBAClC,IAAI,KAAK,CAAC,CAAC,CAAC,YAAY,oBAAU,EAAE;oBAChC,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;oBAC1B,QAAQ,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;iBAChC;qBAAM;oBACH,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;oBACjB,QAAQ,GAAG,SAAS,CAAC;iBACxB;gBAED,IAAI,CAAC,CAAC,KAAK,YAAY,eAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,MAAM,CAAC,IAAI,QAAQ,KAAK,SAAS,CAAC,IAAI,CAAC,CAAC,QAAQ,YAAY,mBAAS,CAAC,CAAC,EAAE;oBACrI,uBAAuB,EAAE,CAAC;iBAC7B;gBACD,aAAa,GAAG,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC;gBAC/E,KAAK,GAAG,KAAK,CAAC,KAAK,CAAC;gBACpB,QAAQ,IAAI,oBAAiB,aAAa,wBAAiB,KAAK,CAAC,KAAK,EAAE,WAAI,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,qBAAkB,KAAK,OAAG,CAAC,CAAC,CAAC,EAAE,QAAI,CAAC;aAC/H;YACD,QAAQ,IAAI,OAAK,YAAY,uBAAkB,kBAAkB,+BAA0B,CAAC;YAE5F,QAAQ,GAAG,kBAAkB,CAAC,QAAQ,CAAC,CAAC;YAExC,QAAQ,GAAG,wBAAsB,QAAU,CAAC;YAC5C,OAAO,IAAI,aAAG,CAAC,IAAI,gBAAM,CAAC,MAAI,QAAQ,MAAG,EAAE,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,CAAC,CAAC;QACrI,CAAC,EAAC,CAAC;AACP,CAAC,EAAC", "sourcesContent": ["import Dimension from '../tree/dimension';\nimport Color from '../tree/color';\nimport Expression from '../tree/expression';\nimport Quoted from '../tree/quoted';\nimport URL from '../tree/url';\n\nexport default () => {\n    return { 'svg-gradient': function(direction) {\n        let stops;\n        let gradientDirectionSvg;\n        let gradientType = 'linear';\n        let rectangleDimension = 'x=\"0\" y=\"0\" width=\"1\" height=\"1\"';\n        const renderEnv = {compress: false};\n        let returner;\n        const directionValue = direction.toCSS(renderEnv);\n        let i;\n        let color;\n        let position;\n        let positionValue;\n        let alpha;\n\n        function throwArgumentDescriptor() {\n            throw { type: 'Argument',\n                message: 'svg-gradient expects direction, start_color [start_position], [color position,]...,' +\n                            ' end_color [end_position] or direction, color list' };\n        }\n\n        if (arguments.length == 2) {\n            if (arguments[1].value.length < 2) {\n                throwArgumentDescriptor();\n            }\n            stops = arguments[1].value;\n        } else if (arguments.length < 3) {\n            throwArgumentDescriptor();\n        } else {\n            stops = Array.prototype.slice.call(arguments, 1);\n        }\n\n        switch (directionValue) {\n            case 'to bottom':\n                gradientDirectionSvg = 'x1=\"0%\" y1=\"0%\" x2=\"0%\" y2=\"100%\"';\n                break;\n            case 'to right':\n                gradientDirectionSvg = 'x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\"';\n                break;\n            case 'to bottom right':\n                gradientDirectionSvg = 'x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\"';\n                break;\n            case 'to top right':\n                gradientDirectionSvg = 'x1=\"0%\" y1=\"100%\" x2=\"100%\" y2=\"0%\"';\n                break;\n            case 'ellipse':\n            case 'ellipse at center':\n                gradientType = 'radial';\n                gradientDirectionSvg = 'cx=\"50%\" cy=\"50%\" r=\"75%\"';\n                rectangleDimension = 'x=\"-50\" y=\"-50\" width=\"101\" height=\"101\"';\n                break;\n            default:\n                throw { type: 'Argument', message: 'svg-gradient direction must be \\'to bottom\\', \\'to right\\',' +\n                    ' \\'to bottom right\\', \\'to top right\\' or \\'ellipse at center\\'' };\n        }\n        returner = `<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 1 1\"><${gradientType}Gradient id=\"g\" ${gradientDirectionSvg}>`;\n\n        for (i = 0; i < stops.length; i += 1) {\n            if (stops[i] instanceof Expression) {\n                color = stops[i].value[0];\n                position = stops[i].value[1];\n            } else {\n                color = stops[i];\n                position = undefined;\n            }\n\n            if (!(color instanceof Color) || (!((i === 0 || i + 1 === stops.length) && position === undefined) && !(position instanceof Dimension))) {\n                throwArgumentDescriptor();\n            }\n            positionValue = position ? position.toCSS(renderEnv) : i === 0 ? '0%' : '100%';\n            alpha = color.alpha;\n            returner += `<stop offset=\"${positionValue}\" stop-color=\"${color.toRGB()}\"${alpha < 1 ? ` stop-opacity=\"${alpha}\"` : ''}/>`;\n        }\n        returner += `</${gradientType}Gradient><rect ${rectangleDimension} fill=\"url(#g)\" /></svg>`;\n\n        returner = encodeURIComponent(returner);\n\n        returner = `data:image/svg+xml,${returner}`;\n        return new URL(new Quoted(`'${returner}'`, returner, false, this.index, this.currentFileInfo), this.index, this.currentFileInfo);\n    }};\n};\n"]}