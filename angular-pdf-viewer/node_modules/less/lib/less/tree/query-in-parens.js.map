{"version": 3, "file": "query-in-parens.js", "sourceRoot": "", "sources": ["../../../src/less/tree/query-in-parens.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAE1B,IAAM,aAAa,GAAG,UAAU,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC;IAC/C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IACpB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC;IACnC,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;IAChB,IAAI,CAAC,MAAM,GAAG,CAAC,CAAC;AACpB,CAAC,CAAC;AAEF,aAAa,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAChD,IAAI,EAAE,eAAe;IAErB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;SAC5C;IACL,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACxC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3C;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,CAAC;QAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC;YACjC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;SACvC;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,aAAa,CAAC", "sourcesContent": ["import Node from './node';\n\nconst QueryInParens = function (op, l, m, op2, r, i) {\n    this.op = op.trim();\n    this.lvalue = l;\n    this.mvalue = m;\n    this.op2 = op2 ? op2.trim() : null;\n    this.rvalue = r;\n    this._index = i;\n};\n\nQueryInParens.prototype = Object.assign(new Node(), {\n    type: 'QueryInParens',\n\n    accept(visitor) {\n        this.lvalue = visitor.visit(this.lvalue);\n        this.mvalue = visitor.visit(this.mvalue);\n        if (this.rvalue) {\n            this.rvalue = visitor.visit(this.rvalue);\n        }\n    },\n\n    eval(context) {\n        this.lvalue = this.lvalue.eval(context);\n        this.mvalue = this.mvalue.eval(context);\n        if (this.rvalue) {\n            this.rvalue = this.rvalue.eval(context);\n        }\n        return this;\n    },\n\n    genCSS(context, output) {\n        this.lvalue.genCSS(context, output);\n        output.add(' ' + this.op + ' ');\n        this.mvalue.genCSS(context, output);\n        if (this.rvalue) {\n            output.add(' ' + this.op2 + ' ');\n            this.rvalue.genCSS(context, output);\n        }\n    },\n});\n\nexport default QueryInParens;\n"]}