{"version": 3, "file": "import.js", "sourceRoot": "", "sources": ["../../../src/less/tree/import.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,0DAA4B;AAC5B,sDAAwB;AACxB,4DAA8B;AAC9B,8DAAgC;AAChC,kEAAoC;AACpC,sDAAkC;AAClC,qEAAsC;AAEtC,EAAE;AACF,mBAAmB;AACnB,EAAE;AACF,0DAA0D;AAC1D,6DAA6D;AAC7D,wDAAwD;AACxD,oEAAoE;AACpE,EAAE;AACF,mEAAmE;AACnE,mEAAmE;AACnE,yCAAyC;AACzC,EAAE;AACF,IAAM,MAAM,GAAG,UAAS,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE,eAAe,EAAE,cAAc;IACnF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IACvB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;IACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC;IAEtB,IAAI,IAAI,CAAC,OAAO,CAAC,IAAI,KAAK,SAAS,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;QACxD,IAAI,CAAC,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;KACxD;SAAM;QACH,IAAM,SAAS,GAAG,IAAI,CAAC,OAAO,EAAE,CAAC;QACjC,IAAI,SAAS,IAAI,qBAAqB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACpD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC;SACnB;KACJ;IACD,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,CAAC;IACxC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;AACpC,CAAC,CAAC;AAEF,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACzC,IAAI,EAAE,QAAQ;IAEd,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;SAChD;QACD,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACrC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAC7D,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SACxC;IACL,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,KAAK,SAAS,EAAE;YACzD,MAAM,CAAC,GAAG,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YACpD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YAClC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;aACzC;YACD,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;IACL,CAAC;IAED,OAAO;QACH,OAAO,CAAC,IAAI,CAAC,IAAI,YAAY,aAAG,CAAC,CAAC,CAAC;YAC/B,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC;IAChD,CAAC;IAED,gBAAgB;QACZ,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QACrB,IAAI,IAAI,YAAY,aAAG,EAAE;YACrB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;SACrB;QACD,IAAI,IAAI,YAAY,gBAAM,EAAE;YACxB,OAAO,IAAI,CAAC,iBAAiB,EAAE,CAAC;SACnC;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,aAAa,YAAC,OAAO;QACjB,IAAI,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC;QAErB,IAAI,IAAI,YAAY,aAAG,EAAE;YACrB,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC;SACrB;QAED,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,cAAc,EAAE,CAAC,CAAC;IAC3H,CAAC;IAED,QAAQ,YAAC,OAAO;QACZ,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QACrC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC;QAEhC,IAAI,CAAC,CAAC,IAAI,YAAY,aAAG,CAAC,EAAE;YACxB,iDAAiD;YACjD,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC;YAC7B,IAAI,QAAQ;gBACR,SAAS;gBACT,OAAO,CAAC,mBAAmB,CAAC,SAAS,CAAC,EAAE;gBACxC,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,WAAW,CAAC,SAAS,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;aAClE;iBAAM;gBACH,IAAI,CAAC,KAAK,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAClD;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAM,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACpC,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,IAAI,CAAC,gBAAgB,EAAE,EAAE;YACnD,IAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,MAAM,KAAK,CAAC,EAAE;gBACtC,MAAM,CAAC,OAAO,CAAC,UAAU,IAAI;oBACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;gBAC9B,CAAC,CACA,CAAC;aACL;iBAAM;gBACH,MAAM,CAAC,kBAAkB,EAAE,CAAC;aAC/B;SACJ;QACD,OAAO,MAAM,CAAC;IAClB,CAAC;IAED,MAAM,YAAC,OAAO;QACV,IAAI,OAAO,CAAC;QACZ,IAAI,QAAQ,CAAC;QACb,IAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAE9D,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAC7B,IAAI;oBACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;iBAC3B;gBACD,OAAO,CAAC,EAAE;oBACN,CAAC,CAAC,OAAO,GAAG,gCAAgC,CAAC;oBAC7C,MAAM,IAAI,oBAAS,CAAC,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACjE;aACJ;YACD,QAAQ,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,IAAI,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,gBAAgB,CAAC;YACnE,IAAK,QAAQ,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAG;gBAChD,QAAQ,CAAC,WAAW,CAAE,IAAI,CAAC,IAAI,CAAC,SAAS,CAAE,CAAC;aAC/C;YAED,OAAO,EAAE,CAAC;SACb;QAED,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,UAAU,EAAE;gBACjC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;aAC3B;YACD,IAAI,IAAI,CAAC,IAAI,EAAE;gBACX,OAAO,EAAE,CAAC;aACb;SACJ;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACrB,IAAM,QAAQ,GAAG,IAAI,mBAAS,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,EACvC;gBACI,QAAQ,EAAE,IAAI,CAAC,gBAAgB;gBAC/B,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS;aAClE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;YAEnB,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAK,CAAC,CAAC,QAAQ,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC;SAClF;aAAM,IAAI,IAAI,CAAC,GAAG,EAAE;YACjB,IAAM,SAAS,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1F,IAAI,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,CAAC,KAAK,EAAE;gBAC9B,MAAM,IAAI,CAAC,KAAK,CAAC;aACpB;YACD,OAAO,SAAS,CAAC;SACpB;aAAM,IAAI,IAAI,CAAC,IAAI,EAAE;YAClB,OAAO,GAAG,IAAI,iBAAO,CAAC,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;YAC9D,OAAO,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YAE7B,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,eAAK,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,KAAK,CAAC;SACxF;aAAM;YACH,OAAO,EAAE,CAAC;SACb;IACL,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,MAAM,CAAC", "sourcesContent": ["import Node from './node';\nimport Media from './media';\nimport URL from './url';\nimport Quoted from './quoted';\nimport Ruleset from './ruleset';\nimport Anonymous from './anonymous';\nimport * as utils from '../utils';\nimport LessError from '../less-error';\n\n//\n// CSS @import node\n//\n// The general strategy here is that we don't want to wait\n// for the parsing to be completed, before we start importing\n// the file. That's because in the context of a browser,\n// most of the time will be spent waiting for the server to respond.\n//\n// On creation, we push the import path to our import queue, though\n// `import,push`, we also pass it a callback, which it'll call once\n// the file has been fetched, and parsed.\n//\nconst Import = function(path, features, options, index, currentFileInfo, visibilityInfo) {\n    this.options = options;\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n    this.path = path;\n    this.features = features;\n    this.allowRoot = true;\n\n    if (this.options.less !== undefined || this.options.inline) {\n        this.css = !this.options.less || this.options.inline;\n    } else {\n        const pathValue = this.getPath();\n        if (pathValue && /[#.&?]css([?;].*)?$/.test(pathValue)) {\n            this.css = true;\n        }\n    }\n    this.copyVisibilityInfo(visibilityInfo);\n    this.setParent(this.features, this);\n    this.setParent(this.path, this);\n};\n\nImport.prototype = Object.assign(new Node(), {\n    type: 'Import',\n\n    accept(visitor) {\n        if (this.features) {\n            this.features = visitor.visit(this.features);\n        }\n        this.path = visitor.visit(this.path);\n        if (!this.options.isPlugin && !this.options.inline && this.root) {\n            this.root = visitor.visit(this.root);\n        }\n    },\n\n    genCSS(context, output) {\n        if (this.css && this.path._fileInfo.reference === undefined) {\n            output.add('@import ', this._fileInfo, this._index);\n            this.path.genCSS(context, output);\n            if (this.features) {\n                output.add(' ');\n                this.features.genCSS(context, output);\n            }\n            output.add(';');\n        }\n    },\n\n    getPath() {\n        return (this.path instanceof URL) ?\n            this.path.value.value : this.path.value;\n    },\n\n    isVariableImport() {\n        let path = this.path;\n        if (path instanceof URL) {\n            path = path.value;\n        }\n        if (path instanceof Quoted) {\n            return path.containsVariables();\n        }\n\n        return true;\n    },\n\n    evalForImport(context) {\n        let path = this.path;\n\n        if (path instanceof URL) {\n            path = path.value;\n        }\n\n        return new Import(path.eval(context), this.features, this.options, this._index, this._fileInfo, this.visibilityInfo());\n    },\n\n    evalPath(context) {\n        const path = this.path.eval(context);\n        const fileInfo = this._fileInfo;\n\n        if (!(path instanceof URL)) {\n            // Add the rootpath if the URL requires a rewrite\n            const pathValue = path.value;\n            if (fileInfo &&\n                pathValue &&\n                context.pathRequiresRewrite(pathValue)) {\n                path.value = context.rewritePath(pathValue, fileInfo.rootpath);\n            } else {\n                path.value = context.normalizePath(path.value);\n            }\n        }\n\n        return path;\n    },\n\n    eval(context) {\n        const result = this.doEval(context);\n        if (this.options.reference || this.blocksVisibility()) {\n            if (result.length || result.length === 0) {\n                result.forEach(function (node) {\n                    node.addVisibilityBlock();\n                }\n                );\n            } else {\n                result.addVisibilityBlock();\n            }\n        }\n        return result;\n    },\n\n    doEval(context) {\n        let ruleset;\n        let registry;\n        const features = this.features && this.features.eval(context);\n\n        if (this.options.isPlugin) {\n            if (this.root && this.root.eval) {\n                try {\n                    this.root.eval(context);\n                }\n                catch (e) {\n                    e.message = 'Plugin error during evaluation';\n                    throw new LessError(e, this.root.imports, this.root.filename);\n                }\n            }\n            registry = context.frames[0] && context.frames[0].functionRegistry;\n            if ( registry && this.root && this.root.functions ) {\n                registry.addMultiple( this.root.functions );\n            }\n\n            return [];\n        }\n\n        if (this.skip) {\n            if (typeof this.skip === 'function') {\n                this.skip = this.skip();\n            }\n            if (this.skip) {\n                return [];\n            }\n        }\n        if (this.options.inline) {\n            const contents = new Anonymous(this.root, 0,\n                {\n                    filename: this.importedFilename,\n                    reference: this.path._fileInfo && this.path._fileInfo.reference\n                }, true, true);\n\n            return this.features ? new Media([contents], this.features.value) : [contents];\n        } else if (this.css) {\n            const newImport = new Import(this.evalPath(context), features, this.options, this._index);\n            if (!newImport.css && this.error) {\n                throw this.error;\n            }\n            return newImport;\n        } else if (this.root) {\n            ruleset = new Ruleset(null, utils.copyArray(this.root.rules));\n            ruleset.evalImports(context);\n\n            return this.features ? new Media(ruleset.rules, this.features.value) : ruleset.rules;\n        } else {\n            return [];\n        }\n    }\n});\n\nexport default Import;\n"]}