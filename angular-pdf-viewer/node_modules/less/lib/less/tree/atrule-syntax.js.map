{"version": 3, "file": "atrule-syntax.js", "sourceRoot": "", "sources": ["../../../src/less/tree/atrule-syntax.js"], "names": [], "mappings": ";;;AAAa,QAAA,kBAAkB,GAAG;IAC9B,aAAa,EAAE,IAAI;CACtB,CAAC;AAEW,QAAA,sBAAsB,GAAG;IAClC,aAAa,EAAE,IAAI;CACtB,CAAC", "sourcesContent": ["export const MediaSyntaxOptions = {\n    queryInParens: true\n};\n\nexport const ContainerSyntaxOptions = {\n    queryInParens: true\n};\n"]}