{"version": 3, "file": "call.js", "sourceRoot": "", "sources": ["../../../src/less/tree/call.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,kEAAoC;AACpC,yFAA0D;AAE1D,EAAE;AACF,wBAAwB;AACxB,EAAE;AACF,IAAM,IAAI,GAAG,UAAS,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,eAAe;IACpD,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACjB,IAAI,CAAC,IAAI,GAAG,IAAI,KAAK,MAAM,CAAC;IAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAAC,SAAS,GAAG,eAAe,CAAC;AACrC,CAAC,CAAA;AAED,IAAI,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IACvC,IAAI,EAAE,MAAM;IAEZ,MAAM,YAAC,OAAO;QACV,IAAI,IAAI,CAAC,IAAI,EAAE;YACX,IAAI,CAAC,IAAI,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;SAC7C;IACL,CAAC;IAED,EAAE;IACF,mCAAmC;IACnC,uDAAuD;IACvD,8DAA8D;IAC9D,0DAA0D;IAC1D,qDAAqD;IACrD,EAAE;IACF,iEAAiE;IACjE,qEAAqE;IACrE,2DAA2D;IAC3D,EAAE;IACF,IAAI,YAAC,OAAO;QAAZ,iBA6DC;QA5DG;;WAEG;QACH,IAAM,kBAAkB,GAAG,OAAO,CAAC,MAAM,CAAC;QAC1C,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5B,IAAI,IAAI,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;YAC7B,OAAO,CAAC,SAAS,EAAE,CAAC;SACvB;QAED,IAAM,QAAQ,GAAG;YACb,IAAI,KAAI,CAAC,IAAI,IAAI,OAAO,CAAC,MAAM,EAAE;gBAC7B,OAAO,CAAC,QAAQ,EAAE,CAAC;aACtB;YACD,OAAO,CAAC,MAAM,GAAG,kBAAkB,CAAC;QACxC,CAAC,CAAC;QAEF,IAAI,MAAM,CAAC;QACX,IAAM,UAAU,GAAG,IAAI,yBAAc,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE5F,IAAI,UAAU,CAAC,OAAO,EAAE,EAAE;YACtB,IAAI;gBACA,MAAM,GAAG,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBACpC,QAAQ,EAAE,CAAC;aACd;YAAC,OAAO,CAAC,EAAE;gBACR,iDAAiD;gBACjD,IAAI,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE;oBACxD,MAAM,CAAC,CAAC;iBACX;gBACD,MAAM;oBACF,IAAI,EAAE,CAAC,CAAC,IAAI,IAAI,SAAS;oBACzB,OAAO,EAAE,gCAA+B,IAAI,CAAC,IAAI,UAAK,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,OAAK,CAAC,CAAC,OAAS,CAAC,CAAC,CAAC,EAAE,CAAE;oBACzF,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;oBACtB,QAAQ,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,QAAQ;oBAClC,IAAI,EAAE,CAAC,CAAC,UAAU;oBAClB,MAAM,EAAE,CAAC,CAAC,YAAY;iBACzB,CAAC;aACL;SACJ;QAED,IAAI,MAAM,KAAK,IAAI,IAAI,MAAM,KAAK,SAAS,EAAE;YACzC,8DAA8D;YAC9D,uDAAuD;YACvD,IAAI,CAAC,CAAC,MAAM,YAAY,cAAI,CAAC,EAAE;gBAC3B,IAAI,CAAC,MAAM,IAAI,MAAM,KAAK,IAAI,EAAE;oBAC5B,MAAM,GAAG,IAAI,mBAAS,CAAC,IAAI,CAAC,CAAC;iBAChC;qBACI;oBACD,MAAM,GAAG,IAAI,mBAAS,CAAC,MAAM,CAAC,QAAQ,EAAE,CAAC,CAAC;iBAC7C;aAEJ;YACD,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC;YAC5B,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC;YAClC,OAAO,MAAM,CAAC;SACjB;QAED,IAAM,IAAI,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAf,CAAe,CAAC,CAAC;QACjD,QAAQ,EAAE,CAAC;QAEX,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;IACvE,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,MAAM,CAAC,GAAG,CAAI,IAAI,CAAC,IAAI,MAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,CAAC;QAE9D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACvC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACrC,IAAI,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBAC1B,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;aACpB;SACJ;QAED,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACpB,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,IAAI,CAAC", "sourcesContent": ["import Node from './node';\nimport Anonymous from './anonymous';\nimport FunctionCaller from '../functions/function-caller';\n\n//\n// A function call node.\n//\nconst Call = function(name, args, index, currentFileInfo) {\n    this.name = name;\n    this.args = args;\n    this.calc = name === 'calc';\n    this._index = index;\n    this._fileInfo = currentFileInfo;\n}\n\nCall.prototype = Object.assign(new Node(), {\n    type: 'Call',\n\n    accept(visitor) {\n        if (this.args) {\n            this.args = visitor.visitArray(this.args);\n        }\n    },\n\n    //\n    // When evaluating a function call,\n    // we either find the function in the functionRegistry,\n    // in which case we call it, passing the  evaluated arguments,\n    // if this returns null or we cannot find the function, we\n    // simply print it out as it appeared originally [2].\n    //\n    // The reason why we evaluate the arguments, is in the case where\n    // we try to pass a variable to a function, like: `saturate(@color)`.\n    // The function should receive the value, not the variable.\n    //\n    eval(context) {\n        /**\n         * Turn off math for calc(), and switch back on for evaluating nested functions\n         */\n        const currentMathContext = context.mathOn;\n        context.mathOn = !this.calc;\n        if (this.calc || context.inCalc) {\n            context.enterCalc();\n        }\n\n        const exitCalc = () => {\n            if (this.calc || context.inCalc) {\n                context.exitCalc();\n            }\n            context.mathOn = currentMathContext;\n        };\n\n        let result;\n        const funcCaller = new FunctionCaller(this.name, context, this.getIndex(), this.fileInfo());\n\n        if (funcCaller.isValid()) {\n            try {\n                result = funcCaller.call(this.args);\n                exitCalc();\n            } catch (e) {\n                // eslint-disable-next-line no-prototype-builtins\n                if (e.hasOwnProperty('line') && e.hasOwnProperty('column')) {\n                    throw e;\n                }\n                throw { \n                    type: e.type || 'Runtime',\n                    message: `Error evaluating function \\`${this.name}\\`${e.message ? `: ${e.message}` : ''}`,\n                    index: this.getIndex(), \n                    filename: this.fileInfo().filename,\n                    line: e.lineNumber,\n                    column: e.columnNumber\n                };\n            }\n        }\n\n        if (result !== null && result !== undefined) {\n            // Results that that are not nodes are cast as Anonymous nodes\n            // Falsy values or booleans are returned as empty nodes\n            if (!(result instanceof Node)) {\n                if (!result || result === true) {\n                    result = new Anonymous(null); \n                }\n                else {\n                    result = new Anonymous(result.toString()); \n                }\n                \n            }\n            result._index = this._index;\n            result._fileInfo = this._fileInfo;\n            return result;\n        }\n\n        const args = this.args.map(a => a.eval(context));\n        exitCalc();\n\n        return new Call(this.name, args, this.getIndex(), this.fileInfo());\n    },\n\n    genCSS(context, output) {\n        output.add(`${this.name}(`, this.fileInfo(), this.getIndex());\n\n        for (let i = 0; i < this.args.length; i++) {\n            this.args[i].genCSS(context, output);\n            if (i + 1 < this.args.length) {\n                output.add(', ');\n            }\n        }\n\n        output.add(')');\n    }\n});\n\nexport default Call;\n"]}