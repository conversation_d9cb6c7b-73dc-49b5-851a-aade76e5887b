{"version": 3, "file": "operation.js", "sourceRoot": "", "sources": ["../../../src/less/tree/operation.js"], "names": [], "mappings": ";;;AAAA,wDAA0B;AAC1B,0DAA4B;AAC5B,kEAAoC;AACpC,8DAA0C;AAC1C,IAAM,IAAI,GAAG,SAAS,CAAC,IAAI,CAAC;AAG5B,IAAM,SAAS,GAAG,UAAS,EAAE,EAAE,QAAQ,EAAE,QAAQ;IAC7C,IAAI,CAAC,EAAE,GAAG,EAAE,CAAC,IAAI,EAAE,CAAC;IACpB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;IACzB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;AAC7B,CAAC,CAAC;AAEF,SAAS,CAAC,SAAS,GAAG,MAAM,CAAC,MAAM,CAAC,IAAI,cAAI,EAAE,EAAE;IAC5C,IAAI,EAAE,WAAW;IAEjB,MAAM,YAAC,OAAO;QACV,IAAI,CAAC,QAAQ,GAAG,OAAO,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,YAAC,OAAO;QACR,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE,CAAC;QAE/E,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAC3B,EAAE,GAAG,IAAI,CAAC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;YACtC,IAAI,CAAC,YAAY,mBAAS,IAAI,CAAC,YAAY,eAAK,EAAE;gBAC9C,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,YAAY,mBAAS,IAAI,CAAC,YAAY,eAAK,EAAE;gBAC9C,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAC;aACnB;YACD,IAAI,CAAC,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,CAAC,OAAO,EAAE;gBAC1B,IACI,CAAC,CAAC,YAAY,SAAS,IAAI,CAAC,YAAY,SAAS,CAAC;uBAC/C,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,CAAC,eAAe,EAC1D;oBACE,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;iBACxD;gBACD,MAAM,EAAE,IAAI,EAAE,WAAW;oBACrB,OAAO,EAAE,8BAA8B,EAAE,CAAC;aACjD;YAED,OAAO,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE,EAAE,CAAC,CAAC,CAAC;SACpC;aAAM;YACH,OAAO,IAAI,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;SACxD;IACL,CAAC;IAED,MAAM,YAAC,OAAO,EAAE,MAAM;QAClB,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;QACD,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;SACnB;QACD,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IAC7C,CAAC;CACJ,CAAC,CAAC;AAEH,kBAAe,SAAS,CAAC", "sourcesContent": ["import Node from './node';\nimport Color from './color';\nimport Dimension from './dimension';\nimport * as Constants from '../constants';\nconst MATH = Constants.Math;\n\n\nconst Operation = function(op, operands, isSpaced) {\n    this.op = op.trim();\n    this.operands = operands;\n    this.isSpaced = isSpaced;\n};\n\nOperation.prototype = Object.assign(new Node(), {\n    type: 'Operation',\n\n    accept(visitor) {\n        this.operands = visitor.visitArray(this.operands);\n    },\n\n    eval(context) {\n        let a = this.operands[0].eval(context), b = this.operands[1].eval(context), op;\n\n        if (context.isMathOn(this.op)) {\n            op = this.op === './' ? '/' : this.op;\n            if (a instanceof Dimension && b instanceof Color) {\n                a = a.toColor();\n            }\n            if (b instanceof Dimension && a instanceof Color) {\n                b = b.toColor();\n            }\n            if (!a.operate || !b.operate) {\n                if (\n                    (a instanceof Operation || b instanceof Operation)\n                    && a.op === '/' && context.math === MATH.PARENS_DIVISION\n                ) {\n                    return new Operation(this.op, [a, b], this.isSpaced);\n                }\n                throw { type: 'Operation',\n                    message: 'Operation on an invalid type' };\n            }\n\n            return a.operate(context, op, b);\n        } else {\n            return new Operation(this.op, [a, b], this.isSpaced);\n        }\n    },\n\n    genCSS(context, output) {\n        this.operands[0].genCSS(context, output);\n        if (this.isSpaced) {\n            output.add(' ');\n        }\n        output.add(this.op);\n        if (this.isSpaced) {\n            output.add(' ');\n        }\n        this.operands[1].genCSS(context, output);\n    }\n});\n\nexport default Operation;\n"]}