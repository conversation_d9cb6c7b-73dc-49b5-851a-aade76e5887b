{"version": 3, "file": "plugin-manager.js", "sourceRoot": "", "sources": ["../../src/less/plugin-manager.js"], "names": [], "mappings": ";;AAAA;;GAEG;AACH;IACI,uBAAY,IAAI;QACZ,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC;QACnB,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;QACxB,IAAI,CAAC,cAAc,GAAG,EAAE,CAAC;QACzB,IAAI,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG,EAAE,CAAC;QACvB,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;QACnB,IAAI,CAAC,WAAW,GAAG,EAAE,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC;IAC9C,CAAC;IAED;;;OAGG;IACH,kCAAU,GAAV,UAAW,OAAO;QACd,IAAI,OAAO,EAAE;YACT,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACrC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;aAC9B;SACJ;IACL,CAAC;IAED;;;;OAIG;IACH,iCAAS,GAAT,UAAU,MAAM,EAAE,QAAQ,EAAE,gBAAgB;QACxC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACnC,IAAI,QAAQ,EAAE;YACV,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC;SACvC;QACD,IAAI,MAAM,CAAC,OAAO,EAAE;YAChB,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,gBAAgB,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,CAAC;SAC7F;IACL,CAAC;IAED;;;OAGG;IACH,2BAAG,GAAH,UAAI,QAAQ;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACtC,CAAC;IAED;;;;OAIG;IACH,kCAAU,GAAV,UAAW,OAAO;QACd,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED;;;;OAIG;IACH,uCAAe,GAAf,UAAgB,YAAY,EAAE,QAAQ;QAClC,IAAI,eAAe,CAAC;QACpB,KAAK,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE;YACtF,IAAI,IAAI,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC,QAAQ,IAAI,QAAQ,EAAE;gBAC1D,MAAM;aACT;SACJ;QACD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAC,YAAY,cAAA,EAAE,QAAQ,UAAA,EAAC,CAAC,CAAC;IAC5E,CAAC;IAED;;;;OAIG;IACH,wCAAgB,GAAhB,UAAiB,aAAa,EAAE,QAAQ;QACpC,IAAI,eAAe,CAAC;QACpB,KAAK,eAAe,GAAG,CAAC,EAAE,eAAe,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,eAAe,EAAE,EAAE;YACvF,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,CAAC,QAAQ,IAAI,QAAQ,EAAE;gBAC3D,MAAM;aACT;SACJ;QACD,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,EAAE,EAAC,aAAa,eAAA,EAAE,QAAQ,UAAA,EAAC,CAAC,CAAC;IAC9E,CAAC;IAED;;;OAGG;IACH,sCAAc,GAAd,UAAe,OAAO;QAClB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IACpC,CAAC;IAED;;;;OAIG;IACH,wCAAgB,GAAhB;QACI,IAAM,aAAa,GAAG,EAAE,CAAC;QACzB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAChD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC;SAC1D;QACD,OAAO,aAAa,CAAC;IACzB,CAAC;IAED;;;;OAIG;IACH,yCAAiB,GAAjB;QACI,IAAM,cAAc,GAAG,EAAE,CAAC;QAC1B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACjD,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC;SAC7D;QACD,OAAO,cAAc,CAAC;IAC1B,CAAC;IAED;;;;OAIG;IACH,mCAAW,GAAX;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB,CAAC;IAED,+BAAO,GAAP;QACI,IAAM,IAAI,GAAG,IAAI,CAAC;QAClB,OAAO;YACH,KAAK,EAAE;gBACH,IAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAC;gBACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;YACD,GAAG,EAAE;gBACD,IAAI,CAAC,QAAQ,IAAI,CAAC,CAAC;gBACnB,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACxC,CAAC;SACJ,CAAC;IACN,CAAC;IAED;;;;OAIG;IACH,uCAAe,GAAf;QACI,OAAO,IAAI,CAAC,YAAY,CAAC;IAC7B,CAAC;IACL,oBAAC;AAAD,CAAC,AAxJD,IAwJC;AAED,IAAI,EAAE,CAAC;AAEP,IAAM,oBAAoB,GAAG,UAAS,IAAI,EAAE,UAAU;IAClD,IAAI,UAAU,IAAI,CAAC,EAAE,EAAE;QACnB,EAAE,GAAG,IAAI,aAAa,CAAC,IAAI,CAAC,CAAC;KAChC;IACD,OAAO,EAAE,CAAC;AACd,CAAC,CAAC;AAEF,EAAE;AACF,kBAAe,oBAAoB,CAAC", "sourcesContent": ["/**\n * Plugin Manager\n */\nclass PluginManager {\n    constructor(less) {\n        this.less = less;\n        this.visitors = [];\n        this.preProcessors = [];\n        this.postProcessors = [];\n        this.installedPlugins = [];\n        this.fileManagers = [];\n        this.iterator = -1;\n        this.pluginCache = {};\n        this.Loader = new less.PluginLoader(less);\n    }\n\n    /**\n     * Adds all the plugins in the array\n     * @param {Array} plugins\n     */\n    addPlugins(plugins) {\n        if (plugins) {\n            for (let i = 0; i < plugins.length; i++) {\n                this.addPlugin(plugins[i]);\n            }\n        }\n    }\n\n    /**\n     *\n     * @param plugin\n     * @param {String} filename\n     */\n    addPlugin(plugin, filename, functionRegistry) {\n        this.installedPlugins.push(plugin);\n        if (filename) {\n            this.pluginCache[filename] = plugin;\n        }\n        if (plugin.install) {\n            plugin.install(this.less, this, functionRegistry || this.less.functions.functionRegistry);\n        }\n    }\n\n    /**\n     *\n     * @param filename\n     */\n    get(filename) {\n        return this.pluginCache[filename];\n    }\n\n    /**\n     * Adds a visitor. The visitor object has options on itself to determine\n     * when it should run.\n     * @param visitor\n     */\n    addVisitor(visitor) {\n        this.visitors.push(visitor);\n    }\n\n    /**\n     * Adds a pre processor object\n     * @param {object} preProcessor\n     * @param {number} priority - guidelines 1 = before import, 1000 = import, 2000 = after import\n     */\n    addPreProcessor(preProcessor, priority) {\n        let indexToInsertAt;\n        for (indexToInsertAt = 0; indexToInsertAt < this.preProcessors.length; indexToInsertAt++) {\n            if (this.preProcessors[indexToInsertAt].priority >= priority) {\n                break;\n            }\n        }\n        this.preProcessors.splice(indexToInsertAt, 0, {preProcessor, priority});\n    }\n\n    /**\n     * Adds a post processor object\n     * @param {object} postProcessor\n     * @param {number} priority - guidelines 1 = before compression, 1000 = compression, 2000 = after compression\n     */\n    addPostProcessor(postProcessor, priority) {\n        let indexToInsertAt;\n        for (indexToInsertAt = 0; indexToInsertAt < this.postProcessors.length; indexToInsertAt++) {\n            if (this.postProcessors[indexToInsertAt].priority >= priority) {\n                break;\n            }\n        }\n        this.postProcessors.splice(indexToInsertAt, 0, {postProcessor, priority});\n    }\n\n    /**\n     *\n     * @param manager\n     */\n    addFileManager(manager) {\n        this.fileManagers.push(manager);\n    }\n\n    /**\n     *\n     * @returns {Array}\n     * @private\n     */\n    getPreProcessors() {\n        const preProcessors = [];\n        for (let i = 0; i < this.preProcessors.length; i++) {\n            preProcessors.push(this.preProcessors[i].preProcessor);\n        }\n        return preProcessors;\n    }\n\n    /**\n     *\n     * @returns {Array}\n     * @private\n     */\n    getPostProcessors() {\n        const postProcessors = [];\n        for (let i = 0; i < this.postProcessors.length; i++) {\n            postProcessors.push(this.postProcessors[i].postProcessor);\n        }\n        return postProcessors;\n    }\n\n    /**\n     *\n     * @returns {Array}\n     * @private\n     */\n    getVisitors() {\n        return this.visitors;\n    }\n\n    visitor() {\n        const self = this;\n        return {\n            first: function() {\n                self.iterator = -1;\n                return self.visitors[self.iterator];\n            },\n            get: function() {\n                self.iterator += 1;\n                return self.visitors[self.iterator];\n            }\n        };\n    }\n\n    /**\n     *\n     * @returns {Array}\n     * @private\n     */\n    getFileManagers() {\n        return this.fileManagers;\n    }\n}\n\nlet pm;\n\nconst PluginManagerFactory = function(less, newFactory) {\n    if (newFactory || !pm) {\n        pm = new PluginManager(less);\n    }\n    return pm;\n};\n\n//\nexport default PluginManagerFactory;\n"]}