@import "http://localhost:8081/test/browser/less/imports/modify-this.css";
@import "http://localhost:8081/test/browser/less/imports/modify-again.css";
.modify {
  my-url: url("http://localhost:8081/test/browser/less/imports/a.png");
}
.modify {
  my-url: url("http://localhost:8081/test/browser/less/imports/b.png");
}
@font-face {
  src: url("/fonts/garamond-pro.ttf");
  src: local(Futura-Medium), url(http://localhost:8081/test/browser/less/relative-urls/fonts.svg#MyGeometricModern) format("svg");
}
#shorthands {
  background: url("http://www.lesscss.org/spec.html") no-repeat 0 4px;
}
#misc {
  background-image: url(http://localhost:8081/test/browser/less/relative-urls/images/image.jpg);
  background: url("#inline-svg");
}
#data-uri {
  background: url(data:image/png;charset=utf-8;base64,
    kiVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAABlBMVEUAAAD/
    k//+l2Z/dAAAAM0lEQVR4nGP4/5/h/1+G/58ZDrAz3D/McH8yw83NDDeNGe4U
    kg9C9zwz3gVLMDA/A6P9/AFGGFyjOXZtQAAAAAElFTkSuQmCC);
  background-image: url(data:image/x-png,f9difSSFIIGFIFJD1f982FSDKAA9==);
  background-image: url(http://fonts.googleapis.com/css?family=\"Rokkitt\":\(400\),700);
}
#svg-data-uri {
  background: transparent url('data:image/svg+xml, <svg version="1.1"><g></g></svg>');
}
.comma-delimited {
  background: url(http://localhost:8081/test/browser/less/relative-urls/bg.jpg) no-repeat, url(http://localhost:8081/test/browser/less/relative-urls/bg.png) repeat-x top left, url(http://localhost:8081/test/browser/less/relative-urls/bg);
}
.values {
  url: url('http://localhost:8081/test/browser/less/relative-urls/Trebuchet');
}
