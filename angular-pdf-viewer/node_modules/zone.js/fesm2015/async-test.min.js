"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */const global$1=globalThis;function __symbol__(e){return(global$1.__Zone_symbol_prefix||"__zone_symbol__")+e}const __global="undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global;class AsyncTestZoneSpec{static get symbolParentUnresolved(){return __symbol__("parentUnresolved")}constructor(e,n,s){this.finishCallback=e,this.failCallback=n,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+s,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===__global[__symbol__("supportWaitUnResolvedChainedPromise")]}isUnresolvedChainedPromisePending(){return this.unresolvedChainedPromiseCount>0}_finishCallbackIfDone(){null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((()=>{this._existingFinishTimer=setTimeout((()=>{this._alreadyErrored||this._pendingMicroTasks||this._pendingMacroTasks||this.finishCallback()}),0)}))}patchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}unPatchPromiseForTest(){if(!this.supportWaitUnresolvedChainedPromise)return;const e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}onScheduleTask(e,n,s,t){return"eventTask"!==t.type&&(this._isSync=!1),"microTask"===t.type&&t.data&&t.data instanceof Promise&&!0===t.data[AsyncTestZoneSpec.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,e.scheduleTask(s,t)}onInvokeTask(e,n,s,t,i,o){return"eventTask"!==t.type&&(this._isSync=!1),e.invokeTask(s,t,i,o)}onCancelTask(e,n,s,t){return"eventTask"!==t.type&&(this._isSync=!1),e.cancelTask(s,t)}onInvoke(e,n,s,t,i,o,r){this.entryFunction||(this.entryFunction=t);try{return this._isSync=!0,e.invoke(s,t,i,o,r)}finally{this._isSync&&this.entryFunction===t&&this._finishCallbackIfDone()}}onHandleError(e,n,s,t){return e.handleError(s,t)&&(this.failCallback(t),this._alreadyErrored=!0),!1}onHasTask(e,n,s,t){e.hasTask(s,t),n===s&&("microTask"==t.change?(this._pendingMicroTasks=t.microTask,this._finishCallbackIfDone()):"macroTask"==t.change&&(this._pendingMacroTasks=t.macroTask,this._finishCallbackIfDone()))}}function patchAsyncTest(e){e.AsyncTestZoneSpec=AsyncTestZoneSpec,e.__load_patch("asynctest",((e,n,s)=>{function t(e,s,t,i){const o=n.current,r=n.AsyncTestZoneSpec;if(void 0===r)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");const a=n.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");const c=a.get();a.assertPresent();const h=n.current.getZoneWith("ProxyZoneSpec"),l=c.getDelegate();return h.parent.run((()=>{const e=new r((()=>{c.getDelegate()==e&&c.setDelegate(l),e.unPatchPromiseForTest(),o.run((()=>{t()}))}),(n=>{c.getDelegate()==e&&c.setDelegate(l),e.unPatchPromiseForTest(),o.run((()=>{i(n)}))}),"test");c.setDelegate(e),e.patchPromiseForTest()})),n.current.runGuarded(e,s)}n[s.symbol("asyncTest")]=function n(s){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),t(s,this,e,(n=>{if("string"==typeof n)return e.fail(new Error(n));e.fail(n)}))}:function(){return new Promise(((e,n)=>{t(s,this,e,n)}))}}}))}patchAsyncTest(Zone);