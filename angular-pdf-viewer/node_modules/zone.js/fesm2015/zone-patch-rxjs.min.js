"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */import{Observable,Subscription,Subscriber}from"rxjs";function patchRxJs(e){e.__load_patch("rxjs",((e,t,r)=>{const n=t.__symbol__,o=Object.defineProperties;r.patchMethod(Observable.prototype,"lift",(e=>(n,o)=>{const i=e.apply(n,o);return i.operator&&(i.operator._zone=t.current,r.patchMethod(i.operator,"call",(e=>(r,n)=>r._zone&&r._zone!==t.current?r._zone.run(e,r,n):e.apply(r,n)))),i})),function(){const e=Observable.prototype,r=e[n("_subscribe")]=e._subscribe;o(Observable.prototype,{_zone:{value:null,writable:!0,configurable:!0},_zoneSource:{value:null,writable:!0,configurable:!0},_zoneSubscribe:{value:null,writable:!0,configurable:!0},source:{configurable:!0,get:function(){return this._zoneSource},set:function(e){this._zone=t.current,this._zoneSource=e}},_subscribe:{configurable:!0,get:function(){if(this._zoneSubscribe)return this._zoneSubscribe;if(this.constructor===Observable)return r;const e=Object.getPrototypeOf(this);return e&&e._subscribe},set:function(e){this._zone=t.current,this._zoneSubscribe=e?function(){if(this._zone&&this._zone!==t.current){const r=this._zone.run(e,this,arguments);if("function"==typeof r){const e=this._zone;return function(){return e!==t.current?e.run(r,this,arguments):r.apply(this,arguments)}}return r}return e.apply(this,arguments)}:e}},subjectFactory:{get:function(){return this._zoneSubjectFactory},set:function(e){const r=this._zone;this._zoneSubjectFactory=function(){return r&&r!==t.current?r.run(e,this,arguments):e.apply(this,arguments)}}}})}(),o(Subscription.prototype,{_zone:{value:null,writable:!0,configurable:!0},_zoneUnsubscribe:{value:null,writable:!0,configurable:!0},_unsubscribe:{get:function(){if(this._zoneUnsubscribe||this._zoneUnsubscribeCleared)return this._zoneUnsubscribe;const e=Object.getPrototypeOf(this);return e&&e._unsubscribe},set:function(e){this._zone=t.current,e?(this._zoneUnsubscribeCleared=!1,this._zoneUnsubscribe=function(){return this._zone&&this._zone!==t.current?this._zone.run(e,this,arguments):e.apply(this,arguments)}):(this._zoneUnsubscribe=e,this._zoneUnsubscribeCleared=!0)}}}),function(){const e=Subscriber.prototype.next,r=Subscriber.prototype.error,n=Subscriber.prototype.complete;Object.defineProperty(Subscriber.prototype,"destination",{configurable:!0,get:function(){return this._zoneDestination},set:function(e){this._zone=t.current,this._zoneDestination=e}}),Subscriber.prototype.next=function(){const r=this._zone;return r&&r!==t.current?r.run(e,this,arguments,"rxjs.Subscriber.next"):e.apply(this,arguments)},Subscriber.prototype.error=function(){const e=this._zone;return e&&e!==t.current?e.run(r,this,arguments,"rxjs.Subscriber.error"):r.apply(this,arguments)},Subscriber.prototype.complete=function(){const e=this._zone;return e&&e!==t.current?e.run(n,this,arguments,"rxjs.Subscriber.complete"):n.call(this)}}()}))}patchRxJs(Zone);