"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e=globalThis;function n(n){return(e.__Zone_symbol_prefix||"__zone_symbol__")+n}var t="undefined"!=typeof window&&window||"undefined"!=typeof self&&self||global,i=function(){function e(e,i,s){this.finishCallback=e,this.failCallback=i,this._pendingMicroTasks=!1,this._pendingMacroTasks=!1,this._alreadyErrored=!1,this._isSync=!1,this._existingFinishTimer=null,this.entryFunction=null,this.runZone=Zone.current,this.unresolvedChainedPromiseCount=0,this.supportWaitUnresolvedChainedPromise=!1,this.name="asyncTestZone for "+s,this.properties={AsyncTestZoneSpec:this},this.supportWaitUnresolvedChainedPromise=!0===t[n("supportWaitUnResolvedChainedPromise")]}return Object.defineProperty(e,"symbolParentUnresolved",{get:function(){return n("parentUnresolved")},enumerable:!1,configurable:!0}),e.prototype.isUnresolvedChainedPromisePending=function(){return this.unresolvedChainedPromiseCount>0},e.prototype._finishCallbackIfDone=function(){var e=this;null!==this._existingFinishTimer&&(clearTimeout(this._existingFinishTimer),this._existingFinishTimer=null),this._pendingMicroTasks||this._pendingMacroTasks||this.supportWaitUnresolvedChainedPromise&&this.isUnresolvedChainedPromisePending()||this.runZone.run((function(){e._existingFinishTimer=setTimeout((function(){e._alreadyErrored||e._pendingMicroTasks||e._pendingMacroTasks||e.finishCallback()}),0)}))},e.prototype.patchPromiseForTest=function(){if(this.supportWaitUnresolvedChainedPromise){var e=Promise[Zone.__symbol__("patchPromiseForTest")];e&&e()}},e.prototype.unPatchPromiseForTest=function(){if(this.supportWaitUnresolvedChainedPromise){var e=Promise[Zone.__symbol__("unPatchPromiseForTest")];e&&e()}},e.prototype.onScheduleTask=function(n,t,i,s){return"eventTask"!==s.type&&(this._isSync=!1),"microTask"===s.type&&s.data&&s.data instanceof Promise&&!0===s.data[e.symbolParentUnresolved]&&this.unresolvedChainedPromiseCount--,n.scheduleTask(i,s)},e.prototype.onInvokeTask=function(e,n,t,i,s,o){return"eventTask"!==i.type&&(this._isSync=!1),e.invokeTask(t,i,s,o)},e.prototype.onCancelTask=function(e,n,t,i){return"eventTask"!==i.type&&(this._isSync=!1),e.cancelTask(t,i)},e.prototype.onInvoke=function(e,n,t,i,s,o,r){this.entryFunction||(this.entryFunction=i);try{return this._isSync=!0,e.invoke(t,i,s,o,r)}finally{this._isSync&&this.entryFunction===i&&this._finishCallbackIfDone()}},e.prototype.onHandleError=function(e,n,t,i){return e.handleError(t,i)&&(this.failCallback(i),this._alreadyErrored=!0),!1},e.prototype.onHasTask=function(e,n,t,i){e.hasTask(t,i),n===t&&("microTask"==i.change?(this._pendingMicroTasks=i.microTask,this._finishCallbackIfDone()):"macroTask"==i.change&&(this._pendingMacroTasks=i.macroTask,this._finishCallbackIfDone()))},e}();!function s(e){e.AsyncTestZoneSpec=i,e.__load_patch("asynctest",(function(e,n,t){function i(e,t,i,s){var o=n.current,r=n.AsyncTestZoneSpec;if(void 0===r)throw new Error("AsyncTestZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/async-test");var a=n.ProxyZoneSpec;if(!a)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");var c=a.get();a.assertPresent();var u=n.current.getZoneWith("ProxyZoneSpec"),h=c.getDelegate();return u.parent.run((function(){var e=new r((function(){c.getDelegate()==e&&c.setDelegate(h),e.unPatchPromiseForTest(),o.run((function(){i()}))}),(function(n){c.getDelegate()==e&&c.setDelegate(h),e.unPatchPromiseForTest(),o.run((function(){s(n)}))}),"test");c.setDelegate(e),e.patchPromiseForTest()})),n.current.runGuarded(e,t)}n[t.symbol("asyncTest")]=function n(t){return e.jasmine?function(e){e||((e=function(){}).fail=function(e){throw e}),i(t,this,e,(function(n){if("string"==typeof n)return e.fail(new Error(n));e.fail(n)}))}:function(){var e=this;return new Promise((function(n,s){i(t,e,n,s)}))}}}))}(Zone)}));