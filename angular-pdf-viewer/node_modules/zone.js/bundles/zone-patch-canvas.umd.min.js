"use strict";
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(t){"function"==typeof define&&define.amd?define(t):t()}((function(){!function t(n){n.__load_patch("canvas",(function(t,n,o){var e=t.HTMLCanvasElement;void 0!==e&&e.prototype&&e.prototype.toBlob&&o.patchMacroTask(e.prototype,"toBlob",(function(t,n){return{name:"HTMLCanvasElement.toBlob",target:t,cbIdx:0,args:n}}))}))}(Zone)}));