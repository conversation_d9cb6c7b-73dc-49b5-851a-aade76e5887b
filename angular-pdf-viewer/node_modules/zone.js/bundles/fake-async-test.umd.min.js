"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var t,r=1,i=arguments.length;r<i;r++)for(var n in t=arguments[r])Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n]);return e},__assign.apply(this,arguments)},__spreadArray=this&&this.__spreadArray||function(e,t,r){if(r||2===arguments.length)for(var i,n=0,s=t.length;n<s;n++)!i&&n in t||(i||(i=Array.prototype.slice.call(t,0,n)),i[n]=t[n]);return e.concat(i||Array.prototype.slice.call(t))};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */
!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){var e,t,r="object"==typeof window&&window||"object"==typeof self&&self||globalThis.global,i=r.Date;function n(){if(0===arguments.length){var e=new i;return e.setTime(n.now()),e}var t=Array.prototype.slice.call(arguments);return new(i.bind.apply(i,__spreadArray([void 0],t,!1)))}n.now=function(){var e=Zone.current.get("FakeAsyncTestZoneSpec");return e?e.getFakeSystemTime():i.now.apply(this,arguments)},n.UTC=i.UTC,n.parse=i.parse;var s=function(){},o=function(){function n(){this._schedulerQueue=[],this._currentTickTime=0,this._currentFakeBaseSystemTime=i.now(),this._currentTickRequeuePeriodicEntries=[]}return n.getNextId=function(){var i=t.nativeSetTimeout.call(r,s,0);return t.nativeClearTimeout.call(r,i),"number"==typeof i?i:e.nextNodeJSId++},n.prototype.getCurrentTickTime=function(){return this._currentTickTime},n.prototype.getFakeSystemTime=function(){return this._currentFakeBaseSystemTime+this._currentTickTime},n.prototype.setFakeBaseSystemTime=function(e){this._currentFakeBaseSystemTime=e},n.prototype.getRealSystemTime=function(){return i.now()},n.prototype.scheduleFunction=function(t,r,i){var n=(i=__assign({args:[],isPeriodic:!1,isRequestAnimationFrame:!1,id:-1,isRequeuePeriodic:!1},i)).id<0?e.nextId:i.id;e.nextId=e.getNextId();var s={endTime:this._currentTickTime+r,id:n,func:t,args:i.args,delay:r,isPeriodic:i.isPeriodic,isRequestAnimationFrame:i.isRequestAnimationFrame};i.isRequeuePeriodic&&this._currentTickRequeuePeriodicEntries.push(s);for(var o=0;o<this._schedulerQueue.length&&!(s.endTime<this._schedulerQueue[o].endTime);o++);return this._schedulerQueue.splice(o,0,s),n},n.prototype.removeScheduledFunctionWithId=function(e){for(var t=0;t<this._schedulerQueue.length;t++)if(this._schedulerQueue[t].id==e){this._schedulerQueue.splice(t,1);break}},n.prototype.removeAll=function(){this._schedulerQueue=[]},n.prototype.getTimerCount=function(){return this._schedulerQueue.length},n.prototype.tickToNext=function(e,t,r){void 0===e&&(e=1),this._schedulerQueue.length<e||this.tick(this._schedulerQueue[e-1].endTime-this._currentTickTime,t,r)},n.prototype.tick=function(e,t,i){void 0===e&&(e=0);var n=this._currentTickTime+e,s=0,o=(i=Object.assign({processNewMacroTasksSynchronously:!0},i)).processNewMacroTasksSynchronously?this._schedulerQueue:this._schedulerQueue.slice();if(0===o.length&&t)t(e);else{for(;o.length>0&&(this._currentTickRequeuePeriodicEntries=[],!(n<o[0].endTime));){var a=o.shift();if(!i.processNewMacroTasksSynchronously){var c=this._schedulerQueue.indexOf(a);c>=0&&this._schedulerQueue.splice(c,1)}if(s=this._currentTickTime,this._currentTickTime=a.endTime,t&&t(this._currentTickTime-s),!a.func.apply(r,a.isRequestAnimationFrame?[this._currentTickTime]:a.args))break;i.processNewMacroTasksSynchronously||this._currentTickRequeuePeriodicEntries.forEach((function(e){for(var t=0;t<o.length&&!(e.endTime<o[t].endTime);t++);o.splice(t,0,e)}))}s=this._currentTickTime,this._currentTickTime=n,t&&t(this._currentTickTime-s)}},n.prototype.flushOnlyPendingTimers=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e,{processNewMacroTasksSynchronously:!1}),this._currentTickTime-t},n.prototype.flush=function(e,t,r){return void 0===e&&(e=20),void 0===t&&(t=!1),t?this.flushPeriodic(r):this.flushNonPeriodic(e,r)},n.prototype.flushPeriodic=function(e){if(0===this._schedulerQueue.length)return 0;var t=this._currentTickTime;return this.tick(this._schedulerQueue[this._schedulerQueue.length-1].endTime-t,e),this._currentTickTime-t},n.prototype.flushNonPeriodic=function(e,t){for(var i=this._currentTickTime,n=0,s=0;this._schedulerQueue.length>0;){if(++s>e)throw new Error("flush failed after reaching the limit of "+e+" tasks. Does your code use a polling timeout?");if(0===this._schedulerQueue.filter((function(e){return!e.isPeriodic&&!e.isRequestAnimationFrame})).length)break;var o=this._schedulerQueue.shift();if(n=this._currentTickTime,this._currentTickTime=o.endTime,t&&t(this._currentTickTime-n),!o.func.apply(r,o.args))break}return this._currentTickTime-i},n}();(e=o).nextNodeJSId=1,e.nextId=-1;var a=function(){function e(e,t,i){void 0===t&&(t=!1),this.trackPendingRequestAnimationFrame=t,this.macroTaskOptions=i,this._scheduler=new o,this._microtasks=[],this._lastError=null,this._uncaughtPromiseErrors=Promise[Zone.__symbol__("uncaughtPromiseErrors")],this.pendingPeriodicTimers=[],this.pendingTimers=[],this.patchDateLocked=!1,this.properties={FakeAsyncTestZoneSpec:this},this.name="fakeAsyncTestZone for "+e,this.macroTaskOptions||(this.macroTaskOptions=r[Zone.__symbol__("FakeAsyncTestMacroTask")])}return e.assertInZone=function(){if(null==Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("The code should be running in the fakeAsync zone to call this function")},e.prototype._fnAndFlush=function(e,t){var i=this;return function(){for(var n=[],s=0;s<arguments.length;s++)n[s]=arguments[s];return e.apply(r,n),null===i._lastError?(null!=t.onSuccess&&t.onSuccess.apply(r),i.flushMicrotasks()):null!=t.onError&&t.onError.apply(r),null===i._lastError}},e._removeTimer=function(e,t){var r=e.indexOf(t);r>-1&&e.splice(r,1)},e.prototype._dequeueTimer=function(t){var r=this;return function(){e._removeTimer(r.pendingTimers,t)}},e.prototype._requeuePeriodicTimer=function(e,t,r,i){var n=this;return function(){-1!==n.pendingPeriodicTimers.indexOf(i)&&n._scheduler.scheduleFunction(e,t,{args:r,isPeriodic:!0,id:i,isRequeuePeriodic:!0})}},e.prototype._dequeuePeriodicTimer=function(t){var r=this;return function(){e._removeTimer(r.pendingPeriodicTimers,t)}},e.prototype._setTimeout=function(e,t,r,i){void 0===i&&(i=!0);var n=this._dequeueTimer(o.nextId),s=this._fnAndFlush(e,{onSuccess:n,onError:n}),a=this._scheduler.scheduleFunction(s,t,{args:r,isRequestAnimationFrame:!i});return i&&this.pendingTimers.push(a),a},e.prototype._clearTimeout=function(t){e._removeTimer(this.pendingTimers,t),this._scheduler.removeScheduledFunctionWithId(t)},e.prototype._setInterval=function(e,t,r){var i=o.nextId,n={onSuccess:null,onError:this._dequeuePeriodicTimer(i)},s=this._fnAndFlush(e,n);return n.onSuccess=this._requeuePeriodicTimer(s,t,r,i),this._scheduler.scheduleFunction(s,t,{args:r,isPeriodic:!0}),this.pendingPeriodicTimers.push(i),i},e.prototype._clearInterval=function(t){e._removeTimer(this.pendingPeriodicTimers,t),this._scheduler.removeScheduledFunctionWithId(t)},e.prototype._resetLastErrorAndThrow=function(){var e=this._lastError||this._uncaughtPromiseErrors[0];throw this._uncaughtPromiseErrors.length=0,this._lastError=null,e},e.prototype.getCurrentTickTime=function(){return this._scheduler.getCurrentTickTime()},e.prototype.getFakeSystemTime=function(){return this._scheduler.getFakeSystemTime()},e.prototype.setFakeBaseSystemTime=function(e){this._scheduler.setFakeBaseSystemTime(e)},e.prototype.getRealSystemTime=function(){return this._scheduler.getRealSystemTime()},e.patchDate=function(){r[Zone.__symbol__("disableDatePatching")]||r.Date!==n&&(r.Date=n,n.prototype=i.prototype,e.checkTimerPatch())},e.resetDate=function(){r.Date===n&&(r.Date=i)},e.checkTimerPatch=function(){if(!t)throw new Error("Expected timers to have been patched.");r.setTimeout!==t.setTimeout&&(r.setTimeout=t.setTimeout,r.clearTimeout=t.clearTimeout),r.setInterval!==t.setInterval&&(r.setInterval=t.setInterval,r.clearInterval=t.clearInterval)},e.prototype.lockDatePatch=function(){this.patchDateLocked=!0,e.patchDate()},e.prototype.unlockDatePatch=function(){this.patchDateLocked=!1,e.resetDate()},e.prototype.tickToNext=function(t,r,i){void 0===t&&(t=1),void 0===i&&(i={processNewMacroTasksSynchronously:!0}),t<=0||(e.assertInZone(),this.flushMicrotasks(),this._scheduler.tickToNext(t,r,i),null!==this._lastError&&this._resetLastErrorAndThrow())},e.prototype.tick=function(t,r,i){void 0===t&&(t=0),void 0===i&&(i={processNewMacroTasksSynchronously:!0}),e.assertInZone(),this.flushMicrotasks(),this._scheduler.tick(t,r,i),null!==this._lastError&&this._resetLastErrorAndThrow()},e.prototype.flushMicrotasks=function(){var t=this;for(e.assertInZone();this._microtasks.length>0;){var r=this._microtasks.shift();r.func.apply(r.target,r.args)}(null!==t._lastError||t._uncaughtPromiseErrors.length)&&t._resetLastErrorAndThrow()},e.prototype.flush=function(t,r,i){e.assertInZone(),this.flushMicrotasks();var n=this._scheduler.flush(t,r,i);return null!==this._lastError&&this._resetLastErrorAndThrow(),n},e.prototype.flushOnlyPendingTimers=function(t){e.assertInZone(),this.flushMicrotasks();var r=this._scheduler.flushOnlyPendingTimers(t);return null!==this._lastError&&this._resetLastErrorAndThrow(),r},e.prototype.removeAllTimers=function(){e.assertInZone(),this._scheduler.removeAll(),this.pendingPeriodicTimers=[],this.pendingTimers=[]},e.prototype.getTimerCount=function(){return this._scheduler.getTimerCount()+this._microtasks.length},e.prototype.onScheduleTask=function(e,t,r,i){switch(i.type){case"microTask":var n=i.data&&i.data.args,s=void 0;if(n){var o=i.data.cbIdx;"number"==typeof n.length&&n.length>o+1&&(s=Array.prototype.slice.call(n,o+1))}this._microtasks.push({func:i.invoke,args:s,target:i.data&&i.data.target});break;case"macroTask":switch(i.source){case"setTimeout":i.data.handleId=this._setTimeout(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"setImmediate":i.data.handleId=this._setTimeout(i.invoke,0,Array.prototype.slice.call(i.data.args,1));break;case"setInterval":i.data.handleId=this._setInterval(i.invoke,i.data.delay,Array.prototype.slice.call(i.data.args,2));break;case"XMLHttpRequest.send":throw new Error("Cannot make XHRs from within a fake async test. Request URL: "+i.data.url);case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":i.data.handleId=this._setTimeout(i.invoke,16,i.data.args,this.trackPendingRequestAnimationFrame);break;default:var a=this.findMacroTaskOption(i);if(a){var c=i.data&&i.data.args,u=c&&c.length>1?c[1]:0,h=a.callbackArgs?a.callbackArgs:c;a.isPeriodic?(i.data.handleId=this._setInterval(i.invoke,u,h),i.data.isPeriodic=!0):i.data.handleId=this._setTimeout(i.invoke,u,h);break}throw new Error("Unknown macroTask scheduled in fake async test: "+i.source)}break;case"eventTask":i=e.scheduleTask(r,i)}return i},e.prototype.onCancelTask=function(e,t,r,i){switch(i.source){case"setTimeout":case"requestAnimationFrame":case"webkitRequestAnimationFrame":case"mozRequestAnimationFrame":return this._clearTimeout(i.data.handleId);case"setInterval":return this._clearInterval(i.data.handleId);default:var n=this.findMacroTaskOption(i);if(n){var s=i.data.handleId;return n.isPeriodic?this._clearInterval(s):this._clearTimeout(s)}return e.cancelTask(r,i)}},e.prototype.onInvoke=function(t,r,i,n,s,o,a){try{return e.patchDate(),t.invoke(i,n,s,o,a)}finally{this.patchDateLocked||e.resetDate()}},e.prototype.findMacroTaskOption=function(e){if(!this.macroTaskOptions)return null;for(var t=0;t<this.macroTaskOptions.length;t++){var r=this.macroTaskOptions[t];if(r.source===e.source)return r}return null},e.prototype.onHandleError=function(e,t,r,i){return this._lastError=i,!1},e}(),c=null;function u(){return Zone&&Zone.ProxyZoneSpec}function h(){c&&c.unlockDatePatch(),c=null,u()&&u().assertPresent().resetDelegate()}function l(e,t){void 0===t&&(t={});var r=t.flush,i=void 0!==r&&r,n=function(){for(var t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];var n=u();if(!n)throw new Error("ProxyZoneSpec is needed for the async() test helper but could not be found. Please make sure that your environment includes zone.js/plugins/proxy");var s=n.assertPresent();if(Zone.current.get("FakeAsyncTestZoneSpec"))throw new Error("fakeAsync() calls can not be nested");try{if(!c){var o=Zone&&Zone.FakeAsyncTestZoneSpec;if(s.getDelegate()instanceof o)throw new Error("fakeAsync() calls can not be nested");c=new o}var a=void 0,l=s.getDelegate();s.setDelegate(c),c.lockDatePatch();try{a=e.apply(this,t),i?c.flush(20,!0):T()}finally{s.setDelegate(l)}if(!i){if(c.pendingPeriodicTimers.length>0)throw new Error("".concat(c.pendingPeriodicTimers.length," ")+"periodic timer(s) still in the queue.");if(c.pendingTimers.length>0)throw new Error("".concat(c.pendingTimers.length," timer(s) still in the queue."))}return a}finally{h()}};return n.isFakeAsync=!0,n}function d(){if(null==c&&null==(c=Zone.current.get("FakeAsyncTestZoneSpec")))throw new Error("The code should be running in the fakeAsync zone to call this function");return c}function m(e,t){void 0===e&&(e=0),void 0===t&&(t=!1),d().tick(e,null,t)}function p(e){return d().flush(e)}function f(){d().pendingPeriodicTimers.length=0}function T(){d().flushMicrotasks()}!function _(e){e.FakeAsyncTestZoneSpec=a,e.__load_patch("fakeasync",(function(e,t,r){t[r.symbol("fakeAsyncTest")]={resetFakeAsyncZone:h,flushMicrotasks:T,discardPeriodicTasks:f,tick:m,flush:p,fakeAsync:l}}),!0),t={setTimeout:r.setTimeout,setInterval:r.setInterval,clearTimeout:r.clearTimeout,clearInterval:r.clearInterval,nativeSetTimeout:r[e.__symbol__("setTimeout")],nativeClearTimeout:r[e.__symbol__("clearTimeout")]},o.nextId=o.getNextId()}(Zone)}));