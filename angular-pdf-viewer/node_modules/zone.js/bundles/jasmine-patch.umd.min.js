"use strict";var __assign=this&&this.__assign||function(){return __assign=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e},__assign.apply(this,arguments)};
/**
 * @license Angular v<unknown>
 * (c) 2010-2024 Google LLC. https://angular.io/
 * License: MIT
 */!function(e){"function"==typeof define&&define.amd?define(e):e()}((function(){!function e(n){n.__load_patch("jasmine",(function(e,n,t){if(!n)throw new Error("Missing: zone.js");if("undefined"==typeof jest&&"undefined"!=typeof jasmine&&!jasmine.__zone_patch__){jasmine.__zone_patch__=!0;var r=n.SyncTestZoneSpec,o=n.ProxyZoneSpec;if(!r)throw new Error("Missing: SyncTestZoneSpec");if(!o)throw new Error("Missing: ProxyZoneSpec");var i=n.current,s=n.__symbol__,a=!0===e[s("fakeAsyncDisablePatchingClock")],c=!a&&(!0===e[s("fakeAsyncPatchLock")]||!0===e[s("fakeAsyncAutoFakeAsyncWhenClockPatched")]);if(!0!==e[s("ignoreUnhandledRejection")]){var u=jasmine.GlobalErrors;u&&!jasmine[s("GlobalErrors")]&&(jasmine[s("GlobalErrors")]=u,jasmine.GlobalErrors=function(){var n=new u,t=n.install;return t&&!n[s("install")]&&(n[s("install")]=t,n.install=function(){var n="undefined"!=typeof process&&!!process.on,r=n?process.listeners("unhandledRejection"):e.eventListeners("unhandledrejection"),o=t.apply(this,arguments);return n?process.removeAllListeners("unhandledRejection"):e.removeAllListeners("unhandledrejection"),r&&r.forEach((function(t){n?process.on("unhandledRejection",t):e.addEventListener("unhandledrejection",t)})),o}),n})}var l=jasmine.getEnv();if(["describe","xdescribe","fdescribe"].forEach((function(e){var n=l[e];l[e]=function(e,t){return n.call(this,e,function o(e,n){return function(){return i.fork(new r("jasmine.describe#".concat(e))).run(n,this,arguments)}}(e,t))}})),["it","xit","fit"].forEach((function(e){var n=l[e];l[s(e)]=n,l[e]=function(e,t,r){return arguments[1]=m(t),n.apply(this,arguments)}})),["beforeEach","afterEach","beforeAll","afterAll"].forEach((function(e){var n=l[e];l[s(e)]=n,l[e]=function(e,t){return arguments[0]=m(e),n.apply(this,arguments)}})),!a){var f=jasmine[s("clock")]=jasmine.clock;jasmine.clock=function(){var e=f.apply(this,arguments);if(!e[s("patched")]){e[s("patched")]=s("patched");var t=e[s("tick")]=e.tick;e.tick=function(){var e=n.current.get("FakeAsyncTestZoneSpec");return e?e.tick.apply(e,arguments):t.apply(this,arguments)};var r=e[s("mockDate")]=e.mockDate;e.mockDate=function(){var e=n.current.get("FakeAsyncTestZoneSpec");if(e){var t=arguments.length>0?arguments[0]:new Date;return e.setFakeBaseSystemTime.apply(e,t&&"function"==typeof t.getTime?[t.getTime()]:arguments)}return r.apply(this,arguments)},c&&["install","uninstall"].forEach((function(t){var r=e[s(t)]=e[t];e[t]=function(){if(!n.FakeAsyncTestZoneSpec)return r.apply(this,arguments);jasmine[s("clockInstalled")]="install"===t}}))}return e}}if(!jasmine[n.__symbol__("createSpyObj")]){var p=jasmine.createSpyObj;jasmine[n.__symbol__("createSpyObj")]=p,jasmine.createSpyObj=function(){var e,n=Array.prototype.slice.call(arguments);if(n.length>=3&&n[2]){var t=Object.defineProperty;Object.defineProperty=function(e,n,r){return t.call(this,e,n,__assign(__assign({},r),{configurable:!0,enumerable:!0}))};try{e=p.apply(this,n)}finally{Object.defineProperty=t}}else e=p.apply(this,n);return e}}var h=jasmine.QueueRunner;jasmine.QueueRunner=function(t){function r(r){var o,s=this;r.onComplete&&(r.onComplete=(o=r.onComplete,function(){s.testProxyZone=null,s.testProxyZoneSpec=null,i.scheduleMicroTask("jasmine.onComplete",o)}));var a=e[n.__symbol__("setTimeout")],c=e[n.__symbol__("clearTimeout")];a&&(r.timeout={setTimeout:a||e.setTimeout,clearTimeout:c||e.clearTimeout}),jasmine.UserContext?(r.userContext||(r.userContext=new jasmine.UserContext),r.userContext.queueRunner=this):(r.userContext||(r.userContext={}),r.userContext.queueRunner=this);var u=r.onException;r.onException=function(e){if(e&&"Timeout - Async callback was not invoked within timeout specified by jasmine.DEFAULT_TIMEOUT_INTERVAL."===e.message){var n=this&&this.testProxyZoneSpec;if(n){var t=n.getAndClearPendingTasksInfo();try{e.message+=t}catch(e){}}}u&&u.call(this,e)},t.call(this,r)}return function(e,n){for(var t in n)n.hasOwnProperty(t)&&(e[t]=n[t]);function r(){this.constructor=e}e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}(r,t),r.prototype.execute=function(){for(var e=this,r=n.current,s=!1;r;){if(r===i){s=!0;break}r=r.parent}if(!s)throw new Error("Unexpected Zone: "+n.current.name);this.testProxyZoneSpec=new o,this.testProxyZone=i.fork(this.testProxyZoneSpec),n.currentTask?t.prototype.execute.call(this):n.current.scheduleMicroTask("jasmine.execute().forceTask",(function(){return h.prototype.execute.call(e)}))},r}(h)}function y(e,t,r,o){var i=!!jasmine[s("clockInstalled")],a=r.testProxyZone;if(i&&c){var u=n[n.__symbol__("fakeAsyncTest")];u&&"function"==typeof u.fakeAsync&&(e=u.fakeAsync(e))}return o?a.run(e,t,[o]):a.run(e,t)}function m(e){return e&&(e.length?function(n){return y(e,this,this.queueRunner,n)}:function(){return y(e,this,this.queueRunner)})}}))}(Zone)}));