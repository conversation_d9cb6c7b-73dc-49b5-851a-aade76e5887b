{"predef": ["console", "require", "define", "describe", "it", "before", "beforeEach", "after", "after<PERSON>ach", "Promise"], "node": true, "devel": true, "bitwise": true, "curly": true, "eqeqeq": true, "forin": false, "immed": true, "latedef": false, "newcap": true, "noarg": true, "noempty": false, "nonew": true, "plusplus": false, "regexp": true, "undef": true, "unused": true, "quotmark": "single", "strict": true, "trailing": true, "asi": false, "boss": false, "debug": false, "eqnull": true, "es5": false, "esnext": false, "evil": false, "expr": true, "funcscope": false, "globalstrict": false, "iterator": false, "lastsemic": false, "laxbreak": false, "laxcomma": false, "loopfunc": true, "multistr": false, "onecase": true, "regexdash": false, "scripturl": false, "smarttabs": false, "shadow": false, "sub": false, "supernew": false, "validthis": false, "nomen": false, "onevar": false, "white": true}