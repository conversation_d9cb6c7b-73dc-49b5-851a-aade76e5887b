{"name": "external-editor", "version": "3.1.0", "description": "Edit a string with the users preferred text editor using $VISUAL or $ENVIRONMENT", "main": "main/index.js", "types": "main/index.d.ts", "scripts": {"test": "mocha --recursive --require ts-node/register --timeout 10000 ./test/spec 'test/spec/**/*.ts'", "compile": "tsc -p tsconfig.json", "lint": "tslint './src/**/*.ts' './test/**/*.ts'"}, "repository": {"type": "git", "url": "git+https://github.com/mrkmg/node-external-editor.git"}, "keywords": ["editor", "external", "user", "visual"], "author": "<PERSON> <<EMAIL>> (https://mrkmg.com)", "license": "MIT", "bugs": {"url": "https://github.com/mrkmg/node-external-editor/issues"}, "homepage": "https://github.com/mrkmg/node-external-editor#readme", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}, "devDependencies": {"@types/chai": "^4.1.4", "@types/chardet": "^0.5.0", "@types/mocha": "^5.2.5", "@types/node": "^10.14.12", "@types/tmp": "0.0.33", "chai": "^4.0.0", "es6-shim": "^0.35.3", "mocha": "^5.2.0", "ts-node": "^7.0.1", "tslint": "^5.18.0", "typescript": "^3.5.2"}, "files": ["main", "example_sync.js", "example_async.js"], "config": {"ndt": {"versions": ["major"]}}}