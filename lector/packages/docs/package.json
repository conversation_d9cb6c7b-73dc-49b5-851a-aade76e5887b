{"name": "docs", "version": "0.0.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "start": "next start", "postinstall": "fumadocs-mdx"}, "dependencies": {"@anaralabs/lector": "workspace:*", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-slot": "^1.1.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "fumadocs-core": "14.6.2", "fumadocs-mdx": "11.1.2", "fumadocs-ui": "14.6.2", "lucide-react": "^0.469.0", "next": "15.1.3", "pdfjs-dist": "^5.0.375", "react": "^19.0.0", "react-dom": "^19.0.0", "shiki": "^1.24.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "use-debounce": "^10.0.4", "uuid": "^11.1.0"}, "devDependencies": {"@types/mdx": "^2.0.13", "@types/node": "22.10.2", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.20", "eslint": "^8", "eslint-config-next": "15.1.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "typescript": "^5.7.2"}}