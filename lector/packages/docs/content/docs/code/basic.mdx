---
title: Basic Example
description: A minimal PDF viewer implementation with basic rendering capabilities
full: true
---

import Basic from "@/components/basic.tsx";

<Basic />

## Implementation

```tsx
"use client";

import { CanvasLayer, <PERSON>, <PERSON>s, Root, TextLayer } from "@anaralabs/lector";
import React from "react";
import "@/lib/setup";

const fileUrl = "/pdf/pathways.pdf";

const Basic = () => {
  return (
    <Root
      source={fileUrl}
      className='w-full h-[500px] border overflow-hidden rounded-lg'
      loader={<div className='p-4'>Loading...</div>}>
      <Pages className='dark:invert-[94%] dark:hue-rotate-180 dark:brightness-[80%] dark:contrast-[228%]'>
        <Page>
          <CanvasLayer />
          <TextLayer />
        </Page>
      </Pages>
    </Root>
  );
};

export default Basic;
```

The example above shows:

- Basic PDF viewer setup with a fixed height container
- Dark mode support with color adjustments
- Loading state handling
- Canvas rendering of PDF pages
- Text layer for text selection and copying
