{
  "$schema": "https://json.schemastore.org/tsconfig",
  "compilerOptions": {
    "jsx": "react-jsx",
    // Type checking
    "strict": true,
    "noUncheckedIndexedAccess": true,
    "alwaysStrict": false, // Don't emit "use strict" to avoid conflicts with "use client"
    // Modules
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "resolveJsonModule": true,
    // Language & Environment
    "target": "ESNext",
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "types": [
      "node"
    ],
    // Emit
    "noEmit": true,
    "declaration": true,
    "declarationMap": true,
    "verbatimModuleSyntax": true,
    "moduleDetection": "force",
    "downlevelIteration": true,
    // Interop
    "allowJs": true,
    "isolatedModules": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    // Misc
    "skipLibCheck": true,
    "skipDefaultLibCheck": true,
    "incremental": true,
    "tsBuildInfoFile": ".tsbuildinfo"
  },
  "include": [
    "**/*.ts",
    "**/*.tsx"
  ],
  "exclude": [
    "node_modules",
    "dist"
  ]
}