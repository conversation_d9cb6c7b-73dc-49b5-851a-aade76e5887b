name: CI/CD

on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, reopened, synchronize]
  workflow_dispatch:

env:
  FORCE_COLOR: 3

jobs:
  lint:
    name: Linting
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0

      - uses: pnpm/action-setup@v2
        with:
          version: latest

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm
          registry-url: "https://registry.npmjs.org"
          cache-dependency-path: ./pnpm-lock.yaml

      - name: Install dependencies
        run: pnpm install

      - name: Run lint
        run: pnpm lint

  test:
    name: Testing
    runs-on: ubuntu-latest
    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0

      - uses: pnpm/action-setup@v2
        with:
          version: latest

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm
          registry-url: "https://registry.npmjs.org"
          cache-dependency-path: ./pnpm-lock.yaml

      - name: Install dependencies
        run: pnpm install

      - name: Run tests
        run: pnpm test

      - name: Build package
        run: pnpm build

  release:
    name: Release
    needs: [lint, test]
    if: github.ref == 'refs/heads/main' && github.event_name != 'pull_request'
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pages: write
      id-token: write
      issues: write
      pull-requests: write

    steps:
      - uses: actions/create-github-app-token@v1
        id: app-token
        with:
          app-id: ${{ secrets.APP_ID }}
          private-key: ${{ secrets.APP_PRIVATE_KEY }}

      - uses: actions/checkout@v4
        with:
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0

      - uses: pnpm/action-setup@v2
        with:
          version: latest

      - uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: pnpm
          registry-url: "https://registry.npmjs.org"
          cache-dependency-path: ./pnpm-lock.yaml

      - name: Install dependencies
        run: pnpm install --ignore-scripts --frozen-lockfile --filter lector...

      - name: Build package
        run: pnpm build --filter @anaralabs/lector

      - name: Set execute permissions
        run: chmod +x ./packages/lector/scripts/prepack.sh

      - name: Semantic Release
        run: ../../node_modules/.bin/semantic-release
        working-directory: packages/lector
        env:
          GITHUB_TOKEN: ${{ steps.app-token.outputs.token }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
