{"$schema": "https://turbo.build/schema.json", "remoteCache": {"enabled": true}, "tasks": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "lint": {"dependsOn": ["^build"], "inputs": ["../../.prettierrc", "src/**", "package.json"], "outputs": []}, "format": {"dependsOn": ["^format"]}, "test": {"dependsOn": ["^test"]}, "dev": {"persistent": true, "cache": false}, "@anaralabs/lector#test": {"outputs": ["dist/**", "coverage/**"], "dependsOn": ["build"]}}}