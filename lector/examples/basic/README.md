# Lector Basic Example

This is a basic example of using <PERSON><PERSON>, a headless PDF viewer for React.

[![Open in StackBlitz](https://developer.stackblitz.com/img/open_in_stackblitz.svg)](https://stackblitz.com/github/anaralabs/lector/tree/main/examples/basic)

## Features

- PDF rendering with canvas layer
- Text selection and copying
- Responsive layout
- Dark mode support
- Loading state handling

## Getting Started

1. Clone the repository:

```bash
git clone https://github.com/anaralabs/lector.git
cd lector/examples/basic
```

2. Install dependencies:

```bash
pnpm install
```

3. Start the development server:

```bash
pnpm dev
```

4. Open [http://localhost:5173](http://localhost:5173) in your browser.

## Learn More

- [Lector Documentation](https://lector-weld.vercel.app/)
- [GitHub Repository](https://github.com/anaralabs/lector)
