{"name": "react-pdf-highlighter-extended", "type": "module", "version": "8.1.0", "description": "Set of modern React components for PDF highlighting", "author": "<PERSON> <<EMAIL>>", "license": "MIT", "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "keywords": ["pdf", "highlight", "annotator", "react-component", "react"], "files": ["dist"], "types": "./dist/esm/index.d.ts", "module": "./dist/esm/index.js", "scripts": {"start": "npm run dev", "dev": "(cd ./example && vite --force)", "build": "npm run clean && npm i && npm run build:esm && npm run build:copy-styles && npm run build:example && npm run build:docs", "build:esm": "tsc", "build:copy-styles": "cp -r ./src/style ./dist/esm", "build:example": "(cd ./example && tsc && vite build && mkdir \"../public/\" && cp -r example-app ../public/)", "build:docs": "npx typedoc", "clean": "rm -rf dist public node_modules package-lock.json"}, "peerDependencies": {"react": "^18.3.1", "react-dom": "^18.3.1"}, "dependencies": {"lodash.debounce": "^4.0.8", "pdfjs-dist": "^4.4.168", "react-rnd": "^10.4.11"}, "repository": {"type": "git", "url": "git+https://github.com/DanielArnould/react-pdf-highlighter.git"}, "bugs": {"url": "https://github.com/Daniel<PERSON><PERSON><PERSON>/react-pdf-highlighter-extended/issues"}, "devDependencies": {"@types/lodash.debounce": "^4.0.9", "@types/node": "^20.14.9", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "typedoc": "latest", "typedoc-theme-category-nav": "latest", "typescript": "^5.3.2", "vite": "^5.3.3"}}