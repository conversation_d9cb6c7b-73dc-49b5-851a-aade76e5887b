{"version": 3, "sources": ["../../../../node_modules/react-is/cjs/react-is.development.js", "../../../../node_modules/react-is/index.js", "../../../../node_modules/object-assign/index.js", "../../../../node_modules/prop-types/lib/ReactPropTypesSecret.js", "../../../../node_modules/prop-types/lib/has.js", "../../../../node_modules/prop-types/checkPropTypes.js", "../../../../node_modules/prop-types/factoryWithTypeCheckers.js", "../../../../node_modules/prop-types/index.js", "../../../../node_modules/clsx/dist/clsx.m.js", "../../../../node_modules/react-draggable/build/cjs/utils/shims.js", "../../../../node_modules/react-draggable/build/cjs/utils/getPrefix.js", "../../../../node_modules/react-draggable/build/cjs/utils/domFns.js", "../../../../node_modules/react-draggable/build/cjs/utils/positionFns.js", "../../../../node_modules/react-draggable/build/cjs/utils/log.js", "../../../../node_modules/react-draggable/build/cjs/DraggableCore.js", "../../../../node_modules/react-draggable/build/cjs/Draggable.js", "../../../../node_modules/react-draggable/build/cjs/cjs.js", "../../../../node_modules/react-rnd/lib/index.js", "../../../../node_modules/re-resizable/lib/index.js", "../../../../node_modules/re-resizable/lib/resizer.js"], "sourcesContent": ["/** @license React v16.13.1\n * react-is.development.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n\n\nif (process.env.NODE_ENV !== \"production\") {\n  (function() {\n'use strict';\n\n// The Symbol used to tag the ReactElement-like types. If there is no native Symbol\n// nor polyfill, then a plain number is used for performance.\nvar hasSymbol = typeof Symbol === 'function' && Symbol.for;\nvar REACT_ELEMENT_TYPE = hasSymbol ? Symbol.for('react.element') : 0xeac7;\nvar REACT_PORTAL_TYPE = hasSymbol ? Symbol.for('react.portal') : 0xeaca;\nvar REACT_FRAGMENT_TYPE = hasSymbol ? Symbol.for('react.fragment') : 0xeacb;\nvar REACT_STRICT_MODE_TYPE = hasSymbol ? Symbol.for('react.strict_mode') : 0xeacc;\nvar REACT_PROFILER_TYPE = hasSymbol ? Symbol.for('react.profiler') : 0xead2;\nvar REACT_PROVIDER_TYPE = hasSymbol ? Symbol.for('react.provider') : 0xeacd;\nvar REACT_CONTEXT_TYPE = hasSymbol ? Symbol.for('react.context') : 0xeace; // TODO: We don't use AsyncMode or ConcurrentMode anymore. They were temporary\n// (unstable) APIs that have been removed. Can we remove the symbols?\n\nvar REACT_ASYNC_MODE_TYPE = hasSymbol ? Symbol.for('react.async_mode') : 0xeacf;\nvar REACT_CONCURRENT_MODE_TYPE = hasSymbol ? Symbol.for('react.concurrent_mode') : 0xeacf;\nvar REACT_FORWARD_REF_TYPE = hasSymbol ? Symbol.for('react.forward_ref') : 0xead0;\nvar REACT_SUSPENSE_TYPE = hasSymbol ? Symbol.for('react.suspense') : 0xead1;\nvar REACT_SUSPENSE_LIST_TYPE = hasSymbol ? Symbol.for('react.suspense_list') : 0xead8;\nvar REACT_MEMO_TYPE = hasSymbol ? Symbol.for('react.memo') : 0xead3;\nvar REACT_LAZY_TYPE = hasSymbol ? Symbol.for('react.lazy') : 0xead4;\nvar REACT_BLOCK_TYPE = hasSymbol ? Symbol.for('react.block') : 0xead9;\nvar REACT_FUNDAMENTAL_TYPE = hasSymbol ? Symbol.for('react.fundamental') : 0xead5;\nvar REACT_RESPONDER_TYPE = hasSymbol ? Symbol.for('react.responder') : 0xead6;\nvar REACT_SCOPE_TYPE = hasSymbol ? Symbol.for('react.scope') : 0xead7;\n\nfunction isValidElementType(type) {\n  return typeof type === 'string' || typeof type === 'function' || // Note: its typeof might be other than 'symbol' or 'number' if it's a polyfill.\n  type === REACT_FRAGMENT_TYPE || type === REACT_CONCURRENT_MODE_TYPE || type === REACT_PROFILER_TYPE || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || typeof type === 'object' && type !== null && (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_FORWARD_REF_TYPE || type.$$typeof === REACT_FUNDAMENTAL_TYPE || type.$$typeof === REACT_RESPONDER_TYPE || type.$$typeof === REACT_SCOPE_TYPE || type.$$typeof === REACT_BLOCK_TYPE);\n}\n\nfunction typeOf(object) {\n  if (typeof object === 'object' && object !== null) {\n    var $$typeof = object.$$typeof;\n\n    switch ($$typeof) {\n      case REACT_ELEMENT_TYPE:\n        var type = object.type;\n\n        switch (type) {\n          case REACT_ASYNC_MODE_TYPE:\n          case REACT_CONCURRENT_MODE_TYPE:\n          case REACT_FRAGMENT_TYPE:\n          case REACT_PROFILER_TYPE:\n          case REACT_STRICT_MODE_TYPE:\n          case REACT_SUSPENSE_TYPE:\n            return type;\n\n          default:\n            var $$typeofType = type && type.$$typeof;\n\n            switch ($$typeofType) {\n              case REACT_CONTEXT_TYPE:\n              case REACT_FORWARD_REF_TYPE:\n              case REACT_LAZY_TYPE:\n              case REACT_MEMO_TYPE:\n              case REACT_PROVIDER_TYPE:\n                return $$typeofType;\n\n              default:\n                return $$typeof;\n            }\n\n        }\n\n      case REACT_PORTAL_TYPE:\n        return $$typeof;\n    }\n  }\n\n  return undefined;\n} // AsyncMode is deprecated along with isAsyncMode\n\nvar AsyncMode = REACT_ASYNC_MODE_TYPE;\nvar ConcurrentMode = REACT_CONCURRENT_MODE_TYPE;\nvar ContextConsumer = REACT_CONTEXT_TYPE;\nvar ContextProvider = REACT_PROVIDER_TYPE;\nvar Element = REACT_ELEMENT_TYPE;\nvar ForwardRef = REACT_FORWARD_REF_TYPE;\nvar Fragment = REACT_FRAGMENT_TYPE;\nvar Lazy = REACT_LAZY_TYPE;\nvar Memo = REACT_MEMO_TYPE;\nvar Portal = REACT_PORTAL_TYPE;\nvar Profiler = REACT_PROFILER_TYPE;\nvar StrictMode = REACT_STRICT_MODE_TYPE;\nvar Suspense = REACT_SUSPENSE_TYPE;\nvar hasWarnedAboutDeprecatedIsAsyncMode = false; // AsyncMode should be deprecated\n\nfunction isAsyncMode(object) {\n  {\n    if (!hasWarnedAboutDeprecatedIsAsyncMode) {\n      hasWarnedAboutDeprecatedIsAsyncMode = true; // Using console['warn'] to evade Babel and ESLint\n\n      console['warn']('The ReactIs.isAsyncMode() alias has been deprecated, ' + 'and will be removed in React 17+. Update your code to use ' + 'ReactIs.isConcurrentMode() instead. It has the exact same API.');\n    }\n  }\n\n  return isConcurrentMode(object) || typeOf(object) === REACT_ASYNC_MODE_TYPE;\n}\nfunction isConcurrentMode(object) {\n  return typeOf(object) === REACT_CONCURRENT_MODE_TYPE;\n}\nfunction isContextConsumer(object) {\n  return typeOf(object) === REACT_CONTEXT_TYPE;\n}\nfunction isContextProvider(object) {\n  return typeOf(object) === REACT_PROVIDER_TYPE;\n}\nfunction isElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nfunction isForwardRef(object) {\n  return typeOf(object) === REACT_FORWARD_REF_TYPE;\n}\nfunction isFragment(object) {\n  return typeOf(object) === REACT_FRAGMENT_TYPE;\n}\nfunction isLazy(object) {\n  return typeOf(object) === REACT_LAZY_TYPE;\n}\nfunction isMemo(object) {\n  return typeOf(object) === REACT_MEMO_TYPE;\n}\nfunction isPortal(object) {\n  return typeOf(object) === REACT_PORTAL_TYPE;\n}\nfunction isProfiler(object) {\n  return typeOf(object) === REACT_PROFILER_TYPE;\n}\nfunction isStrictMode(object) {\n  return typeOf(object) === REACT_STRICT_MODE_TYPE;\n}\nfunction isSuspense(object) {\n  return typeOf(object) === REACT_SUSPENSE_TYPE;\n}\n\nexports.AsyncMode = AsyncMode;\nexports.ConcurrentMode = ConcurrentMode;\nexports.ContextConsumer = ContextConsumer;\nexports.ContextProvider = ContextProvider;\nexports.Element = Element;\nexports.ForwardRef = ForwardRef;\nexports.Fragment = Fragment;\nexports.Lazy = Lazy;\nexports.Memo = Memo;\nexports.Portal = Portal;\nexports.Profiler = Profiler;\nexports.StrictMode = StrictMode;\nexports.Suspense = Suspense;\nexports.isAsyncMode = isAsyncMode;\nexports.isConcurrentMode = isConcurrentMode;\nexports.isContextConsumer = isContextConsumer;\nexports.isContextProvider = isContextProvider;\nexports.isElement = isElement;\nexports.isForwardRef = isForwardRef;\nexports.isFragment = isFragment;\nexports.isLazy = isLazy;\nexports.isMemo = isMemo;\nexports.isPortal = isPortal;\nexports.isProfiler = isProfiler;\nexports.isStrictMode = isStrictMode;\nexports.isSuspense = isSuspense;\nexports.isValidElementType = isValidElementType;\nexports.typeOf = typeOf;\n  })();\n}\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/*\nobject-assign\n(c) <PERSON><PERSON> Sorhus\n@license MIT\n*/\n\n'use strict';\n/* eslint-disable no-unused-vars */\nvar getOwnPropertySymbols = Object.getOwnPropertySymbols;\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\nvar propIsEnumerable = Object.prototype.propertyIsEnumerable;\n\nfunction toObject(val) {\n\tif (val === null || val === undefined) {\n\t\tthrow new TypeError('Object.assign cannot be called with null or undefined');\n\t}\n\n\treturn Object(val);\n}\n\nfunction shouldUseNative() {\n\ttry {\n\t\tif (!Object.assign) {\n\t\t\treturn false;\n\t\t}\n\n\t\t// Detect buggy property enumeration order in older V8 versions.\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=4118\n\t\tvar test1 = new String('abc');  // eslint-disable-line no-new-wrappers\n\t\ttest1[5] = 'de';\n\t\tif (Object.getOwnPropertyNames(test1)[0] === '5') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test2 = {};\n\t\tfor (var i = 0; i < 10; i++) {\n\t\t\ttest2['_' + String.fromCharCode(i)] = i;\n\t\t}\n\t\tvar order2 = Object.getOwnPropertyNames(test2).map(function (n) {\n\t\t\treturn test2[n];\n\t\t});\n\t\tif (order2.join('') !== '0123456789') {\n\t\t\treturn false;\n\t\t}\n\n\t\t// https://bugs.chromium.org/p/v8/issues/detail?id=3056\n\t\tvar test3 = {};\n\t\t'abcdefghijklmnopqrst'.split('').forEach(function (letter) {\n\t\t\ttest3[letter] = letter;\n\t\t});\n\t\tif (Object.keys(Object.assign({}, test3)).join('') !==\n\t\t\t\t'abcdefghijklmnopqrst') {\n\t\t\treturn false;\n\t\t}\n\n\t\treturn true;\n\t} catch (err) {\n\t\t// We don't expect any of the above to throw, but better to be safe.\n\t\treturn false;\n\t}\n}\n\nmodule.exports = shouldUseNative() ? Object.assign : function (target, source) {\n\tvar from;\n\tvar to = toObject(target);\n\tvar symbols;\n\n\tfor (var s = 1; s < arguments.length; s++) {\n\t\tfrom = Object(arguments[s]);\n\n\t\tfor (var key in from) {\n\t\t\tif (hasOwnProperty.call(from, key)) {\n\t\t\t\tto[key] = from[key];\n\t\t\t}\n\t\t}\n\n\t\tif (getOwnPropertySymbols) {\n\t\t\tsymbols = getOwnPropertySymbols(from);\n\t\t\tfor (var i = 0; i < symbols.length; i++) {\n\t\t\t\tif (propIsEnumerable.call(from, symbols[i])) {\n\t\t\t\t\tto[symbols[i]] = from[symbols[i]];\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\treturn to;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n\nmodule.exports = ReactPropTypesSecret;\n", "module.exports = Function.call.bind(Object.prototype.hasOwnProperty);\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n  var loggedTypeFailures = {};\n  var has = require('./lib/has');\n\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) { /**/ }\n  };\n}\n\n/**\n * Assert that the values match with the type specs.\n * Error messages are memorized and will only be shown once.\n *\n * @param {object} typeSpecs Map of name to a ReactPropType\n * @param {object} values Runtime values that need to be type-checked\n * @param {string} location e.g. \"prop\", \"context\", \"child context\"\n * @param {string} componentName Name of the component for error messages.\n * @param {?Function} getStack Returns the component stack.\n * @private\n */\nfunction checkPropTypes(typeSpecs, values, location, componentName, getStack) {\n  if (process.env.NODE_ENV !== 'production') {\n    for (var typeSpecName in typeSpecs) {\n      if (has(typeSpecs, typeSpecName)) {\n        var error;\n        // Prop type validation may throw. In case they do, we don't want to\n        // fail the render phase where it didn't fail before. So we log it.\n        // After these have been cleaned up, we'll let them throw.\n        try {\n          // This is intentionally an invariant that gets caught. It's the same\n          // behavior as without this statement except with a better message.\n          if (typeof typeSpecs[typeSpecName] !== 'function') {\n            var err = Error(\n              (componentName || 'React class') + ': ' + location + ' type `' + typeSpecName + '` is invalid; ' +\n              'it must be a function, usually from the `prop-types` package, but received `' + typeof typeSpecs[typeSpecName] + '`.' +\n              'This often happens because of typos such as `PropTypes.function` instead of `PropTypes.func`.'\n            );\n            err.name = 'Invariant Violation';\n            throw err;\n          }\n          error = typeSpecs[typeSpecName](values, typeSpecName, componentName, location, null, ReactPropTypesSecret);\n        } catch (ex) {\n          error = ex;\n        }\n        if (error && !(error instanceof Error)) {\n          printWarning(\n            (componentName || 'React class') + ': type specification of ' +\n            location + ' `' + typeSpecName + '` is invalid; the type checker ' +\n            'function must return `null` or an `Error` but returned a ' + typeof error + '. ' +\n            'You may have forgotten to pass an argument to the type checker ' +\n            'creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and ' +\n            'shape all require an argument).'\n          );\n        }\n        if (error instanceof Error && !(error.message in loggedTypeFailures)) {\n          // Only monitor this failure once because there tends to be a lot of the\n          // same error.\n          loggedTypeFailures[error.message] = true;\n\n          var stack = getStack ? getStack() : '';\n\n          printWarning(\n            'Failed ' + location + ' type: ' + error.message + (stack != null ? stack : '')\n          );\n        }\n      }\n    }\n  }\n}\n\n/**\n * Resets warning cache when testing.\n *\n * @private\n */\ncheckPropTypes.resetWarningCache = function() {\n  if (process.env.NODE_ENV !== 'production') {\n    loggedTypeFailures = {};\n  }\n}\n\nmodule.exports = checkPropTypes;\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactIs = require('react-is');\nvar assign = require('object-assign');\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nvar has = require('./lib/has');\nvar checkPropTypes = require('./checkPropTypes');\n\nvar printWarning = function() {};\n\nif (process.env.NODE_ENV !== 'production') {\n  printWarning = function(text) {\n    var message = 'Warning: ' + text;\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n}\n\nfunction emptyFunctionThatReturnsNull() {\n  return null;\n}\n\nmodule.exports = function(isValidElement, throwOnDirectAccess) {\n  /* global Symbol */\n  var ITERATOR_SYMBOL = typeof Symbol === 'function' && Symbol.iterator;\n  var FAUX_ITERATOR_SYMBOL = '@@iterator'; // Before Symbol spec.\n\n  /**\n   * Returns the iterator method function contained on the iterable object.\n   *\n   * Be sure to invoke the function with the iterable as context:\n   *\n   *     var iteratorFn = getIteratorFn(myIterable);\n   *     if (iteratorFn) {\n   *       var iterator = iteratorFn.call(myIterable);\n   *       ...\n   *     }\n   *\n   * @param {?object} maybeIterable\n   * @return {?function}\n   */\n  function getIteratorFn(maybeIterable) {\n    var iteratorFn = maybeIterable && (ITERATOR_SYMBOL && maybeIterable[ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL]);\n    if (typeof iteratorFn === 'function') {\n      return iteratorFn;\n    }\n  }\n\n  /**\n   * Collection of methods that allow declaration and validation of props that are\n   * supplied to React components. Example usage:\n   *\n   *   var Props = require('ReactPropTypes');\n   *   var MyArticle = React.createClass({\n   *     propTypes: {\n   *       // An optional string prop named \"description\".\n   *       description: Props.string,\n   *\n   *       // A required enum prop named \"category\".\n   *       category: Props.oneOf(['News','Photos']).isRequired,\n   *\n   *       // A prop named \"dialog\" that requires an instance of Dialog.\n   *       dialog: Props.instanceOf(Dialog).isRequired\n   *     },\n   *     render: function() { ... }\n   *   });\n   *\n   * A more formal specification of how these methods are used:\n   *\n   *   type := array|bool|func|object|number|string|oneOf([...])|instanceOf(...)\n   *   decl := ReactPropTypes.{type}(.isRequired)?\n   *\n   * Each and every declaration produces a function with the same signature. This\n   * allows the creation of custom validation functions. For example:\n   *\n   *  var MyLink = React.createClass({\n   *    propTypes: {\n   *      // An optional string or URI prop named \"href\".\n   *      href: function(props, propName, componentName) {\n   *        var propValue = props[propName];\n   *        if (propValue != null && typeof propValue !== 'string' &&\n   *            !(propValue instanceof URI)) {\n   *          return new Error(\n   *            'Expected a string or an URI for ' + propName + ' in ' +\n   *            componentName\n   *          );\n   *        }\n   *      }\n   *    },\n   *    render: function() {...}\n   *  });\n   *\n   * @internal\n   */\n\n  var ANONYMOUS = '<<anonymous>>';\n\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithThrowingShims.js`.\n  var ReactPropTypes = {\n    array: createPrimitiveTypeChecker('array'),\n    bigint: createPrimitiveTypeChecker('bigint'),\n    bool: createPrimitiveTypeChecker('boolean'),\n    func: createPrimitiveTypeChecker('function'),\n    number: createPrimitiveTypeChecker('number'),\n    object: createPrimitiveTypeChecker('object'),\n    string: createPrimitiveTypeChecker('string'),\n    symbol: createPrimitiveTypeChecker('symbol'),\n\n    any: createAnyTypeChecker(),\n    arrayOf: createArrayOfTypeChecker,\n    element: createElementTypeChecker(),\n    elementType: createElementTypeTypeChecker(),\n    instanceOf: createInstanceTypeChecker,\n    node: createNodeChecker(),\n    objectOf: createObjectOfTypeChecker,\n    oneOf: createEnumTypeChecker,\n    oneOfType: createUnionTypeChecker,\n    shape: createShapeTypeChecker,\n    exact: createStrictShapeTypeChecker,\n  };\n\n  /**\n   * inlined Object.is polyfill to avoid requiring consumers ship their own\n   * https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\n   */\n  /*eslint-disable no-self-compare*/\n  function is(x, y) {\n    // SameValue algorithm\n    if (x === y) {\n      // Steps 1-5, 7-10\n      // Steps 6.b-6.e: +0 != -0\n      return x !== 0 || 1 / x === 1 / y;\n    } else {\n      // Step 6.a: NaN == NaN\n      return x !== x && y !== y;\n    }\n  }\n  /*eslint-enable no-self-compare*/\n\n  /**\n   * We use an Error-like object for backward compatibility as people may call\n   * PropTypes directly and inspect their output. However, we don't use real\n   * Errors anymore. We don't inspect their stack anyway, and creating them\n   * is prohibitively expensive if they are created too often, such as what\n   * happens in oneOfType() for any type before the one that matched.\n   */\n  function PropTypeError(message, data) {\n    this.message = message;\n    this.data = data && typeof data === 'object' ? data: {};\n    this.stack = '';\n  }\n  // Make `instanceof Error` still work for returned errors.\n  PropTypeError.prototype = Error.prototype;\n\n  function createChainableTypeChecker(validate) {\n    if (process.env.NODE_ENV !== 'production') {\n      var manualPropTypeCallCache = {};\n      var manualPropTypeWarningCount = 0;\n    }\n    function checkType(isRequired, props, propName, componentName, location, propFullName, secret) {\n      componentName = componentName || ANONYMOUS;\n      propFullName = propFullName || propName;\n\n      if (secret !== ReactPropTypesSecret) {\n        if (throwOnDirectAccess) {\n          // New behavior only for users of `prop-types` package\n          var err = new Error(\n            'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n            'Use `PropTypes.checkPropTypes()` to call them. ' +\n            'Read more at http://fb.me/use-check-prop-types'\n          );\n          err.name = 'Invariant Violation';\n          throw err;\n        } else if (process.env.NODE_ENV !== 'production' && typeof console !== 'undefined') {\n          // Old behavior for people using React.PropTypes\n          var cacheKey = componentName + ':' + propName;\n          if (\n            !manualPropTypeCallCache[cacheKey] &&\n            // Avoid spamming the console because they are often not actionable except for lib authors\n            manualPropTypeWarningCount < 3\n          ) {\n            printWarning(\n              'You are manually calling a React.PropTypes validation ' +\n              'function for the `' + propFullName + '` prop on `' + componentName + '`. This is deprecated ' +\n              'and will throw in the standalone `prop-types` package. ' +\n              'You may be seeing this warning due to a third-party PropTypes ' +\n              'library. See https://fb.me/react-warning-dont-call-proptypes ' + 'for details.'\n            );\n            manualPropTypeCallCache[cacheKey] = true;\n            manualPropTypeWarningCount++;\n          }\n        }\n      }\n      if (props[propName] == null) {\n        if (isRequired) {\n          if (props[propName] === null) {\n            return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required ' + ('in `' + componentName + '`, but its value is `null`.'));\n          }\n          return new PropTypeError('The ' + location + ' `' + propFullName + '` is marked as required in ' + ('`' + componentName + '`, but its value is `undefined`.'));\n        }\n        return null;\n      } else {\n        return validate(props, propName, componentName, location, propFullName);\n      }\n    }\n\n    var chainedCheckType = checkType.bind(null, false);\n    chainedCheckType.isRequired = checkType.bind(null, true);\n\n    return chainedCheckType;\n  }\n\n  function createPrimitiveTypeChecker(expectedType) {\n    function validate(props, propName, componentName, location, propFullName, secret) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== expectedType) {\n        // `propValue` being instance of, say, date/regexp, pass the 'object'\n        // check, but we can offer a more precise error message here rather than\n        // 'of type `object`'.\n        var preciseType = getPreciseType(propValue);\n\n        return new PropTypeError(\n          'Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + preciseType + '` supplied to `' + componentName + '`, expected ') + ('`' + expectedType + '`.'),\n          {expectedType: expectedType}\n        );\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createAnyTypeChecker() {\n    return createChainableTypeChecker(emptyFunctionThatReturnsNull);\n  }\n\n  function createArrayOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside arrayOf.');\n      }\n      var propValue = props[propName];\n      if (!Array.isArray(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an array.'));\n      }\n      for (var i = 0; i < propValue.length; i++) {\n        var error = typeChecker(propValue, i, componentName, location, propFullName + '[' + i + ']', ReactPropTypesSecret);\n        if (error instanceof Error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!isValidElement(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createElementTypeTypeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      if (!ReactIs.isValidElementType(propValue)) {\n        var propType = getPropType(propValue);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected a single ReactElement type.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createInstanceTypeChecker(expectedClass) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!(props[propName] instanceof expectedClass)) {\n        var expectedClassName = expectedClass.name || ANONYMOUS;\n        var actualClassName = getClassName(props[propName]);\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + actualClassName + '` supplied to `' + componentName + '`, expected ') + ('instance of `' + expectedClassName + '`.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createEnumTypeChecker(expectedValues) {\n    if (!Array.isArray(expectedValues)) {\n      if (process.env.NODE_ENV !== 'production') {\n        if (arguments.length > 1) {\n          printWarning(\n            'Invalid arguments supplied to oneOf, expected an array, got ' + arguments.length + ' arguments. ' +\n            'A common mistake is to write oneOf(x, y, z) instead of oneOf([x, y, z]).'\n          );\n        } else {\n          printWarning('Invalid argument supplied to oneOf, expected an array.');\n        }\n      }\n      return emptyFunctionThatReturnsNull;\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      for (var i = 0; i < expectedValues.length; i++) {\n        if (is(propValue, expectedValues[i])) {\n          return null;\n        }\n      }\n\n      var valuesString = JSON.stringify(expectedValues, function replacer(key, value) {\n        var type = getPreciseType(value);\n        if (type === 'symbol') {\n          return String(value);\n        }\n        return value;\n      });\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of value `' + String(propValue) + '` ' + ('supplied to `' + componentName + '`, expected one of ' + valuesString + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createObjectOfTypeChecker(typeChecker) {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (typeof typeChecker !== 'function') {\n        return new PropTypeError('Property `' + propFullName + '` of component `' + componentName + '` has invalid PropType notation inside objectOf.');\n      }\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type ' + ('`' + propType + '` supplied to `' + componentName + '`, expected an object.'));\n      }\n      for (var key in propValue) {\n        if (has(propValue, key)) {\n          var error = typeChecker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n          if (error instanceof Error) {\n            return error;\n          }\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createUnionTypeChecker(arrayOfTypeCheckers) {\n    if (!Array.isArray(arrayOfTypeCheckers)) {\n      process.env.NODE_ENV !== 'production' ? printWarning('Invalid argument supplied to oneOfType, expected an instance of array.') : void 0;\n      return emptyFunctionThatReturnsNull;\n    }\n\n    for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n      var checker = arrayOfTypeCheckers[i];\n      if (typeof checker !== 'function') {\n        printWarning(\n          'Invalid argument supplied to oneOfType. Expected an array of check functions, but ' +\n          'received ' + getPostfixForTypeWarning(checker) + ' at index ' + i + '.'\n        );\n        return emptyFunctionThatReturnsNull;\n      }\n    }\n\n    function validate(props, propName, componentName, location, propFullName) {\n      var expectedTypes = [];\n      for (var i = 0; i < arrayOfTypeCheckers.length; i++) {\n        var checker = arrayOfTypeCheckers[i];\n        var checkerResult = checker(props, propName, componentName, location, propFullName, ReactPropTypesSecret);\n        if (checkerResult == null) {\n          return null;\n        }\n        if (checkerResult.data && has(checkerResult.data, 'expectedType')) {\n          expectedTypes.push(checkerResult.data.expectedType);\n        }\n      }\n      var expectedTypesMessage = (expectedTypes.length > 0) ? ', expected one of type [' + expectedTypes.join(', ') + ']': '';\n      return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`' + expectedTypesMessage + '.'));\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createNodeChecker() {\n    function validate(props, propName, componentName, location, propFullName) {\n      if (!isNode(props[propName])) {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` supplied to ' + ('`' + componentName + '`, expected a ReactNode.'));\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function invalidValidatorError(componentName, location, propFullName, key, type) {\n    return new PropTypeError(\n      (componentName || 'React class') + ': ' + location + ' type `' + propFullName + '.' + key + '` is invalid; ' +\n      'it must be a function, usually from the `prop-types` package, but received `' + type + '`.'\n    );\n  }\n\n  function createShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      for (var key in shapeTypes) {\n        var checker = shapeTypes[key];\n        if (typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n    return createChainableTypeChecker(validate);\n  }\n\n  function createStrictShapeTypeChecker(shapeTypes) {\n    function validate(props, propName, componentName, location, propFullName) {\n      var propValue = props[propName];\n      var propType = getPropType(propValue);\n      if (propType !== 'object') {\n        return new PropTypeError('Invalid ' + location + ' `' + propFullName + '` of type `' + propType + '` ' + ('supplied to `' + componentName + '`, expected `object`.'));\n      }\n      // We need to check all keys in case some are required but missing from props.\n      var allKeys = assign({}, props[propName], shapeTypes);\n      for (var key in allKeys) {\n        var checker = shapeTypes[key];\n        if (has(shapeTypes, key) && typeof checker !== 'function') {\n          return invalidValidatorError(componentName, location, propFullName, key, getPreciseType(checker));\n        }\n        if (!checker) {\n          return new PropTypeError(\n            'Invalid ' + location + ' `' + propFullName + '` key `' + key + '` supplied to `' + componentName + '`.' +\n            '\\nBad object: ' + JSON.stringify(props[propName], null, '  ') +\n            '\\nValid keys: ' + JSON.stringify(Object.keys(shapeTypes), null, '  ')\n          );\n        }\n        var error = checker(propValue, key, componentName, location, propFullName + '.' + key, ReactPropTypesSecret);\n        if (error) {\n          return error;\n        }\n      }\n      return null;\n    }\n\n    return createChainableTypeChecker(validate);\n  }\n\n  function isNode(propValue) {\n    switch (typeof propValue) {\n      case 'number':\n      case 'string':\n      case 'undefined':\n        return true;\n      case 'boolean':\n        return !propValue;\n      case 'object':\n        if (Array.isArray(propValue)) {\n          return propValue.every(isNode);\n        }\n        if (propValue === null || isValidElement(propValue)) {\n          return true;\n        }\n\n        var iteratorFn = getIteratorFn(propValue);\n        if (iteratorFn) {\n          var iterator = iteratorFn.call(propValue);\n          var step;\n          if (iteratorFn !== propValue.entries) {\n            while (!(step = iterator.next()).done) {\n              if (!isNode(step.value)) {\n                return false;\n              }\n            }\n          } else {\n            // Iterator will provide entry [k,v] tuples rather than values.\n            while (!(step = iterator.next()).done) {\n              var entry = step.value;\n              if (entry) {\n                if (!isNode(entry[1])) {\n                  return false;\n                }\n              }\n            }\n          }\n        } else {\n          return false;\n        }\n\n        return true;\n      default:\n        return false;\n    }\n  }\n\n  function isSymbol(propType, propValue) {\n    // Native Symbol.\n    if (propType === 'symbol') {\n      return true;\n    }\n\n    // falsy value can't be a Symbol\n    if (!propValue) {\n      return false;\n    }\n\n    // 19.4.3.5 Symbol.prototype[@@toStringTag] === 'Symbol'\n    if (propValue['@@toStringTag'] === 'Symbol') {\n      return true;\n    }\n\n    // Fallback for non-spec compliant Symbols which are polyfilled.\n    if (typeof Symbol === 'function' && propValue instanceof Symbol) {\n      return true;\n    }\n\n    return false;\n  }\n\n  // Equivalent of `typeof` but with special handling for array and regexp.\n  function getPropType(propValue) {\n    var propType = typeof propValue;\n    if (Array.isArray(propValue)) {\n      return 'array';\n    }\n    if (propValue instanceof RegExp) {\n      // Old webkits (at least until Android 4.0) return 'function' rather than\n      // 'object' for typeof a RegExp. We'll normalize this here so that /bla/\n      // passes PropTypes.object.\n      return 'object';\n    }\n    if (isSymbol(propType, propValue)) {\n      return 'symbol';\n    }\n    return propType;\n  }\n\n  // This handles more types than `getPropType`. Only used for error messages.\n  // See `createPrimitiveTypeChecker`.\n  function getPreciseType(propValue) {\n    if (typeof propValue === 'undefined' || propValue === null) {\n      return '' + propValue;\n    }\n    var propType = getPropType(propValue);\n    if (propType === 'object') {\n      if (propValue instanceof Date) {\n        return 'date';\n      } else if (propValue instanceof RegExp) {\n        return 'regexp';\n      }\n    }\n    return propType;\n  }\n\n  // Returns a string that is postfixed to a warning about an invalid type.\n  // For example, \"undefined\" or \"of type array\"\n  function getPostfixForTypeWarning(value) {\n    var type = getPreciseType(value);\n    switch (type) {\n      case 'array':\n      case 'object':\n        return 'an ' + type;\n      case 'boolean':\n      case 'date':\n      case 'regexp':\n        return 'a ' + type;\n      default:\n        return type;\n    }\n  }\n\n  // Returns class name of the object, if any.\n  function getClassName(propValue) {\n    if (!propValue.constructor || !propValue.constructor.name) {\n      return ANONYMOUS;\n    }\n    return propValue.constructor.name;\n  }\n\n  ReactPropTypes.checkPropTypes = checkPropTypes;\n  ReactPropTypes.resetWarningCache = checkPropTypes.resetWarningCache;\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n", "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\nif (process.env.NODE_ENV !== 'production') {\n  var ReactIs = require('react-is');\n\n  // By explicitly using `prop-types` you are opting into new development behavior.\n  // http://fb.me/prop-types-in-prod\n  var throwOnDirectAccess = true;\n  module.exports = require('./factoryWithTypeCheckers')(ReactIs.isElement, throwOnDirectAccess);\n} else {\n  // By explicitly using `prop-types` you are opting into new production behavior.\n  // http://fb.me/prop-types-in-prod\n  module.exports = require('./factoryWithThrowingShims')();\n}\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e))for(t=0;t<e.length;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f);else for(t in e)e[t]&&(n&&(n+=\" \"),n+=t);return n}export function clsx(){for(var e,t,f=0,n=\"\";f<arguments.length;)(e=arguments[f++])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.dontSetMe = dontSetMe;\nexports.findInArray = findInArray;\nexports.int = int;\nexports.isFunction = isFunction;\nexports.isNum = isNum;\n// @credits https://gist.github.com/rogozhnikoff/a43cfed27c41e4e68cdc\nfunction findInArray(array /*: Array<any> | TouchList*/, callback /*: Function*/) /*: any*/{\n  for (let i = 0, length = array.length; i < length; i++) {\n    if (callback.apply(callback, [array[i], i, array])) return array[i];\n  }\n}\nfunction isFunction(func /*: any*/) /*: boolean %checks*/{\n  // $FlowIgnore[method-unbinding]\n  return typeof func === 'function' || Object.prototype.toString.call(func) === '[object Function]';\n}\nfunction isNum(num /*: any*/) /*: boolean %checks*/{\n  return typeof num === 'number' && !isNaN(num);\n}\nfunction int(a /*: string*/) /*: number*/{\n  return parseInt(a, 10);\n}\nfunction dontSetMe(props /*: Object*/, propName /*: string*/, componentName /*: string*/) /*: ?Error*/{\n  if (props[propName]) {\n    return new Error(\"Invalid prop \".concat(propName, \" passed to \").concat(componentName, \" - do not set this, set it on the child.\"));\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.browserPrefixToKey = browserPrefixToKey;\nexports.browserPrefixToStyle = browserPrefixToStyle;\nexports.default = void 0;\nexports.getPrefix = getPrefix;\nconst prefixes = ['Moz', 'Webkit', 'O', 'ms'];\nfunction getPrefix() /*: string*/{\n  var _window$document;\n  let prop /*: string*/ = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 'transform';\n  // Ensure we're running in an environment where there is actually a global\n  // `window` obj\n  if (typeof window === 'undefined') return '';\n\n  // If we're in a pseudo-browser server-side environment, this access\n  // path may not exist, so bail out if it doesn't.\n  const style = (_window$document = window.document) === null || _window$document === void 0 || (_window$document = _window$document.documentElement) === null || _window$document === void 0 ? void 0 : _window$document.style;\n  if (!style) return '';\n  if (prop in style) return '';\n  for (let i = 0; i < prefixes.length; i++) {\n    if (browserPrefixToKey(prop, prefixes[i]) in style) return prefixes[i];\n  }\n  return '';\n}\nfunction browserPrefixToKey(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? \"\".concat(prefix).concat(kebabToTitleCase(prop)) : prop;\n}\nfunction browserPrefixToStyle(prop /*: string*/, prefix /*: string*/) /*: string*/{\n  return prefix ? \"-\".concat(prefix.toLowerCase(), \"-\").concat(prop) : prop;\n}\nfunction kebabToTitleCase(str /*: string*/) /*: string*/{\n  let out = '';\n  let shouldCapitalize = true;\n  for (let i = 0; i < str.length; i++) {\n    if (shouldCapitalize) {\n      out += str[i].toUpperCase();\n      shouldCapitalize = false;\n    } else if (str[i] === '-') {\n      shouldCapitalize = true;\n    } else {\n      out += str[i];\n    }\n  }\n  return out;\n}\n\n// Default export is the prefix itself, like 'Moz', 'Webkit', etc\n// Note that you may have to re-test for certain things; for instance, Chrome 50\n// can handle unprefixed `transform`, but not unprefixed `user-select`\nvar _default = exports.default = (getPrefix() /*: string*/);", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.addClassName = addClassName;\nexports.addEvent = addEvent;\nexports.addUserSelectStyles = addUserSelectStyles;\nexports.createCSSTransform = createCSSTransform;\nexports.createSVGTransform = createSVGTransform;\nexports.getTouch = getTouch;\nexports.getTouchIdentifier = getTouchIdentifier;\nexports.getTranslation = getTranslation;\nexports.innerHeight = innerHeight;\nexports.innerWidth = innerWidth;\nexports.matchesSelector = matchesSelector;\nexports.matchesSelectorAndParentsTo = matchesSelectorAndParentsTo;\nexports.offsetXYFromParent = offsetXYFromParent;\nexports.outerHeight = outerHeight;\nexports.outerWidth = outerWidth;\nexports.removeClassName = removeClassName;\nexports.removeEvent = removeEvent;\nexports.removeUserSelectStyles = removeUserSelectStyles;\nvar _shims = require(\"./shims\");\nvar _getPrefix = _interopRequireWildcard(require(\"./getPrefix\"));\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\n/*:: import type {ControlPosition, PositionOffsetControlPosition, MouseTouchEvent} from './types';*/\nlet matchesSelectorFunc = '';\nfunction matchesSelector(el /*: Node*/, selector /*: string*/) /*: boolean*/{\n  if (!matchesSelectorFunc) {\n    matchesSelectorFunc = (0, _shims.findInArray)(['matches', 'webkitMatchesSelector', 'mozMatchesSelector', 'msMatchesSelector', 'oMatchesSelector'], function (method) {\n      // $FlowIgnore: Doesn't think elements are indexable\n      return (0, _shims.isFunction)(el[method]);\n    });\n  }\n\n  // Might not be found entirely (not an Element?) - in that case, bail\n  // $FlowIgnore: Doesn't think elements are indexable\n  if (!(0, _shims.isFunction)(el[matchesSelectorFunc])) return false;\n\n  // $FlowIgnore: Doesn't think elements are indexable\n  return el[matchesSelectorFunc](selector);\n}\n\n// Works up the tree to the draggable itself attempting to match selector.\nfunction matchesSelectorAndParentsTo(el /*: Node*/, selector /*: string*/, baseNode /*: Node*/) /*: boolean*/{\n  let node = el;\n  do {\n    if (matchesSelector(node, selector)) return true;\n    if (node === baseNode) return false;\n    // $FlowIgnore[incompatible-type]\n    node = node.parentNode;\n  } while (node);\n  return false;\n}\nfunction addEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.addEventListener) {\n    el.addEventListener(event, handler, options);\n  } else if (el.attachEvent) {\n    el.attachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = handler;\n  }\n}\nfunction removeEvent(el /*: ?Node*/, event /*: string*/, handler /*: Function*/, inputOptions /*: Object*/) /*: void*/{\n  if (!el) return;\n  const options = {\n    capture: true,\n    ...inputOptions\n  };\n  // $FlowIgnore[method-unbinding]\n  if (el.removeEventListener) {\n    el.removeEventListener(event, handler, options);\n  } else if (el.detachEvent) {\n    el.detachEvent('on' + event, handler);\n  } else {\n    // $FlowIgnore: Doesn't think elements are indexable\n    el['on' + event] = null;\n  }\n}\nfunction outerHeight(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetTop which is including margin. See getBoundPosition\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height += (0, _shims.int)(computedStyle.borderTopWidth);\n  height += (0, _shims.int)(computedStyle.borderBottomWidth);\n  return height;\n}\nfunction outerWidth(node /*: HTMLElement*/) /*: number*/{\n  // This is deliberately excluding margin for our calculations, since we are using\n  // offsetLeft which is including margin. See getBoundPosition\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width += (0, _shims.int)(computedStyle.borderLeftWidth);\n  width += (0, _shims.int)(computedStyle.borderRightWidth);\n  return width;\n}\nfunction innerHeight(node /*: HTMLElement*/) /*: number*/{\n  let height = node.clientHeight;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  height -= (0, _shims.int)(computedStyle.paddingTop);\n  height -= (0, _shims.int)(computedStyle.paddingBottom);\n  return height;\n}\nfunction innerWidth(node /*: HTMLElement*/) /*: number*/{\n  let width = node.clientWidth;\n  const computedStyle = node.ownerDocument.defaultView.getComputedStyle(node);\n  width -= (0, _shims.int)(computedStyle.paddingLeft);\n  width -= (0, _shims.int)(computedStyle.paddingRight);\n  return width;\n}\n/*:: interface EventWithOffset {\n  clientX: number, clientY: number\n}*/\n// Get from offsetParent\nfunction offsetXYFromParent(evt /*: EventWithOffset*/, offsetParent /*: HTMLElement*/, scale /*: number*/) /*: ControlPosition*/{\n  const isBody = offsetParent === offsetParent.ownerDocument.body;\n  const offsetParentRect = isBody ? {\n    left: 0,\n    top: 0\n  } : offsetParent.getBoundingClientRect();\n  const x = (evt.clientX + offsetParent.scrollLeft - offsetParentRect.left) / scale;\n  const y = (evt.clientY + offsetParent.scrollTop - offsetParentRect.top) / scale;\n  return {\n    x,\n    y\n  };\n}\nfunction createCSSTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: Object*/{\n  const translation = getTranslation(controlPos, positionOffset, 'px');\n  return {\n    [(0, _getPrefix.browserPrefixToKey)('transform', _getPrefix.default)]: translation\n  };\n}\nfunction createSVGTransform(controlPos /*: ControlPosition*/, positionOffset /*: PositionOffsetControlPosition*/) /*: string*/{\n  const translation = getTranslation(controlPos, positionOffset, '');\n  return translation;\n}\nfunction getTranslation(_ref /*:: */, positionOffset /*: PositionOffsetControlPosition*/, unitSuffix /*: string*/) /*: string*/{\n  let {\n    x,\n    y\n  } /*: ControlPosition*/ = _ref /*: ControlPosition*/;\n  let translation = \"translate(\".concat(x).concat(unitSuffix, \",\").concat(y).concat(unitSuffix, \")\");\n  if (positionOffset) {\n    const defaultX = \"\".concat(typeof positionOffset.x === 'string' ? positionOffset.x : positionOffset.x + unitSuffix);\n    const defaultY = \"\".concat(typeof positionOffset.y === 'string' ? positionOffset.y : positionOffset.y + unitSuffix);\n    translation = \"translate(\".concat(defaultX, \", \").concat(defaultY, \")\") + translation;\n  }\n  return translation;\n}\nfunction getTouch(e /*: MouseTouchEvent*/, identifier /*: number*/) /*: ?{clientX: number, clientY: number}*/{\n  return e.targetTouches && (0, _shims.findInArray)(e.targetTouches, t => identifier === t.identifier) || e.changedTouches && (0, _shims.findInArray)(e.changedTouches, t => identifier === t.identifier);\n}\nfunction getTouchIdentifier(e /*: MouseTouchEvent*/) /*: ?number*/{\n  if (e.targetTouches && e.targetTouches[0]) return e.targetTouches[0].identifier;\n  if (e.changedTouches && e.changedTouches[0]) return e.changedTouches[0].identifier;\n}\n\n// User-select Hacks:\n//\n// Useful for preventing blue highlights all over everything when dragging.\n\n// Note we're passing `document` b/c we could be iframed\nfunction addUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  let styleEl = doc.getElementById('react-draggable-style-el');\n  if (!styleEl) {\n    styleEl = doc.createElement('style');\n    styleEl.type = 'text/css';\n    styleEl.id = 'react-draggable-style-el';\n    styleEl.innerHTML = '.react-draggable-transparent-selection *::-moz-selection {all: inherit;}\\n';\n    styleEl.innerHTML += '.react-draggable-transparent-selection *::selection {all: inherit;}\\n';\n    doc.getElementsByTagName('head')[0].appendChild(styleEl);\n  }\n  if (doc.body) addClassName(doc.body, 'react-draggable-transparent-selection');\n}\nfunction removeUserSelectStyles(doc /*: ?Document*/) {\n  if (!doc) return;\n  try {\n    if (doc.body) removeClassName(doc.body, 'react-draggable-transparent-selection');\n    // $FlowIgnore: IE\n    if (doc.selection) {\n      // $FlowIgnore: IE\n      doc.selection.empty();\n    } else {\n      // Remove selection caused by scroll, unless it's a focused input\n      // (we use doc.defaultView in case we're in an iframe)\n      const selection = (doc.defaultView || window).getSelection();\n      if (selection && selection.type !== 'Caret') {\n        selection.removeAllRanges();\n      }\n    }\n  } catch (e) {\n    // probably IE\n  }\n}\nfunction addClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.add(className);\n  } else {\n    if (!el.className.match(new RegExp(\"(?:^|\\\\s)\".concat(className, \"(?!\\\\S)\")))) {\n      el.className += \" \".concat(className);\n    }\n  }\n}\nfunction removeClassName(el /*: HTMLElement*/, className /*: string*/) {\n  if (el.classList) {\n    el.classList.remove(className);\n  } else {\n    el.className = el.className.replace(new RegExp(\"(?:^|\\\\s)\".concat(className, \"(?!\\\\S)\"), 'g'), '');\n  }\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.canDragX = canDragX;\nexports.canDragY = canDragY;\nexports.createCoreData = createCoreData;\nexports.createDraggableData = createDraggableData;\nexports.getBoundPosition = getBoundPosition;\nexports.getControlPosition = getControlPosition;\nexports.snapToGrid = snapToGrid;\nvar _shims = require(\"./shims\");\nvar _domFns = require(\"./domFns\");\n/*:: import type Draggable from '../Draggable';*/\n/*:: import type {Bounds, ControlPosition, DraggableData, MouseTouchEvent} from './types';*/\n/*:: import type DraggableCore from '../DraggableCore';*/\nfunction getBoundPosition(draggable /*: Draggable*/, x /*: number*/, y /*: number*/) /*: [number, number]*/{\n  // If no bounds, short-circuit and move on\n  if (!draggable.props.bounds) return [x, y];\n\n  // Clone new bounds\n  let {\n    bounds\n  } = draggable.props;\n  bounds = typeof bounds === 'string' ? bounds : cloneBounds(bounds);\n  const node = findDOMNode(draggable);\n  if (typeof bounds === 'string') {\n    const {\n      ownerDocument\n    } = node;\n    const ownerWindow = ownerDocument.defaultView;\n    let boundNode;\n    if (bounds === 'parent') {\n      boundNode = node.parentNode;\n    } else {\n      boundNode = ownerDocument.querySelector(bounds);\n    }\n    if (!(boundNode instanceof ownerWindow.HTMLElement)) {\n      throw new Error('Bounds selector \"' + bounds + '\" could not find an element.');\n    }\n    const boundNodeEl /*: HTMLElement*/ = boundNode; // for Flow, can't seem to refine correctly\n    const nodeStyle = ownerWindow.getComputedStyle(node);\n    const boundNodeStyle = ownerWindow.getComputedStyle(boundNodeEl);\n    // Compute bounds. This is a pain with padding and offsets but this gets it exactly right.\n    bounds = {\n      left: -node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingLeft) + (0, _shims.int)(nodeStyle.marginLeft),\n      top: -node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingTop) + (0, _shims.int)(nodeStyle.marginTop),\n      right: (0, _domFns.innerWidth)(boundNodeEl) - (0, _domFns.outerWidth)(node) - node.offsetLeft + (0, _shims.int)(boundNodeStyle.paddingRight) - (0, _shims.int)(nodeStyle.marginRight),\n      bottom: (0, _domFns.innerHeight)(boundNodeEl) - (0, _domFns.outerHeight)(node) - node.offsetTop + (0, _shims.int)(boundNodeStyle.paddingBottom) - (0, _shims.int)(nodeStyle.marginBottom)\n    };\n  }\n\n  // Keep x and y below right and bottom limits...\n  if ((0, _shims.isNum)(bounds.right)) x = Math.min(x, bounds.right);\n  if ((0, _shims.isNum)(bounds.bottom)) y = Math.min(y, bounds.bottom);\n\n  // But above left and top limits.\n  if ((0, _shims.isNum)(bounds.left)) x = Math.max(x, bounds.left);\n  if ((0, _shims.isNum)(bounds.top)) y = Math.max(y, bounds.top);\n  return [x, y];\n}\nfunction snapToGrid(grid /*: [number, number]*/, pendingX /*: number*/, pendingY /*: number*/) /*: [number, number]*/{\n  const x = Math.round(pendingX / grid[0]) * grid[0];\n  const y = Math.round(pendingY / grid[1]) * grid[1];\n  return [x, y];\n}\nfunction canDragX(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'x';\n}\nfunction canDragY(draggable /*: Draggable*/) /*: boolean*/{\n  return draggable.props.axis === 'both' || draggable.props.axis === 'y';\n}\n\n// Get {x, y} positions from event.\nfunction getControlPosition(e /*: MouseTouchEvent*/, touchIdentifier /*: ?number*/, draggableCore /*: DraggableCore*/) /*: ?ControlPosition*/{\n  const touchObj = typeof touchIdentifier === 'number' ? (0, _domFns.getTouch)(e, touchIdentifier) : null;\n  if (typeof touchIdentifier === 'number' && !touchObj) return null; // not the right touch\n  const node = findDOMNode(draggableCore);\n  // User can provide an offsetParent if desired.\n  const offsetParent = draggableCore.props.offsetParent || node.offsetParent || node.ownerDocument.body;\n  return (0, _domFns.offsetXYFromParent)(touchObj || e, offsetParent, draggableCore.props.scale);\n}\n\n// Create an data object exposed by <DraggableCore>'s events\nfunction createCoreData(draggable /*: DraggableCore*/, x /*: number*/, y /*: number*/) /*: DraggableData*/{\n  const isStart = !(0, _shims.isNum)(draggable.lastX);\n  const node = findDOMNode(draggable);\n  if (isStart) {\n    // If this is our first move, use the x and y as last coords.\n    return {\n      node,\n      deltaX: 0,\n      deltaY: 0,\n      lastX: x,\n      lastY: y,\n      x,\n      y\n    };\n  } else {\n    // Otherwise calculate proper values.\n    return {\n      node,\n      deltaX: x - draggable.lastX,\n      deltaY: y - draggable.lastY,\n      lastX: draggable.lastX,\n      lastY: draggable.lastY,\n      x,\n      y\n    };\n  }\n}\n\n// Create an data exposed by <Draggable>'s events\nfunction createDraggableData(draggable /*: Draggable*/, coreData /*: DraggableData*/) /*: DraggableData*/{\n  const scale = draggable.props.scale;\n  return {\n    node: coreData.node,\n    x: draggable.state.x + coreData.deltaX / scale,\n    y: draggable.state.y + coreData.deltaY / scale,\n    deltaX: coreData.deltaX / scale,\n    deltaY: coreData.deltaY / scale,\n    lastX: draggable.state.x,\n    lastY: draggable.state.y\n  };\n}\n\n// A lot faster than stringify/parse\nfunction cloneBounds(bounds /*: Bounds*/) /*: Bounds*/{\n  return {\n    left: bounds.left,\n    top: bounds.top,\n    right: bounds.right,\n    bottom: bounds.bottom\n  };\n}\nfunction findDOMNode(draggable /*: Draggable | DraggableCore*/) /*: HTMLElement*/{\n  const node = draggable.findDOMNode();\n  if (!node) {\n    throw new Error('<DraggableCore>: Unmounted during event!');\n  }\n  // $FlowIgnore we can't assert on HTMLElement due to tests... FIXME\n  return node;\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = log;\n/*eslint no-console:0*/\nfunction log() {\n  if (undefined) console.log(...arguments);\n}", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); }\n/*:: import type {EventHandler, MouseTouchEvent} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n// Simple abstraction for dragging events names.\nconst eventsFor = {\n  touch: {\n    start: 'touchstart',\n    move: 'touchmove',\n    stop: 'touchend'\n  },\n  mouse: {\n    start: 'mousedown',\n    move: 'mousemove',\n    stop: 'mouseup'\n  }\n};\n\n// Default to mouse events.\nlet dragEventFor = eventsFor.mouse;\n/*:: export type DraggableData = {\n  node: HTMLElement,\n  x: number, y: number,\n  deltaX: number, deltaY: number,\n  lastX: number, lastY: number,\n};*/\n/*:: export type DraggableEventHandler = (e: MouseEvent, data: DraggableData) => void | false;*/\n/*:: export type ControlPosition = {x: number, y: number};*/\n/*:: export type PositionOffsetControlPosition = {x: number|string, y: number|string};*/\n/*:: export type DraggableCoreDefaultProps = {\n  allowAnyClick: boolean,\n  disabled: boolean,\n  enableUserSelectHack: boolean,\n  onStart: DraggableEventHandler,\n  onDrag: DraggableEventHandler,\n  onStop: DraggableEventHandler,\n  onMouseDown: (e: MouseEvent) => void,\n  scale: number,\n};*/\n/*:: export type DraggableCoreProps = {\n  ...DraggableCoreDefaultProps,\n  cancel: string,\n  children: ReactElement<any>,\n  offsetParent: HTMLElement,\n  grid: [number, number],\n  handle: string,\n  nodeRef?: ?React.ElementRef<any>,\n};*/\n//\n// Define <DraggableCore>.\n//\n// <DraggableCore> is for advanced usage of <Draggable>. It maintains minimal internal state so it can\n// work well with libraries that require more control over the element.\n//\n\nclass DraggableCore extends React.Component /*:: <DraggableCoreProps>*/{\n  constructor() {\n    super(...arguments);\n    _defineProperty(this, \"dragging\", false);\n    // Used while dragging to determine deltas.\n    _defineProperty(this, \"lastX\", NaN);\n    _defineProperty(this, \"lastY\", NaN);\n    _defineProperty(this, \"touchIdentifier\", null);\n    _defineProperty(this, \"mounted\", false);\n    _defineProperty(this, \"handleDragStart\", e => {\n      // Make it possible to attach event handlers on top of this one.\n      this.props.onMouseDown(e);\n\n      // Only accept left-clicks.\n      if (!this.props.allowAnyClick && typeof e.button === 'number' && e.button !== 0) return false;\n\n      // Get nodes. Be sure to grab relative document (could be iframed)\n      const thisNode = this.findDOMNode();\n      if (!thisNode || !thisNode.ownerDocument || !thisNode.ownerDocument.body) {\n        throw new Error('<DraggableCore> not mounted on DragStart!');\n      }\n      const {\n        ownerDocument\n      } = thisNode;\n\n      // Short circuit if handle or cancel prop was provided and selector doesn't match.\n      if (this.props.disabled || !(e.target instanceof ownerDocument.defaultView.Node) || this.props.handle && !(0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.handle, thisNode) || this.props.cancel && (0, _domFns.matchesSelectorAndParentsTo)(e.target, this.props.cancel, thisNode)) {\n        return;\n      }\n\n      // Prevent scrolling on mobile devices, like ipad/iphone.\n      // Important that this is after handle/cancel.\n      if (e.type === 'touchstart') e.preventDefault();\n\n      // Set touch identifier in component state if this is a touch event. This allows us to\n      // distinguish between individual touches on multitouch screens by identifying which\n      // touchpoint was set to this element.\n      const touchIdentifier = (0, _domFns.getTouchIdentifier)(e);\n      this.touchIdentifier = touchIdentifier;\n\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, touchIdentifier, this);\n      if (position == null) return; // not possible but satisfies flow\n      const {\n        x,\n        y\n      } = position;\n\n      // Create an event object with all the data parents need to make a decision here.\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDragStart: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, cancel.\n      (0, _log.default)('calling', this.props.onStart);\n      const shouldUpdate = this.props.onStart(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) return;\n\n      // Add a style to the body to disable user-select. This prevents text from\n      // being selected all over the page.\n      if (this.props.enableUserSelectHack) (0, _domFns.addUserSelectStyles)(ownerDocument);\n\n      // Initiate dragging. Set the current x and y as offsets\n      // so we know how much we've moved during the drag. This allows us\n      // to drag elements around even if they have been moved, without issue.\n      this.dragging = true;\n      this.lastX = x;\n      this.lastY = y;\n\n      // Add events to the document directly so we catch when the user's mouse/touch moves outside of\n      // this element. We use different events depending on whether or not we have detected that this\n      // is a touch-capable device.\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.move, this.handleDrag);\n      (0, _domFns.addEvent)(ownerDocument, dragEventFor.stop, this.handleDragStop);\n    });\n    _defineProperty(this, \"handleDrag\", e => {\n      // Get the current drag point from the event. This is used as the offset.\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX,\n          deltaY = y - this.lastY;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        if (!deltaX && !deltaY) return; // skip useless drag\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n      (0, _log.default)('DraggableCore: handleDrag: %j', coreEvent);\n\n      // Call event handler. If it returns explicit false, trigger end.\n      const shouldUpdate = this.props.onDrag(e, coreEvent);\n      if (shouldUpdate === false || this.mounted === false) {\n        try {\n          // $FlowIgnore\n          this.handleDragStop(new MouseEvent('mouseup'));\n        } catch (err) {\n          // Old browsers\n          const event = ((document.createEvent('MouseEvents') /*: any*/) /*: MouseTouchEvent*/);\n          // I see why this insanity was deprecated\n          // $FlowIgnore\n          event.initMouseEvent('mouseup', true, true, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);\n          this.handleDragStop(event);\n        }\n        return;\n      }\n      this.lastX = x;\n      this.lastY = y;\n    });\n    _defineProperty(this, \"handleDragStop\", e => {\n      if (!this.dragging) return;\n      const position = (0, _positionFns.getControlPosition)(e, this.touchIdentifier, this);\n      if (position == null) return;\n      let {\n        x,\n        y\n      } = position;\n\n      // Snap to grid if prop has been provided\n      if (Array.isArray(this.props.grid)) {\n        let deltaX = x - this.lastX || 0;\n        let deltaY = y - this.lastY || 0;\n        [deltaX, deltaY] = (0, _positionFns.snapToGrid)(this.props.grid, deltaX, deltaY);\n        x = this.lastX + deltaX, y = this.lastY + deltaY;\n      }\n      const coreEvent = (0, _positionFns.createCoreData)(this, x, y);\n\n      // Call event handler\n      const shouldContinue = this.props.onStop(e, coreEvent);\n      if (shouldContinue === false || this.mounted === false) return false;\n      const thisNode = this.findDOMNode();\n      if (thisNode) {\n        // Remove user-select hack\n        if (this.props.enableUserSelectHack) (0, _domFns.removeUserSelectStyles)(thisNode.ownerDocument);\n      }\n      (0, _log.default)('DraggableCore: handleDragStop: %j', coreEvent);\n\n      // Reset the el.\n      this.dragging = false;\n      this.lastX = NaN;\n      this.lastY = NaN;\n      if (thisNode) {\n        // Remove event handlers\n        (0, _log.default)('DraggableCore: Removing handlers');\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.move, this.handleDrag);\n        (0, _domFns.removeEvent)(thisNode.ownerDocument, dragEventFor.stop, this.handleDragStop);\n      }\n    });\n    _defineProperty(this, \"onMouseDown\", e => {\n      dragEventFor = eventsFor.mouse; // on touchscreen laptops we could switch back to mouse\n\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onMouseUp\", e => {\n      dragEventFor = eventsFor.mouse;\n      return this.handleDragStop(e);\n    });\n    // Same as onMouseDown (start drag), but now consider this a touch device.\n    _defineProperty(this, \"onTouchStart\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStart(e);\n    });\n    _defineProperty(this, \"onTouchEnd\", e => {\n      // We're on a touch device now, so change the event handlers\n      dragEventFor = eventsFor.touch;\n      return this.handleDragStop(e);\n    });\n  }\n  componentDidMount() {\n    this.mounted = true;\n    // Touch handlers must be added with {passive: false} to be cancelable.\n    // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      (0, _domFns.addEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n    }\n  }\n  componentWillUnmount() {\n    this.mounted = false;\n    // Remove any leftover event handlers. Remove both touch and mouse handlers in case\n    // some browser quirk caused a touch event to fire during a mouse move, or vice versa.\n    const thisNode = this.findDOMNode();\n    if (thisNode) {\n      const {\n        ownerDocument\n      } = thisNode;\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.move, this.handleDrag);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.mouse.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(ownerDocument, eventsFor.touch.stop, this.handleDragStop);\n      (0, _domFns.removeEvent)(thisNode, eventsFor.touch.start, this.onTouchStart, {\n        passive: false\n      });\n      if (this.props.enableUserSelectHack) (0, _domFns.removeUserSelectStyles)(ownerDocument);\n    }\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    var _this$props, _this$props2;\n    return (_this$props = this.props) !== null && _this$props !== void 0 && _this$props.nodeRef ? (_this$props2 = this.props) === null || _this$props2 === void 0 || (_this$props2 = _this$props2.nodeRef) === null || _this$props2 === void 0 ? void 0 : _this$props2.current : _reactDom.default.findDOMNode(this);\n  }\n  render() /*: React.Element<any>*/{\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.cloneElement(React.Children.only(this.props.children), {\n      // Note: mouseMove handler is attached to document so it will still function\n      // when the user drags quickly and leaves the bounds of the element.\n      onMouseDown: this.onMouseDown,\n      onMouseUp: this.onMouseUp,\n      // onTouchStart is added on `componentDidMount` so they can be added with\n      // {passive: false}, which allows it to cancel. See\n      // https://developers.google.com/web/updates/2017/01/scrolling-intervention\n      onTouchEnd: this.onTouchEnd\n    });\n  }\n}\nexports.default = DraggableCore;\n_defineProperty(DraggableCore, \"displayName\", 'DraggableCore');\n_defineProperty(DraggableCore, \"propTypes\", {\n  /**\n   * `allowAnyClick` allows dragging using any mouse button.\n   * By default, we only accept the left button.\n   *\n   * Defaults to `false`.\n   */\n  allowAnyClick: _propTypes.default.bool,\n  children: _propTypes.default.node.isRequired,\n  /**\n   * `disabled`, if true, stops the <Draggable> from dragging. All handlers,\n   * with the exception of `onMouseDown`, will not fire.\n   */\n  disabled: _propTypes.default.bool,\n  /**\n   * By default, we add 'user-select:none' attributes to the document body\n   * to prevent ugly text selection during drag. If this is causing problems\n   * for your app, set this to `false`.\n   */\n  enableUserSelectHack: _propTypes.default.bool,\n  /**\n   * `offsetParent`, if set, uses the passed DOM node to compute drag offsets\n   * instead of using the parent node.\n   */\n  offsetParent: function (props /*: DraggableCoreProps*/, propName /*: $Keys<DraggableCoreProps>*/) {\n    if (props[propName] && props[propName].nodeType !== 1) {\n      throw new Error('Draggable\\'s offsetParent must be a DOM Node.');\n    }\n  },\n  /**\n   * `grid` specifies the x and y that dragging should snap to.\n   */\n  grid: _propTypes.default.arrayOf(_propTypes.default.number),\n  /**\n   * `handle` specifies a selector to be used as the handle that initiates drag.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable handle=\".handle\">\n   *              <div>\n   *                  <div className=\"handle\">Click me to drag</div>\n   *                  <div>This is some other content</div>\n   *              </div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  handle: _propTypes.default.string,\n  /**\n   * `cancel` specifies a selector to be used to prevent drag initialization.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *           return(\n   *               <Draggable cancel=\".cancel\">\n   *                   <div>\n   *                     <div className=\"cancel\">You can't drag from here</div>\n   *                     <div>Dragging here works fine</div>\n   *                   </div>\n   *               </Draggable>\n   *           );\n   *       }\n   *   });\n   * ```\n   */\n  cancel: _propTypes.default.string,\n  /* If running in React Strict mode, ReactDOM.findDOMNode() is deprecated.\n   * Unfortunately, in order for <Draggable> to work properly, we need raw access\n   * to the underlying DOM node. If you want to avoid the warning, pass a `nodeRef`\n   * as in this example:\n   *\n   * function MyComponent() {\n   *   const nodeRef = React.useRef(null);\n   *   return (\n   *     <Draggable nodeRef={nodeRef}>\n   *       <div ref={nodeRef}>Example Target</div>\n   *     </Draggable>\n   *   );\n   * }\n   *\n   * This can be used for arbitrarily nested components, so long as the ref ends up\n   * pointing to the actual child DOM node and not a custom component.\n   */\n  nodeRef: _propTypes.default.object,\n  /**\n   * Called when dragging starts.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onStart: _propTypes.default.func,\n  /**\n   * Called while dragging.\n   * If this function returns the boolean false, dragging will be canceled.\n   */\n  onDrag: _propTypes.default.func,\n  /**\n   * Called when dragging stops.\n   * If this function returns the boolean false, the drag will remain active.\n   */\n  onStop: _propTypes.default.func,\n  /**\n   * A workaround option which can be passed if onMouseDown needs to be accessed,\n   * since it'll always be blocked (as there is internal use of onMouseDown)\n   */\n  onMouseDown: _propTypes.default.func,\n  /**\n   * `scale`, if set, applies scaling while dragging an element\n   */\n  scale: _propTypes.default.number,\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(DraggableCore, \"defaultProps\", {\n  allowAnyClick: false,\n  // by default only accept left click\n  disabled: false,\n  enableUserSelectHack: true,\n  onStart: function () {},\n  onDrag: function () {},\n  onStop: function () {},\n  onMouseDown: function () {},\n  scale: 1\n});", "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nObject.defineProperty(exports, \"DraggableCore\", {\n  enumerable: true,\n  get: function () {\n    return _DraggableCore.default;\n  }\n});\nexports.default = void 0;\nvar React = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nvar _reactDom = _interopRequireDefault(require(\"react-dom\"));\nvar _clsx = _interopRequireDefault(require(\"clsx\"));\nvar _domFns = require(\"./utils/domFns\");\nvar _positionFns = require(\"./utils/positionFns\");\nvar _shims = require(\"./utils/shims\");\nvar _DraggableCore = _interopRequireDefault(require(\"./DraggableCore\"));\nvar _log = _interopRequireDefault(require(\"./utils/log\"));\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\nfunction _getRequireWildcardCache(nodeInterop) { if (typeof WeakMap !== \"function\") return null; var cacheBabelInterop = new WeakMap(); var cacheNodeInterop = new WeakMap(); return (_getRequireWildcardCache = function (nodeInterop) { return nodeInterop ? cacheNodeInterop : cacheBabelInterop; })(nodeInterop); }\nfunction _interopRequireWildcard(obj, nodeInterop) { if (!nodeInterop && obj && obj.__esModule) { return obj; } if (obj === null || typeof obj !== \"object\" && typeof obj !== \"function\") { return { default: obj }; } var cache = _getRequireWildcardCache(nodeInterop); if (cache && cache.has(obj)) { return cache.get(obj); } var newObj = {}; var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor; for (var key in obj) { if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) { var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null; if (desc && (desc.get || desc.set)) { Object.defineProperty(newObj, key, desc); } else { newObj[key] = obj[key]; } } } newObj.default = obj; if (cache) { cache.set(obj, newObj); } return newObj; }\nfunction _extends() { _extends = Object.assign ? Object.assign.bind() : function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; }; return _extends.apply(this, arguments); }\nfunction _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }\nfunction _toPropertyKey(arg) { var key = _toPrimitive(arg, \"string\"); return typeof key === \"symbol\" ? key : String(key); }\nfunction _toPrimitive(input, hint) { if (typeof input !== \"object\" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || \"default\"); if (typeof res !== \"object\") return res; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (hint === \"string\" ? String : Number)(input); } /*:: import type {ControlPosition, PositionOffsetControlPosition, DraggableCoreProps, DraggableCoreDefaultProps} from './DraggableCore';*/\n/*:: import type {Bounds, DraggableEventHandler} from './utils/types';*/\n/*:: import type {Element as ReactElement} from 'react';*/\n/*:: type DraggableState = {\n  dragging: boolean,\n  dragged: boolean,\n  x: number, y: number,\n  slackX: number, slackY: number,\n  isElementSVG: boolean,\n  prevPropsPosition: ?ControlPosition,\n};*/\n/*:: export type DraggableDefaultProps = {\n  ...DraggableCoreDefaultProps,\n  axis: 'both' | 'x' | 'y' | 'none',\n  bounds: Bounds | string | false,\n  defaultClassName: string,\n  defaultClassNameDragging: string,\n  defaultClassNameDragged: string,\n  defaultPosition: ControlPosition,\n  scale: number,\n};*/\n/*:: export type DraggableProps = {\n  ...DraggableCoreProps,\n  ...DraggableDefaultProps,\n  positionOffset: PositionOffsetControlPosition,\n  position: ControlPosition,\n};*/\n//\n// Define <Draggable>\n//\nclass Draggable extends React.Component /*:: <DraggableProps, DraggableState>*/{\n  // React 16.3+\n  // Arity (props, state)\n  static getDerivedStateFromProps(_ref /*:: */, _ref2 /*:: */) /*: ?Partial<DraggableState>*/{\n    let {\n      position\n    } /*: DraggableProps*/ = _ref /*: DraggableProps*/;\n    let {\n      prevPropsPosition\n    } /*: DraggableState*/ = _ref2 /*: DraggableState*/;\n    // Set x/y if a new position is provided in props that is different than the previous.\n    if (position && (!prevPropsPosition || position.x !== prevPropsPosition.x || position.y !== prevPropsPosition.y)) {\n      (0, _log.default)('Draggable: getDerivedStateFromProps %j', {\n        position,\n        prevPropsPosition\n      });\n      return {\n        x: position.x,\n        y: position.y,\n        prevPropsPosition: {\n          ...position\n        }\n      };\n    }\n    return null;\n  }\n  constructor(props /*: DraggableProps*/) {\n    super(props);\n    _defineProperty(this, \"onDragStart\", (e, coreData) => {\n      (0, _log.default)('Draggable: onDragStart: %j', coreData);\n\n      // Short-circuit if user's callback killed it.\n      const shouldStart = this.props.onStart(e, (0, _positionFns.createDraggableData)(this, coreData));\n      // Kills start event on core as well, so move handlers are never bound.\n      if (shouldStart === false) return false;\n      this.setState({\n        dragging: true,\n        dragged: true\n      });\n    });\n    _defineProperty(this, \"onDrag\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n      (0, _log.default)('Draggable: onDrag: %j', coreData);\n      const uiData = (0, _positionFns.createDraggableData)(this, coreData);\n      const newState = {\n        x: uiData.x,\n        y: uiData.y,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // Keep within bounds.\n      if (this.props.bounds) {\n        // Save original x and y.\n        const {\n          x,\n          y\n        } = newState;\n\n        // Add slack to the values used to calculate bound position. This will ensure that if\n        // we start removing slack, the element won't react to it right away until it's been\n        // completely removed.\n        newState.x += this.state.slackX;\n        newState.y += this.state.slackY;\n\n        // Get bound position. This will ceil/floor the x and y within the boundaries.\n        const [newStateX, newStateY] = (0, _positionFns.getBoundPosition)(this, newState.x, newState.y);\n        newState.x = newStateX;\n        newState.y = newStateY;\n\n        // Recalculate slack by noting how much was shaved by the boundPosition handler.\n        newState.slackX = this.state.slackX + (x - newState.x);\n        newState.slackY = this.state.slackY + (y - newState.y);\n\n        // Update the event we fire to reflect what really happened after bounds took effect.\n        uiData.x = newState.x;\n        uiData.y = newState.y;\n        uiData.deltaX = newState.x - this.state.x;\n        uiData.deltaY = newState.y - this.state.y;\n      }\n\n      // Short-circuit if user's callback killed it.\n      const shouldUpdate = this.props.onDrag(e, uiData);\n      if (shouldUpdate === false) return false;\n      this.setState(newState);\n    });\n    _defineProperty(this, \"onDragStop\", (e, coreData) => {\n      if (!this.state.dragging) return false;\n\n      // Short-circuit if user's callback killed it.\n      const shouldContinue = this.props.onStop(e, (0, _positionFns.createDraggableData)(this, coreData));\n      if (shouldContinue === false) return false;\n      (0, _log.default)('Draggable: onDragStop: %j', coreData);\n      const newState /*: Partial<DraggableState>*/ = {\n        dragging: false,\n        slackX: 0,\n        slackY: 0\n      };\n\n      // If this is a controlled component, the result of this operation will be to\n      // revert back to the old position. We expect a handler on `onDragStop`, at the least.\n      const controlled = Boolean(this.props.position);\n      if (controlled) {\n        const {\n          x,\n          y\n        } = this.props.position;\n        newState.x = x;\n        newState.y = y;\n      }\n      this.setState(newState);\n    });\n    this.state = {\n      // Whether or not we are currently dragging.\n      dragging: false,\n      // Whether or not we have been dragged before.\n      dragged: false,\n      // Current transform x and y.\n      x: props.position ? props.position.x : props.defaultPosition.x,\n      y: props.position ? props.position.y : props.defaultPosition.y,\n      prevPropsPosition: {\n        ...props.position\n      },\n      // Used for compensating for out-of-bounds drags\n      slackX: 0,\n      slackY: 0,\n      // Can only determine if SVG after mounting\n      isElementSVG: false\n    };\n    if (props.position && !(props.onDrag || props.onStop)) {\n      // eslint-disable-next-line no-console\n      console.warn('A `position` was applied to this <Draggable>, without drag handlers. This will make this ' + 'component effectively undraggable. Please attach `onDrag` or `onStop` handlers so you can adjust the ' + '`position` of this element.');\n    }\n  }\n  componentDidMount() {\n    // Check to see if the element passed is an instanceof SVGElement\n    if (typeof window.SVGElement !== 'undefined' && this.findDOMNode() instanceof window.SVGElement) {\n      this.setState({\n        isElementSVG: true\n      });\n    }\n  }\n  componentWillUnmount() {\n    this.setState({\n      dragging: false\n    }); // prevents invariant if unmounted while dragging\n  }\n\n  // React Strict Mode compatibility: if `nodeRef` is passed, we will use it instead of trying to find\n  // the underlying DOM node ourselves. See the README for more information.\n  findDOMNode() /*: ?HTMLElement*/{\n    var _this$props$nodeRef$c, _this$props;\n    return (_this$props$nodeRef$c = (_this$props = this.props) === null || _this$props === void 0 || (_this$props = _this$props.nodeRef) === null || _this$props === void 0 ? void 0 : _this$props.current) !== null && _this$props$nodeRef$c !== void 0 ? _this$props$nodeRef$c : _reactDom.default.findDOMNode(this);\n  }\n  render() /*: ReactElement<any>*/{\n    const {\n      axis,\n      bounds,\n      children,\n      defaultPosition,\n      defaultClassName,\n      defaultClassNameDragging,\n      defaultClassNameDragged,\n      position,\n      positionOffset,\n      scale,\n      ...draggableCoreProps\n    } = this.props;\n    let style = {};\n    let svgTransform = null;\n\n    // If this is controlled, we don't want to move it - unless it's dragging.\n    const controlled = Boolean(position);\n    const draggable = !controlled || this.state.dragging;\n    const validPosition = position || defaultPosition;\n    const transformOpts = {\n      // Set left if horizontal drag is enabled\n      x: (0, _positionFns.canDragX)(this) && draggable ? this.state.x : validPosition.x,\n      // Set top if vertical drag is enabled\n      y: (0, _positionFns.canDragY)(this) && draggable ? this.state.y : validPosition.y\n    };\n\n    // If this element was SVG, we use the `transform` attribute.\n    if (this.state.isElementSVG) {\n      svgTransform = (0, _domFns.createSVGTransform)(transformOpts, positionOffset);\n    } else {\n      // Add a CSS transform to move the element around. This allows us to move the element around\n      // without worrying about whether or not it is relatively or absolutely positioned.\n      // If the item you are dragging already has a transform set, wrap it in a <span> so <Draggable>\n      // has a clean slate.\n      style = (0, _domFns.createCSSTransform)(transformOpts, positionOffset);\n    }\n\n    // Mark with class while dragging\n    const className = (0, _clsx.default)(children.props.className || '', defaultClassName, {\n      [defaultClassNameDragging]: this.state.dragging,\n      [defaultClassNameDragged]: this.state.dragged\n    });\n\n    // Reuse the child provided\n    // This makes it flexible to use whatever element is wanted (div, ul, etc)\n    return /*#__PURE__*/React.createElement(_DraggableCore.default, _extends({}, draggableCoreProps, {\n      onStart: this.onDragStart,\n      onDrag: this.onDrag,\n      onStop: this.onDragStop\n    }), /*#__PURE__*/React.cloneElement(React.Children.only(children), {\n      className: className,\n      style: {\n        ...children.props.style,\n        ...style\n      },\n      transform: svgTransform\n    }));\n  }\n}\nexports.default = Draggable;\n_defineProperty(Draggable, \"displayName\", 'Draggable');\n_defineProperty(Draggable, \"propTypes\", {\n  // Accepts all props <DraggableCore> accepts.\n  ..._DraggableCore.default.propTypes,\n  /**\n   * `axis` determines which axis the draggable can move.\n   *\n   *  Note that all callbacks will still return data as normal. This only\n   *  controls flushing to the DOM.\n   *\n   * 'both' allows movement horizontally and vertically.\n   * 'x' limits movement to horizontal axis.\n   * 'y' limits movement to vertical axis.\n   * 'none' limits all movement.\n   *\n   * Defaults to 'both'.\n   */\n  axis: _propTypes.default.oneOf(['both', 'x', 'y', 'none']),\n  /**\n   * `bounds` determines the range of movement available to the element.\n   * Available values are:\n   *\n   * 'parent' restricts movement within the Draggable's parent node.\n   *\n   * Alternatively, pass an object with the following properties, all of which are optional:\n   *\n   * {left: LEFT_BOUND, right: RIGHT_BOUND, bottom: BOTTOM_BOUND, top: TOP_BOUND}\n   *\n   * All values are in px.\n   *\n   * Example:\n   *\n   * ```jsx\n   *   let App = React.createClass({\n   *       render: function () {\n   *         return (\n   *            <Draggable bounds={{right: 300, bottom: 300}}>\n   *              <div>Content</div>\n   *           </Draggable>\n   *         );\n   *       }\n   *   });\n   * ```\n   */\n  bounds: _propTypes.default.oneOfType([_propTypes.default.shape({\n    left: _propTypes.default.number,\n    right: _propTypes.default.number,\n    top: _propTypes.default.number,\n    bottom: _propTypes.default.number\n  }), _propTypes.default.string, _propTypes.default.oneOf([false])]),\n  defaultClassName: _propTypes.default.string,\n  defaultClassNameDragging: _propTypes.default.string,\n  defaultClassNameDragged: _propTypes.default.string,\n  /**\n   * `defaultPosition` specifies the x and y that the dragged item should start at\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable defaultPosition={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  defaultPosition: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  positionOffset: _propTypes.default.shape({\n    x: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string]),\n    y: _propTypes.default.oneOfType([_propTypes.default.number, _propTypes.default.string])\n  }),\n  /**\n   * `position`, if present, defines the current position of the element.\n   *\n   *  This is similar to how form elements in React work - if no `position` is supplied, the component\n   *  is uncontrolled.\n   *\n   * Example:\n   *\n   * ```jsx\n   *      let App = React.createClass({\n   *          render: function () {\n   *              return (\n   *                  <Draggable position={{x: 25, y: 25}}>\n   *                      <div>I start with transformX: 25px and transformY: 25px;</div>\n   *                  </Draggable>\n   *              );\n   *          }\n   *      });\n   * ```\n   */\n  position: _propTypes.default.shape({\n    x: _propTypes.default.number,\n    y: _propTypes.default.number\n  }),\n  /**\n   * These properties should be defined on the child, not here.\n   */\n  className: _shims.dontSetMe,\n  style: _shims.dontSetMe,\n  transform: _shims.dontSetMe\n});\n_defineProperty(Draggable, \"defaultProps\", {\n  ..._DraggableCore.default.defaultProps,\n  axis: 'both',\n  bounds: false,\n  defaultClassName: 'react-draggable',\n  defaultClassNameDragging: 'react-draggable-dragging',\n  defaultClassNameDragged: 'react-draggable-dragged',\n  defaultPosition: {\n    x: 0,\n    y: 0\n  },\n  scale: 1\n});", "\"use strict\";\n\nconst {\n  default: Draggable,\n  DraggableCore\n} = require('./Draggable');\n\n// Previous versions of this lib exported <Draggable> as the root export. As to no-// them, or TypeScript, we export *both* as the root and as 'default'.\n// See https://github.com/mzabriskie/react-draggable/pull/254\n// and https://github.com/mzabriskie/react-draggable/issues/266\nmodule.exports = Draggable;\nmodule.exports.default = Draggable;\nmodule.exports.DraggableCore = DraggableCore;", "import { createElement, PureComponent } from 'react';\nimport Draggable from 'react-draggable';\nimport { Resizable } from 're-resizable';\nimport { flushSync } from 'react-dom';\n\n/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation. All rights reserved.\r\nLicensed under the Apache License, Version 2.0 (the \"License\"); you may not use\r\nthis file except in compliance with the License. You may obtain a copy of the\r\nLicense at http://www.apache.org/licenses/LICENSE-2.0\r\n\r\nTHIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\nKIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED\r\nWARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,\r\nMERCHANTABLITY OR NON-INFRINGEMENT.\r\n\r\nSee the Apache Version 2.0 License for specific language governing permissions\r\nand limitations under the License.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}\n\nvar resizableStyle = {\n    width: \"auto\",\n    height: \"auto\",\n    display: \"inline-block\",\n    position: \"absolute\",\n    top: 0,\n    left: 0,\n};\nvar getEnableResizingByFlag = function (flag) { return ({\n    bottom: flag,\n    bottomLeft: flag,\n    bottomRight: flag,\n    left: flag,\n    right: flag,\n    top: flag,\n    topLeft: flag,\n    topRight: flag,\n}); };\nvar Rnd = /** @class */ (function (_super) {\n    __extends(Rnd, _super);\n    function Rnd(props) {\n        var _this = _super.call(this, props) || this;\n        _this.resizingPosition = { x: 0, y: 0 };\n        _this.offsetFromParent = { left: 0, top: 0 };\n        _this.resizableElement = { current: null };\n        _this.originalPosition = { x: 0, y: 0 };\n        _this.state = {\n            resizing: false,\n            bounds: {\n                top: 0,\n                right: 0,\n                bottom: 0,\n                left: 0,\n            },\n            maxWidth: props.maxWidth,\n            maxHeight: props.maxHeight,\n        };\n        _this.onResizeStart = _this.onResizeStart.bind(_this);\n        _this.onResize = _this.onResize.bind(_this);\n        _this.onResizeStop = _this.onResizeStop.bind(_this);\n        _this.onDragStart = _this.onDragStart.bind(_this);\n        _this.onDrag = _this.onDrag.bind(_this);\n        _this.onDragStop = _this.onDragStop.bind(_this);\n        _this.getMaxSizesFromProps = _this.getMaxSizesFromProps.bind(_this);\n        return _this;\n    }\n    Rnd.prototype.componentDidMount = function () {\n        this.updateOffsetFromParent();\n        var _a = this.offsetFromParent, left = _a.left, top = _a.top;\n        var _b = this.getDraggablePosition(), x = _b.x, y = _b.y;\n        this.draggable.setState({\n            x: x - left,\n            y: y - top,\n        });\n        // HACK: Apply position adjustment\n        this.forceUpdate();\n    };\n    // HACK: To get `react-draggable` state x and y.\n    Rnd.prototype.getDraggablePosition = function () {\n        var _a = this.draggable.state, x = _a.x, y = _a.y;\n        return { x: x, y: y };\n    };\n    Rnd.prototype.getParent = function () {\n        return this.resizable && this.resizable.parentNode;\n    };\n    Rnd.prototype.getParentSize = function () {\n        return this.resizable.getParentSize();\n    };\n    Rnd.prototype.getMaxSizesFromProps = function () {\n        var maxWidth = typeof this.props.maxWidth === \"undefined\" ? Number.MAX_SAFE_INTEGER : this.props.maxWidth;\n        var maxHeight = typeof this.props.maxHeight === \"undefined\" ? Number.MAX_SAFE_INTEGER : this.props.maxHeight;\n        return { maxWidth: maxWidth, maxHeight: maxHeight };\n    };\n    Rnd.prototype.getSelfElement = function () {\n        return this.resizable && this.resizable.resizable;\n    };\n    Rnd.prototype.getOffsetHeight = function (boundary) {\n        var scale = this.props.scale;\n        switch (this.props.bounds) {\n            case \"window\":\n                return window.innerHeight / scale;\n            case \"body\":\n                return document.body.offsetHeight / scale;\n            default:\n                return boundary.offsetHeight;\n        }\n    };\n    Rnd.prototype.getOffsetWidth = function (boundary) {\n        var scale = this.props.scale;\n        switch (this.props.bounds) {\n            case \"window\":\n                return window.innerWidth / scale;\n            case \"body\":\n                return document.body.offsetWidth / scale;\n            default:\n                return boundary.offsetWidth;\n        }\n    };\n    Rnd.prototype.onDragStart = function (e, data) {\n        if (this.props.onDragStart) {\n            this.props.onDragStart(e, data);\n        }\n        var pos = this.getDraggablePosition();\n        this.originalPosition = pos;\n        if (!this.props.bounds)\n            return;\n        var parent = this.getParent();\n        var scale = this.props.scale;\n        var boundary;\n        if (this.props.bounds === \"parent\") {\n            boundary = parent;\n        }\n        else if (this.props.bounds === \"body\") {\n            var parentRect_1 = parent.getBoundingClientRect();\n            var parentLeft_1 = parentRect_1.left;\n            var parentTop_1 = parentRect_1.top;\n            var bodyRect = document.body.getBoundingClientRect();\n            var left_1 = -(parentLeft_1 - parent.offsetLeft * scale - bodyRect.left) / scale;\n            var top_1 = -(parentTop_1 - parent.offsetTop * scale - bodyRect.top) / scale;\n            var right = (document.body.offsetWidth - this.resizable.size.width * scale) / scale + left_1;\n            var bottom = (document.body.offsetHeight - this.resizable.size.height * scale) / scale + top_1;\n            return this.setState({ bounds: { top: top_1, right: right, bottom: bottom, left: left_1 } });\n        }\n        else if (this.props.bounds === \"window\") {\n            if (!this.resizable)\n                return;\n            var parentRect_2 = parent.getBoundingClientRect();\n            var parentLeft_2 = parentRect_2.left;\n            var parentTop_2 = parentRect_2.top;\n            var left_2 = -(parentLeft_2 - parent.offsetLeft * scale) / scale;\n            var top_2 = -(parentTop_2 - parent.offsetTop * scale) / scale;\n            var right = (window.innerWidth - this.resizable.size.width * scale) / scale + left_2;\n            var bottom = (window.innerHeight - this.resizable.size.height * scale) / scale + top_2;\n            return this.setState({ bounds: { top: top_2, right: right, bottom: bottom, left: left_2 } });\n        }\n        else if (typeof this.props.bounds === \"string\") {\n            boundary = document.querySelector(this.props.bounds);\n        }\n        else if (this.props.bounds instanceof HTMLElement) {\n            boundary = this.props.bounds;\n        }\n        if (!(boundary instanceof HTMLElement) || !(parent instanceof HTMLElement)) {\n            return;\n        }\n        var boundaryRect = boundary.getBoundingClientRect();\n        var boundaryLeft = boundaryRect.left;\n        var boundaryTop = boundaryRect.top;\n        var parentRect = parent.getBoundingClientRect();\n        var parentLeft = parentRect.left;\n        var parentTop = parentRect.top;\n        var left = (boundaryLeft - parentLeft) / scale;\n        var top = boundaryTop - parentTop;\n        if (!this.resizable)\n            return;\n        this.updateOffsetFromParent();\n        var offset = this.offsetFromParent;\n        this.setState({\n            bounds: {\n                top: top - offset.top,\n                right: left + (boundary.offsetWidth - this.resizable.size.width) - offset.left / scale,\n                bottom: top + (boundary.offsetHeight - this.resizable.size.height) - offset.top,\n                left: left - offset.left / scale,\n            },\n        });\n    };\n    Rnd.prototype.onDrag = function (e, data) {\n        if (!this.props.onDrag)\n            return;\n        var _a = this.offsetFromParent, left = _a.left, top = _a.top;\n        if (!this.props.dragAxis || this.props.dragAxis === \"both\") {\n            return this.props.onDrag(e, __assign(__assign({}, data), { x: data.x + left, y: data.y + top }));\n        }\n        else if (this.props.dragAxis === \"x\") {\n            return this.props.onDrag(e, __assign(__assign({}, data), { x: data.x + left, y: this.originalPosition.y + top, deltaY: 0 }));\n        }\n        else if (this.props.dragAxis === \"y\") {\n            return this.props.onDrag(e, __assign(__assign({}, data), { x: this.originalPosition.x + left, y: data.y + top, deltaX: 0 }));\n        }\n    };\n    Rnd.prototype.onDragStop = function (e, data) {\n        if (!this.props.onDragStop)\n            return;\n        var _a = this.offsetFromParent, left = _a.left, top = _a.top;\n        if (!this.props.dragAxis || this.props.dragAxis === \"both\") {\n            return this.props.onDragStop(e, __assign(__assign({}, data), { x: data.x + left, y: data.y + top }));\n        }\n        else if (this.props.dragAxis === \"x\") {\n            return this.props.onDragStop(e, __assign(__assign({}, data), { x: data.x + left, y: this.originalPosition.y + top, deltaY: 0 }));\n        }\n        else if (this.props.dragAxis === \"y\") {\n            return this.props.onDragStop(e, __assign(__assign({}, data), { x: this.originalPosition.x + left, y: data.y + top, deltaX: 0 }));\n        }\n    };\n    Rnd.prototype.onResizeStart = function (e, dir, elementRef) {\n        e.stopPropagation();\n        this.setState({\n            resizing: true,\n        });\n        var scale = this.props.scale;\n        var offset = this.offsetFromParent;\n        var pos = this.getDraggablePosition();\n        this.resizingPosition = { x: pos.x + offset.left, y: pos.y + offset.top };\n        this.originalPosition = pos;\n        if (this.props.bounds) {\n            var parent_1 = this.getParent();\n            var boundary = void 0;\n            if (this.props.bounds === \"parent\") {\n                boundary = parent_1;\n            }\n            else if (this.props.bounds === \"body\") {\n                boundary = document.body;\n            }\n            else if (this.props.bounds === \"window\") {\n                boundary = window;\n            }\n            else if (typeof this.props.bounds === \"string\") {\n                boundary = document.querySelector(this.props.bounds);\n            }\n            else if (this.props.bounds instanceof HTMLElement) {\n                boundary = this.props.bounds;\n            }\n            var self_1 = this.getSelfElement();\n            if (self_1 instanceof Element &&\n                (boundary instanceof HTMLElement || boundary === window) &&\n                parent_1 instanceof HTMLElement) {\n                var _a = this.getMaxSizesFromProps(), maxWidth = _a.maxWidth, maxHeight = _a.maxHeight;\n                var parentSize = this.getParentSize();\n                if (maxWidth && typeof maxWidth === \"string\") {\n                    if (maxWidth.endsWith(\"%\")) {\n                        var ratio = Number(maxWidth.replace(\"%\", \"\")) / 100;\n                        maxWidth = parentSize.width * ratio;\n                    }\n                    else if (maxWidth.endsWith(\"px\")) {\n                        maxWidth = Number(maxWidth.replace(\"px\", \"\"));\n                    }\n                }\n                if (maxHeight && typeof maxHeight === \"string\") {\n                    if (maxHeight.endsWith(\"%\")) {\n                        var ratio = Number(maxHeight.replace(\"%\", \"\")) / 100;\n                        maxHeight = parentSize.height * ratio;\n                    }\n                    else if (maxHeight.endsWith(\"px\")) {\n                        maxHeight = Number(maxHeight.replace(\"px\", \"\"));\n                    }\n                }\n                var selfRect = self_1.getBoundingClientRect();\n                var selfLeft = selfRect.left;\n                var selfTop = selfRect.top;\n                var boundaryRect = this.props.bounds === \"window\" ? { left: 0, top: 0 } : boundary.getBoundingClientRect();\n                var boundaryLeft = boundaryRect.left;\n                var boundaryTop = boundaryRect.top;\n                var offsetWidth = this.getOffsetWidth(boundary);\n                var offsetHeight = this.getOffsetHeight(boundary);\n                var hasLeft = dir.toLowerCase().endsWith(\"left\");\n                var hasRight = dir.toLowerCase().endsWith(\"right\");\n                var hasTop = dir.startsWith(\"top\");\n                var hasBottom = dir.startsWith(\"bottom\");\n                if ((hasLeft || hasTop) && this.resizable) {\n                    var max = (selfLeft - boundaryLeft) / scale + this.resizable.size.width;\n                    this.setState({ maxWidth: max > Number(maxWidth) ? maxWidth : max });\n                }\n                // INFO: To set bounds in `lock aspect ratio with bounds` case. See also that story.\n                if (hasRight || (this.props.lockAspectRatio && !hasLeft && !hasTop)) {\n                    var max = offsetWidth + (boundaryLeft - selfLeft) / scale;\n                    this.setState({ maxWidth: max > Number(maxWidth) ? maxWidth : max });\n                }\n                if ((hasTop || hasLeft) && this.resizable) {\n                    var max = (selfTop - boundaryTop) / scale + this.resizable.size.height;\n                    this.setState({\n                        maxHeight: max > Number(maxHeight) ? maxHeight : max,\n                    });\n                }\n                // INFO: To set bounds in `lock aspect ratio with bounds` case. See also that story.\n                if (hasBottom || (this.props.lockAspectRatio && !hasTop && !hasLeft)) {\n                    var max = offsetHeight + (boundaryTop - selfTop) / scale;\n                    this.setState({\n                        maxHeight: max > Number(maxHeight) ? maxHeight : max,\n                    });\n                }\n            }\n        }\n        else {\n            this.setState({\n                maxWidth: this.props.maxWidth,\n                maxHeight: this.props.maxHeight,\n            });\n        }\n        if (this.props.onResizeStart) {\n            this.props.onResizeStart(e, dir, elementRef);\n        }\n    };\n    Rnd.prototype.onResize = function (e, direction, elementRef, delta) {\n        var _this = this;\n        // INFO: Apply x and y position adjustments caused by resizing to draggable\n        var newPos = { x: this.originalPosition.x, y: this.originalPosition.y };\n        var left = -delta.width;\n        var top = -delta.height;\n        var directions = [\"top\", \"left\", \"topLeft\", \"bottomLeft\", \"topRight\"];\n        if (directions.includes(direction)) {\n            if (direction === \"bottomLeft\") {\n                newPos.x += left;\n            }\n            else if (direction === \"topRight\") {\n                newPos.y += top;\n            }\n            else {\n                newPos.x += left;\n                newPos.y += top;\n            }\n        }\n        var draggableState = this.draggable.state;\n        if (newPos.x !== draggableState.x || newPos.y !== draggableState.y) {\n            flushSync(function () {\n                _this.draggable.setState(newPos);\n            });\n        }\n        this.updateOffsetFromParent();\n        var offset = this.offsetFromParent;\n        var x = this.getDraggablePosition().x + offset.left;\n        var y = this.getDraggablePosition().y + offset.top;\n        this.resizingPosition = { x: x, y: y };\n        if (!this.props.onResize)\n            return;\n        this.props.onResize(e, direction, elementRef, delta, {\n            x: x,\n            y: y,\n        });\n    };\n    Rnd.prototype.onResizeStop = function (e, direction, elementRef, delta) {\n        this.setState({\n            resizing: false,\n        });\n        var _a = this.getMaxSizesFromProps(), maxWidth = _a.maxWidth, maxHeight = _a.maxHeight;\n        this.setState({ maxWidth: maxWidth, maxHeight: maxHeight });\n        if (this.props.onResizeStop) {\n            this.props.onResizeStop(e, direction, elementRef, delta, this.resizingPosition);\n        }\n    };\n    Rnd.prototype.updateSize = function (size) {\n        if (!this.resizable)\n            return;\n        this.resizable.updateSize({ width: size.width, height: size.height });\n    };\n    Rnd.prototype.updatePosition = function (position) {\n        this.draggable.setState(position);\n    };\n    Rnd.prototype.updateOffsetFromParent = function () {\n        var scale = this.props.scale;\n        var parent = this.getParent();\n        var self = this.getSelfElement();\n        if (!parent || self === null) {\n            return {\n                top: 0,\n                left: 0,\n            };\n        }\n        var parentRect = parent.getBoundingClientRect();\n        var parentLeft = parentRect.left;\n        var parentTop = parentRect.top;\n        var selfRect = self.getBoundingClientRect();\n        var position = this.getDraggablePosition();\n        var scrollLeft = parent.scrollLeft;\n        var scrollTop = parent.scrollTop;\n        this.offsetFromParent = {\n            left: selfRect.left - parentLeft + scrollLeft - position.x * scale,\n            top: selfRect.top - parentTop + scrollTop - position.y * scale,\n        };\n    };\n    Rnd.prototype.render = function () {\n        var _this = this;\n        var _a = this.props, disableDragging = _a.disableDragging, style = _a.style, dragHandleClassName = _a.dragHandleClassName, position = _a.position, onMouseDown = _a.onMouseDown, onMouseUp = _a.onMouseUp, dragAxis = _a.dragAxis, dragGrid = _a.dragGrid, bounds = _a.bounds, enableUserSelectHack = _a.enableUserSelectHack, cancel = _a.cancel, children = _a.children, onResizeStart = _a.onResizeStart, onResize = _a.onResize, onResizeStop = _a.onResizeStop, onDragStart = _a.onDragStart, onDrag = _a.onDrag, onDragStop = _a.onDragStop, resizeHandleStyles = _a.resizeHandleStyles, resizeHandleClasses = _a.resizeHandleClasses, resizeHandleComponent = _a.resizeHandleComponent, enableResizing = _a.enableResizing, resizeGrid = _a.resizeGrid, resizeHandleWrapperClass = _a.resizeHandleWrapperClass, resizeHandleWrapperStyle = _a.resizeHandleWrapperStyle, scale = _a.scale, allowAnyClick = _a.allowAnyClick, dragPositionOffset = _a.dragPositionOffset, resizableProps = __rest(_a, [\"disableDragging\", \"style\", \"dragHandleClassName\", \"position\", \"onMouseDown\", \"onMouseUp\", \"dragAxis\", \"dragGrid\", \"bounds\", \"enableUserSelectHack\", \"cancel\", \"children\", \"onResizeStart\", \"onResize\", \"onResizeStop\", \"onDragStart\", \"onDrag\", \"onDragStop\", \"resizeHandleStyles\", \"resizeHandleClasses\", \"resizeHandleComponent\", \"enableResizing\", \"resizeGrid\", \"resizeHandleWrapperClass\", \"resizeHandleWrapperStyle\", \"scale\", \"allowAnyClick\", \"dragPositionOffset\"]);\n        var defaultValue = this.props.default ? __assign({}, this.props.default) : undefined;\n        // Remove unknown props, see also https://reactjs.org/warnings/unknown-prop.html\n        delete resizableProps.default;\n        var cursorStyle = disableDragging || dragHandleClassName ? { cursor: \"auto\" } : { cursor: \"move\" };\n        var innerStyle = __assign(__assign(__assign({}, resizableStyle), cursorStyle), style);\n        var _b = this.offsetFromParent, left = _b.left, top = _b.top;\n        var draggablePosition;\n        if (position) {\n            draggablePosition = {\n                x: position.x - left,\n                y: position.y - top,\n            };\n        }\n        // INFO: Make uncontorolled component when resizing to control position by setPostion.\n        var pos = this.state.resizing ? undefined : draggablePosition;\n        var dragAxisOrUndefined = this.state.resizing ? \"both\" : dragAxis;\n        return (createElement(Draggable, { ref: function (c) {\n                if (!c)\n                    return;\n                _this.draggable = c;\n            }, handle: dragHandleClassName ? \".\".concat(dragHandleClassName) : undefined, defaultPosition: defaultValue, onMouseDown: onMouseDown, \n            // @ts-expect-error\n            onMouseUp: onMouseUp, onStart: this.onDragStart, onDrag: this.onDrag, onStop: this.onDragStop, axis: dragAxisOrUndefined, disabled: disableDragging, grid: dragGrid, bounds: bounds ? this.state.bounds : undefined, position: pos, enableUserSelectHack: enableUserSelectHack, cancel: cancel, scale: scale, allowAnyClick: allowAnyClick, nodeRef: this.resizableElement, positionOffset: dragPositionOffset },\n            createElement(Resizable, __assign({}, resizableProps, { ref: function (c) {\n                    if (!c)\n                        return;\n                    _this.resizable = c;\n                    _this.resizableElement.current = c.resizable;\n                }, defaultSize: defaultValue, size: this.props.size, enable: typeof enableResizing === \"boolean\" ? getEnableResizingByFlag(enableResizing) : enableResizing, onResizeStart: this.onResizeStart, onResize: this.onResize, onResizeStop: this.onResizeStop, style: innerStyle, minWidth: this.props.minWidth, minHeight: this.props.minHeight, maxWidth: this.state.resizing ? this.state.maxWidth : this.props.maxWidth, maxHeight: this.state.resizing ? this.state.maxHeight : this.props.maxHeight, grid: resizeGrid, handleWrapperClass: resizeHandleWrapperClass, handleWrapperStyle: resizeHandleWrapperStyle, lockAspectRatio: this.props.lockAspectRatio, lockAspectRatioExtraWidth: this.props.lockAspectRatioExtraWidth, lockAspectRatioExtraHeight: this.props.lockAspectRatioExtraHeight, handleStyles: resizeHandleStyles, handleClasses: resizeHandleClasses, handleComponent: resizeHandleComponent, scale: this.props.scale }), children)));\n    };\n    Rnd.defaultProps = {\n        maxWidth: Number.MAX_SAFE_INTEGER,\n        maxHeight: Number.MAX_SAFE_INTEGER,\n        scale: 1,\n        onResizeStart: function () { },\n        onResize: function () { },\n        onResizeStop: function () { },\n        onDragStart: function () { },\n        onDrag: function () { },\n        onDragStop: function () { },\n    };\n    return Rnd;\n}(PureComponent));\n\nexport { Rnd };\n", "var __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nimport { PureComponent } from 'react';\nimport { flushSync } from 'react-dom';\nimport { Resizer } from './resizer';\nvar DEFAULT_SIZE = {\n    width: 'auto',\n    height: 'auto',\n};\nvar clamp = function (n, min, max) { return Math.max(Math.min(n, max), min); };\nvar snap = function (n, size, gridGap) {\n    var v = Math.round(n / size);\n    return v * size + gridGap * (v - 1);\n};\nvar hasDirection = function (dir, target) {\n    return new RegExp(dir, 'i').test(target);\n};\n// INFO: In case of window is a Proxy and does not porxy Events correctly, use isTouchEvent & isMouseEvent to distinguish event type instead of `instanceof`.\nvar isTouchEvent = function (event) {\n    return Boolean(event.touches && event.touches.length);\n};\nvar isMouseEvent = function (event) {\n    return Boolean((event.clientX || event.clientX === 0) &&\n        (event.clientY || event.clientY === 0));\n};\nvar findClosestSnap = function (n, snapArray, snapGap) {\n    if (snapGap === void 0) { snapGap = 0; }\n    var closestGapIndex = snapArray.reduce(function (prev, curr, index) { return (Math.abs(curr - n) < Math.abs(snapArray[prev] - n) ? index : prev); }, 0);\n    var gap = Math.abs(snapArray[closestGapIndex] - n);\n    return snapGap === 0 || gap < snapGap ? snapArray[closestGapIndex] : n;\n};\nvar getStringSize = function (n) {\n    n = n.toString();\n    if (n === 'auto') {\n        return n;\n    }\n    if (n.endsWith('px')) {\n        return n;\n    }\n    if (n.endsWith('%')) {\n        return n;\n    }\n    if (n.endsWith('vh')) {\n        return n;\n    }\n    if (n.endsWith('vw')) {\n        return n;\n    }\n    if (n.endsWith('vmax')) {\n        return n;\n    }\n    if (n.endsWith('vmin')) {\n        return n;\n    }\n    return \"\".concat(n, \"px\");\n};\nvar getPixelSize = function (size, parentSize, innerWidth, innerHeight) {\n    if (size && typeof size === 'string') {\n        if (size.endsWith('px')) {\n            return Number(size.replace('px', ''));\n        }\n        if (size.endsWith('%')) {\n            var ratio = Number(size.replace('%', '')) / 100;\n            return parentSize * ratio;\n        }\n        if (size.endsWith('vw')) {\n            var ratio = Number(size.replace('vw', '')) / 100;\n            return innerWidth * ratio;\n        }\n        if (size.endsWith('vh')) {\n            var ratio = Number(size.replace('vh', '')) / 100;\n            return innerHeight * ratio;\n        }\n    }\n    return size;\n};\nvar calculateNewMax = function (parentSize, innerWidth, innerHeight, maxWidth, maxHeight, minWidth, minHeight) {\n    maxWidth = getPixelSize(maxWidth, parentSize.width, innerWidth, innerHeight);\n    maxHeight = getPixelSize(maxHeight, parentSize.height, innerWidth, innerHeight);\n    minWidth = getPixelSize(minWidth, parentSize.width, innerWidth, innerHeight);\n    minHeight = getPixelSize(minHeight, parentSize.height, innerWidth, innerHeight);\n    return {\n        maxWidth: typeof maxWidth === 'undefined' ? undefined : Number(maxWidth),\n        maxHeight: typeof maxHeight === 'undefined' ? undefined : Number(maxHeight),\n        minWidth: typeof minWidth === 'undefined' ? undefined : Number(minWidth),\n        minHeight: typeof minHeight === 'undefined' ? undefined : Number(minHeight),\n    };\n};\n/**\n * transform T | [T, T] to [T, T]\n * @param val\n * @returns\n */\n// tslint:disable-next-line\nvar normalizeToPair = function (val) { return (Array.isArray(val) ? val : [val, val]); };\nvar definedProps = [\n    'as',\n    'ref',\n    'style',\n    'className',\n    'grid',\n    'gridGap',\n    'snap',\n    'bounds',\n    'boundsByDirection',\n    'size',\n    'defaultSize',\n    'minWidth',\n    'minHeight',\n    'maxWidth',\n    'maxHeight',\n    'lockAspectRatio',\n    'lockAspectRatioExtraWidth',\n    'lockAspectRatioExtraHeight',\n    'enable',\n    'handleStyles',\n    'handleClasses',\n    'handleWrapperStyle',\n    'handleWrapperClass',\n    'children',\n    'onResizeStart',\n    'onResize',\n    'onResizeStop',\n    'handleComponent',\n    'scale',\n    'resizeRatio',\n    'snapGap',\n];\n// HACK: This class is used to calculate % size.\nvar baseClassName = '__resizable_base__';\nvar Resizable = /** @class */ (function (_super) {\n    __extends(Resizable, _super);\n    function Resizable(props) {\n        var _a, _b, _c, _d;\n        var _this = _super.call(this, props) || this;\n        _this.ratio = 1;\n        _this.resizable = null;\n        // For parent boundary\n        _this.parentLeft = 0;\n        _this.parentTop = 0;\n        // For boundary\n        _this.resizableLeft = 0;\n        _this.resizableRight = 0;\n        _this.resizableTop = 0;\n        _this.resizableBottom = 0;\n        // For target boundary\n        _this.targetLeft = 0;\n        _this.targetTop = 0;\n        _this.delta = {\n            width: 0,\n            height: 0,\n        };\n        _this.appendBase = function () {\n            if (!_this.resizable || !_this.window) {\n                return null;\n            }\n            var parent = _this.parentNode;\n            if (!parent) {\n                return null;\n            }\n            var element = _this.window.document.createElement('div');\n            element.style.width = '100%';\n            element.style.height = '100%';\n            element.style.position = 'absolute';\n            element.style.transform = 'scale(0, 0)';\n            element.style.left = '0';\n            element.style.flex = '0 0 100%';\n            if (element.classList) {\n                element.classList.add(baseClassName);\n            }\n            else {\n                element.className += baseClassName;\n            }\n            parent.appendChild(element);\n            return element;\n        };\n        _this.removeBase = function (base) {\n            var parent = _this.parentNode;\n            if (!parent) {\n                return;\n            }\n            parent.removeChild(base);\n        };\n        _this.state = {\n            isResizing: false,\n            width: (_b = (_a = _this.propsSize) === null || _a === void 0 ? void 0 : _a.width) !== null && _b !== void 0 ? _b : 'auto',\n            height: (_d = (_c = _this.propsSize) === null || _c === void 0 ? void 0 : _c.height) !== null && _d !== void 0 ? _d : 'auto',\n            direction: 'right',\n            original: {\n                x: 0,\n                y: 0,\n                width: 0,\n                height: 0,\n            },\n            backgroundStyle: {\n                height: '100%',\n                width: '100%',\n                backgroundColor: 'rgba(0,0,0,0)',\n                cursor: 'auto',\n                opacity: 0,\n                position: 'fixed',\n                zIndex: 9999,\n                top: '0',\n                left: '0',\n                bottom: '0',\n                right: '0',\n            },\n            flexBasis: undefined,\n        };\n        _this.onResizeStart = _this.onResizeStart.bind(_this);\n        _this.onMouseMove = _this.onMouseMove.bind(_this);\n        _this.onMouseUp = _this.onMouseUp.bind(_this);\n        return _this;\n    }\n    Object.defineProperty(Resizable.prototype, \"parentNode\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            return this.resizable.parentNode;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"window\", {\n        get: function () {\n            if (!this.resizable) {\n                return null;\n            }\n            if (!this.resizable.ownerDocument) {\n                return null;\n            }\n            return this.resizable.ownerDocument.defaultView;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"propsSize\", {\n        get: function () {\n            return this.props.size || this.props.defaultSize || DEFAULT_SIZE;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"size\", {\n        get: function () {\n            var width = 0;\n            var height = 0;\n            if (this.resizable && this.window) {\n                var orgWidth = this.resizable.offsetWidth;\n                var orgHeight = this.resizable.offsetHeight;\n                // HACK: Set position `relative` to get parent size.\n                //       This is because when re-resizable set `absolute`, I can not get base width correctly.\n                var orgPosition = this.resizable.style.position;\n                if (orgPosition !== 'relative') {\n                    this.resizable.style.position = 'relative';\n                }\n                // INFO: Use original width or height if set auto.\n                width = this.resizable.style.width !== 'auto' ? this.resizable.offsetWidth : orgWidth;\n                height = this.resizable.style.height !== 'auto' ? this.resizable.offsetHeight : orgHeight;\n                // Restore original position\n                this.resizable.style.position = orgPosition;\n            }\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Resizable.prototype, \"sizeStyle\", {\n        get: function () {\n            var _this = this;\n            var size = this.props.size;\n            var getSize = function (key) {\n                var _a;\n                if (typeof _this.state[key] === 'undefined' || _this.state[key] === 'auto') {\n                    return 'auto';\n                }\n                if (_this.propsSize && _this.propsSize[key] && ((_a = _this.propsSize[key]) === null || _a === void 0 ? void 0 : _a.toString().endsWith('%'))) {\n                    if (_this.state[key].toString().endsWith('%')) {\n                        return _this.state[key].toString();\n                    }\n                    var parentSize = _this.getParentSize();\n                    var value = Number(_this.state[key].toString().replace('px', ''));\n                    var percent = (value / parentSize[key]) * 100;\n                    return \"\".concat(percent, \"%\");\n                }\n                return getStringSize(_this.state[key]);\n            };\n            var width = size && typeof size.width !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.width)\n                : getSize('width');\n            var height = size && typeof size.height !== 'undefined' && !this.state.isResizing\n                ? getStringSize(size.height)\n                : getSize('height');\n            return { width: width, height: height };\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Resizable.prototype.getParentSize = function () {\n        if (!this.parentNode) {\n            if (!this.window) {\n                return { width: 0, height: 0 };\n            }\n            return { width: this.window.innerWidth, height: this.window.innerHeight };\n        }\n        var base = this.appendBase();\n        if (!base) {\n            return { width: 0, height: 0 };\n        }\n        // INFO: To calculate parent width with flex layout\n        var wrapChanged = false;\n        var wrap = this.parentNode.style.flexWrap;\n        if (wrap !== 'wrap') {\n            wrapChanged = true;\n            this.parentNode.style.flexWrap = 'wrap';\n            // HACK: Use relative to get parent padding size\n        }\n        base.style.position = 'relative';\n        base.style.minWidth = '100%';\n        base.style.minHeight = '100%';\n        var size = {\n            width: base.offsetWidth,\n            height: base.offsetHeight,\n        };\n        if (wrapChanged) {\n            this.parentNode.style.flexWrap = wrap;\n        }\n        this.removeBase(base);\n        return size;\n    };\n    Resizable.prototype.bindEvents = function () {\n        if (this.window) {\n            this.window.addEventListener('mouseup', this.onMouseUp);\n            this.window.addEventListener('mousemove', this.onMouseMove);\n            this.window.addEventListener('mouseleave', this.onMouseUp);\n            this.window.addEventListener('touchmove', this.onMouseMove, {\n                capture: true,\n                passive: false,\n            });\n            this.window.addEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.unbindEvents = function () {\n        if (this.window) {\n            this.window.removeEventListener('mouseup', this.onMouseUp);\n            this.window.removeEventListener('mousemove', this.onMouseMove);\n            this.window.removeEventListener('mouseleave', this.onMouseUp);\n            this.window.removeEventListener('touchmove', this.onMouseMove, true);\n            this.window.removeEventListener('touchend', this.onMouseUp);\n        }\n    };\n    Resizable.prototype.componentDidMount = function () {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        this.setState({\n            width: this.state.width || this.size.width,\n            height: this.state.height || this.size.height,\n            flexBasis: computedStyle.flexBasis !== 'auto' ? computedStyle.flexBasis : undefined,\n        });\n    };\n    Resizable.prototype.componentWillUnmount = function () {\n        if (this.window) {\n            this.unbindEvents();\n        }\n    };\n    Resizable.prototype.createSizeForCssProperty = function (newSize, kind) {\n        var propsSize = this.propsSize && this.propsSize[kind];\n        return this.state[kind] === 'auto' &&\n            this.state.original[kind] === newSize &&\n            (typeof propsSize === 'undefined' || propsSize === 'auto')\n            ? 'auto'\n            : newSize;\n    };\n    Resizable.prototype.calculateNewMaxFromBoundary = function (maxWidth, maxHeight) {\n        var boundsByDirection = this.props.boundsByDirection;\n        var direction = this.state.direction;\n        var widthByDirection = boundsByDirection && hasDirection('left', direction);\n        var heightByDirection = boundsByDirection && hasDirection('top', direction);\n        var boundWidth;\n        var boundHeight;\n        if (this.props.bounds === 'parent') {\n            var parent_1 = this.parentNode;\n            if (parent_1) {\n                boundWidth = widthByDirection\n                    ? this.resizableRight - this.parentLeft\n                    : parent_1.offsetWidth + (this.parentLeft - this.resizableLeft);\n                boundHeight = heightByDirection\n                    ? this.resizableBottom - this.parentTop\n                    : parent_1.offsetHeight + (this.parentTop - this.resizableTop);\n            }\n        }\n        else if (this.props.bounds === 'window') {\n            if (this.window) {\n                boundWidth = widthByDirection ? this.resizableRight : this.window.innerWidth - this.resizableLeft;\n                boundHeight = heightByDirection ? this.resizableBottom : this.window.innerHeight - this.resizableTop;\n            }\n        }\n        else if (this.props.bounds) {\n            boundWidth = widthByDirection\n                ? this.resizableRight - this.targetLeft\n                : this.props.bounds.offsetWidth + (this.targetLeft - this.resizableLeft);\n            boundHeight = heightByDirection\n                ? this.resizableBottom - this.targetTop\n                : this.props.bounds.offsetHeight + (this.targetTop - this.resizableTop);\n        }\n        if (boundWidth && Number.isFinite(boundWidth)) {\n            maxWidth = maxWidth && maxWidth < boundWidth ? maxWidth : boundWidth;\n        }\n        if (boundHeight && Number.isFinite(boundHeight)) {\n            maxHeight = maxHeight && maxHeight < boundHeight ? maxHeight : boundHeight;\n        }\n        return { maxWidth: maxWidth, maxHeight: maxHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromDirection = function (clientX, clientY) {\n        var scale = this.props.scale || 1;\n        var _a = normalizeToPair(this.props.resizeRatio || 1), resizeRatioX = _a[0], resizeRatioY = _a[1];\n        var _b = this.state, direction = _b.direction, original = _b.original;\n        var _c = this.props, lockAspectRatio = _c.lockAspectRatio, lockAspectRatioExtraHeight = _c.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _c.lockAspectRatioExtraWidth;\n        var newWidth = original.width;\n        var newHeight = original.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (hasDirection('right', direction)) {\n            newWidth = original.width + ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('left', direction)) {\n            newWidth = original.width - ((clientX - original.x) * resizeRatioX) / scale;\n            if (lockAspectRatio) {\n                newHeight = (newWidth - extraWidth) / this.ratio + extraHeight;\n            }\n        }\n        if (hasDirection('bottom', direction)) {\n            newHeight = original.height + ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        if (hasDirection('top', direction)) {\n            newHeight = original.height - ((clientY - original.y) * resizeRatioY) / scale;\n            if (lockAspectRatio) {\n                newWidth = (newHeight - extraHeight) * this.ratio + extraWidth;\n            }\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.calculateNewSizeFromAspectRatio = function (newWidth, newHeight, max, min) {\n        var _a = this.props, lockAspectRatio = _a.lockAspectRatio, lockAspectRatioExtraHeight = _a.lockAspectRatioExtraHeight, lockAspectRatioExtraWidth = _a.lockAspectRatioExtraWidth;\n        var computedMinWidth = typeof min.width === 'undefined' ? 10 : min.width;\n        var computedMaxWidth = typeof max.width === 'undefined' || max.width < 0 ? newWidth : max.width;\n        var computedMinHeight = typeof min.height === 'undefined' ? 10 : min.height;\n        var computedMaxHeight = typeof max.height === 'undefined' || max.height < 0 ? newHeight : max.height;\n        var extraHeight = lockAspectRatioExtraHeight || 0;\n        var extraWidth = lockAspectRatioExtraWidth || 0;\n        if (lockAspectRatio) {\n            var extraMinWidth = (computedMinHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMaxWidth = (computedMaxHeight - extraHeight) * this.ratio + extraWidth;\n            var extraMinHeight = (computedMinWidth - extraWidth) / this.ratio + extraHeight;\n            var extraMaxHeight = (computedMaxWidth - extraWidth) / this.ratio + extraHeight;\n            var lockedMinWidth = Math.max(computedMinWidth, extraMinWidth);\n            var lockedMaxWidth = Math.min(computedMaxWidth, extraMaxWidth);\n            var lockedMinHeight = Math.max(computedMinHeight, extraMinHeight);\n            var lockedMaxHeight = Math.min(computedMaxHeight, extraMaxHeight);\n            newWidth = clamp(newWidth, lockedMinWidth, lockedMaxWidth);\n            newHeight = clamp(newHeight, lockedMinHeight, lockedMaxHeight);\n        }\n        else {\n            newWidth = clamp(newWidth, computedMinWidth, computedMaxWidth);\n            newHeight = clamp(newHeight, computedMinHeight, computedMaxHeight);\n        }\n        return { newWidth: newWidth, newHeight: newHeight };\n    };\n    Resizable.prototype.setBoundingClientRect = function () {\n        var adjustedScale = 1 / (this.props.scale || 1);\n        // For parent boundary\n        if (this.props.bounds === 'parent') {\n            var parent_2 = this.parentNode;\n            if (parent_2) {\n                var parentRect = parent_2.getBoundingClientRect();\n                this.parentLeft = parentRect.left * adjustedScale;\n                this.parentTop = parentRect.top * adjustedScale;\n            }\n        }\n        // For target(html element) boundary\n        if (this.props.bounds && typeof this.props.bounds !== 'string') {\n            var targetRect = this.props.bounds.getBoundingClientRect();\n            this.targetLeft = targetRect.left * adjustedScale;\n            this.targetTop = targetRect.top * adjustedScale;\n        }\n        // For boundary\n        if (this.resizable) {\n            var _a = this.resizable.getBoundingClientRect(), left = _a.left, top_1 = _a.top, right = _a.right, bottom = _a.bottom;\n            this.resizableLeft = left * adjustedScale;\n            this.resizableRight = right * adjustedScale;\n            this.resizableTop = top_1 * adjustedScale;\n            this.resizableBottom = bottom * adjustedScale;\n        }\n    };\n    Resizable.prototype.onResizeStart = function (event, direction) {\n        if (!this.resizable || !this.window) {\n            return;\n        }\n        var clientX = 0;\n        var clientY = 0;\n        if (event.nativeEvent && isMouseEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.clientX;\n            clientY = event.nativeEvent.clientY;\n        }\n        else if (event.nativeEvent && isTouchEvent(event.nativeEvent)) {\n            clientX = event.nativeEvent.touches[0].clientX;\n            clientY = event.nativeEvent.touches[0].clientY;\n        }\n        if (this.props.onResizeStart) {\n            if (this.resizable) {\n                var startResize = this.props.onResizeStart(event, direction, this.resizable);\n                if (startResize === false) {\n                    return;\n                }\n            }\n        }\n        // Fix #168\n        if (this.props.size) {\n            if (typeof this.props.size.height !== 'undefined' && this.props.size.height !== this.state.height) {\n                this.setState({ height: this.props.size.height });\n            }\n            if (typeof this.props.size.width !== 'undefined' && this.props.size.width !== this.state.width) {\n                this.setState({ width: this.props.size.width });\n            }\n        }\n        // For lockAspectRatio case\n        this.ratio =\n            typeof this.props.lockAspectRatio === 'number' ? this.props.lockAspectRatio : this.size.width / this.size.height;\n        var flexBasis;\n        var computedStyle = this.window.getComputedStyle(this.resizable);\n        if (computedStyle.flexBasis !== 'auto') {\n            var parent_3 = this.parentNode;\n            if (parent_3) {\n                var dir = this.window.getComputedStyle(parent_3).flexDirection;\n                this.flexDir = dir.startsWith('row') ? 'row' : 'column';\n                flexBasis = computedStyle.flexBasis;\n            }\n        }\n        // For boundary\n        this.setBoundingClientRect();\n        this.bindEvents();\n        var state = {\n            original: {\n                x: clientX,\n                y: clientY,\n                width: this.size.width,\n                height: this.size.height,\n            },\n            isResizing: true,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: this.window.getComputedStyle(event.target).cursor || 'auto' }),\n            direction: direction,\n            flexBasis: flexBasis,\n        };\n        this.setState(state);\n    };\n    Resizable.prototype.onMouseMove = function (event) {\n        var _this = this;\n        if (!this.state.isResizing || !this.resizable || !this.window) {\n            return;\n        }\n        if (this.window.TouchEvent && isTouchEvent(event)) {\n            try {\n                event.preventDefault();\n                event.stopPropagation();\n            }\n            catch (e) {\n                // Ignore on fail\n            }\n        }\n        var _a = this.props, maxWidth = _a.maxWidth, maxHeight = _a.maxHeight, minWidth = _a.minWidth, minHeight = _a.minHeight;\n        var clientX = isTouchEvent(event) ? event.touches[0].clientX : event.clientX;\n        var clientY = isTouchEvent(event) ? event.touches[0].clientY : event.clientY;\n        var _b = this.state, direction = _b.direction, original = _b.original, width = _b.width, height = _b.height;\n        var parentSize = this.getParentSize();\n        var max = calculateNewMax(parentSize, this.window.innerWidth, this.window.innerHeight, maxWidth, maxHeight, minWidth, minHeight);\n        maxWidth = max.maxWidth;\n        maxHeight = max.maxHeight;\n        minWidth = max.minWidth;\n        minHeight = max.minHeight;\n        // Calculate new size\n        var _c = this.calculateNewSizeFromDirection(clientX, clientY), newHeight = _c.newHeight, newWidth = _c.newWidth;\n        // Calculate max size from boundary settings\n        var boundaryMax = this.calculateNewMaxFromBoundary(maxWidth, maxHeight);\n        if (this.props.snap && this.props.snap.x) {\n            newWidth = findClosestSnap(newWidth, this.props.snap.x, this.props.snapGap);\n        }\n        if (this.props.snap && this.props.snap.y) {\n            newHeight = findClosestSnap(newHeight, this.props.snap.y, this.props.snapGap);\n        }\n        // Calculate new size from aspect ratio\n        var newSize = this.calculateNewSizeFromAspectRatio(newWidth, newHeight, { width: boundaryMax.maxWidth, height: boundaryMax.maxHeight }, { width: minWidth, height: minHeight });\n        newWidth = newSize.newWidth;\n        newHeight = newSize.newHeight;\n        if (this.props.grid) {\n            var newGridWidth = snap(newWidth, this.props.grid[0], this.props.gridGap ? this.props.gridGap[0] : 0);\n            var newGridHeight = snap(newHeight, this.props.grid[1], this.props.gridGap ? this.props.gridGap[1] : 0);\n            var gap = this.props.snapGap || 0;\n            var w = gap === 0 || Math.abs(newGridWidth - newWidth) <= gap ? newGridWidth : newWidth;\n            var h = gap === 0 || Math.abs(newGridHeight - newHeight) <= gap ? newGridHeight : newHeight;\n            newWidth = w;\n            newHeight = h;\n        }\n        var delta = {\n            width: newWidth - original.width,\n            height: newHeight - original.height,\n        };\n        this.delta = delta;\n        if (width && typeof width === 'string') {\n            if (width.endsWith('%')) {\n                var percent = (newWidth / parentSize.width) * 100;\n                newWidth = \"\".concat(percent, \"%\");\n            }\n            else if (width.endsWith('vw')) {\n                var vw = (newWidth / this.window.innerWidth) * 100;\n                newWidth = \"\".concat(vw, \"vw\");\n            }\n            else if (width.endsWith('vh')) {\n                var vh = (newWidth / this.window.innerHeight) * 100;\n                newWidth = \"\".concat(vh, \"vh\");\n            }\n        }\n        if (height && typeof height === 'string') {\n            if (height.endsWith('%')) {\n                var percent = (newHeight / parentSize.height) * 100;\n                newHeight = \"\".concat(percent, \"%\");\n            }\n            else if (height.endsWith('vw')) {\n                var vw = (newHeight / this.window.innerWidth) * 100;\n                newHeight = \"\".concat(vw, \"vw\");\n            }\n            else if (height.endsWith('vh')) {\n                var vh = (newHeight / this.window.innerHeight) * 100;\n                newHeight = \"\".concat(vh, \"vh\");\n            }\n        }\n        var newState = {\n            width: this.createSizeForCssProperty(newWidth, 'width'),\n            height: this.createSizeForCssProperty(newHeight, 'height'),\n        };\n        if (this.flexDir === 'row') {\n            newState.flexBasis = newState.width;\n        }\n        else if (this.flexDir === 'column') {\n            newState.flexBasis = newState.height;\n        }\n        var widthChanged = this.state.width !== newState.width;\n        var heightChanged = this.state.height !== newState.height;\n        var flexBaseChanged = this.state.flexBasis !== newState.flexBasis;\n        var changed = widthChanged || heightChanged || flexBaseChanged;\n        if (changed) {\n            // For v18, update state sync\n            flushSync(function () {\n                _this.setState(newState);\n            });\n        }\n        if (this.props.onResize) {\n            if (changed) {\n                this.props.onResize(event, direction, this.resizable, delta);\n            }\n        }\n    };\n    Resizable.prototype.onMouseUp = function (event) {\n        var _a, _b;\n        var _c = this.state, isResizing = _c.isResizing, direction = _c.direction, original = _c.original;\n        if (!isResizing || !this.resizable) {\n            return;\n        }\n        if (this.props.onResizeStop) {\n            this.props.onResizeStop(event, direction, this.resizable, this.delta);\n        }\n        if (this.props.size) {\n            this.setState({ width: (_a = this.props.size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = this.props.size.height) !== null && _b !== void 0 ? _b : 'auto' });\n        }\n        this.unbindEvents();\n        this.setState({\n            isResizing: false,\n            backgroundStyle: __assign(__assign({}, this.state.backgroundStyle), { cursor: 'auto' }),\n        });\n    };\n    Resizable.prototype.updateSize = function (size) {\n        var _a, _b;\n        this.setState({ width: (_a = size.width) !== null && _a !== void 0 ? _a : 'auto', height: (_b = size.height) !== null && _b !== void 0 ? _b : 'auto' });\n    };\n    Resizable.prototype.renderResizer = function () {\n        var _this = this;\n        var _a = this.props, enable = _a.enable, handleStyles = _a.handleStyles, handleClasses = _a.handleClasses, handleWrapperStyle = _a.handleWrapperStyle, handleWrapperClass = _a.handleWrapperClass, handleComponent = _a.handleComponent;\n        if (!enable) {\n            return null;\n        }\n        var resizers = Object.keys(enable).map(function (dir) {\n            if (enable[dir] !== false) {\n                return (_jsx(Resizer, { direction: dir, onResizeStart: _this.onResizeStart, replaceStyles: handleStyles && handleStyles[dir], className: handleClasses && handleClasses[dir], children: handleComponent && handleComponent[dir] ? handleComponent[dir] : null }, dir));\n            }\n            return null;\n        });\n        // #93 Wrap the resize box in span (will not break 100% width/height)\n        return (_jsx(\"div\", { className: handleWrapperClass, style: handleWrapperStyle, children: resizers }));\n    };\n    Resizable.prototype.render = function () {\n        var _this = this;\n        var extendsProps = Object.keys(this.props).reduce(function (acc, key) {\n            if (definedProps.indexOf(key) !== -1) {\n                return acc;\n            }\n            acc[key] = _this.props[key];\n            return acc;\n        }, {});\n        var style = __assign(__assign(__assign({ position: 'relative', userSelect: this.state.isResizing ? 'none' : 'auto' }, this.props.style), this.sizeStyle), { maxWidth: this.props.maxWidth, maxHeight: this.props.maxHeight, minWidth: this.props.minWidth, minHeight: this.props.minHeight, boxSizing: 'border-box', flexShrink: 0 });\n        if (this.state.flexBasis) {\n            style.flexBasis = this.state.flexBasis;\n        }\n        var Wrapper = this.props.as || 'div';\n        return (_jsxs(Wrapper, __assign({ style: style, className: this.props.className }, extendsProps, { \n            // `ref` is after `extendsProps` to ensure this one wins over a version\n            // passed in\n            ref: function (c) {\n                if (c) {\n                    _this.resizable = c;\n                }\n            }, children: [this.state.isResizing && _jsx(\"div\", { style: this.state.backgroundStyle }), this.props.children, this.renderResizer()] })));\n    };\n    Resizable.defaultProps = {\n        as: 'div',\n        onResizeStart: function () { },\n        onResize: function () { },\n        onResizeStop: function () { },\n        enable: {\n            top: true,\n            right: true,\n            bottom: true,\n            left: true,\n            topRight: true,\n            bottomRight: true,\n            bottomLeft: true,\n            topLeft: true,\n        },\n        style: {},\n        grid: [1, 1],\n        gridGap: [0, 0],\n        lockAspectRatio: false,\n        lockAspectRatioExtraWidth: 0,\n        lockAspectRatioExtraHeight: 0,\n        scale: 1,\n        resizeRatio: 1,\n        snapGap: 0,\n    };\n    return Resizable;\n}(PureComponent));\nexport { Resizable };\n", "var __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { memo, useCallback, useMemo } from 'react';\nvar rowSizeBase = {\n    width: '100%',\n    height: '10px',\n    top: '0px',\n    left: '0px',\n    cursor: 'row-resize',\n};\nvar colSizeBase = {\n    width: '10px',\n    height: '100%',\n    top: '0px',\n    left: '0px',\n    cursor: 'col-resize',\n};\nvar edgeBase = {\n    width: '20px',\n    height: '20px',\n    position: 'absolute',\n    zIndex: 1,\n};\nvar styles = {\n    top: __assign(__assign({}, rowSizeBase), { top: '-5px' }),\n    right: __assign(__assign({}, colSizeBase), { left: undefined, right: '-5px' }),\n    bottom: __assign(__assign({}, rowSizeBase), { top: undefined, bottom: '-5px' }),\n    left: __assign(__assign({}, colSizeBase), { left: '-5px' }),\n    topRight: __assign(__assign({}, edgeBase), { right: '-10px', top: '-10px', cursor: 'ne-resize' }),\n    bottomRight: __assign(__assign({}, edgeBase), { right: '-10px', bottom: '-10px', cursor: 'se-resize' }),\n    bottomLeft: __assign(__assign({}, edgeBase), { left: '-10px', bottom: '-10px', cursor: 'sw-resize' }),\n    topLeft: __assign(__assign({}, edgeBase), { left: '-10px', top: '-10px', cursor: 'nw-resize' }),\n};\nexport var Resizer = memo(function (props) {\n    var onResizeStart = props.onResizeStart, direction = props.direction, children = props.children, replaceStyles = props.replaceStyles, className = props.className;\n    var onMouseDown = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var onTouchStart = useCallback(function (e) {\n        onResizeStart(e, direction);\n    }, [onResizeStart, direction]);\n    var style = useMemo(function () {\n        return __assign(__assign({ position: 'absolute', userSelect: 'none' }, styles[direction]), (replaceStyles !== null && replaceStyles !== void 0 ? replaceStyles : {}));\n    }, [replaceStyles, direction]);\n    return (_jsx(\"div\", { className: className || undefined, style: style, onMouseDown: onMouseDown, onTouchStart: onTouchStart, children: children }));\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAaA,QAAI,MAAuC;AACzC,OAAC,WAAW;AACd;AAIA,YAAI,YAAY,OAAO,WAAW,cAAc,OAAO;AACvD,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AACnE,YAAI,oBAAoB,YAAY,OAAO,IAAI,cAAc,IAAI;AACjE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,qBAAqB,YAAY,OAAO,IAAI,eAAe,IAAI;AAGnE,YAAI,wBAAwB,YAAY,OAAO,IAAI,kBAAkB,IAAI;AACzE,YAAI,6BAA6B,YAAY,OAAO,IAAI,uBAAuB,IAAI;AACnF,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,sBAAsB,YAAY,OAAO,IAAI,gBAAgB,IAAI;AACrE,YAAI,2BAA2B,YAAY,OAAO,IAAI,qBAAqB,IAAI;AAC/E,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,kBAAkB,YAAY,OAAO,IAAI,YAAY,IAAI;AAC7D,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAC/D,YAAI,yBAAyB,YAAY,OAAO,IAAI,mBAAmB,IAAI;AAC3E,YAAI,uBAAuB,YAAY,OAAO,IAAI,iBAAiB,IAAI;AACvE,YAAI,mBAAmB,YAAY,OAAO,IAAI,aAAa,IAAI;AAE/D,iBAAS,mBAAmB,MAAM;AAChC,iBAAO,OAAO,SAAS,YAAY,OAAO,SAAS;AAAA,UACnD,SAAS,uBAAuB,SAAS,8BAA8B,SAAS,uBAAuB,SAAS,0BAA0B,SAAS,uBAAuB,SAAS,4BAA4B,OAAO,SAAS,YAAY,SAAS,SAAS,KAAK,aAAa,mBAAmB,KAAK,aAAa,mBAAmB,KAAK,aAAa,uBAAuB,KAAK,aAAa,sBAAsB,KAAK,aAAa,0BAA0B,KAAK,aAAa,0BAA0B,KAAK,aAAa,wBAAwB,KAAK,aAAa,oBAAoB,KAAK,aAAa;AAAA,QACplB;AAEA,iBAAS,OAAO,QAAQ;AACtB,cAAI,OAAO,WAAW,YAAY,WAAW,MAAM;AACjD,gBAAI,WAAW,OAAO;AAEtB,oBAAQ,UAAU;AAAA,cAChB,KAAK;AACH,oBAAI,OAAO,OAAO;AAElB,wBAAQ,MAAM;AAAA,kBACZ,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AAAA,kBACL,KAAK;AACH,2BAAO;AAAA,kBAET;AACE,wBAAI,eAAe,QAAQ,KAAK;AAEhC,4BAAQ,cAAc;AAAA,sBACpB,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AAAA,sBACL,KAAK;AACH,+BAAO;AAAA,sBAET;AACE,+BAAO;AAAA,oBACX;AAAA,gBAEJ;AAAA,cAEF,KAAK;AACH,uBAAO;AAAA,YACX;AAAA,UACF;AAEA,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY;AAChB,YAAI,iBAAiB;AACrB,YAAI,kBAAkB;AACtB,YAAI,kBAAkB;AACtB,YAAIA,WAAU;AACd,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,OAAO;AACX,YAAI,OAAO;AACX,YAAI,SAAS;AACb,YAAI,WAAW;AACf,YAAI,aAAa;AACjB,YAAI,WAAW;AACf,YAAI,sCAAsC;AAE1C,iBAAS,YAAY,QAAQ;AAC3B;AACE,gBAAI,CAAC,qCAAqC;AACxC,oDAAsC;AAEtC,sBAAQ,MAAM,EAAE,+KAAyL;AAAA,YAC3M;AAAA,UACF;AAEA,iBAAO,iBAAiB,MAAM,KAAK,OAAO,MAAM,MAAM;AAAA,QACxD;AACA,iBAAS,iBAAiB,QAAQ;AAChC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,kBAAkB,QAAQ;AACjC,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,UAAU,QAAQ;AACzB,iBAAO,OAAO,WAAW,YAAY,WAAW,QAAQ,OAAO,aAAa;AAAA,QAC9E;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,OAAO,QAAQ;AACtB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,SAAS,QAAQ;AACxB,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,aAAa,QAAQ;AAC5B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AACA,iBAAS,WAAW,QAAQ;AAC1B,iBAAO,OAAO,MAAM,MAAM;AAAA,QAC5B;AAEA,gBAAQ,YAAY;AACpB,gBAAQ,iBAAiB;AACzB,gBAAQ,kBAAkB;AAC1B,gBAAQ,kBAAkB;AAC1B,gBAAQ,UAAUA;AAClB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,OAAO;AACf,gBAAQ,OAAO;AACf,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,WAAW;AACnB,gBAAQ,cAAc;AACtB,gBAAQ,mBAAmB;AAC3B,gBAAQ,oBAAoB;AAC5B,gBAAQ,oBAAoB;AAC5B,gBAAQ,YAAY;AACpB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,SAAS;AACjB,gBAAQ,SAAS;AACjB,gBAAQ,WAAW;AACnB,gBAAQ,aAAa;AACrB,gBAAQ,eAAe;AACvB,gBAAQ,aAAa;AACrB,gBAAQ,qBAAqB;AAC7B,gBAAQ,SAAS;AAAA,MACf,GAAG;AAAA,IACL;AAAA;AAAA;;;ACpLA;AAAA;AAAA;AAEA,QAAI,OAAuC;AACzC,aAAO,UAAU;AAAA,IACnB,OAAO;AACL,aAAO,UAAU;AAAA,IACnB;AAAA;AAAA;;;ACNA;AAAA;AAAA;AAQA,QAAI,wBAAwB,OAAO;AACnC,QAAI,iBAAiB,OAAO,UAAU;AACtC,QAAI,mBAAmB,OAAO,UAAU;AAExC,aAAS,SAAS,KAAK;AACtB,UAAI,QAAQ,QAAQ,QAAQ,QAAW;AACtC,cAAM,IAAI,UAAU,uDAAuD;AAAA,MAC5E;AAEA,aAAO,OAAO,GAAG;AAAA,IAClB;AAEA,aAAS,kBAAkB;AAC1B,UAAI;AACH,YAAI,CAAC,OAAO,QAAQ;AACnB,iBAAO;AAAA,QACR;AAKA,YAAI,QAAQ,IAAI,OAAO,KAAK;AAC5B,cAAM,CAAC,IAAI;AACX,YAAI,OAAO,oBAAoB,KAAK,EAAE,CAAC,MAAM,KAAK;AACjD,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,iBAAS,IAAI,GAAG,IAAI,IAAI,KAAK;AAC5B,gBAAM,MAAM,OAAO,aAAa,CAAC,CAAC,IAAI;AAAA,QACvC;AACA,YAAI,SAAS,OAAO,oBAAoB,KAAK,EAAE,IAAI,SAAU,GAAG;AAC/D,iBAAO,MAAM,CAAC;AAAA,QACf,CAAC;AACD,YAAI,OAAO,KAAK,EAAE,MAAM,cAAc;AACrC,iBAAO;AAAA,QACR;AAGA,YAAI,QAAQ,CAAC;AACb,+BAAuB,MAAM,EAAE,EAAE,QAAQ,SAAU,QAAQ;AAC1D,gBAAM,MAAM,IAAI;AAAA,QACjB,CAAC;AACD,YAAI,OAAO,KAAK,OAAO,OAAO,CAAC,GAAG,KAAK,CAAC,EAAE,KAAK,EAAE,MAC/C,wBAAwB;AACzB,iBAAO;AAAA,QACR;AAEA,eAAO;AAAA,MACR,SAAS,KAAK;AAEb,eAAO;AAAA,MACR;AAAA,IACD;AAEA,WAAO,UAAU,gBAAgB,IAAI,OAAO,SAAS,SAAU,QAAQ,QAAQ;AAC9E,UAAI;AACJ,UAAI,KAAK,SAAS,MAAM;AACxB,UAAI;AAEJ,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAC1C,eAAO,OAAO,UAAU,CAAC,CAAC;AAE1B,iBAAS,OAAO,MAAM;AACrB,cAAI,eAAe,KAAK,MAAM,GAAG,GAAG;AACnC,eAAG,GAAG,IAAI,KAAK,GAAG;AAAA,UACnB;AAAA,QACD;AAEA,YAAI,uBAAuB;AAC1B,oBAAU,sBAAsB,IAAI;AACpC,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACxC,gBAAI,iBAAiB,KAAK,MAAM,QAAQ,CAAC,CAAC,GAAG;AAC5C,iBAAG,QAAQ,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;AAAA,YACjC;AAAA,UACD;AAAA,QACD;AAAA,MACD;AAEA,aAAO;AAAA,IACR;AAAA;AAAA;;;ACzFA;AAAA;AAAA;AASA,QAAI,uBAAuB;AAE3B,WAAO,UAAU;AAAA;AAAA;;;ACXjB;AAAA;AAAA,WAAO,UAAU,SAAS,KAAK,KAAK,OAAO,UAAU,cAAc;AAAA;AAAA;;;ACAnE;AAAA;AAAA;AASA,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACrC,6BAAuB;AACvB,2BAAqB,CAAC;AACtB,YAAM;AAEV,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAO;AAAA,MACrB;AAAA,IACF;AAhBM;AACA;AACA;AA2BN,aAAS,eAAe,WAAW,QAAQ,UAAU,eAAe,UAAU;AAC5E,UAAI,MAAuC;AACzC,iBAAS,gBAAgB,WAAW;AAClC,cAAI,IAAI,WAAW,YAAY,GAAG;AAChC,gBAAI;AAIJ,gBAAI;AAGF,kBAAI,OAAO,UAAU,YAAY,MAAM,YAAY;AACjD,oBAAI,MAAM;AAAA,mBACP,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,+FACC,OAAO,UAAU,YAAY,IAAI;AAAA,gBAEpH;AACA,oBAAI,OAAO;AACX,sBAAM;AAAA,cACR;AACA,sBAAQ,UAAU,YAAY,EAAE,QAAQ,cAAc,eAAe,UAAU,MAAM,oBAAoB;AAAA,YAC3G,SAAS,IAAI;AACX,sBAAQ;AAAA,YACV;AACA,gBAAI,SAAS,EAAE,iBAAiB,QAAQ;AACtC;AAAA,iBACG,iBAAiB,iBAAiB,6BACnC,WAAW,OAAO,eAAe,6FAC6B,OAAO,QAAQ;AAAA,cAI/E;AAAA,YACF;AACA,gBAAI,iBAAiB,SAAS,EAAE,MAAM,WAAW,qBAAqB;AAGpE,iCAAmB,MAAM,OAAO,IAAI;AAEpC,kBAAI,QAAQ,WAAW,SAAS,IAAI;AAEpC;AAAA,gBACE,YAAY,WAAW,YAAY,MAAM,WAAW,SAAS,OAAO,QAAQ;AAAA,cAC9E;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAOA,mBAAe,oBAAoB,WAAW;AAC5C,UAAI,MAAuC;AACzC,6BAAqB,CAAC;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtGjB;AAAA;AAAA;AASA,QAAI,UAAU;AACd,QAAI,SAAS;AAEb,QAAI,uBAAuB;AAC3B,QAAI,MAAM;AACV,QAAI,iBAAiB;AAErB,QAAI,eAAe,WAAW;AAAA,IAAC;AAE/B,QAAI,MAAuC;AACzC,qBAAe,SAAS,MAAM;AAC5B,YAAI,UAAU,cAAc;AAC5B,YAAI,OAAO,YAAY,aAAa;AAClC,kBAAQ,MAAM,OAAO;AAAA,QACvB;AACA,YAAI;AAIF,gBAAM,IAAI,MAAM,OAAO;AAAA,QACzB,SAAS,GAAG;AAAA,QAAC;AAAA,MACf;AAAA,IACF;AAEA,aAAS,+BAA+B;AACtC,aAAO;AAAA,IACT;AAEA,WAAO,UAAU,SAAS,gBAAgB,qBAAqB;AAE7D,UAAI,kBAAkB,OAAO,WAAW,cAAc,OAAO;AAC7D,UAAI,uBAAuB;AAgB3B,eAAS,cAAc,eAAe;AACpC,YAAI,aAAa,kBAAkB,mBAAmB,cAAc,eAAe,KAAK,cAAc,oBAAoB;AAC1H,YAAI,OAAO,eAAe,YAAY;AACpC,iBAAO;AAAA,QACT;AAAA,MACF;AAiDA,UAAI,YAAY;AAIhB,UAAI,iBAAiB;AAAA,QACnB,OAAO,2BAA2B,OAAO;AAAA,QACzC,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,MAAM,2BAA2B,SAAS;AAAA,QAC1C,MAAM,2BAA2B,UAAU;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAC3C,QAAQ,2BAA2B,QAAQ;AAAA,QAE3C,KAAK,qBAAqB;AAAA,QAC1B,SAAS;AAAA,QACT,SAAS,yBAAyB;AAAA,QAClC,aAAa,6BAA6B;AAAA,QAC1C,YAAY;AAAA,QACZ,MAAM,kBAAkB;AAAA,QACxB,UAAU;AAAA,QACV,OAAO;AAAA,QACP,WAAW;AAAA,QACX,OAAO;AAAA,QACP,OAAO;AAAA,MACT;AAOA,eAAS,GAAG,GAAG,GAAG;AAEhB,YAAI,MAAM,GAAG;AAGX,iBAAO,MAAM,KAAK,IAAI,MAAM,IAAI;AAAA,QAClC,OAAO;AAEL,iBAAO,MAAM,KAAK,MAAM;AAAA,QAC1B;AAAA,MACF;AAUA,eAAS,cAAc,SAAS,MAAM;AACpC,aAAK,UAAU;AACf,aAAK,OAAO,QAAQ,OAAO,SAAS,WAAW,OAAM,CAAC;AACtD,aAAK,QAAQ;AAAA,MACf;AAEA,oBAAc,YAAY,MAAM;AAEhC,eAAS,2BAA2B,UAAU;AAC5C,YAAI,MAAuC;AACzC,cAAI,0BAA0B,CAAC;AAC/B,cAAI,6BAA6B;AAAA,QACnC;AACA,iBAAS,UAAU,YAAY,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAC7F,0BAAgB,iBAAiB;AACjC,yBAAe,gBAAgB;AAE/B,cAAI,WAAW,sBAAsB;AACnC,gBAAI,qBAAqB;AAEvB,kBAAI,MAAM,IAAI;AAAA,gBACZ;AAAA,cAGF;AACA,kBAAI,OAAO;AACX,oBAAM;AAAA,YACR,WAAoD,OAAO,YAAY,aAAa;AAElF,kBAAI,WAAW,gBAAgB,MAAM;AACrC,kBACE,CAAC,wBAAwB,QAAQ;AAAA,cAEjC,6BAA6B,GAC7B;AACA;AAAA,kBACE,6EACuB,eAAe,gBAAgB,gBAAgB;AAAA,gBAIxE;AACA,wCAAwB,QAAQ,IAAI;AACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM,QAAQ,KAAK,MAAM;AAC3B,gBAAI,YAAY;AACd,kBAAI,MAAM,QAAQ,MAAM,MAAM;AAC5B,uBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,8BAA8B,SAAS,gBAAgB,8BAA8B;AAAA,cAC1J;AACA,qBAAO,IAAI,cAAc,SAAS,WAAW,OAAO,eAAe,iCAAiC,MAAM,gBAAgB,mCAAmC;AAAA,YAC/J;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,SAAS,OAAO,UAAU,eAAe,UAAU,YAAY;AAAA,UACxE;AAAA,QACF;AAEA,YAAI,mBAAmB,UAAU,KAAK,MAAM,KAAK;AACjD,yBAAiB,aAAa,UAAU,KAAK,MAAM,IAAI;AAEvD,eAAO;AAAA,MACT;AAEA,eAAS,2BAA2B,cAAc;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc,QAAQ;AAChF,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,cAAc;AAI7B,gBAAI,cAAc,eAAe,SAAS;AAE1C,mBAAO,IAAI;AAAA,cACT,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,cAAc,oBAAoB,gBAAgB,mBAAmB,MAAM,eAAe;AAAA,cAC9J,EAAC,aAA0B;AAAA,YAC7B;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB;AAC9B,eAAO,2BAA2B,4BAA4B;AAAA,MAChE;AAEA,eAAS,yBAAyB,aAAa;AAC7C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,iDAAiD;AAAA,UAC/I;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,MAAM,QAAQ,SAAS,GAAG;AAC7B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AACzC,gBAAI,QAAQ,YAAY,WAAW,GAAG,eAAe,UAAU,eAAe,MAAM,IAAI,KAAK,oBAAoB;AACjH,gBAAI,iBAAiB,OAAO;AAC1B,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,2BAA2B;AAClC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,eAAe,SAAS,GAAG;AAC9B,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,qCAAqC;AAAA,UACnL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,+BAA+B;AACtC,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,CAAC,QAAQ,mBAAmB,SAAS,GAAG;AAC1C,gBAAI,WAAW,YAAY,SAAS;AACpC,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,0CAA0C;AAAA,UACxL;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,eAAe;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,EAAE,MAAM,QAAQ,aAAa,gBAAgB;AAC/C,gBAAI,oBAAoB,cAAc,QAAQ;AAC9C,gBAAI,kBAAkB,aAAa,MAAM,QAAQ,CAAC;AAClD,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,kBAAkB,oBAAoB,gBAAgB,mBAAmB,kBAAkB,oBAAoB,KAAK;AAAA,UACnN;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,gBAAgB;AAC7C,YAAI,CAAC,MAAM,QAAQ,cAAc,GAAG;AAClC,cAAI,MAAuC;AACzC,gBAAI,UAAU,SAAS,GAAG;AACxB;AAAA,gBACE,iEAAiE,UAAU,SAAS;AAAA,cAEtF;AAAA,YACF,OAAO;AACL,2BAAa,wDAAwD;AAAA,YACvE;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,mBAAS,IAAI,GAAG,IAAI,eAAe,QAAQ,KAAK;AAC9C,gBAAI,GAAG,WAAW,eAAe,CAAC,CAAC,GAAG;AACpC,qBAAO;AAAA,YACT;AAAA,UACF;AAEA,cAAI,eAAe,KAAK,UAAU,gBAAgB,SAAS,SAAS,KAAK,OAAO;AAC9E,gBAAI,OAAO,eAAe,KAAK;AAC/B,gBAAI,SAAS,UAAU;AACrB,qBAAO,OAAO,KAAK;AAAA,YACrB;AACA,mBAAO;AAAA,UACT,CAAC;AACD,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,iBAAiB,OAAO,SAAS,IAAI,QAAQ,kBAAkB,gBAAgB,wBAAwB,eAAe,IAAI;AAAA,QACnM;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,0BAA0B,aAAa;AAC9C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,OAAO,gBAAgB,YAAY;AACrC,mBAAO,IAAI,cAAc,eAAe,eAAe,qBAAqB,gBAAgB,kDAAkD;AAAA,UAChJ;AACA,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,MAAM,WAAW,oBAAoB,gBAAgB,yBAAyB;AAAA,UACvK;AACA,mBAAS,OAAO,WAAW;AACzB,gBAAI,IAAI,WAAW,GAAG,GAAG;AACvB,kBAAI,QAAQ,YAAY,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC/G,kBAAI,iBAAiB,OAAO;AAC1B,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,uBAAuB,qBAAqB;AACnD,YAAI,CAAC,MAAM,QAAQ,mBAAmB,GAAG;AACvC,iBAAwC,aAAa,wEAAwE,IAAI;AACjI,iBAAO;AAAA,QACT;AAEA,iBAAS,IAAI,GAAG,IAAI,oBAAoB,QAAQ,KAAK;AACnD,cAAI,UAAU,oBAAoB,CAAC;AACnC,cAAI,OAAO,YAAY,YAAY;AACjC;AAAA,cACE,gGACc,yBAAyB,OAAO,IAAI,eAAe,IAAI;AAAA,YACvE;AACA,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,gBAAgB,CAAC;AACrB,mBAASC,KAAI,GAAGA,KAAI,oBAAoB,QAAQA,MAAK;AACnD,gBAAIC,WAAU,oBAAoBD,EAAC;AACnC,gBAAI,gBAAgBC,SAAQ,OAAO,UAAU,eAAe,UAAU,cAAc,oBAAoB;AACxG,gBAAI,iBAAiB,MAAM;AACzB,qBAAO;AAAA,YACT;AACA,gBAAI,cAAc,QAAQ,IAAI,cAAc,MAAM,cAAc,GAAG;AACjE,4BAAc,KAAK,cAAc,KAAK,YAAY;AAAA,YACpD;AAAA,UACF;AACA,cAAI,uBAAwB,cAAc,SAAS,IAAK,6BAA6B,cAAc,KAAK,IAAI,IAAI,MAAK;AACrH,iBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,MAAM,uBAAuB,IAAI;AAAA,QACpJ;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,oBAAoB;AAC3B,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,GAAG;AAC5B,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,oBAAoB,MAAM,gBAAgB,2BAA2B;AAAA,UAC9I;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,sBAAsB,eAAe,UAAU,cAAc,KAAK,MAAM;AAC/E,eAAO,IAAI;AAAA,WACR,iBAAiB,iBAAiB,OAAO,WAAW,YAAY,eAAe,MAAM,MAAM,+FACX,OAAO;AAAA,QAC1F;AAAA,MACF;AAEA,eAAS,uBAAuB,YAAY;AAC1C,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AACA,mBAAS,OAAO,YAAY;AAC1B,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,OAAO,YAAY,YAAY;AACjC,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,6BAA6B,YAAY;AAChD,iBAAS,SAAS,OAAO,UAAU,eAAe,UAAU,cAAc;AACxE,cAAI,YAAY,MAAM,QAAQ;AAC9B,cAAI,WAAW,YAAY,SAAS;AACpC,cAAI,aAAa,UAAU;AACzB,mBAAO,IAAI,cAAc,aAAa,WAAW,OAAO,eAAe,gBAAgB,WAAW,QAAQ,kBAAkB,gBAAgB,wBAAwB;AAAA,UACtK;AAEA,cAAI,UAAU,OAAO,CAAC,GAAG,MAAM,QAAQ,GAAG,UAAU;AACpD,mBAAS,OAAO,SAAS;AACvB,gBAAI,UAAU,WAAW,GAAG;AAC5B,gBAAI,IAAI,YAAY,GAAG,KAAK,OAAO,YAAY,YAAY;AACzD,qBAAO,sBAAsB,eAAe,UAAU,cAAc,KAAK,eAAe,OAAO,CAAC;AAAA,YAClG;AACA,gBAAI,CAAC,SAAS;AACZ,qBAAO,IAAI;AAAA,gBACT,aAAa,WAAW,OAAO,eAAe,YAAY,MAAM,oBAAoB,gBAAgB,qBACjF,KAAK,UAAU,MAAM,QAAQ,GAAG,MAAM,IAAI,IAC7D,mBAAmB,KAAK,UAAU,OAAO,KAAK,UAAU,GAAG,MAAM,IAAI;AAAA,cACvE;AAAA,YACF;AACA,gBAAI,QAAQ,QAAQ,WAAW,KAAK,eAAe,UAAU,eAAe,MAAM,KAAK,oBAAoB;AAC3G,gBAAI,OAAO;AACT,qBAAO;AAAA,YACT;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AAEA,eAAO,2BAA2B,QAAQ;AAAA,MAC5C;AAEA,eAAS,OAAO,WAAW;AACzB,gBAAQ,OAAO,WAAW;AAAA,UACxB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO;AAAA,UACT,KAAK;AACH,mBAAO,CAAC;AAAA,UACV,KAAK;AACH,gBAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,qBAAO,UAAU,MAAM,MAAM;AAAA,YAC/B;AACA,gBAAI,cAAc,QAAQ,eAAe,SAAS,GAAG;AACnD,qBAAO;AAAA,YACT;AAEA,gBAAI,aAAa,cAAc,SAAS;AACxC,gBAAI,YAAY;AACd,kBAAI,WAAW,WAAW,KAAK,SAAS;AACxC,kBAAI;AACJ,kBAAI,eAAe,UAAU,SAAS;AACpC,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,CAAC,OAAO,KAAK,KAAK,GAAG;AACvB,2BAAO;AAAA,kBACT;AAAA,gBACF;AAAA,cACF,OAAO;AAEL,uBAAO,EAAE,OAAO,SAAS,KAAK,GAAG,MAAM;AACrC,sBAAI,QAAQ,KAAK;AACjB,sBAAI,OAAO;AACT,wBAAI,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG;AACrB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AAEA,mBAAO;AAAA,UACT;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAEA,eAAS,SAAS,UAAU,WAAW;AAErC,YAAI,aAAa,UAAU;AACzB,iBAAO;AAAA,QACT;AAGA,YAAI,CAAC,WAAW;AACd,iBAAO;AAAA,QACT;AAGA,YAAI,UAAU,eAAe,MAAM,UAAU;AAC3C,iBAAO;AAAA,QACT;AAGA,YAAI,OAAO,WAAW,cAAc,qBAAqB,QAAQ;AAC/D,iBAAO;AAAA,QACT;AAEA,eAAO;AAAA,MACT;AAGA,eAAS,YAAY,WAAW;AAC9B,YAAI,WAAW,OAAO;AACtB,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC5B,iBAAO;AAAA,QACT;AACA,YAAI,qBAAqB,QAAQ;AAI/B,iBAAO;AAAA,QACT;AACA,YAAI,SAAS,UAAU,SAAS,GAAG;AACjC,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAIA,eAAS,eAAe,WAAW;AACjC,YAAI,OAAO,cAAc,eAAe,cAAc,MAAM;AAC1D,iBAAO,KAAK;AAAA,QACd;AACA,YAAI,WAAW,YAAY,SAAS;AACpC,YAAI,aAAa,UAAU;AACzB,cAAI,qBAAqB,MAAM;AAC7B,mBAAO;AAAA,UACT,WAAW,qBAAqB,QAAQ;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAIA,eAAS,yBAAyB,OAAO;AACvC,YAAI,OAAO,eAAe,KAAK;AAC/B,gBAAQ,MAAM;AAAA,UACZ,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,QAAQ;AAAA,UACjB,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACH,mBAAO,OAAO;AAAA,UAChB;AACE,mBAAO;AAAA,QACX;AAAA,MACF;AAGA,eAAS,aAAa,WAAW;AAC/B,YAAI,CAAC,UAAU,eAAe,CAAC,UAAU,YAAY,MAAM;AACzD,iBAAO;AAAA,QACT;AACA,eAAO,UAAU,YAAY;AAAA,MAC/B;AAEA,qBAAe,iBAAiB;AAChC,qBAAe,oBAAoB,eAAe;AAClD,qBAAe,YAAY;AAE3B,aAAO;AAAA,IACT;AAAA;AAAA;;;ACjmBA;AAAA;AAOA,QAAI,MAAuC;AACrC,gBAAU;AAIV,4BAAsB;AAC1B,aAAO,UAAU,kCAAqC,QAAQ,WAAW,mBAAmB;AAAA,IAC9F,OAAO;AAGL,aAAO,UAAU,KAAsC;AAAA,IACzD;AAVM;AAIA;AAAA;AAAA;;;ACZN;AAAA;AAAA;AAAA;AAAA;AAAA,SAAS,EAAE,GAAE;AAAC,MAAI,GAAE,GAAE,IAAE;AAAG,MAAG,YAAU,OAAO,KAAG,YAAU,OAAO,EAAE,MAAG;AAAA,WAAU,YAAU,OAAO,EAAE,KAAG,MAAM,QAAQ,CAAC,EAAE,MAAI,IAAE,GAAE,IAAE,EAAE,QAAO,IAAI,GAAE,CAAC,MAAI,IAAE,EAAE,EAAE,CAAC,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAA,MAAQ,MAAI,KAAK,EAAE,GAAE,CAAC,MAAI,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAQ,SAAS,OAAM;AAAC,WAAQ,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,SAAQ,EAAC,IAAE,UAAU,GAAG,OAAK,IAAE,EAAE,CAAC,OAAK,MAAI,KAAG,MAAK,KAAG;AAAG,SAAO;AAAC;AAAjW,IAAyW;AAAzW;AAAA;AAAkW,IAAO,iBAAQ;AAAA;AAAA;;;ACAjX;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,YAAY;AACpB,YAAQ,cAAc;AACtB,YAAQ,MAAM;AACd,YAAQ,aAAa;AACrB,YAAQ,QAAQ;AAEhB,aAAS,YAAY,OAAoC,UAAkC;AACzF,eAAS,IAAI,GAAG,SAAS,MAAM,QAAQ,IAAI,QAAQ,KAAK;AACtD,YAAI,SAAS,MAAM,UAAU,CAAC,MAAM,CAAC,GAAG,GAAG,KAAK,CAAC,EAAG,QAAO,MAAM,CAAC;AAAA,MACpE;AAAA,IACF;AACA,aAAS,WAAW,MAAqC;AAEvD,aAAO,OAAO,SAAS,cAAc,OAAO,UAAU,SAAS,KAAK,IAAI,MAAM;AAAA,IAChF;AACA,aAAS,MAAM,KAAoC;AACjD,aAAO,OAAO,QAAQ,YAAY,CAAC,MAAM,GAAG;AAAA,IAC9C;AACA,aAAS,IAAI,GAA4B;AACvC,aAAO,SAAS,GAAG,EAAE;AAAA,IACvB;AACA,aAAS,UAAU,OAAoB,UAAuB,eAAwC;AACpG,UAAI,MAAM,QAAQ,GAAG;AACnB,eAAO,IAAI,MAAM,gBAAgB,OAAO,UAAU,aAAa,EAAE,OAAO,eAAe,0CAA0C,CAAC;AAAA,MACpI;AAAA,IACF;AAAA;AAAA;;;AC9BA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,qBAAqB;AAC7B,YAAQ,uBAAuB;AAC/B,YAAQ,UAAU;AAClB,YAAQ,YAAY;AACpB,QAAM,WAAW,CAAC,OAAO,UAAU,KAAK,IAAI;AAC5C,aAAS,YAAwB;AAC/B,UAAI;AACJ,UAAI,OAAoB,UAAU,SAAS,KAAK,UAAU,CAAC,MAAM,SAAY,UAAU,CAAC,IAAI;AAG5F,UAAI,OAAO,WAAW,YAAa,QAAO;AAI1C,YAAM,SAAS,mBAAmB,OAAO,cAAc,QAAQ,qBAAqB,WAAW,mBAAmB,iBAAiB,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB;AACxN,UAAI,CAAC,MAAO,QAAO;AACnB,UAAI,QAAQ,MAAO,QAAO;AAC1B,eAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AACxC,YAAI,mBAAmB,MAAM,SAAS,CAAC,CAAC,KAAK,MAAO,QAAO,SAAS,CAAC;AAAA,MACvE;AACA,aAAO;AAAA,IACT;AACA,aAAS,mBAAmB,MAAmB,QAAiC;AAC9E,aAAO,SAAS,GAAG,OAAO,MAAM,EAAE,OAAO,iBAAiB,IAAI,CAAC,IAAI;AAAA,IACrE;AACA,aAAS,qBAAqB,MAAmB,QAAiC;AAChF,aAAO,SAAS,IAAI,OAAO,OAAO,YAAY,GAAG,GAAG,EAAE,OAAO,IAAI,IAAI;AAAA,IACvE;AACA,aAAS,iBAAiB,KAA8B;AACtD,UAAI,MAAM;AACV,UAAI,mBAAmB;AACvB,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,kBAAkB;AACpB,iBAAO,IAAI,CAAC,EAAE,YAAY;AAC1B,6BAAmB;AAAA,QACrB,WAAW,IAAI,CAAC,MAAM,KAAK;AACzB,6BAAmB;AAAA,QACrB,OAAO;AACL,iBAAO,IAAI,CAAC;AAAA,QACd;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAKA,QAAI,WAAW,QAAQ,UAAW,UAAU;AAAA;AAAA;;;ACpD5C;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,eAAe;AACvB,YAAQ,WAAW;AACnB,YAAQ,sBAAsB;AAC9B,YAAQ,qBAAqB;AAC7B,YAAQ,qBAAqB;AAC7B,YAAQ,WAAW;AACnB,YAAQ,qBAAqB;AAC7B,YAAQ,iBAAiB;AACzB,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,8BAA8B;AACtC,YAAQ,qBAAqB;AAC7B,YAAQ,cAAc;AACtB,YAAQ,aAAa;AACrB,YAAQ,kBAAkB;AAC1B,YAAQ,cAAc;AACtB,YAAQ,yBAAyB;AACjC,QAAI,SAAS;AACb,QAAI,aAAa,wBAAwB,mBAAsB;AAC/D,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAAUC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AACtT,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,SAAS,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,UAAU;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AAEnyB,QAAI,sBAAsB;AAC1B,aAAS,gBAAgB,IAAe,UAAoC;AAC1E,UAAI,CAAC,qBAAqB;AACxB,+BAAuB,GAAG,OAAO,aAAa,CAAC,WAAW,yBAAyB,sBAAsB,qBAAqB,kBAAkB,GAAG,SAAU,QAAQ;AAEnK,kBAAQ,GAAG,OAAO,YAAY,GAAG,MAAM,CAAC;AAAA,QAC1C,CAAC;AAAA,MACH;AAIA,UAAI,EAAE,GAAG,OAAO,YAAY,GAAG,mBAAmB,CAAC,EAAG,QAAO;AAG7D,aAAO,GAAG,mBAAmB,EAAE,QAAQ;AAAA,IACzC;AAGA,aAAS,4BAA4B,IAAe,UAAuB,UAAkC;AAC3G,UAAI,OAAO;AACX,SAAG;AACD,YAAI,gBAAgB,MAAM,QAAQ,EAAG,QAAO;AAC5C,YAAI,SAAS,SAAU,QAAO;AAE9B,eAAO,KAAK;AAAA,MACd,SAAS;AACT,aAAO;AAAA,IACT;AACA,aAAS,SAAS,IAAgB,OAAoB,SAAwB,cAAqC;AACjH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,kBAAkB;AACvB,WAAG,iBAAiB,OAAO,SAAS,OAAO;AAAA,MAC7C,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,IAAgB,OAAoB,SAAwB,cAAqC;AACpH,UAAI,CAAC,GAAI;AACT,YAAM,UAAU;AAAA,QACd,SAAS;AAAA,QACT,GAAG;AAAA,MACL;AAEA,UAAI,GAAG,qBAAqB;AAC1B,WAAG,oBAAoB,OAAO,SAAS,OAAO;AAAA,MAChD,WAAW,GAAG,aAAa;AACzB,WAAG,YAAY,OAAO,OAAO,OAAO;AAAA,MACtC,OAAO;AAEL,WAAG,OAAO,KAAK,IAAI;AAAA,MACrB;AAAA,IACF;AACA,aAAS,YAAY,MAAoC;AAGvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,cAAc;AACtD,iBAAW,GAAG,OAAO,KAAK,cAAc,iBAAiB;AACzD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AAGtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,eAAe;AACtD,gBAAU,GAAG,OAAO,KAAK,cAAc,gBAAgB;AACvD,aAAO;AAAA,IACT;AACA,aAAS,YAAY,MAAoC;AACvD,UAAI,SAAS,KAAK;AAClB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,iBAAW,GAAG,OAAO,KAAK,cAAc,UAAU;AAClD,iBAAW,GAAG,OAAO,KAAK,cAAc,aAAa;AACrD,aAAO;AAAA,IACT;AACA,aAAS,WAAW,MAAoC;AACtD,UAAI,QAAQ,KAAK;AACjB,YAAM,gBAAgB,KAAK,cAAc,YAAY,iBAAiB,IAAI;AAC1E,gBAAU,GAAG,OAAO,KAAK,cAAc,WAAW;AAClD,gBAAU,GAAG,OAAO,KAAK,cAAc,YAAY;AACnD,aAAO;AAAA,IACT;AAKA,aAAS,mBAAmB,KAA2B,cAAgC,OAAyC;AAC9H,YAAM,SAAS,iBAAiB,aAAa,cAAc;AAC3D,YAAM,mBAAmB,SAAS;AAAA,QAChC,MAAM;AAAA,QACN,KAAK;AAAA,MACP,IAAI,aAAa,sBAAsB;AACvC,YAAM,KAAK,IAAI,UAAU,aAAa,aAAa,iBAAiB,QAAQ;AAC5E,YAAM,KAAK,IAAI,UAAU,aAAa,YAAY,iBAAiB,OAAO;AAC1E,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,IAAI;AACnE,aAAO;AAAA,QACL,EAAE,GAAG,WAAW,oBAAoB,aAAa,WAAW,OAAO,CAAC,GAAG;AAAA,MACzE;AAAA,IACF;AACA,aAAS,mBAAmB,YAAkC,gBAAgE;AAC5H,YAAM,cAAc,eAAe,YAAY,gBAAgB,EAAE;AACjE,aAAO;AAAA,IACT;AACA,aAAS,eAAe,MAAc,gBAAoD,YAAqC;AAC7H,UAAI;AAAA,QACF;AAAA,QACA;AAAA,MACF,IAA0B;AAC1B,UAAI,cAAc,aAAa,OAAO,CAAC,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,CAAC,EAAE,OAAO,YAAY,GAAG;AACjG,UAAI,gBAAgB;AAClB,cAAM,WAAW,GAAG,OAAO,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAClH,cAAM,WAAW,GAAG,OAAO,OAAO,eAAe,MAAM,WAAW,eAAe,IAAI,eAAe,IAAI,UAAU;AAClH,sBAAc,aAAa,OAAO,UAAU,IAAI,EAAE,OAAO,UAAU,GAAG,IAAI;AAAA,MAC5E;AACA,aAAO;AAAA,IACT;AACA,aAAS,SAAS,GAAyB,YAAkE;AAC3G,aAAO,EAAE,kBAAkB,GAAG,OAAO,aAAa,EAAE,eAAe,OAAK,eAAe,EAAE,UAAU,KAAK,EAAE,mBAAmB,GAAG,OAAO,aAAa,EAAE,gBAAgB,OAAK,eAAe,EAAE,UAAU;AAAA,IACxM;AACA,aAAS,mBAAmB,GAAsC;AAChE,UAAI,EAAE,iBAAiB,EAAE,cAAc,CAAC,EAAG,QAAO,EAAE,cAAc,CAAC,EAAE;AACrE,UAAI,EAAE,kBAAkB,EAAE,eAAe,CAAC,EAAG,QAAO,EAAE,eAAe,CAAC,EAAE;AAAA,IAC1E;AAOA,aAAS,oBAAoB,KAAqB;AAChD,UAAI,CAAC,IAAK;AACV,UAAI,UAAU,IAAI,eAAe,0BAA0B;AAC3D,UAAI,CAAC,SAAS;AACZ,kBAAU,IAAI,cAAc,OAAO;AACnC,gBAAQ,OAAO;AACf,gBAAQ,KAAK;AACb,gBAAQ,YAAY;AACpB,gBAAQ,aAAa;AACrB,YAAI,qBAAqB,MAAM,EAAE,CAAC,EAAE,YAAY,OAAO;AAAA,MACzD;AACA,UAAI,IAAI,KAAM,cAAa,IAAI,MAAM,uCAAuC;AAAA,IAC9E;AACA,aAAS,uBAAuB,KAAqB;AACnD,UAAI,CAAC,IAAK;AACV,UAAI;AACF,YAAI,IAAI,KAAM,iBAAgB,IAAI,MAAM,uCAAuC;AAE/E,YAAI,IAAI,WAAW;AAEjB,cAAI,UAAU,MAAM;AAAA,QACtB,OAAO;AAGL,gBAAM,aAAa,IAAI,eAAe,QAAQ,aAAa;AAC3D,cAAI,aAAa,UAAU,SAAS,SAAS;AAC3C,sBAAU,gBAAgB;AAAA,UAC5B;AAAA,QACF;AAAA,MACF,SAAS,GAAG;AAAA,MAEZ;AAAA,IACF;AACA,aAAS,aAAa,IAAsB,WAAwB;AAClE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,IAAI,SAAS;AAAA,MAC5B,OAAO;AACL,YAAI,CAAC,GAAG,UAAU,MAAM,IAAI,OAAO,YAAY,OAAO,WAAW,SAAS,CAAC,CAAC,GAAG;AAC7E,aAAG,aAAa,IAAI,OAAO,SAAS;AAAA,QACtC;AAAA,MACF;AAAA,IACF;AACA,aAAS,gBAAgB,IAAsB,WAAwB;AACrE,UAAI,GAAG,WAAW;AAChB,WAAG,UAAU,OAAO,SAAS;AAAA,MAC/B,OAAO;AACL,WAAG,YAAY,GAAG,UAAU,QAAQ,IAAI,OAAO,YAAY,OAAO,WAAW,SAAS,GAAG,GAAG,GAAG,EAAE;AAAA,MACnG;AAAA,IACF;AAAA;AAAA;;;AC7NA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,WAAW;AACnB,YAAQ,WAAW;AACnB,YAAQ,iBAAiB;AACzB,YAAQ,sBAAsB;AAC9B,YAAQ,mBAAmB;AAC3B,YAAQ,qBAAqB;AAC7B,YAAQ,aAAa;AACrB,QAAI,SAAS;AACb,QAAI,UAAU;AAId,aAAS,iBAAiB,WAA2B,GAAgB,GAAsC;AAEzG,UAAI,CAAC,UAAU,MAAM,OAAQ,QAAO,CAAC,GAAG,CAAC;AAGzC,UAAI;AAAA,QACF;AAAA,MACF,IAAI,UAAU;AACd,eAAS,OAAO,WAAW,WAAW,SAAS,YAAY,MAAM;AACjE,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,OAAO,WAAW,UAAU;AAC9B,cAAM;AAAA,UACJ;AAAA,QACF,IAAI;AACJ,cAAM,cAAc,cAAc;AAClC,YAAI;AACJ,YAAI,WAAW,UAAU;AACvB,sBAAY,KAAK;AAAA,QACnB,OAAO;AACL,sBAAY,cAAc,cAAc,MAAM;AAAA,QAChD;AACA,YAAI,EAAE,qBAAqB,YAAY,cAAc;AACnD,gBAAM,IAAI,MAAM,sBAAsB,SAAS,8BAA8B;AAAA,QAC/E;AACA,cAAM,cAAgC;AACtC,cAAM,YAAY,YAAY,iBAAiB,IAAI;AACnD,cAAM,iBAAiB,YAAY,iBAAiB,WAAW;AAE/D,iBAAS;AAAA,UACP,MAAM,CAAC,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,WAAW,KAAK,GAAG,OAAO,KAAK,UAAU,UAAU;AAAA,UAC3G,KAAK,CAAC,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,UAAU,KAAK,GAAG,OAAO,KAAK,UAAU,SAAS;AAAA,UACvG,QAAQ,GAAG,QAAQ,YAAY,WAAW,KAAK,GAAG,QAAQ,YAAY,IAAI,IAAI,KAAK,cAAc,GAAG,OAAO,KAAK,eAAe,YAAY,KAAK,GAAG,OAAO,KAAK,UAAU,WAAW;AAAA,UACpL,SAAS,GAAG,QAAQ,aAAa,WAAW,KAAK,GAAG,QAAQ,aAAa,IAAI,IAAI,KAAK,aAAa,GAAG,OAAO,KAAK,eAAe,aAAa,KAAK,GAAG,OAAO,KAAK,UAAU,YAAY;AAAA,QAC1L;AAAA,MACF;AAGA,WAAK,GAAG,OAAO,OAAO,OAAO,KAAK,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,KAAK;AACjE,WAAK,GAAG,OAAO,OAAO,OAAO,MAAM,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,MAAM;AAGnE,WAAK,GAAG,OAAO,OAAO,OAAO,IAAI,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,IAAI;AAC/D,WAAK,GAAG,OAAO,OAAO,OAAO,GAAG,EAAG,KAAI,KAAK,IAAI,GAAG,OAAO,GAAG;AAC7D,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,WAAW,MAA6B,UAAuB,UAA6C;AACnH,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,YAAM,IAAI,KAAK,MAAM,WAAW,KAAK,CAAC,CAAC,IAAI,KAAK,CAAC;AACjD,aAAO,CAAC,GAAG,CAAC;AAAA,IACd;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AACA,aAAS,SAAS,WAAwC;AACxD,aAAO,UAAU,MAAM,SAAS,UAAU,UAAU,MAAM,SAAS;AAAA,IACrE;AAGA,aAAS,mBAAmB,GAAyB,iBAA+B,eAAyD;AAC3I,YAAM,WAAW,OAAO,oBAAoB,YAAY,GAAG,QAAQ,UAAU,GAAG,eAAe,IAAI;AACnG,UAAI,OAAO,oBAAoB,YAAY,CAAC,SAAU,QAAO;AAC7D,YAAM,OAAO,YAAY,aAAa;AAEtC,YAAM,eAAe,cAAc,MAAM,gBAAgB,KAAK,gBAAgB,KAAK,cAAc;AACjG,cAAQ,GAAG,QAAQ,oBAAoB,YAAY,GAAG,cAAc,cAAc,MAAM,KAAK;AAAA,IAC/F;AAGA,aAAS,eAAe,WAA+B,GAAgB,GAAmC;AACxG,YAAM,UAAU,EAAE,GAAG,OAAO,OAAO,UAAU,KAAK;AAClD,YAAM,OAAO,YAAY,SAAS;AAClC,UAAI,SAAS;AAEX,eAAO;AAAA,UACL;AAAA,UACA,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,UACA;AAAA,QACF;AAAA,MACF,OAAO;AAEL,eAAO;AAAA,UACL;AAAA,UACA,QAAQ,IAAI,UAAU;AAAA,UACtB,QAAQ,IAAI,UAAU;AAAA,UACtB,OAAO,UAAU;AAAA,UACjB,OAAO,UAAU;AAAA,UACjB;AAAA,UACA;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAGA,aAAS,oBAAoB,WAA2B,UAAiD;AACvG,YAAM,QAAQ,UAAU,MAAM;AAC9B,aAAO;AAAA,QACL,MAAM,SAAS;AAAA,QACf,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,GAAG,UAAU,MAAM,IAAI,SAAS,SAAS;AAAA,QACzC,QAAQ,SAAS,SAAS;AAAA,QAC1B,QAAQ,SAAS,SAAS;AAAA,QAC1B,OAAO,UAAU,MAAM;AAAA,QACvB,OAAO,UAAU,MAAM;AAAA,MACzB;AAAA,IACF;AAGA,aAAS,YAAY,QAAiC;AACpD,aAAO;AAAA,QACL,MAAM,OAAO;AAAA,QACb,KAAK,OAAO;AAAA,QACZ,OAAO,OAAO;AAAA,QACd,QAAQ,OAAO;AAAA,MACjB;AAAA,IACF;AACA,aAAS,YAAY,WAA4D;AAC/E,YAAM,OAAO,UAAU,YAAY;AACnC,UAAI,CAAC,MAAM;AACT,cAAM,IAAI,MAAM,0CAA0C;AAAA,MAC5D;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC/IA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAElB,aAAS,MAAM;AACb,UAAI,OAAW,SAAQ,IAAI,GAAG,SAAS;AAAA,IACzC;AAAA;AAAA;;;ACTA;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAAUC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AACtT,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,SAAS,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,UAAU;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACnyB,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AAIxX,QAAM,YAAY;AAAA,MAChB,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,MACA,OAAO;AAAA,QACL,OAAO;AAAA,QACP,MAAM;AAAA,QACN,MAAM;AAAA,MACR;AAAA,IACF;AAGA,QAAI,eAAe,UAAU;AAoC7B,QAAM,gBAAN,cAA4B,MAAM,UAAqC;AAAA,MACrE,cAAc;AACZ,cAAM,GAAG,SAAS;AAClB,wBAAgB,MAAM,YAAY,KAAK;AAEvC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,SAAS,GAAG;AAClC,wBAAgB,MAAM,mBAAmB,IAAI;AAC7C,wBAAgB,MAAM,WAAW,KAAK;AACtC,wBAAgB,MAAM,mBAAmB,OAAK;AAE5C,eAAK,MAAM,YAAY,CAAC;AAGxB,cAAI,CAAC,KAAK,MAAM,iBAAiB,OAAO,EAAE,WAAW,YAAY,EAAE,WAAW,EAAG,QAAO;AAGxF,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,CAAC,YAAY,CAAC,SAAS,iBAAiB,CAAC,SAAS,cAAc,MAAM;AACxE,kBAAM,IAAI,MAAM,2CAA2C;AAAA,UAC7D;AACA,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AAGJ,cAAI,KAAK,MAAM,YAAY,EAAE,EAAE,kBAAkB,cAAc,YAAY,SAAS,KAAK,MAAM,UAAU,EAAE,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,KAAK,KAAK,MAAM,WAAW,GAAG,QAAQ,6BAA6B,EAAE,QAAQ,KAAK,MAAM,QAAQ,QAAQ,GAAG;AACjS;AAAA,UACF;AAIA,cAAI,EAAE,SAAS,aAAc,GAAE,eAAe;AAK9C,gBAAM,mBAAmB,GAAG,QAAQ,oBAAoB,CAAC;AACzD,eAAK,kBAAkB;AAGvB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,iBAAiB,IAAI;AAC9E,cAAI,YAAY,KAAM;AACtB,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,sCAAsC,SAAS;AAGjE,WAAC,GAAG,KAAK,SAAS,WAAW,KAAK,MAAM,OAAO;AAC/C,gBAAM,eAAe,KAAK,MAAM,QAAQ,GAAG,SAAS;AACpD,cAAI,iBAAiB,SAAS,KAAK,YAAY,MAAO;AAItD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,qBAAqB,aAAa;AAKnF,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AAKb,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,UAAU;AACvE,WAAC,GAAG,QAAQ,UAAU,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,QAC7E,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,OACpB,SAAS,IAAI,KAAK;AACpB,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,CAAC,UAAU,CAAC,OAAQ;AACxB,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAC7D,WAAC,GAAG,KAAK,SAAS,iCAAiC,SAAS;AAG5D,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,SAAS;AACnD,cAAI,iBAAiB,SAAS,KAAK,YAAY,OAAO;AACpD,gBAAI;AAEF,mBAAK,eAAe,IAAI,WAAW,SAAS,CAAC;AAAA,YAC/C,SAAS,KAAK;AAEZ,oBAAM,QAAU,SAAS,YAAY,aAAa;AAGlD,oBAAM,eAAe,WAAW,MAAM,MAAM,QAAQ,GAAG,GAAG,GAAG,GAAG,GAAG,OAAO,OAAO,OAAO,OAAO,GAAG,IAAI;AACtG,mBAAK,eAAe,KAAK;AAAA,YAC3B;AACA;AAAA,UACF;AACA,eAAK,QAAQ;AACb,eAAK,QAAQ;AAAA,QACf,CAAC;AACD,wBAAgB,MAAM,kBAAkB,OAAK;AAC3C,cAAI,CAAC,KAAK,SAAU;AACpB,gBAAM,YAAY,GAAG,aAAa,oBAAoB,GAAG,KAAK,iBAAiB,IAAI;AACnF,cAAI,YAAY,KAAM;AACtB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,UACF,IAAI;AAGJ,cAAI,MAAM,QAAQ,KAAK,MAAM,IAAI,GAAG;AAClC,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,gBAAI,SAAS,IAAI,KAAK,SAAS;AAC/B,aAAC,QAAQ,MAAM,KAAK,GAAG,aAAa,YAAY,KAAK,MAAM,MAAM,QAAQ,MAAM;AAC/E,gBAAI,KAAK,QAAQ,QAAQ,IAAI,KAAK,QAAQ;AAAA,UAC5C;AACA,gBAAM,aAAa,GAAG,aAAa,gBAAgB,MAAM,GAAG,CAAC;AAG7D,gBAAM,iBAAiB,KAAK,MAAM,OAAO,GAAG,SAAS;AACrD,cAAI,mBAAmB,SAAS,KAAK,YAAY,MAAO,QAAO;AAC/D,gBAAM,WAAW,KAAK,YAAY;AAClC,cAAI,UAAU;AAEZ,gBAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,wBAAwB,SAAS,aAAa;AAAA,UACjG;AACA,WAAC,GAAG,KAAK,SAAS,qCAAqC,SAAS;AAGhE,eAAK,WAAW;AAChB,eAAK,QAAQ;AACb,eAAK,QAAQ;AACb,cAAI,UAAU;AAEZ,aAAC,GAAG,KAAK,SAAS,kCAAkC;AACpD,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,UAAU;AACnF,aAAC,GAAG,QAAQ,aAAa,SAAS,eAAe,aAAa,MAAM,KAAK,cAAc;AAAA,UACzF;AAAA,QACF,CAAC;AACD,wBAAgB,MAAM,eAAe,OAAK;AACxC,yBAAe,UAAU;AAEzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,aAAa,OAAK;AACtC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAED,wBAAgB,MAAM,gBAAgB,OAAK;AAEzC,yBAAe,UAAU;AACzB,iBAAO,KAAK,gBAAgB,CAAC;AAAA,QAC/B,CAAC;AACD,wBAAgB,MAAM,cAAc,OAAK;AAEvC,yBAAe,UAAU;AACzB,iBAAO,KAAK,eAAe,CAAC;AAAA,QAC9B,CAAC;AAAA,MACH;AAAA,MACA,oBAAoB;AAClB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,WAAC,GAAG,QAAQ,UAAU,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YACxE,SAAS;AAAA,UACX,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,UAAU;AAGf,cAAM,WAAW,KAAK,YAAY;AAClC,YAAI,UAAU;AACZ,gBAAM;AAAA,YACJ;AAAA,UACF,IAAI;AACJ,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,UAAU;AAC7E,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,eAAe,UAAU,MAAM,MAAM,KAAK,cAAc;AACjF,WAAC,GAAG,QAAQ,aAAa,UAAU,UAAU,MAAM,OAAO,KAAK,cAAc;AAAA,YAC3E,SAAS;AAAA,UACX,CAAC;AACD,cAAI,KAAK,MAAM,qBAAsB,EAAC,GAAG,QAAQ,wBAAwB,aAAa;AAAA,QACxF;AAAA,MACF;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,YAAI,aAAa;AACjB,gBAAQ,cAAc,KAAK,WAAW,QAAQ,gBAAgB,UAAU,YAAY,WAAW,eAAe,KAAK,WAAW,QAAQ,iBAAiB,WAAW,eAAe,aAAa,aAAa,QAAQ,iBAAiB,SAAS,SAAS,aAAa,UAAU,UAAU,QAAQ,YAAY,IAAI;AAAA,MACjT;AAAA,MACA,SAAiC;AAG/B,eAAoB,MAAM,aAAa,MAAM,SAAS,KAAK,KAAK,MAAM,QAAQ,GAAG;AAAA;AAAA;AAAA,UAG/E,aAAa,KAAK;AAAA,UAClB,WAAW,KAAK;AAAA;AAAA;AAAA;AAAA,UAIhB,YAAY,KAAK;AAAA,QACnB,CAAC;AAAA,MACH;AAAA,IACF;AACA,YAAQ,UAAU;AAClB,oBAAgB,eAAe,eAAe,eAAe;AAC7D,oBAAgB,eAAe,aAAa;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAO1C,eAAe,WAAW,QAAQ;AAAA,MAClC,UAAU,WAAW,QAAQ,KAAK;AAAA;AAAA;AAAA;AAAA;AAAA,MAKlC,UAAU,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAM7B,sBAAsB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAKzC,cAAc,SAAU,OAAgC,UAA0C;AAChG,YAAI,MAAM,QAAQ,KAAK,MAAM,QAAQ,EAAE,aAAa,GAAG;AACrD,gBAAM,IAAI,MAAM,8CAA+C;AAAA,QACjE;AAAA,MACF;AAAA;AAAA;AAAA;AAAA,MAIA,MAAM,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB1D,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqB3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB3B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,SAAS,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK5B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,QAAQ,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA,MAK3B,aAAa,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAIhC,OAAO,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA,MAI1B,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgB,eAAe,gBAAgB;AAAA,MAC7C,eAAe;AAAA;AAAA,MAEf,UAAU;AAAA,MACV,sBAAsB;AAAA,MACtB,SAAS,WAAY;AAAA,MAAC;AAAA,MACtB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,QAAQ,WAAY;AAAA,MAAC;AAAA,MACrB,aAAa,WAAY;AAAA,MAAC;AAAA,MAC1B,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;ACjbD;AAAA;AAAA;AAEA,WAAO,eAAe,SAAS,cAAc;AAAA,MAC3C,OAAO;AAAA,IACT,CAAC;AACD,WAAO,eAAe,SAAS,iBAAiB;AAAA,MAC9C,YAAY;AAAA,MACZ,KAAK,WAAY;AACf,eAAO,eAAe;AAAA,MACxB;AAAA,IACF,CAAC;AACD,YAAQ,UAAU;AAClB,QAAI,QAAQ,wBAAwB,eAAgB;AACpD,QAAI,aAAa,uBAAuB,oBAAqB;AAC7D,QAAI,YAAY,uBAAuB,mBAAoB;AAC3D,QAAI,QAAQ,uBAAuB,6CAAe;AAClD,QAAI,UAAU;AACd,QAAI,eAAe;AACnB,QAAI,SAAS;AACb,QAAI,iBAAiB,uBAAuB,uBAA0B;AACtE,QAAI,OAAO,uBAAuB,aAAsB;AACxD,aAAS,uBAAuB,KAAK;AAAE,aAAO,OAAO,IAAI,aAAa,MAAM,EAAE,SAAS,IAAI;AAAA,IAAG;AAC9F,aAAS,yBAAyB,aAAa;AAAE,UAAI,OAAO,YAAY,WAAY,QAAO;AAAM,UAAI,oBAAoB,oBAAI,QAAQ;AAAG,UAAI,mBAAmB,oBAAI,QAAQ;AAAG,cAAQ,2BAA2B,SAAUC,cAAa;AAAE,eAAOA,eAAc,mBAAmB;AAAA,MAAmB,GAAG,WAAW;AAAA,IAAG;AACtT,aAAS,wBAAwB,KAAK,aAAa;AAAE,UAAI,CAAC,eAAe,OAAO,IAAI,YAAY;AAAE,eAAO;AAAA,MAAK;AAAE,UAAI,QAAQ,QAAQ,OAAO,QAAQ,YAAY,OAAO,QAAQ,YAAY;AAAE,eAAO,EAAE,SAAS,IAAI;AAAA,MAAG;AAAE,UAAI,QAAQ,yBAAyB,WAAW;AAAG,UAAI,SAAS,MAAM,IAAI,GAAG,GAAG;AAAE,eAAO,MAAM,IAAI,GAAG;AAAA,MAAG;AAAE,UAAI,SAAS,CAAC;AAAG,UAAI,wBAAwB,OAAO,kBAAkB,OAAO;AAA0B,eAAS,OAAO,KAAK;AAAE,YAAI,QAAQ,aAAa,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG,GAAG;AAAE,cAAI,OAAO,wBAAwB,OAAO,yBAAyB,KAAK,GAAG,IAAI;AAAM,cAAI,SAAS,KAAK,OAAO,KAAK,MAAM;AAAE,mBAAO,eAAe,QAAQ,KAAK,IAAI;AAAA,UAAG,OAAO;AAAE,mBAAO,GAAG,IAAI,IAAI,GAAG;AAAA,UAAG;AAAA,QAAE;AAAA,MAAE;AAAE,aAAO,UAAU;AAAK,UAAI,OAAO;AAAE,cAAM,IAAI,KAAK,MAAM;AAAA,MAAG;AAAE,aAAO;AAAA,IAAQ;AACnyB,aAAS,WAAW;AAAE,iBAAW,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,SAAU,QAAQ;AAAE,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAAE,cAAI,SAAS,UAAU,CAAC;AAAG,mBAAS,OAAO,QAAQ;AAAE,gBAAI,OAAO,UAAU,eAAe,KAAK,QAAQ,GAAG,GAAG;AAAE,qBAAO,GAAG,IAAI,OAAO,GAAG;AAAA,YAAG;AAAA,UAAE;AAAA,QAAE;AAAE,eAAO;AAAA,MAAQ;AAAG,aAAO,SAAS,MAAM,MAAM,SAAS;AAAA,IAAG;AAClV,aAAS,gBAAgB,KAAK,KAAK,OAAO;AAAE,YAAM,eAAe,GAAG;AAAG,UAAI,OAAO,KAAK;AAAE,eAAO,eAAe,KAAK,KAAK,EAAE,OAAc,YAAY,MAAM,cAAc,MAAM,UAAU,KAAK,CAAC;AAAA,MAAG,OAAO;AAAE,YAAI,GAAG,IAAI;AAAA,MAAO;AAAE,aAAO;AAAA,IAAK;AAC3O,aAAS,eAAe,KAAK;AAAE,UAAI,MAAM,aAAa,KAAK,QAAQ;AAAG,aAAO,OAAO,QAAQ,WAAW,MAAM,OAAO,GAAG;AAAA,IAAG;AAC1H,aAAS,aAAa,OAAO,MAAM;AAAE,UAAI,OAAO,UAAU,YAAY,UAAU,KAAM,QAAO;AAAO,UAAI,OAAO,MAAM,OAAO,WAAW;AAAG,UAAI,SAAS,QAAW;AAAE,YAAI,MAAM,KAAK,KAAK,OAAO,QAAQ,SAAS;AAAG,YAAI,OAAO,QAAQ,SAAU,QAAO;AAAK,cAAM,IAAI,UAAU,8CAA8C;AAAA,MAAG;AAAE,cAAQ,SAAS,WAAW,SAAS,QAAQ,KAAK;AAAA,IAAG;AA8BxX,QAAMC,aAAN,cAAwB,MAAM,UAAiD;AAAA;AAAA;AAAA,MAG7E,OAAO,yBAAyB,MAAc,OAA6C;AACzF,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AACzB,YAAI;AAAA,UACF;AAAA,QACF,IAAyB;AAEzB,YAAI,aAAa,CAAC,qBAAqB,SAAS,MAAM,kBAAkB,KAAK,SAAS,MAAM,kBAAkB,IAAI;AAChH,WAAC,GAAG,KAAK,SAAS,0CAA0C;AAAA,YAC1D;AAAA,YACA;AAAA,UACF,CAAC;AACD,iBAAO;AAAA,YACL,GAAG,SAAS;AAAA,YACZ,GAAG,SAAS;AAAA,YACZ,mBAAmB;AAAA,cACjB,GAAG;AAAA,YACL;AAAA,UACF;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,MACA,YAAY,OAA4B;AACtC,cAAM,KAAK;AACX,wBAAgB,MAAM,eAAe,CAAC,GAAG,aAAa;AACpD,WAAC,GAAG,KAAK,SAAS,8BAA8B,QAAQ;AAGxD,gBAAM,cAAc,KAAK,MAAM,QAAQ,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AAE/F,cAAI,gBAAgB,MAAO,QAAO;AAClC,eAAK,SAAS;AAAA,YACZ,UAAU;AAAA,YACV,SAAS;AAAA,UACX,CAAC;AAAA,QACH,CAAC;AACD,wBAAgB,MAAM,UAAU,CAAC,GAAG,aAAa;AAC/C,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AACjC,WAAC,GAAG,KAAK,SAAS,yBAAyB,QAAQ;AACnD,gBAAM,UAAU,GAAG,aAAa,qBAAqB,MAAM,QAAQ;AACnE,gBAAM,WAAW;AAAA,YACf,GAAG,OAAO;AAAA,YACV,GAAG,OAAO;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAGA,cAAI,KAAK,MAAM,QAAQ;AAErB,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI;AAKJ,qBAAS,KAAK,KAAK,MAAM;AACzB,qBAAS,KAAK,KAAK,MAAM;AAGzB,kBAAM,CAAC,WAAW,SAAS,KAAK,GAAG,aAAa,kBAAkB,MAAM,SAAS,GAAG,SAAS,CAAC;AAC9F,qBAAS,IAAI;AACb,qBAAS,IAAI;AAGb,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AACpD,qBAAS,SAAS,KAAK,MAAM,UAAU,IAAI,SAAS;AAGpD,mBAAO,IAAI,SAAS;AACpB,mBAAO,IAAI,SAAS;AACpB,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AACxC,mBAAO,SAAS,SAAS,IAAI,KAAK,MAAM;AAAA,UAC1C;AAGA,gBAAM,eAAe,KAAK,MAAM,OAAO,GAAG,MAAM;AAChD,cAAI,iBAAiB,MAAO,QAAO;AACnC,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,wBAAgB,MAAM,cAAc,CAAC,GAAG,aAAa;AACnD,cAAI,CAAC,KAAK,MAAM,SAAU,QAAO;AAGjC,gBAAM,iBAAiB,KAAK,MAAM,OAAO,IAAI,GAAG,aAAa,qBAAqB,MAAM,QAAQ,CAAC;AACjG,cAAI,mBAAmB,MAAO,QAAO;AACrC,WAAC,GAAG,KAAK,SAAS,6BAA6B,QAAQ;AACvD,gBAAM,WAAyC;AAAA,YAC7C,UAAU;AAAA,YACV,QAAQ;AAAA,YACR,QAAQ;AAAA,UACV;AAIA,gBAAM,aAAa,QAAQ,KAAK,MAAM,QAAQ;AAC9C,cAAI,YAAY;AACd,kBAAM;AAAA,cACJ;AAAA,cACA;AAAA,YACF,IAAI,KAAK,MAAM;AACf,qBAAS,IAAI;AACb,qBAAS,IAAI;AAAA,UACf;AACA,eAAK,SAAS,QAAQ;AAAA,QACxB,CAAC;AACD,aAAK,QAAQ;AAAA;AAAA,UAEX,UAAU;AAAA;AAAA,UAEV,SAAS;AAAA;AAAA,UAET,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,GAAG,MAAM,WAAW,MAAM,SAAS,IAAI,MAAM,gBAAgB;AAAA,UAC7D,mBAAmB;AAAA,YACjB,GAAG,MAAM;AAAA,UACX;AAAA;AAAA,UAEA,QAAQ;AAAA,UACR,QAAQ;AAAA;AAAA,UAER,cAAc;AAAA,QAChB;AACA,YAAI,MAAM,YAAY,EAAE,MAAM,UAAU,MAAM,SAAS;AAErD,kBAAQ,KAAK,2NAAqO;AAAA,QACpP;AAAA,MACF;AAAA,MACA,oBAAoB;AAElB,YAAI,OAAO,OAAO,eAAe,eAAe,KAAK,YAAY,aAAa,OAAO,YAAY;AAC/F,eAAK,SAAS;AAAA,YACZ,cAAc;AAAA,UAChB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,MACA,uBAAuB;AACrB,aAAK,SAAS;AAAA,UACZ,UAAU;AAAA,QACZ,CAAC;AAAA,MACH;AAAA;AAAA;AAAA,MAIA,cAAgC;AAC9B,YAAI,uBAAuB;AAC3B,gBAAQ,yBAAyB,cAAc,KAAK,WAAW,QAAQ,gBAAgB,WAAW,cAAc,YAAY,aAAa,QAAQ,gBAAgB,SAAS,SAAS,YAAY,aAAa,QAAQ,0BAA0B,SAAS,wBAAwB,UAAU,QAAQ,YAAY,IAAI;AAAA,MACnT;AAAA,MACA,SAAgC;AAC9B,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA;AAAA,UACA,GAAG;AAAA,QACL,IAAI,KAAK;AACT,YAAI,QAAQ,CAAC;AACb,YAAI,eAAe;AAGnB,cAAM,aAAa,QAAQ,QAAQ;AACnC,cAAM,YAAY,CAAC,cAAc,KAAK,MAAM;AAC5C,cAAM,gBAAgB,YAAY;AAClC,cAAM,gBAAgB;AAAA;AAAA,UAEpB,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA;AAAA,UAEhF,IAAI,GAAG,aAAa,UAAU,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,cAAc;AAAA,QAClF;AAGA,YAAI,KAAK,MAAM,cAAc;AAC3B,0BAAgB,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QAC9E,OAAO;AAKL,mBAAS,GAAG,QAAQ,oBAAoB,eAAe,cAAc;AAAA,QACvE;AAGA,cAAM,aAAa,GAAG,MAAM,SAAS,SAAS,MAAM,aAAa,IAAI,kBAAkB;AAAA,UACrF,CAAC,wBAAwB,GAAG,KAAK,MAAM;AAAA,UACvC,CAAC,uBAAuB,GAAG,KAAK,MAAM;AAAA,QACxC,CAAC;AAID,eAAoB,MAAM,cAAc,eAAe,SAAS,SAAS,CAAC,GAAG,oBAAoB;AAAA,UAC/F,SAAS,KAAK;AAAA,UACd,QAAQ,KAAK;AAAA,UACb,QAAQ,KAAK;AAAA,QACf,CAAC,GAAgB,MAAM,aAAa,MAAM,SAAS,KAAK,QAAQ,GAAG;AAAA,UACjE;AAAA,UACA,OAAO;AAAA,YACL,GAAG,SAAS,MAAM;AAAA,YAClB,GAAG;AAAA,UACL;AAAA,UACA,WAAW;AAAA,QACb,CAAC,CAAC;AAAA,MACJ;AAAA,IACF;AACA,YAAQ,UAAUA;AAClB,oBAAgBA,YAAW,eAAe,WAAW;AACrD,oBAAgBA,YAAW,aAAa;AAAA;AAAA,MAEtC,GAAG,eAAe,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAc1B,MAAM,WAAW,QAAQ,MAAM,CAAC,QAAQ,KAAK,KAAK,MAAM,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MA2BzD,QAAQ,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,MAAM;AAAA,QAC7D,MAAM,WAAW,QAAQ;AAAA,QACzB,OAAO,WAAW,QAAQ;AAAA,QAC1B,KAAK,WAAW,QAAQ;AAAA,QACxB,QAAQ,WAAW,QAAQ;AAAA,MAC7B,CAAC,GAAG,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;AAAA,MACjE,kBAAkB,WAAW,QAAQ;AAAA,MACrC,0BAA0B,WAAW,QAAQ;AAAA,MAC7C,yBAAyB,WAAW,QAAQ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAkB5C,iBAAiB,WAAW,QAAQ,MAAM;AAAA,QACxC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA,MACD,gBAAgB,WAAW,QAAQ,MAAM;AAAA,QACvC,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,QACtF,GAAG,WAAW,QAAQ,UAAU,CAAC,WAAW,QAAQ,QAAQ,WAAW,QAAQ,MAAM,CAAC;AAAA,MACxF,CAAC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAqBD,UAAU,WAAW,QAAQ,MAAM;AAAA,QACjC,GAAG,WAAW,QAAQ;AAAA,QACtB,GAAG,WAAW,QAAQ;AAAA,MACxB,CAAC;AAAA;AAAA;AAAA;AAAA,MAID,WAAW,OAAO;AAAA,MAClB,OAAO,OAAO;AAAA,MACd,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,oBAAgBA,YAAW,gBAAgB;AAAA,MACzC,GAAG,eAAe,QAAQ;AAAA,MAC1B,MAAM;AAAA,MACN,QAAQ;AAAA,MACR,kBAAkB;AAAA,MAClB,0BAA0B;AAAA,MAC1B,yBAAyB;AAAA,MACzB,iBAAiB;AAAA,QACf,GAAG;AAAA,QACH,GAAG;AAAA,MACL;AAAA,MACA,OAAO;AAAA,IACT,CAAC;AAAA;AAAA;;;AC1YD;AAAA;AAAA;AAEA,QAAM;AAAA,MACJ,SAASC;AAAA,MACT;AAAA,IACF,IAAI;AAKJ,WAAO,UAAUA;AACjB,WAAO,QAAQ,UAAUA;AACzB,WAAO,QAAQ,gBAAgB;AAAA;AAAA;;;ACZ/B,IAAAC,gBAA6C;AAC7C,6BAAsB;;;ACyBtB,IAAAC,sBAA2C;AAC3C,IAAAC,gBAA8B;AAC9B,uBAA0B;;;ACjB1B,yBAA4B;AAC5B,mBAA2C;AAZ3C,IAAI,WAAsC,WAAY;AAClD,aAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAO,SAAS,MAAM,MAAM,SAAS;AACzC;AAGA,IAAI,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACZ;AACA,IAAI,cAAc;AAAA,EACd,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AAAA,EACN,QAAQ;AACZ;AACA,IAAI,WAAW;AAAA,EACX,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,QAAQ;AACZ;AACA,IAAI,SAAS;AAAA,EACT,KAAK,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,KAAK,OAAO,CAAC;AAAA,EACxD,OAAO,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAM,QAAW,OAAO,OAAO,CAAC;AAAA,EAC7E,QAAQ,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,KAAK,QAAW,QAAQ,OAAO,CAAC;AAAA,EAC9E,MAAM,SAAS,SAAS,CAAC,GAAG,WAAW,GAAG,EAAE,MAAM,OAAO,CAAC;AAAA,EAC1D,UAAU,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,OAAO,SAAS,KAAK,SAAS,QAAQ,YAAY,CAAC;AAAA,EAChG,aAAa,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,OAAO,SAAS,QAAQ,SAAS,QAAQ,YAAY,CAAC;AAAA,EACtG,YAAY,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,MAAM,SAAS,QAAQ,SAAS,QAAQ,YAAY,CAAC;AAAA,EACpG,SAAS,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG,EAAE,MAAM,SAAS,KAAK,SAAS,QAAQ,YAAY,CAAC;AAClG;AACO,IAAI,cAAU,mBAAK,SAAU,OAAO;AACvC,MAAI,gBAAgB,MAAM,eAAe,YAAY,MAAM,WAAW,WAAW,MAAM,UAAU,gBAAgB,MAAM,eAAe,YAAY,MAAM;AACxJ,MAAI,kBAAc,0BAAY,SAAU,GAAG;AACvC,kBAAc,GAAG,SAAS;AAAA,EAC9B,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,MAAI,mBAAe,0BAAY,SAAU,GAAG;AACxC,kBAAc,GAAG,SAAS;AAAA,EAC9B,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,MAAI,YAAQ,sBAAQ,WAAY;AAC5B,WAAO,SAAS,SAAS,EAAE,UAAU,YAAY,YAAY,OAAO,GAAG,OAAO,SAAS,CAAC,GAAI,kBAAkB,QAAQ,kBAAkB,SAAS,gBAAgB,CAAC,CAAE;AAAA,EACxK,GAAG,CAAC,eAAe,SAAS,CAAC;AAC7B,aAAQ,mBAAAC,KAAK,OAAO,EAAE,WAAW,aAAa,QAAW,OAAc,aAA0B,cAA4B,SAAmB,CAAC;AACrJ,CAAC;;;ADvDD,IAAI,YAAyC,2BAAY;AACrD,MAAIC,iBAAgB,SAAU,GAAG,GAAG;AAChC,IAAAA,iBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,MAAAD,GAAE,YAAYC;AAAA,IAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,eAAS,KAAKA,GAAG,KAAI,OAAO,UAAU,eAAe,KAAKA,IAAG,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,IAAG;AACpG,WAAOF,eAAc,GAAG,CAAC;AAAA,EAC7B;AACA,SAAO,SAAU,GAAG,GAAG;AACnB,QAAI,OAAO,MAAM,cAAc,MAAM;AACjC,YAAM,IAAI,UAAU,yBAAyB,OAAO,CAAC,IAAI,+BAA+B;AAC5F,IAAAA,eAAc,GAAG,CAAC;AAClB,aAAS,KAAK;AAAE,WAAK,cAAc;AAAA,IAAG;AACtC,MAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AAAA,EACtF;AACJ,EAAG;AACH,IAAIG,YAAsC,WAAY;AAClD,EAAAA,YAAW,OAAO,UAAU,SAAS,GAAG;AACpC,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC;AAC1D,UAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAClB;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAKA,IAAI,eAAe;AAAA,EACf,OAAO;AAAA,EACP,QAAQ;AACZ;AACA,IAAI,QAAQ,SAAU,GAAG,KAAK,KAAK;AAAE,SAAO,KAAK,IAAI,KAAK,IAAI,GAAG,GAAG,GAAG,GAAG;AAAG;AAC7E,IAAI,OAAO,SAAU,GAAG,MAAM,SAAS;AACnC,MAAI,IAAI,KAAK,MAAM,IAAI,IAAI;AAC3B,SAAO,IAAI,OAAO,WAAW,IAAI;AACrC;AACA,IAAI,eAAe,SAAU,KAAK,QAAQ;AACtC,SAAO,IAAI,OAAO,KAAK,GAAG,EAAE,KAAK,MAAM;AAC3C;AAEA,IAAI,eAAe,SAAU,OAAO;AAChC,SAAO,QAAQ,MAAM,WAAW,MAAM,QAAQ,MAAM;AACxD;AACA,IAAI,eAAe,SAAU,OAAO;AAChC,SAAO,SAAS,MAAM,WAAW,MAAM,YAAY,OAC9C,MAAM,WAAW,MAAM,YAAY,EAAE;AAC9C;AACA,IAAI,kBAAkB,SAAU,GAAG,WAAW,SAAS;AACnD,MAAI,YAAY,QAAQ;AAAE,cAAU;AAAA,EAAG;AACvC,MAAI,kBAAkB,UAAU,OAAO,SAAU,MAAM,MAAM,OAAO;AAAE,WAAQ,KAAK,IAAI,OAAO,CAAC,IAAI,KAAK,IAAI,UAAU,IAAI,IAAI,CAAC,IAAI,QAAQ;AAAA,EAAO,GAAG,CAAC;AACtJ,MAAI,MAAM,KAAK,IAAI,UAAU,eAAe,IAAI,CAAC;AACjD,SAAO,YAAY,KAAK,MAAM,UAAU,UAAU,eAAe,IAAI;AACzE;AACA,IAAI,gBAAgB,SAAU,GAAG;AAC7B,MAAI,EAAE,SAAS;AACf,MAAI,MAAM,QAAQ;AACd,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,GAAG,GAAG;AACjB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,IAAI,GAAG;AAClB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,MAAM,GAAG;AACpB,WAAO;AAAA,EACX;AACA,MAAI,EAAE,SAAS,MAAM,GAAG;AACpB,WAAO;AAAA,EACX;AACA,SAAO,GAAG,OAAO,GAAG,IAAI;AAC5B;AACA,IAAI,eAAe,SAAU,MAAM,YAAY,YAAY,aAAa;AACpE,MAAI,QAAQ,OAAO,SAAS,UAAU;AAClC,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,aAAO,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC;AAAA,IACxC;AACA,QAAI,KAAK,SAAS,GAAG,GAAG;AACpB,UAAI,QAAQ,OAAO,KAAK,QAAQ,KAAK,EAAE,CAAC,IAAI;AAC5C,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,UAAI,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI;AAC7C,aAAO,aAAa;AAAA,IACxB;AACA,QAAI,KAAK,SAAS,IAAI,GAAG;AACrB,UAAI,QAAQ,OAAO,KAAK,QAAQ,MAAM,EAAE,CAAC,IAAI;AAC7C,aAAO,cAAc;AAAA,IACzB;AAAA,EACJ;AACA,SAAO;AACX;AACA,IAAI,kBAAkB,SAAU,YAAY,YAAY,aAAa,UAAU,WAAW,UAAU,WAAW;AAC3G,aAAW,aAAa,UAAU,WAAW,OAAO,YAAY,WAAW;AAC3E,cAAY,aAAa,WAAW,WAAW,QAAQ,YAAY,WAAW;AAC9E,aAAW,aAAa,UAAU,WAAW,OAAO,YAAY,WAAW;AAC3E,cAAY,aAAa,WAAW,WAAW,QAAQ,YAAY,WAAW;AAC9E,SAAO;AAAA,IACH,UAAU,OAAO,aAAa,cAAc,SAAY,OAAO,QAAQ;AAAA,IACvE,WAAW,OAAO,cAAc,cAAc,SAAY,OAAO,SAAS;AAAA,IAC1E,UAAU,OAAO,aAAa,cAAc,SAAY,OAAO,QAAQ;AAAA,IACvE,WAAW,OAAO,cAAc,cAAc,SAAY,OAAO,SAAS;AAAA,EAC9E;AACJ;AAOA,IAAI,kBAAkB,SAAU,KAAK;AAAE,SAAQ,MAAM,QAAQ,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG;AAAI;AACvF,IAAI,eAAe;AAAA,EACf;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAEA,IAAI,gBAAgB;AACpB,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,WAAU,OAAO;AACtB,UAAI,IAAI,IAAI,IAAI;AAChB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,QAAQ;AACd,YAAM,YAAY;AAElB,YAAM,aAAa;AACnB,YAAM,YAAY;AAElB,YAAM,gBAAgB;AACtB,YAAM,iBAAiB;AACvB,YAAM,eAAe;AACrB,YAAM,kBAAkB;AAExB,YAAM,aAAa;AACnB,YAAM,YAAY;AAClB,YAAM,QAAQ;AAAA,QACV,OAAO;AAAA,QACP,QAAQ;AAAA,MACZ;AACA,YAAM,aAAa,WAAY;AAC3B,YAAI,CAAC,MAAM,aAAa,CAAC,MAAM,QAAQ;AACnC,iBAAO;AAAA,QACX;AACA,YAAI,SAAS,MAAM;AACnB,YAAI,CAAC,QAAQ;AACT,iBAAO;AAAA,QACX;AACA,YAAI,UAAU,MAAM,OAAO,SAAS,cAAc,KAAK;AACvD,gBAAQ,MAAM,QAAQ;AACtB,gBAAQ,MAAM,SAAS;AACvB,gBAAQ,MAAM,WAAW;AACzB,gBAAQ,MAAM,YAAY;AAC1B,gBAAQ,MAAM,OAAO;AACrB,gBAAQ,MAAM,OAAO;AACrB,YAAI,QAAQ,WAAW;AACnB,kBAAQ,UAAU,IAAI,aAAa;AAAA,QACvC,OACK;AACD,kBAAQ,aAAa;AAAA,QACzB;AACA,eAAO,YAAY,OAAO;AAC1B,eAAO;AAAA,MACX;AACA,YAAM,aAAa,SAAU,MAAM;AAC/B,YAAI,SAAS,MAAM;AACnB,YAAI,CAAC,QAAQ;AACT;AAAA,QACJ;AACA,eAAO,YAAY,IAAI;AAAA,MAC3B;AACA,YAAM,QAAQ;AAAA,QACV,YAAY;AAAA,QACZ,QAAQ,MAAM,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,WAAW,QAAQ,OAAO,SAAS,KAAK;AAAA,QACpH,SAAS,MAAM,KAAK,MAAM,eAAe,QAAQ,OAAO,SAAS,SAAS,GAAG,YAAY,QAAQ,OAAO,SAAS,KAAK;AAAA,QACtH,WAAW;AAAA,QACX,UAAU;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,OAAO;AAAA,UACP,QAAQ;AAAA,QACZ;AAAA,QACA,iBAAiB;AAAA,UACb,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,iBAAiB;AAAA,UACjB,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,OAAO;AAAA,QACX;AAAA,QACA,WAAW;AAAA,MACf;AACA,YAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,YAAY,MAAM,UAAU,KAAK,KAAK;AAC5C,aAAO;AAAA,IACX;AACA,WAAO,eAAeA,WAAU,WAAW,cAAc;AAAA,MACrD,KAAK,WAAY;AACb,YAAI,CAAC,KAAK,WAAW;AACjB,iBAAO;AAAA,QACX;AACA,eAAO,KAAK,UAAU;AAAA,MAC1B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,UAAU;AAAA,MACjD,KAAK,WAAY;AACb,YAAI,CAAC,KAAK,WAAW;AACjB,iBAAO;AAAA,QACX;AACA,YAAI,CAAC,KAAK,UAAU,eAAe;AAC/B,iBAAO;AAAA,QACX;AACA,eAAO,KAAK,UAAU,cAAc;AAAA,MACxC;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,aAAa;AAAA,MACpD,KAAK,WAAY;AACb,eAAO,KAAK,MAAM,QAAQ,KAAK,MAAM,eAAe;AAAA,MACxD;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,QAAQ;AAAA,MAC/C,KAAK,WAAY;AACb,YAAI,QAAQ;AACZ,YAAI,SAAS;AACb,YAAI,KAAK,aAAa,KAAK,QAAQ;AAC/B,cAAI,WAAW,KAAK,UAAU;AAC9B,cAAI,YAAY,KAAK,UAAU;AAG/B,cAAI,cAAc,KAAK,UAAU,MAAM;AACvC,cAAI,gBAAgB,YAAY;AAC5B,iBAAK,UAAU,MAAM,WAAW;AAAA,UACpC;AAEA,kBAAQ,KAAK,UAAU,MAAM,UAAU,SAAS,KAAK,UAAU,cAAc;AAC7E,mBAAS,KAAK,UAAU,MAAM,WAAW,SAAS,KAAK,UAAU,eAAe;AAEhF,eAAK,UAAU,MAAM,WAAW;AAAA,QACpC;AACA,eAAO,EAAE,OAAc,OAAe;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,WAAO,eAAeA,WAAU,WAAW,aAAa;AAAA,MACpD,KAAK,WAAY;AACb,YAAI,QAAQ;AACZ,YAAI,OAAO,KAAK,MAAM;AACtB,YAAI,UAAU,SAAU,KAAK;AACzB,cAAI;AACJ,cAAI,OAAO,MAAM,MAAM,GAAG,MAAM,eAAe,MAAM,MAAM,GAAG,MAAM,QAAQ;AACxE,mBAAO;AAAA,UACX;AACA,cAAI,MAAM,aAAa,MAAM,UAAU,GAAG,OAAO,KAAK,MAAM,UAAU,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,SAAS,EAAE,SAAS,GAAG,IAAI;AAC3I,gBAAI,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,SAAS,GAAG,GAAG;AAC3C,qBAAO,MAAM,MAAM,GAAG,EAAE,SAAS;AAAA,YACrC;AACA,gBAAI,aAAa,MAAM,cAAc;AACrC,gBAAI,QAAQ,OAAO,MAAM,MAAM,GAAG,EAAE,SAAS,EAAE,QAAQ,MAAM,EAAE,CAAC;AAChE,gBAAI,UAAW,QAAQ,WAAW,GAAG,IAAK;AAC1C,mBAAO,GAAG,OAAO,SAAS,GAAG;AAAA,UACjC;AACA,iBAAO,cAAc,MAAM,MAAM,GAAG,CAAC;AAAA,QACzC;AACA,YAAI,QAAQ,QAAQ,OAAO,KAAK,UAAU,eAAe,CAAC,KAAK,MAAM,aAC/D,cAAc,KAAK,KAAK,IACxB,QAAQ,OAAO;AACrB,YAAI,SAAS,QAAQ,OAAO,KAAK,WAAW,eAAe,CAAC,KAAK,MAAM,aACjE,cAAc,KAAK,MAAM,IACzB,QAAQ,QAAQ;AACtB,eAAO,EAAE,OAAc,OAAe;AAAA,MAC1C;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAC5C,UAAI,CAAC,KAAK,YAAY;AAClB,YAAI,CAAC,KAAK,QAAQ;AACd,iBAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,QACjC;AACA,eAAO,EAAE,OAAO,KAAK,OAAO,YAAY,QAAQ,KAAK,OAAO,YAAY;AAAA,MAC5E;AACA,UAAI,OAAO,KAAK,WAAW;AAC3B,UAAI,CAAC,MAAM;AACP,eAAO,EAAE,OAAO,GAAG,QAAQ,EAAE;AAAA,MACjC;AAEA,UAAI,cAAc;AAClB,UAAI,OAAO,KAAK,WAAW,MAAM;AACjC,UAAI,SAAS,QAAQ;AACjB,sBAAc;AACd,aAAK,WAAW,MAAM,WAAW;AAAA,MAErC;AACA,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,WAAW;AACtB,WAAK,MAAM,YAAY;AACvB,UAAI,OAAO;AAAA,QACP,OAAO,KAAK;AAAA,QACZ,QAAQ,KAAK;AAAA,MACjB;AACA,UAAI,aAAa;AACb,aAAK,WAAW,MAAM,WAAW;AAAA,MACrC;AACA,WAAK,WAAW,IAAI;AACpB,aAAO;AAAA,IACX;AACA,IAAAA,WAAU,UAAU,aAAa,WAAY;AACzC,UAAI,KAAK,QAAQ;AACb,aAAK,OAAO,iBAAiB,WAAW,KAAK,SAAS;AACtD,aAAK,OAAO,iBAAiB,aAAa,KAAK,WAAW;AAC1D,aAAK,OAAO,iBAAiB,cAAc,KAAK,SAAS;AACzD,aAAK,OAAO,iBAAiB,aAAa,KAAK,aAAa;AAAA,UACxD,SAAS;AAAA,UACT,SAAS;AAAA,QACb,CAAC;AACD,aAAK,OAAO,iBAAiB,YAAY,KAAK,SAAS;AAAA,MAC3D;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,eAAe,WAAY;AAC3C,UAAI,KAAK,QAAQ;AACb,aAAK,OAAO,oBAAoB,WAAW,KAAK,SAAS;AACzD,aAAK,OAAO,oBAAoB,aAAa,KAAK,WAAW;AAC7D,aAAK,OAAO,oBAAoB,cAAc,KAAK,SAAS;AAC5D,aAAK,OAAO,oBAAoB,aAAa,KAAK,aAAa,IAAI;AACnE,aAAK,OAAO,oBAAoB,YAAY,KAAK,SAAS;AAAA,MAC9D;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,oBAAoB,WAAY;AAChD,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AACjC;AAAA,MACJ;AACA,UAAI,gBAAgB,KAAK,OAAO,iBAAiB,KAAK,SAAS;AAC/D,WAAK,SAAS;AAAA,QACV,OAAO,KAAK,MAAM,SAAS,KAAK,KAAK;AAAA,QACrC,QAAQ,KAAK,MAAM,UAAU,KAAK,KAAK;AAAA,QACvC,WAAW,cAAc,cAAc,SAAS,cAAc,YAAY;AAAA,MAC9E,CAAC;AAAA,IACL;AACA,IAAAA,WAAU,UAAU,uBAAuB,WAAY;AACnD,UAAI,KAAK,QAAQ;AACb,aAAK,aAAa;AAAA,MACtB;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,2BAA2B,SAAU,SAAS,MAAM;AACpE,UAAI,YAAY,KAAK,aAAa,KAAK,UAAU,IAAI;AACrD,aAAO,KAAK,MAAM,IAAI,MAAM,UACxB,KAAK,MAAM,SAAS,IAAI,MAAM,YAC7B,OAAO,cAAc,eAAe,cAAc,UACjD,SACA;AAAA,IACV;AACA,IAAAA,WAAU,UAAU,8BAA8B,SAAU,UAAU,WAAW;AAC7E,UAAI,oBAAoB,KAAK,MAAM;AACnC,UAAI,YAAY,KAAK,MAAM;AAC3B,UAAI,mBAAmB,qBAAqB,aAAa,QAAQ,SAAS;AAC1E,UAAI,oBAAoB,qBAAqB,aAAa,OAAO,SAAS;AAC1E,UAAI;AACJ,UAAI;AACJ,UAAI,KAAK,MAAM,WAAW,UAAU;AAChC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,uBAAa,mBACP,KAAK,iBAAiB,KAAK,aAC3B,SAAS,eAAe,KAAK,aAAa,KAAK;AACrD,wBAAc,oBACR,KAAK,kBAAkB,KAAK,YAC5B,SAAS,gBAAgB,KAAK,YAAY,KAAK;AAAA,QACzD;AAAA,MACJ,WACS,KAAK,MAAM,WAAW,UAAU;AACrC,YAAI,KAAK,QAAQ;AACb,uBAAa,mBAAmB,KAAK,iBAAiB,KAAK,OAAO,aAAa,KAAK;AACpF,wBAAc,oBAAoB,KAAK,kBAAkB,KAAK,OAAO,cAAc,KAAK;AAAA,QAC5F;AAAA,MACJ,WACS,KAAK,MAAM,QAAQ;AACxB,qBAAa,mBACP,KAAK,iBAAiB,KAAK,aAC3B,KAAK,MAAM,OAAO,eAAe,KAAK,aAAa,KAAK;AAC9D,sBAAc,oBACR,KAAK,kBAAkB,KAAK,YAC5B,KAAK,MAAM,OAAO,gBAAgB,KAAK,YAAY,KAAK;AAAA,MAClE;AACA,UAAI,cAAc,OAAO,SAAS,UAAU,GAAG;AAC3C,mBAAW,YAAY,WAAW,aAAa,WAAW;AAAA,MAC9D;AACA,UAAI,eAAe,OAAO,SAAS,WAAW,GAAG;AAC7C,oBAAY,aAAa,YAAY,cAAc,YAAY;AAAA,MACnE;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,gCAAgC,SAAU,SAAS,SAAS;AAC5E,UAAI,QAAQ,KAAK,MAAM,SAAS;AAChC,UAAI,KAAK,gBAAgB,KAAK,MAAM,eAAe,CAAC,GAAG,eAAe,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAChG,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,WAAW,GAAG;AAC7D,UAAI,KAAK,KAAK,OAAO,kBAAkB,GAAG,iBAAiB,6BAA6B,GAAG,4BAA4B,4BAA4B,GAAG;AACtJ,UAAI,WAAW,SAAS;AACxB,UAAI,YAAY,SAAS;AACzB,UAAI,cAAc,8BAA8B;AAChD,UAAI,aAAa,6BAA6B;AAC9C,UAAI,aAAa,SAAS,SAAS,GAAG;AAClC,mBAAW,SAAS,SAAU,UAAU,SAAS,KAAK,eAAgB;AACtE,YAAI,iBAAiB;AACjB,uBAAa,WAAW,cAAc,KAAK,QAAQ;AAAA,QACvD;AAAA,MACJ;AACA,UAAI,aAAa,QAAQ,SAAS,GAAG;AACjC,mBAAW,SAAS,SAAU,UAAU,SAAS,KAAK,eAAgB;AACtE,YAAI,iBAAiB;AACjB,uBAAa,WAAW,cAAc,KAAK,QAAQ;AAAA,QACvD;AAAA,MACJ;AACA,UAAI,aAAa,UAAU,SAAS,GAAG;AACnC,oBAAY,SAAS,UAAW,UAAU,SAAS,KAAK,eAAgB;AACxE,YAAI,iBAAiB;AACjB,sBAAY,YAAY,eAAe,KAAK,QAAQ;AAAA,QACxD;AAAA,MACJ;AACA,UAAI,aAAa,OAAO,SAAS,GAAG;AAChC,oBAAY,SAAS,UAAW,UAAU,SAAS,KAAK,eAAgB;AACxE,YAAI,iBAAiB;AACjB,sBAAY,YAAY,eAAe,KAAK,QAAQ;AAAA,QACxD;AAAA,MACJ;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,kCAAkC,SAAU,UAAU,WAAW,KAAK,KAAK;AAC3F,UAAI,KAAK,KAAK,OAAO,kBAAkB,GAAG,iBAAiB,6BAA6B,GAAG,4BAA4B,4BAA4B,GAAG;AACtJ,UAAI,mBAAmB,OAAO,IAAI,UAAU,cAAc,KAAK,IAAI;AACnE,UAAI,mBAAmB,OAAO,IAAI,UAAU,eAAe,IAAI,QAAQ,IAAI,WAAW,IAAI;AAC1F,UAAI,oBAAoB,OAAO,IAAI,WAAW,cAAc,KAAK,IAAI;AACrE,UAAI,oBAAoB,OAAO,IAAI,WAAW,eAAe,IAAI,SAAS,IAAI,YAAY,IAAI;AAC9F,UAAI,cAAc,8BAA8B;AAChD,UAAI,aAAa,6BAA6B;AAC9C,UAAI,iBAAiB;AACjB,YAAI,iBAAiB,oBAAoB,eAAe,KAAK,QAAQ;AACrE,YAAI,iBAAiB,oBAAoB,eAAe,KAAK,QAAQ;AACrE,YAAI,kBAAkB,mBAAmB,cAAc,KAAK,QAAQ;AACpE,YAAI,kBAAkB,mBAAmB,cAAc,KAAK,QAAQ;AACpE,YAAI,iBAAiB,KAAK,IAAI,kBAAkB,aAAa;AAC7D,YAAI,iBAAiB,KAAK,IAAI,kBAAkB,aAAa;AAC7D,YAAI,kBAAkB,KAAK,IAAI,mBAAmB,cAAc;AAChE,YAAI,kBAAkB,KAAK,IAAI,mBAAmB,cAAc;AAChE,mBAAW,MAAM,UAAU,gBAAgB,cAAc;AACzD,oBAAY,MAAM,WAAW,iBAAiB,eAAe;AAAA,MACjE,OACK;AACD,mBAAW,MAAM,UAAU,kBAAkB,gBAAgB;AAC7D,oBAAY,MAAM,WAAW,mBAAmB,iBAAiB;AAAA,MACrE;AACA,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,WAAU,UAAU,wBAAwB,WAAY;AACpD,UAAI,gBAAgB,KAAK,KAAK,MAAM,SAAS;AAE7C,UAAI,KAAK,MAAM,WAAW,UAAU;AAChC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,cAAI,aAAa,SAAS,sBAAsB;AAChD,eAAK,aAAa,WAAW,OAAO;AACpC,eAAK,YAAY,WAAW,MAAM;AAAA,QACtC;AAAA,MACJ;AAEA,UAAI,KAAK,MAAM,UAAU,OAAO,KAAK,MAAM,WAAW,UAAU;AAC5D,YAAI,aAAa,KAAK,MAAM,OAAO,sBAAsB;AACzD,aAAK,aAAa,WAAW,OAAO;AACpC,aAAK,YAAY,WAAW,MAAM;AAAA,MACtC;AAEA,UAAI,KAAK,WAAW;AAChB,YAAI,KAAK,KAAK,UAAU,sBAAsB,GAAG,OAAO,GAAG,MAAM,QAAQ,GAAG,KAAK,QAAQ,GAAG,OAAO,SAAS,GAAG;AAC/G,aAAK,gBAAgB,OAAO;AAC5B,aAAK,iBAAiB,QAAQ;AAC9B,aAAK,eAAe,QAAQ;AAC5B,aAAK,kBAAkB,SAAS;AAAA,MACpC;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,gBAAgB,SAAU,OAAO,WAAW;AAC5D,UAAI,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AACjC;AAAA,MACJ;AACA,UAAI,UAAU;AACd,UAAI,UAAU;AACd,UAAI,MAAM,eAAe,aAAa,MAAM,WAAW,GAAG;AACtD,kBAAU,MAAM,YAAY;AAC5B,kBAAU,MAAM,YAAY;AAAA,MAChC,WACS,MAAM,eAAe,aAAa,MAAM,WAAW,GAAG;AAC3D,kBAAU,MAAM,YAAY,QAAQ,CAAC,EAAE;AACvC,kBAAU,MAAM,YAAY,QAAQ,CAAC,EAAE;AAAA,MAC3C;AACA,UAAI,KAAK,MAAM,eAAe;AAC1B,YAAI,KAAK,WAAW;AAChB,cAAI,cAAc,KAAK,MAAM,cAAc,OAAO,WAAW,KAAK,SAAS;AAC3E,cAAI,gBAAgB,OAAO;AACvB;AAAA,UACJ;AAAA,QACJ;AAAA,MACJ;AAEA,UAAI,KAAK,MAAM,MAAM;AACjB,YAAI,OAAO,KAAK,MAAM,KAAK,WAAW,eAAe,KAAK,MAAM,KAAK,WAAW,KAAK,MAAM,QAAQ;AAC/F,eAAK,SAAS,EAAE,QAAQ,KAAK,MAAM,KAAK,OAAO,CAAC;AAAA,QACpD;AACA,YAAI,OAAO,KAAK,MAAM,KAAK,UAAU,eAAe,KAAK,MAAM,KAAK,UAAU,KAAK,MAAM,OAAO;AAC5F,eAAK,SAAS,EAAE,OAAO,KAAK,MAAM,KAAK,MAAM,CAAC;AAAA,QAClD;AAAA,MACJ;AAEA,WAAK,QACD,OAAO,KAAK,MAAM,oBAAoB,WAAW,KAAK,MAAM,kBAAkB,KAAK,KAAK,QAAQ,KAAK,KAAK;AAC9G,UAAI;AACJ,UAAI,gBAAgB,KAAK,OAAO,iBAAiB,KAAK,SAAS;AAC/D,UAAI,cAAc,cAAc,QAAQ;AACpC,YAAI,WAAW,KAAK;AACpB,YAAI,UAAU;AACV,cAAI,MAAM,KAAK,OAAO,iBAAiB,QAAQ,EAAE;AACjD,eAAK,UAAU,IAAI,WAAW,KAAK,IAAI,QAAQ;AAC/C,sBAAY,cAAc;AAAA,QAC9B;AAAA,MACJ;AAEA,WAAK,sBAAsB;AAC3B,WAAK,WAAW;AAChB,UAAI,QAAQ;AAAA,QACR,UAAU;AAAA,UACN,GAAG;AAAA,UACH,GAAG;AAAA,UACH,OAAO,KAAK,KAAK;AAAA,UACjB,QAAQ,KAAK,KAAK;AAAA,QACtB;AAAA,QACA,YAAY;AAAA,QACZ,iBAAiBD,UAASA,UAAS,CAAC,GAAG,KAAK,MAAM,eAAe,GAAG,EAAE,QAAQ,KAAK,OAAO,iBAAiB,MAAM,MAAM,EAAE,UAAU,OAAO,CAAC;AAAA,QAC3I;AAAA,QACA;AAAA,MACJ;AACA,WAAK,SAAS,KAAK;AAAA,IACvB;AACA,IAAAC,WAAU,UAAU,cAAc,SAAU,OAAO;AAC/C,UAAI,QAAQ;AACZ,UAAI,CAAC,KAAK,MAAM,cAAc,CAAC,KAAK,aAAa,CAAC,KAAK,QAAQ;AAC3D;AAAA,MACJ;AACA,UAAI,KAAK,OAAO,cAAc,aAAa,KAAK,GAAG;AAC/C,YAAI;AACA,gBAAM,eAAe;AACrB,gBAAM,gBAAgB;AAAA,QAC1B,SACO,GAAG;AAAA,QAEV;AAAA,MACJ;AACA,UAAI,KAAK,KAAK,OAAO,WAAW,GAAG,UAAU,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,YAAY,GAAG;AAC9G,UAAI,UAAU,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,EAAE,UAAU,MAAM;AACrE,UAAI,UAAU,aAAa,KAAK,IAAI,MAAM,QAAQ,CAAC,EAAE,UAAU,MAAM;AACrE,UAAI,KAAK,KAAK,OAAO,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,QAAQ,GAAG,OAAO,SAAS,GAAG;AACrG,UAAI,aAAa,KAAK,cAAc;AACpC,UAAI,MAAM,gBAAgB,YAAY,KAAK,OAAO,YAAY,KAAK,OAAO,aAAa,UAAU,WAAW,UAAU,SAAS;AAC/H,iBAAW,IAAI;AACf,kBAAY,IAAI;AAChB,iBAAW,IAAI;AACf,kBAAY,IAAI;AAEhB,UAAI,KAAK,KAAK,8BAA8B,SAAS,OAAO,GAAG,YAAY,GAAG,WAAW,WAAW,GAAG;AAEvG,UAAI,cAAc,KAAK,4BAA4B,UAAU,SAAS;AACtE,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,GAAG;AACtC,mBAAW,gBAAgB,UAAU,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAAA,MAC9E;AACA,UAAI,KAAK,MAAM,QAAQ,KAAK,MAAM,KAAK,GAAG;AACtC,oBAAY,gBAAgB,WAAW,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,OAAO;AAAA,MAChF;AAEA,UAAI,UAAU,KAAK,gCAAgC,UAAU,WAAW,EAAE,OAAO,YAAY,UAAU,QAAQ,YAAY,UAAU,GAAG,EAAE,OAAO,UAAU,QAAQ,UAAU,CAAC;AAC9K,iBAAW,QAAQ;AACnB,kBAAY,QAAQ;AACpB,UAAI,KAAK,MAAM,MAAM;AACjB,YAAI,eAAe,KAAK,UAAU,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,CAAC,IAAI,CAAC;AACpG,YAAI,gBAAgB,KAAK,WAAW,KAAK,MAAM,KAAK,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ,CAAC,IAAI,CAAC;AACtG,YAAI,MAAM,KAAK,MAAM,WAAW;AAChC,YAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,eAAe,QAAQ,KAAK,MAAM,eAAe;AAC/E,YAAI,IAAI,QAAQ,KAAK,KAAK,IAAI,gBAAgB,SAAS,KAAK,MAAM,gBAAgB;AAClF,mBAAW;AACX,oBAAY;AAAA,MAChB;AACA,UAAI,QAAQ;AAAA,QACR,OAAO,WAAW,SAAS;AAAA,QAC3B,QAAQ,YAAY,SAAS;AAAA,MACjC;AACA,WAAK,QAAQ;AACb,UAAI,SAAS,OAAO,UAAU,UAAU;AACpC,YAAI,MAAM,SAAS,GAAG,GAAG;AACrB,cAAI,UAAW,WAAW,WAAW,QAAS;AAC9C,qBAAW,GAAG,OAAO,SAAS,GAAG;AAAA,QACrC,WACS,MAAM,SAAS,IAAI,GAAG;AAC3B,cAAI,KAAM,WAAW,KAAK,OAAO,aAAc;AAC/C,qBAAW,GAAG,OAAO,IAAI,IAAI;AAAA,QACjC,WACS,MAAM,SAAS,IAAI,GAAG;AAC3B,cAAI,KAAM,WAAW,KAAK,OAAO,cAAe;AAChD,qBAAW,GAAG,OAAO,IAAI,IAAI;AAAA,QACjC;AAAA,MACJ;AACA,UAAI,UAAU,OAAO,WAAW,UAAU;AACtC,YAAI,OAAO,SAAS,GAAG,GAAG;AACtB,cAAI,UAAW,YAAY,WAAW,SAAU;AAChD,sBAAY,GAAG,OAAO,SAAS,GAAG;AAAA,QACtC,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,cAAI,KAAM,YAAY,KAAK,OAAO,aAAc;AAChD,sBAAY,GAAG,OAAO,IAAI,IAAI;AAAA,QAClC,WACS,OAAO,SAAS,IAAI,GAAG;AAC5B,cAAI,KAAM,YAAY,KAAK,OAAO,cAAe;AACjD,sBAAY,GAAG,OAAO,IAAI,IAAI;AAAA,QAClC;AAAA,MACJ;AACA,UAAI,WAAW;AAAA,QACX,OAAO,KAAK,yBAAyB,UAAU,OAAO;AAAA,QACtD,QAAQ,KAAK,yBAAyB,WAAW,QAAQ;AAAA,MAC7D;AACA,UAAI,KAAK,YAAY,OAAO;AACxB,iBAAS,YAAY,SAAS;AAAA,MAClC,WACS,KAAK,YAAY,UAAU;AAChC,iBAAS,YAAY,SAAS;AAAA,MAClC;AACA,UAAI,eAAe,KAAK,MAAM,UAAU,SAAS;AACjD,UAAI,gBAAgB,KAAK,MAAM,WAAW,SAAS;AACnD,UAAI,kBAAkB,KAAK,MAAM,cAAc,SAAS;AACxD,UAAI,UAAU,gBAAgB,iBAAiB;AAC/C,UAAI,SAAS;AAET,wCAAU,WAAY;AAClB,gBAAM,SAAS,QAAQ;AAAA,QAC3B,CAAC;AAAA,MACL;AACA,UAAI,KAAK,MAAM,UAAU;AACrB,YAAI,SAAS;AACT,eAAK,MAAM,SAAS,OAAO,WAAW,KAAK,WAAW,KAAK;AAAA,QAC/D;AAAA,MACJ;AAAA,IACJ;AACA,IAAAA,WAAU,UAAU,YAAY,SAAU,OAAO;AAC7C,UAAI,IAAI;AACR,UAAI,KAAK,KAAK,OAAO,aAAa,GAAG,YAAY,YAAY,GAAG,WAAW,WAAW,GAAG;AACzF,UAAI,CAAC,cAAc,CAAC,KAAK,WAAW;AAChC;AAAA,MACJ;AACA,UAAI,KAAK,MAAM,cAAc;AACzB,aAAK,MAAM,aAAa,OAAO,WAAW,KAAK,WAAW,KAAK,KAAK;AAAA,MACxE;AACA,UAAI,KAAK,MAAM,MAAM;AACjB,aAAK,SAAS,EAAE,QAAQ,KAAK,KAAK,MAAM,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS,KAAK,KAAK,MAAM,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,CAAC;AAAA,MAChL;AACA,WAAK,aAAa;AAClB,WAAK,SAAS;AAAA,QACV,YAAY;AAAA,QACZ,iBAAiBD,UAASA,UAAS,CAAC,GAAG,KAAK,MAAM,eAAe,GAAG,EAAE,QAAQ,OAAO,CAAC;AAAA,MAC1F,CAAC;AAAA,IACL;AACA,IAAAC,WAAU,UAAU,aAAa,SAAU,MAAM;AAC7C,UAAI,IAAI;AACR,WAAK,SAAS,EAAE,QAAQ,KAAK,KAAK,WAAW,QAAQ,OAAO,SAAS,KAAK,QAAQ,SAAS,KAAK,KAAK,YAAY,QAAQ,OAAO,SAAS,KAAK,OAAO,CAAC;AAAA,IAC1J;AACA,IAAAA,WAAU,UAAU,gBAAgB,WAAY;AAC5C,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,SAAS,GAAG,QAAQ,eAAe,GAAG,cAAc,gBAAgB,GAAG,eAAe,qBAAqB,GAAG,oBAAoB,qBAAqB,GAAG,oBAAoB,kBAAkB,GAAG;AACxN,UAAI,CAAC,QAAQ;AACT,eAAO;AAAA,MACX;AACA,UAAI,WAAW,OAAO,KAAK,MAAM,EAAE,IAAI,SAAU,KAAK;AAClD,YAAI,OAAO,GAAG,MAAM,OAAO;AACvB,qBAAQ,oBAAAC,KAAK,SAAS,EAAE,WAAW,KAAK,eAAe,MAAM,eAAe,eAAe,gBAAgB,aAAa,GAAG,GAAG,WAAW,iBAAiB,cAAc,GAAG,GAAG,UAAU,mBAAmB,gBAAgB,GAAG,IAAI,gBAAgB,GAAG,IAAI,KAAK,GAAG,GAAG;AAAA,QACxQ;AACA,eAAO;AAAA,MACX,CAAC;AAED,iBAAQ,oBAAAA,KAAK,OAAO,EAAE,WAAW,oBAAoB,OAAO,oBAAoB,UAAU,SAAS,CAAC;AAAA,IACxG;AACA,IAAAD,WAAU,UAAU,SAAS,WAAY;AACrC,UAAI,QAAQ;AACZ,UAAI,eAAe,OAAO,KAAK,KAAK,KAAK,EAAE,OAAO,SAAU,KAAK,KAAK;AAClE,YAAI,aAAa,QAAQ,GAAG,MAAM,IAAI;AAClC,iBAAO;AAAA,QACX;AACA,YAAI,GAAG,IAAI,MAAM,MAAM,GAAG;AAC1B,eAAO;AAAA,MACX,GAAG,CAAC,CAAC;AACL,UAAI,QAAQD,UAASA,UAASA,UAAS,EAAE,UAAU,YAAY,YAAY,KAAK,MAAM,aAAa,SAAS,OAAO,GAAG,KAAK,MAAM,KAAK,GAAG,KAAK,SAAS,GAAG,EAAE,UAAU,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,UAAU,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,WAAW,cAAc,YAAY,EAAE,CAAC;AACpU,UAAI,KAAK,MAAM,WAAW;AACtB,cAAM,YAAY,KAAK,MAAM;AAAA,MACjC;AACA,UAAI,UAAU,KAAK,MAAM,MAAM;AAC/B,iBAAQ,oBAAAG,MAAM,SAASH,UAAS,EAAE,OAAc,WAAW,KAAK,MAAM,UAAU,GAAG,cAAc;AAAA;AAAA;AAAA,QAG7F,KAAK,SAAU,GAAG;AACd,cAAI,GAAG;AACH,kBAAM,YAAY;AAAA,UACtB;AAAA,QACJ;AAAA,QAAG,UAAU,CAAC,KAAK,MAAM,kBAAc,oBAAAE,KAAK,OAAO,EAAE,OAAO,KAAK,MAAM,gBAAgB,CAAC,GAAG,KAAK,MAAM,UAAU,KAAK,cAAc,CAAC;AAAA,MAAE,CAAC,CAAC;AAAA,IAChJ;AACA,IAAAD,WAAU,eAAe;AAAA,MACrB,IAAI;AAAA,MACJ,eAAe,WAAY;AAAA,MAAE;AAAA,MAC7B,UAAU,WAAY;AAAA,MAAE;AAAA,MACxB,cAAc,WAAY;AAAA,MAAE;AAAA,MAC5B,QAAQ;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA,QACP,QAAQ;AAAA,QACR,MAAM;AAAA,QACN,UAAU;AAAA,QACV,aAAa;AAAA,QACb,YAAY;AAAA,QACZ,SAAS;AAAA,MACb;AAAA,MACA,OAAO,CAAC;AAAA,MACR,MAAM,CAAC,GAAG,CAAC;AAAA,MACX,SAAS,CAAC,GAAG,CAAC;AAAA,MACd,iBAAiB;AAAA,MACjB,2BAA2B;AAAA,MAC3B,4BAA4B;AAAA,MAC5B,OAAO;AAAA,MACP,aAAa;AAAA,MACb,SAAS;AAAA,IACb;AACA,WAAOA;AAAA,EACX,EAAE,2BAAa;AAAA;;;AD1wBf,IAAAG,oBAA0B;AAkB1B,IAAI,gBAAgB,SAAS,GAAG,GAAG;AAC/B,kBAAgB,OAAO,kBAClB,EAAE,WAAW,CAAC,EAAE,aAAa,SAAS,SAAUC,IAAGC,IAAG;AAAE,IAAAD,GAAE,YAAYC;AAAA,EAAG,KAC1E,SAAUD,IAAGC,IAAG;AAAE,aAAS,KAAKA,GAAG,KAAIA,GAAE,eAAe,CAAC,EAAG,CAAAD,GAAE,CAAC,IAAIC,GAAE,CAAC;AAAA,EAAG;AAC7E,SAAO,cAAc,GAAG,CAAC;AAC7B;AAEA,SAASC,WAAU,GAAG,GAAG;AACrB,gBAAc,GAAG,CAAC;AAClB,WAAS,KAAK;AAAE,SAAK,cAAc;AAAA,EAAG;AACtC,IAAE,YAAY,MAAM,OAAO,OAAO,OAAO,CAAC,KAAK,GAAG,YAAY,EAAE,WAAW,IAAI,GAAG;AACtF;AAEA,IAAIC,YAAW,WAAW;AACtB,EAAAA,YAAW,OAAO,UAAU,SAASA,UAAS,GAAG;AAC7C,aAAS,GAAG,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AACjD,UAAI,UAAU,CAAC;AACf,eAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,EAAG,GAAE,CAAC,IAAI,EAAE,CAAC;AAAA,IAC/E;AACA,WAAO;AAAA,EACX;AACA,SAAOA,UAAS,MAAM,MAAM,SAAS;AACzC;AAEA,SAAS,OAAO,GAAG,GAAG;AAClB,MAAI,IAAI,CAAC;AACT,WAAS,KAAK,EAAG,KAAI,OAAO,UAAU,eAAe,KAAK,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,IAAI;AAC9E,MAAE,CAAC,IAAI,EAAE,CAAC;AACd,MAAI,KAAK,QAAQ,OAAO,OAAO,0BAA0B;AACrD,aAAS,IAAI,GAAG,IAAI,OAAO,sBAAsB,CAAC,GAAG,IAAI,EAAE,QAAQ,KAAK;AACpE,UAAI,EAAE,QAAQ,EAAE,CAAC,CAAC,IAAI,KAAK,OAAO,UAAU,qBAAqB,KAAK,GAAG,EAAE,CAAC,CAAC;AACzE,UAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;AAAA,IACxB;AACJ,SAAO;AACX;AAEA,IAAI,iBAAiB;AAAA,EACjB,OAAO;AAAA,EACP,QAAQ;AAAA,EACR,SAAS;AAAA,EACT,UAAU;AAAA,EACV,KAAK;AAAA,EACL,MAAM;AACV;AACA,IAAI,0BAA0B,SAAU,MAAM;AAAE,SAAQ;AAAA,IACpD,QAAQ;AAAA,IACR,YAAY;AAAA,IACZ,aAAa;AAAA,IACb,MAAM;AAAA,IACN,OAAO;AAAA,IACP,KAAK;AAAA,IACL,SAAS;AAAA,IACT,UAAU;AAAA,EACd;AAAI;AACJ,IAAI;AAAA;AAAA,EAAqB,SAAU,QAAQ;AACvC,IAAAD,WAAUE,MAAK,MAAM;AACrB,aAASA,KAAI,OAAO;AAChB,UAAI,QAAQ,OAAO,KAAK,MAAM,KAAK,KAAK;AACxC,YAAM,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAE;AACtC,YAAM,mBAAmB,EAAE,MAAM,GAAG,KAAK,EAAE;AAC3C,YAAM,mBAAmB,EAAE,SAAS,KAAK;AACzC,YAAM,mBAAmB,EAAE,GAAG,GAAG,GAAG,EAAE;AACtC,YAAM,QAAQ;AAAA,QACV,UAAU;AAAA,QACV,QAAQ;AAAA,UACJ,KAAK;AAAA,UACL,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,MAAM;AAAA,QACV;AAAA,QACA,UAAU,MAAM;AAAA,QAChB,WAAW,MAAM;AAAA,MACrB;AACA,YAAM,gBAAgB,MAAM,cAAc,KAAK,KAAK;AACpD,YAAM,WAAW,MAAM,SAAS,KAAK,KAAK;AAC1C,YAAM,eAAe,MAAM,aAAa,KAAK,KAAK;AAClD,YAAM,cAAc,MAAM,YAAY,KAAK,KAAK;AAChD,YAAM,SAAS,MAAM,OAAO,KAAK,KAAK;AACtC,YAAM,aAAa,MAAM,WAAW,KAAK,KAAK;AAC9C,YAAM,uBAAuB,MAAM,qBAAqB,KAAK,KAAK;AAClE,aAAO;AAAA,IACX;AACA,IAAAA,KAAI,UAAU,oBAAoB,WAAY;AAC1C,WAAK,uBAAuB;AAC5B,UAAI,KAAK,KAAK,kBAAkB,OAAO,GAAG,MAAM,MAAM,GAAG;AACzD,UAAI,KAAK,KAAK,qBAAqB,GAAG,IAAI,GAAG,GAAG,IAAI,GAAG;AACvD,WAAK,UAAU,SAAS;AAAA,QACpB,GAAG,IAAI;AAAA,QACP,GAAG,IAAI;AAAA,MACX,CAAC;AAED,WAAK,YAAY;AAAA,IACrB;AAEA,IAAAA,KAAI,UAAU,uBAAuB,WAAY;AAC7C,UAAI,KAAK,KAAK,UAAU,OAAO,IAAI,GAAG,GAAG,IAAI,GAAG;AAChD,aAAO,EAAE,GAAM,EAAK;AAAA,IACxB;AACA,IAAAA,KAAI,UAAU,YAAY,WAAY;AAClC,aAAO,KAAK,aAAa,KAAK,UAAU;AAAA,IAC5C;AACA,IAAAA,KAAI,UAAU,gBAAgB,WAAY;AACtC,aAAO,KAAK,UAAU,cAAc;AAAA,IACxC;AACA,IAAAA,KAAI,UAAU,uBAAuB,WAAY;AAC7C,UAAI,WAAW,OAAO,KAAK,MAAM,aAAa,cAAc,OAAO,mBAAmB,KAAK,MAAM;AACjG,UAAI,YAAY,OAAO,KAAK,MAAM,cAAc,cAAc,OAAO,mBAAmB,KAAK,MAAM;AACnG,aAAO,EAAE,UAAoB,UAAqB;AAAA,IACtD;AACA,IAAAA,KAAI,UAAU,iBAAiB,WAAY;AACvC,aAAO,KAAK,aAAa,KAAK,UAAU;AAAA,IAC5C;AACA,IAAAA,KAAI,UAAU,kBAAkB,SAAU,UAAU;AAChD,UAAI,QAAQ,KAAK,MAAM;AACvB,cAAQ,KAAK,MAAM,QAAQ;AAAA,QACvB,KAAK;AACD,iBAAO,OAAO,cAAc;AAAA,QAChC,KAAK;AACD,iBAAO,SAAS,KAAK,eAAe;AAAA,QACxC;AACI,iBAAO,SAAS;AAAA,MACxB;AAAA,IACJ;AACA,IAAAA,KAAI,UAAU,iBAAiB,SAAU,UAAU;AAC/C,UAAI,QAAQ,KAAK,MAAM;AACvB,cAAQ,KAAK,MAAM,QAAQ;AAAA,QACvB,KAAK;AACD,iBAAO,OAAO,aAAa;AAAA,QAC/B,KAAK;AACD,iBAAO,SAAS,KAAK,cAAc;AAAA,QACvC;AACI,iBAAO,SAAS;AAAA,MACxB;AAAA,IACJ;AACA,IAAAA,KAAI,UAAU,cAAc,SAAU,GAAG,MAAM;AAC3C,UAAI,KAAK,MAAM,aAAa;AACxB,aAAK,MAAM,YAAY,GAAG,IAAI;AAAA,MAClC;AACA,UAAI,MAAM,KAAK,qBAAqB;AACpC,WAAK,mBAAmB;AACxB,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,UAAI,SAAS,KAAK,UAAU;AAC5B,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI;AACJ,UAAI,KAAK,MAAM,WAAW,UAAU;AAChC,mBAAW;AAAA,MACf,WACS,KAAK,MAAM,WAAW,QAAQ;AACnC,YAAI,eAAe,OAAO,sBAAsB;AAChD,YAAI,eAAe,aAAa;AAChC,YAAI,cAAc,aAAa;AAC/B,YAAI,WAAW,SAAS,KAAK,sBAAsB;AACnD,YAAI,SAAS,EAAE,eAAe,OAAO,aAAa,QAAQ,SAAS,QAAQ;AAC3E,YAAI,QAAQ,EAAE,cAAc,OAAO,YAAY,QAAQ,SAAS,OAAO;AACvE,YAAI,SAAS,SAAS,KAAK,cAAc,KAAK,UAAU,KAAK,QAAQ,SAAS,QAAQ;AACtF,YAAI,UAAU,SAAS,KAAK,eAAe,KAAK,UAAU,KAAK,SAAS,SAAS,QAAQ;AACzF,eAAO,KAAK,SAAS,EAAE,QAAQ,EAAE,KAAK,OAAO,OAAc,QAAgB,MAAM,OAAO,EAAE,CAAC;AAAA,MAC/F,WACS,KAAK,MAAM,WAAW,UAAU;AACrC,YAAI,CAAC,KAAK;AACN;AACJ,YAAI,eAAe,OAAO,sBAAsB;AAChD,YAAI,eAAe,aAAa;AAChC,YAAI,cAAc,aAAa;AAC/B,YAAI,SAAS,EAAE,eAAe,OAAO,aAAa,SAAS;AAC3D,YAAI,QAAQ,EAAE,cAAc,OAAO,YAAY,SAAS;AACxD,YAAI,SAAS,OAAO,aAAa,KAAK,UAAU,KAAK,QAAQ,SAAS,QAAQ;AAC9E,YAAI,UAAU,OAAO,cAAc,KAAK,UAAU,KAAK,SAAS,SAAS,QAAQ;AACjF,eAAO,KAAK,SAAS,EAAE,QAAQ,EAAE,KAAK,OAAO,OAAc,QAAgB,MAAM,OAAO,EAAE,CAAC;AAAA,MAC/F,WACS,OAAO,KAAK,MAAM,WAAW,UAAU;AAC5C,mBAAW,SAAS,cAAc,KAAK,MAAM,MAAM;AAAA,MACvD,WACS,KAAK,MAAM,kBAAkB,aAAa;AAC/C,mBAAW,KAAK,MAAM;AAAA,MAC1B;AACA,UAAI,EAAE,oBAAoB,gBAAgB,EAAE,kBAAkB,cAAc;AACxE;AAAA,MACJ;AACA,UAAI,eAAe,SAAS,sBAAsB;AAClD,UAAI,eAAe,aAAa;AAChC,UAAI,cAAc,aAAa;AAC/B,UAAI,aAAa,OAAO,sBAAsB;AAC9C,UAAI,aAAa,WAAW;AAC5B,UAAI,YAAY,WAAW;AAC3B,UAAI,QAAQ,eAAe,cAAc;AACzC,UAAI,MAAM,cAAc;AACxB,UAAI,CAAC,KAAK;AACN;AACJ,WAAK,uBAAuB;AAC5B,UAAI,SAAS,KAAK;AAClB,WAAK,SAAS;AAAA,QACV,QAAQ;AAAA,UACJ,KAAK,MAAM,OAAO;AAAA,UAClB,OAAO,QAAQ,SAAS,cAAc,KAAK,UAAU,KAAK,SAAS,OAAO,OAAO;AAAA,UACjF,QAAQ,OAAO,SAAS,eAAe,KAAK,UAAU,KAAK,UAAU,OAAO;AAAA,UAC5E,MAAM,OAAO,OAAO,OAAO;AAAA,QAC/B;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,KAAI,UAAU,SAAS,SAAU,GAAG,MAAM;AACtC,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,UAAI,KAAK,KAAK,kBAAkB,OAAO,GAAG,MAAM,MAAM,GAAG;AACzD,UAAI,CAAC,KAAK,MAAM,YAAY,KAAK,MAAM,aAAa,QAAQ;AACxD,eAAO,KAAK,MAAM,OAAO,GAAGD,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,MACnG,WACS,KAAK,MAAM,aAAa,KAAK;AAClC,eAAO,KAAK,MAAM,OAAO,GAAGA,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,iBAAiB,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AAAA,MAC/H,WACS,KAAK,MAAM,aAAa,KAAK;AAClC,eAAO,KAAK,MAAM,OAAO,GAAGA,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,iBAAiB,IAAI,MAAM,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AAAA,MAC/H;AAAA,IACJ;AACA,IAAAC,KAAI,UAAU,aAAa,SAAU,GAAG,MAAM;AAC1C,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,UAAI,KAAK,KAAK,kBAAkB,OAAO,GAAG,MAAM,MAAM,GAAG;AACzD,UAAI,CAAC,KAAK,MAAM,YAAY,KAAK,MAAM,aAAa,QAAQ;AACxD,eAAO,KAAK,MAAM,WAAW,GAAGD,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,IAAI,IAAI,CAAC,CAAC;AAAA,MACvG,WACS,KAAK,MAAM,aAAa,KAAK;AAClC,eAAO,KAAK,MAAM,WAAW,GAAGA,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,IAAI,MAAM,GAAG,KAAK,iBAAiB,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnI,WACS,KAAK,MAAM,aAAa,KAAK;AAClC,eAAO,KAAK,MAAM,WAAW,GAAGA,UAASA,UAAS,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG,KAAK,iBAAiB,IAAI,MAAM,GAAG,KAAK,IAAI,KAAK,QAAQ,EAAE,CAAC,CAAC;AAAA,MACnI;AAAA,IACJ;AACA,IAAAC,KAAI,UAAU,gBAAgB,SAAU,GAAG,KAAK,YAAY;AACxD,QAAE,gBAAgB;AAClB,WAAK,SAAS;AAAA,QACV,UAAU;AAAA,MACd,CAAC;AACD,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,SAAS,KAAK;AAClB,UAAI,MAAM,KAAK,qBAAqB;AACpC,WAAK,mBAAmB,EAAE,GAAG,IAAI,IAAI,OAAO,MAAM,GAAG,IAAI,IAAI,OAAO,IAAI;AACxE,WAAK,mBAAmB;AACxB,UAAI,KAAK,MAAM,QAAQ;AACnB,YAAI,WAAW,KAAK,UAAU;AAC9B,YAAI,WAAW;AACf,YAAI,KAAK,MAAM,WAAW,UAAU;AAChC,qBAAW;AAAA,QACf,WACS,KAAK,MAAM,WAAW,QAAQ;AACnC,qBAAW,SAAS;AAAA,QACxB,WACS,KAAK,MAAM,WAAW,UAAU;AACrC,qBAAW;AAAA,QACf,WACS,OAAO,KAAK,MAAM,WAAW,UAAU;AAC5C,qBAAW,SAAS,cAAc,KAAK,MAAM,MAAM;AAAA,QACvD,WACS,KAAK,MAAM,kBAAkB,aAAa;AAC/C,qBAAW,KAAK,MAAM;AAAA,QAC1B;AACA,YAAI,SAAS,KAAK,eAAe;AACjC,YAAI,kBAAkB,YACjB,oBAAoB,eAAe,aAAa,WACjD,oBAAoB,aAAa;AACjC,cAAI,KAAK,KAAK,qBAAqB,GAAG,WAAW,GAAG,UAAU,YAAY,GAAG;AAC7E,cAAI,aAAa,KAAK,cAAc;AACpC,cAAI,YAAY,OAAO,aAAa,UAAU;AAC1C,gBAAI,SAAS,SAAS,GAAG,GAAG;AACxB,kBAAI,QAAQ,OAAO,SAAS,QAAQ,KAAK,EAAE,CAAC,IAAI;AAChD,yBAAW,WAAW,QAAQ;AAAA,YAClC,WACS,SAAS,SAAS,IAAI,GAAG;AAC9B,yBAAW,OAAO,SAAS,QAAQ,MAAM,EAAE,CAAC;AAAA,YAChD;AAAA,UACJ;AACA,cAAI,aAAa,OAAO,cAAc,UAAU;AAC5C,gBAAI,UAAU,SAAS,GAAG,GAAG;AACzB,kBAAI,QAAQ,OAAO,UAAU,QAAQ,KAAK,EAAE,CAAC,IAAI;AACjD,0BAAY,WAAW,SAAS;AAAA,YACpC,WACS,UAAU,SAAS,IAAI,GAAG;AAC/B,0BAAY,OAAO,UAAU,QAAQ,MAAM,EAAE,CAAC;AAAA,YAClD;AAAA,UACJ;AACA,cAAI,WAAW,OAAO,sBAAsB;AAC5C,cAAI,WAAW,SAAS;AACxB,cAAI,UAAU,SAAS;AACvB,cAAI,eAAe,KAAK,MAAM,WAAW,WAAW,EAAE,MAAM,GAAG,KAAK,EAAE,IAAI,SAAS,sBAAsB;AACzG,cAAI,eAAe,aAAa;AAChC,cAAI,cAAc,aAAa;AAC/B,cAAI,cAAc,KAAK,eAAe,QAAQ;AAC9C,cAAI,eAAe,KAAK,gBAAgB,QAAQ;AAChD,cAAI,UAAU,IAAI,YAAY,EAAE,SAAS,MAAM;AAC/C,cAAI,WAAW,IAAI,YAAY,EAAE,SAAS,OAAO;AACjD,cAAI,SAAS,IAAI,WAAW,KAAK;AACjC,cAAI,YAAY,IAAI,WAAW,QAAQ;AACvC,eAAK,WAAW,WAAW,KAAK,WAAW;AACvC,gBAAI,OAAO,WAAW,gBAAgB,QAAQ,KAAK,UAAU,KAAK;AAClE,iBAAK,SAAS,EAAE,UAAU,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,CAAC;AAAA,UACvE;AAEA,cAAI,YAAa,KAAK,MAAM,mBAAmB,CAAC,WAAW,CAAC,QAAS;AACjE,gBAAI,MAAM,eAAe,eAAe,YAAY;AACpD,iBAAK,SAAS,EAAE,UAAU,MAAM,OAAO,QAAQ,IAAI,WAAW,IAAI,CAAC;AAAA,UACvE;AACA,eAAK,UAAU,YAAY,KAAK,WAAW;AACvC,gBAAI,OAAO,UAAU,eAAe,QAAQ,KAAK,UAAU,KAAK;AAChE,iBAAK,SAAS;AAAA,cACV,WAAW,MAAM,OAAO,SAAS,IAAI,YAAY;AAAA,YACrD,CAAC;AAAA,UACL;AAEA,cAAI,aAAc,KAAK,MAAM,mBAAmB,CAAC,UAAU,CAAC,SAAU;AAClE,gBAAI,MAAM,gBAAgB,cAAc,WAAW;AACnD,iBAAK,SAAS;AAAA,cACV,WAAW,MAAM,OAAO,SAAS,IAAI,YAAY;AAAA,YACrD,CAAC;AAAA,UACL;AAAA,QACJ;AAAA,MACJ,OACK;AACD,aAAK,SAAS;AAAA,UACV,UAAU,KAAK,MAAM;AAAA,UACrB,WAAW,KAAK,MAAM;AAAA,QAC1B,CAAC;AAAA,MACL;AACA,UAAI,KAAK,MAAM,eAAe;AAC1B,aAAK,MAAM,cAAc,GAAG,KAAK,UAAU;AAAA,MAC/C;AAAA,IACJ;AACA,IAAAA,KAAI,UAAU,WAAW,SAAU,GAAG,WAAW,YAAY,OAAO;AAChE,UAAI,QAAQ;AAEZ,UAAI,SAAS,EAAE,GAAG,KAAK,iBAAiB,GAAG,GAAG,KAAK,iBAAiB,EAAE;AACtE,UAAI,OAAO,CAAC,MAAM;AAClB,UAAI,MAAM,CAAC,MAAM;AACjB,UAAI,aAAa,CAAC,OAAO,QAAQ,WAAW,cAAc,UAAU;AACpE,UAAI,WAAW,SAAS,SAAS,GAAG;AAChC,YAAI,cAAc,cAAc;AAC5B,iBAAO,KAAK;AAAA,QAChB,WACS,cAAc,YAAY;AAC/B,iBAAO,KAAK;AAAA,QAChB,OACK;AACD,iBAAO,KAAK;AACZ,iBAAO,KAAK;AAAA,QAChB;AAAA,MACJ;AACA,UAAI,iBAAiB,KAAK,UAAU;AACpC,UAAI,OAAO,MAAM,eAAe,KAAK,OAAO,MAAM,eAAe,GAAG;AAChE,yCAAU,WAAY;AAClB,gBAAM,UAAU,SAAS,MAAM;AAAA,QACnC,CAAC;AAAA,MACL;AACA,WAAK,uBAAuB;AAC5B,UAAI,SAAS,KAAK;AAClB,UAAI,IAAI,KAAK,qBAAqB,EAAE,IAAI,OAAO;AAC/C,UAAI,IAAI,KAAK,qBAAqB,EAAE,IAAI,OAAO;AAC/C,WAAK,mBAAmB,EAAE,GAAM,EAAK;AACrC,UAAI,CAAC,KAAK,MAAM;AACZ;AACJ,WAAK,MAAM,SAAS,GAAG,WAAW,YAAY,OAAO;AAAA,QACjD;AAAA,QACA;AAAA,MACJ,CAAC;AAAA,IACL;AACA,IAAAA,KAAI,UAAU,eAAe,SAAU,GAAG,WAAW,YAAY,OAAO;AACpE,WAAK,SAAS;AAAA,QACV,UAAU;AAAA,MACd,CAAC;AACD,UAAI,KAAK,KAAK,qBAAqB,GAAG,WAAW,GAAG,UAAU,YAAY,GAAG;AAC7E,WAAK,SAAS,EAAE,UAAoB,UAAqB,CAAC;AAC1D,UAAI,KAAK,MAAM,cAAc;AACzB,aAAK,MAAM,aAAa,GAAG,WAAW,YAAY,OAAO,KAAK,gBAAgB;AAAA,MAClF;AAAA,IACJ;AACA,IAAAA,KAAI,UAAU,aAAa,SAAU,MAAM;AACvC,UAAI,CAAC,KAAK;AACN;AACJ,WAAK,UAAU,WAAW,EAAE,OAAO,KAAK,OAAO,QAAQ,KAAK,OAAO,CAAC;AAAA,IACxE;AACA,IAAAA,KAAI,UAAU,iBAAiB,SAAU,UAAU;AAC/C,WAAK,UAAU,SAAS,QAAQ;AAAA,IACpC;AACA,IAAAA,KAAI,UAAU,yBAAyB,WAAY;AAC/C,UAAI,QAAQ,KAAK,MAAM;AACvB,UAAI,SAAS,KAAK,UAAU;AAC5B,UAAI,OAAO,KAAK,eAAe;AAC/B,UAAI,CAAC,UAAU,SAAS,MAAM;AAC1B,eAAO;AAAA,UACH,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,MACJ;AACA,UAAI,aAAa,OAAO,sBAAsB;AAC9C,UAAI,aAAa,WAAW;AAC5B,UAAI,YAAY,WAAW;AAC3B,UAAI,WAAW,KAAK,sBAAsB;AAC1C,UAAI,WAAW,KAAK,qBAAqB;AACzC,UAAI,aAAa,OAAO;AACxB,UAAI,YAAY,OAAO;AACvB,WAAK,mBAAmB;AAAA,QACpB,MAAM,SAAS,OAAO,aAAa,aAAa,SAAS,IAAI;AAAA,QAC7D,KAAK,SAAS,MAAM,YAAY,YAAY,SAAS,IAAI;AAAA,MAC7D;AAAA,IACJ;AACA,IAAAA,KAAI,UAAU,SAAS,WAAY;AAC/B,UAAI,QAAQ;AACZ,UAAI,KAAK,KAAK,OAAO,kBAAkB,GAAG,iBAAiB,QAAQ,GAAG,OAAO,sBAAsB,GAAG,qBAAqB,WAAW,GAAG,UAAU,cAAc,GAAG,aAAa,YAAY,GAAG,WAAW,WAAW,GAAG,UAAU,WAAW,GAAG,UAAU,SAAS,GAAG,QAAQ,uBAAuB,GAAG,sBAAsB,SAAS,GAAG,QAAQ,WAAW,GAAG,UAAU,gBAAgB,GAAG,eAAe,WAAW,GAAG,UAAU,eAAe,GAAG,cAAc,cAAc,GAAG,aAAa,SAAS,GAAG,QAAQ,aAAa,GAAG,YAAY,qBAAqB,GAAG,oBAAoB,sBAAsB,GAAG,qBAAqB,wBAAwB,GAAG,uBAAuB,iBAAiB,GAAG,gBAAgB,aAAa,GAAG,YAAY,2BAA2B,GAAG,0BAA0B,2BAA2B,GAAG,0BAA0B,QAAQ,GAAG,OAAO,gBAAgB,GAAG,eAAe,qBAAqB,GAAG,oBAAoB,iBAAiB,OAAO,IAAI,CAAC,mBAAmB,SAAS,uBAAuB,YAAY,eAAe,aAAa,YAAY,YAAY,UAAU,wBAAwB,UAAU,YAAY,iBAAiB,YAAY,gBAAgB,eAAe,UAAU,cAAc,sBAAsB,uBAAuB,yBAAyB,kBAAkB,cAAc,4BAA4B,4BAA4B,SAAS,iBAAiB,oBAAoB,CAAC;AACx5C,UAAI,eAAe,KAAK,MAAM,UAAUD,UAAS,CAAC,GAAG,KAAK,MAAM,OAAO,IAAI;AAE3E,aAAO,eAAe;AACtB,UAAI,cAAc,mBAAmB,sBAAsB,EAAE,QAAQ,OAAO,IAAI,EAAE,QAAQ,OAAO;AACjG,UAAI,aAAaA,UAASA,UAASA,UAAS,CAAC,GAAG,cAAc,GAAG,WAAW,GAAG,KAAK;AACpF,UAAI,KAAK,KAAK,kBAAkB,OAAO,GAAG,MAAM,MAAM,GAAG;AACzD,UAAI;AACJ,UAAI,UAAU;AACV,4BAAoB;AAAA,UAChB,GAAG,SAAS,IAAI;AAAA,UAChB,GAAG,SAAS,IAAI;AAAA,QACpB;AAAA,MACJ;AAEA,UAAI,MAAM,KAAK,MAAM,WAAW,SAAY;AAC5C,UAAI,sBAAsB,KAAK,MAAM,WAAW,SAAS;AACzD,iBAAQ;AAAA,QAAc,uBAAAE;AAAA,QAAW;AAAA,UAAE,KAAK,SAAU,GAAG;AAC7C,gBAAI,CAAC;AACD;AACJ,kBAAM,YAAY;AAAA,UACtB;AAAA,UAAG,QAAQ,sBAAsB,IAAI,OAAO,mBAAmB,IAAI;AAAA,UAAW,iBAAiB;AAAA,UAAc;AAAA;AAAA,UAE7G;AAAA,UAAsB,SAAS,KAAK;AAAA,UAAa,QAAQ,KAAK;AAAA,UAAQ,QAAQ,KAAK;AAAA,UAAY,MAAM;AAAA,UAAqB,UAAU;AAAA,UAAiB,MAAM;AAAA,UAAU,QAAQ,SAAS,KAAK,MAAM,SAAS;AAAA,UAAW,UAAU;AAAA,UAAK;AAAA,UAA4C;AAAA,UAAgB;AAAA,UAAc;AAAA,UAA8B,SAAS,KAAK;AAAA,UAAkB,gBAAgB;AAAA,QAAmB;AAAA,YAC/Y,6BAAc,WAAWF,UAAS,CAAC,GAAG,gBAAgB,EAAE,KAAK,SAAU,GAAG;AAClE,cAAI,CAAC;AACD;AACJ,gBAAM,YAAY;AAClB,gBAAM,iBAAiB,UAAU,EAAE;AAAA,QACvC,GAAG,aAAa,cAAc,MAAM,KAAK,MAAM,MAAM,QAAQ,OAAO,mBAAmB,YAAY,wBAAwB,cAAc,IAAI,gBAAgB,eAAe,KAAK,eAAe,UAAU,KAAK,UAAU,cAAc,KAAK,cAAc,OAAO,YAAY,UAAU,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,UAAU,KAAK,MAAM,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,UAAU,WAAW,KAAK,MAAM,WAAW,KAAK,MAAM,YAAY,KAAK,MAAM,WAAW,MAAM,YAAY,oBAAoB,0BAA0B,oBAAoB,0BAA0B,iBAAiB,KAAK,MAAM,iBAAiB,2BAA2B,KAAK,MAAM,2BAA2B,4BAA4B,KAAK,MAAM,4BAA4B,cAAc,oBAAoB,eAAe,qBAAqB,iBAAiB,uBAAuB,OAAO,KAAK,MAAM,MAAM,CAAC,GAAG,QAAQ;AAAA,MAAC;AAAA,IACp6B;AACA,IAAAC,KAAI,eAAe;AAAA,MACf,UAAU,OAAO;AAAA,MACjB,WAAW,OAAO;AAAA,MAClB,OAAO;AAAA,MACP,eAAe,WAAY;AAAA,MAAE;AAAA,MAC7B,UAAU,WAAY;AAAA,MAAE;AAAA,MACxB,cAAc,WAAY;AAAA,MAAE;AAAA,MAC5B,aAAa,WAAY;AAAA,MAAE;AAAA,MAC3B,QAAQ,WAAY;AAAA,MAAE;AAAA,MACtB,YAAY,WAAY;AAAA,MAAE;AAAA,IAC9B;AACA,WAAOA;AAAA,EACX,EAAE,2BAAa;AAAA;", "names": ["Element", "i", "checker", "nodeInterop", "nodeInterop", "nodeInterop", "Draggable", "Draggable", "import_react", "import_jsx_runtime", "import_react", "_jsx", "extendStatics", "d", "b", "__assign", "Resizable", "_jsx", "_jsxs", "import_react_dom", "d", "b", "__extends", "__assign", "Rnd", "Draggable"]}