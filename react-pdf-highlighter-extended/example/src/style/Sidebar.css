.sidebar {
    overflow: auto;
    color: #333333;
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.highlight__image__container::-webkit-scrollbar,
.sidebar::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

.highlight__image__container::-webkit-scrollbar-thumb,
.sidebar::-webkit-scrollbar-thumb {
    background-color: #ccc;
    border-radius: 5px;
}

.highlight__image__container::-webkit-scrollbar-thumb:hover,
.sidebar::-webkit-scrollbar-thumb:hover {
    background-color: #aaa;
}

.highlight__image__container::-webkit-scrollbar-track,
.sidebar::-webkit-scrollbar-track {
    background-color: #f4f4f4;
    border-radius: 5px;
}

.highlight__image__container::-webkit-scrollbar-track-piece,
.sidebar::-webkit-scrollbar-track-piece {
    background-color: none;
    border-radius: 5px;
}

.sidebar__highlights {
    list-style: none;
    padding: 0;
}

.highlight__location {
    margin-top: 0.5rem;
    text-align: right;
    font-size: 0.8rem;
    color: #777;
}

.highlight__image__container {
    overflow: auto;
    max-width: 100%;
}

.highlight__image {
    border: 1px solid #ddd;
    border-radius: 4px;
}

.sidebar__highlight {
    padding: 1rem;
    cursor: pointer;
    transition: background 140ms ease-in;
    border-bottom: 1px solid #e1e1e1;
}

.sidebar__highlight:hover {
    background: #f8f9fa;
}

a {
    color: #007bff;
}

blockquote {
    padding: 0;
    margin: 0;
    quotes: "\201c" "\201d";
}

blockquote:before {
    content: open-quote;
}

blockquote:after {
    content: close-quote;
}

/* Add these styles for the buttons */
.sidebar__toggle,
.sidebar__reset {
    display: inline-block;
    padding: 0.5rem 1rem;
    font-size: 1rem;
    font-weight: 500;
    line-height: 1.75;
    text-align: center;
    text-decoration: none;
    white-space: nowrap;
    border: 1px solid #2196f3;
    background-color: #2196f3;
    color: #fff;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.sidebar__reset {
    border: 1px solid #f44336;
    background-color: #f44336;
}

.sidebar__toggle:hover {
    background-color: #1565c0;
    border-color: #1565c0;
}

.sidebar__reset:hover {
    background-color: #d32f2f;
    border-color: #d32f2f;
}