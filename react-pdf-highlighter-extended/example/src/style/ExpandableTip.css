.Tip__compact {
  cursor: pointer;
  background-color: #2c3e50;
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: #fff;
  padding: 8px 16px;
  border-radius: 4px;
  font-size: larger;
}

.Tip__card {
  padding: 16px;
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #e1e1e1;
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.Tip__card textarea {
  font-size: 16px;
  width: 100%;
  height: 70px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.Tip__card input[type="submit"] {
  margin-top: 0.5rem;
  padding: 10px 16px;
  font-size: 16px;
  background-color: #2196f3;
  color: #fff;
  border: 1px solid #2196f3;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.Tip__card input[type="submit"]:hover {
  background-color: #1565c0;
  border-color: #1565c0;
}
